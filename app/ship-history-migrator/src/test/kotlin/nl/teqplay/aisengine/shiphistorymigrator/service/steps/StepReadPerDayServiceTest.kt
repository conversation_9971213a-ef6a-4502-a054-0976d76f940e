package nl.teqplay.aisengine.shiphistorymigrator.service.steps

import com.amazonaws.services.s3.AmazonS3
import com.amazonaws.services.s3.model.ListObjectsV2Request
import com.amazonaws.services.s3.model.ListObjectsV2Result
import com.amazonaws.services.s3.model.S3ObjectSummary
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveConfiguration
import nl.teqplay.aisengine.bucketing.storage.archive.ArchiveClientService
import nl.teqplay.aisengine.bucketing.storage.archive.ArchiveReadStorageImpl
import nl.teqplay.aisengine.platform.model.LegacyHistoricShipInfo
import nl.teqplay.aisengine.shiphistorymigrator.ConcurrentMapOfSubjectToFileType
import nl.teqplay.aisengine.shiphistorymigrator.MapOfFileNameToArchiveBucketId
import nl.teqplay.aisengine.shiphistorymigrator.config.PlatformConfig
import nl.teqplay.aisengine.shiphistorymigrator.model.S3FileType
import nl.teqplay.aisengine.shiphistorymigrator.model.S3FileType.Enum.BY_AREA
import nl.teqplay.aisengine.shiphistorymigrator.model.S3FileType.Enum.BY_MMSI
import nl.teqplay.aisengine.shiphistorymigrator.service.s3.S3StorageService
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.time.LocalDate

class StepReadPerDayServiceTest {

    private val s3StorageService = mock<S3StorageService>()
    private val platformConfig = mock<PlatformConfig>()

    private val stepReadPerDayService = StepReadPerDayService(
        s3StorageService,
        platformConfig,
    )

    @Test
    fun readAndStore() {
        val archiveConfig = mock<BucketArchiveConfiguration>()
        whenever(archiveConfig.name).thenReturn("bucket")
        whenever(platformConfig.archive).thenReturn(archiveConfig)

        val archiveClientService = mock<ArchiveClientService>()
        whenever(platformConfig.archiveClientService).thenReturn(archiveClientService)

        val archiveReadStorage = mock<ArchiveReadStorageImpl<LegacyHistoricShipInfo>>()
        whenever(platformConfig.archiveReadStorage).thenReturn(archiveReadStorage)

        // S3 client, two list requests to process truncated results
        val s3Client = mock<AmazonS3>()

        val fileName1 = "ship_${LocalDate.EPOCH},0.zip"
        val listObjectsResult1 = mock<ListObjectsV2Result>()
        whenever(listObjectsResult1.isTruncated).thenReturn(true)
        whenever(listObjectsResult1.objectSummaries).thenReturn(listOf(S3ObjectSummary().apply { key = fileName1 }))

        val fileName2 = "ship_${LocalDate.EPOCH},4.0,52.0.zip"
        val listObjectsResult2 = mock<ListObjectsV2Result>()
        whenever(listObjectsResult2.isTruncated).thenReturn(false)
        whenever(listObjectsResult2.objectSummaries).thenReturn(listOf(S3ObjectSummary().apply { key = fileName2 }))

        whenever(s3Client.listObjectsV2(any<ListObjectsV2Request>()))
            .thenReturn(listObjectsResult1)
            .thenReturn(listObjectsResult2)
        whenever(archiveClientService.s3Client).thenReturn(s3Client)

        // this would actually read data from S3, instead check that the bucket ids
        // are correct and return the expected data type
        val files: MapOfFileNameToArchiveBucketId = mutableMapOf(
            fileName1 to "${LocalDate.EPOCH},0",
            fileName2 to "${LocalDate.EPOCH},4.0,52.0",
        )
        val expectedIndex = ConcurrentMapOfSubjectToFileType()
        expectedIndex["0"] = S3FileType("1970-01,0", BY_MMSI)
        expectedIndex["4,0_52,0"] = S3FileType("1970-01,4.0,52.0", BY_AREA)
        whenever(s3StorageService.readAndStore(any(), eq(files))).thenReturn(expectedIndex)

        val index = stepReadPerDayService.readAndStore(LocalDate.EPOCH)
        assertEquals(expectedIndex, index)
    }
}
