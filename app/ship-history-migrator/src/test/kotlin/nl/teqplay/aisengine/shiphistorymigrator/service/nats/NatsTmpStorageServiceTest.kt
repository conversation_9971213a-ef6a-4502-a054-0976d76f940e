package nl.teqplay.aisengine.shiphistorymigrator.service.nats

import nl.teqplay.aisengine.shiphistorymigrator.MapOfSubjectToRawData
import nl.teqplay.aisengine.shiphistorymigrator.model.PublishData
import nl.teqplay.aisengine.shiphistorymigrator.model.S3FileType
import nl.teqplay.aisengine.shiphistorymigrator.model.S3FileType.Enum.BY_MMSI
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.LocalDate
import java.util.concurrent.LinkedBlockingQueue

class NatsTmpStorageServiceTest {

    private val natsTmpStorageContext = mock<NatsTmpStorageContext>()
    private val natsTmpStorageContextService = mock<NatsTmpStorageContextService>().apply {
        whenever(createContext(any())).thenReturn(natsTmpStorageContext)
    }
    private val natsTmpStorageService = NatsTmpStorageService(
        natsTmpStorageContextService,
    )

    @Test
    fun publish() {
        val queue = LinkedBlockingQueue<PublishData>(1)
        whenever(natsTmpStorageContext.incomingQueue).thenReturn(queue)

        val data = PublishData(
            date = LocalDate.EPOCH,
            fileType = S3FileType("key", BY_MMSI),
            subject = "subject",
            data = emptyList(),
        )
        natsTmpStorageService.publish(data)

        assertTrue(queue.isNotEmpty())
    }

    @Test
    fun waitForQueue() {
        whenever(natsTmpStorageContext.empty)
            .thenReturn(false)
            .thenReturn(true)

        natsTmpStorageService.waitForQueue()

        assertTrue(natsTmpStorageContext.empty)
    }

    @Test
    fun readDataAndQueue() {
        whenever(natsTmpStorageContext.readAndEnqueue(any())).then {
            val queue = it.getArgument<LinkedBlockingQueue<MapOfSubjectToRawData>>(0)
            queue.put(emptyMap())
        }.then { /* do nothing after the first time */ }

        val queue = LinkedBlockingQueue<MapOfSubjectToRawData>(1)
        natsTmpStorageService.readAndEnqueue(queue)
        assertTrue(queue.isNotEmpty())
    }

    @Test
    fun purge() {
        natsTmpStorageService.purge()
        verify(natsTmpStorageContext, times(64)).purge()
    }
}
