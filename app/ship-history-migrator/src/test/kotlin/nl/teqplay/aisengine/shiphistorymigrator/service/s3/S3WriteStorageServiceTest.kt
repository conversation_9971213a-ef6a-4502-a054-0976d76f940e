package nl.teqplay.aisengine.shiphistorymigrator.service.s3

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.aisengine.bucketing.model.AisHistoricOrderedDiffMessage
import nl.teqplay.aisengine.shiphistorymigrator.config.ArchiveWriteStorageByArea
import nl.teqplay.aisengine.shiphistorymigrator.config.ArchiveWriteStorageByMmsi
import nl.teqplay.aisengine.shiphistorymigrator.config.ShipHistoryConfig
import nl.teqplay.aisengine.shiphistorymigrator.model.S3FileType
import nl.teqplay.aisengine.shiphistorymigrator.model.S3FileType.Enum.BY_AREA
import nl.teqplay.aisengine.shiphistorymigrator.model.S3FileType.Enum.BY_MMSI
import nl.teqplay.aisengine.shiphistorymigrator.service.GZipService
import nl.teqplay.aisengine.shiphistorymigrator.service.nats.NatsPayloadService
import nl.teqplay.aisengine.shiphistorymigrator.service.s3.S3WriteStorageService.QueuedWrite
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.LocalDate

class S3WriteStorageServiceTest {

    private val shipHistory = mock<ShipHistoryConfig>()
    private val objectMapper = jacksonObjectMapper()
    private val natsPayloadService = NatsPayloadService(GZipService(), objectMapper)
    private val s3WriteStorageService = S3WriteStorageService(
        shipHistory,
        natsPayloadService,
        objectMapper,
    )

    @Test
    fun writeByMmsi() {
        val calls = mutableListOf<Map<LocalDate, ByteArray>>()
        val archiveWriteStorage = mock<ArchiveWriteStorageByMmsi>().apply {
            whenever(overwriteDirect(any(), any()))
                .thenAnswer {
                    calls.add(it.getArgument(1))
                    false
                }
                .thenAnswer {
                    calls.add(it.getArgument(1))
                    true
                }
        }
        whenever(shipHistory.archiveWriteStorageByMmsi).thenReturn(archiveWriteStorage)

        val item = QueuedWrite(
            fileType = S3FileType("2023-06,244060924", BY_MMSI),
            data = mapOf(
                "2023-06-01" to natsPayloadService.serialize(emptyList()),
                "2023-06-02" to natsPayloadService.serialize(emptyList()),
            )
        )

        s3WriteStorageService.write(item)
        s3WriteStorageService.stats()

        verify(archiveWriteStorage, times(2)).overwriteDirect(eq("mmsi,2023-06,244060924.zip"), any())

        val data = item.data
            .mapKeys { LocalDate.parse(it.key) }
            .mapValues { natsPayloadService.deserialize(it.value) }
        calls.forEach {
            assertEquals(data, it.mapValues { objectMapper.readValue<List<AisHistoricOrderedDiffMessage>>(it.value) })
        }
    }

    @Test
    fun writeByArea() {
        val calls = mutableListOf<Map<LocalDate, ByteArray>>()
        val archiveWriteStorage = mock<ArchiveWriteStorageByArea>().apply {
            whenever(overwriteDirect(any(), any()))
                .thenAnswer {
                    calls.add(it.getArgument(1))
                    false
                }
                .thenAnswer {
                    calls.add(it.getArgument(1))
                    true
                }
        }
        whenever(shipHistory.archiveWriteStorageByArea).thenReturn(archiveWriteStorage)

        val item = QueuedWrite(
            fileType = S3FileType("2023-06,4.4,51.8", BY_AREA),
            data = mapOf(
                "2023-06-01" to natsPayloadService.serialize(emptyList()),
                "2023-06-02" to natsPayloadService.serialize(emptyList()),
            )
        )

        s3WriteStorageService.write(item)
        s3WriteStorageService.stats()

        verify(archiveWriteStorage, times(2)).overwriteDirect(eq("area,2023-06,4.4,51.8.zip"), any())
        val data = item.data
            .mapKeys { LocalDate.parse(it.key) }
            .mapValues { natsPayloadService.deserialize(it.value) }
        calls.forEach {
            assertEquals(data, it.mapValues { objectMapper.readValue<List<AisHistoricOrderedDiffMessage>>(it.value) })
        }
    }
}
