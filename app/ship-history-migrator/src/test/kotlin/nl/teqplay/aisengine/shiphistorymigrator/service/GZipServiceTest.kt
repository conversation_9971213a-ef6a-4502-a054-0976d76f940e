package nl.teqplay.aisengine.shiphistorymigrator.service

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class GZipServiceTest {

    private val gZipService = GZipService()

    @Test
    fun zipUnzip() {
        val data = "data"
        val gzipped = gZipService.zip(data)
        val output = gZipService.unzip(gzipped)
        assertEquals(output, data)
    }
}
