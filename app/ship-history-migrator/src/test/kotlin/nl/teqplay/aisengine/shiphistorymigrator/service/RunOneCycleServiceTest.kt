package nl.teqplay.aisengine.shiphistorymigrator.service

import nl.teqplay.aisengine.shiphistorymigrator.ConcurrentMapOfSubjectToFileType
import nl.teqplay.aisengine.shiphistorymigrator.model.S3FileType
import nl.teqplay.aisengine.shiphistorymigrator.model.S3FileType.Enum.BY_MMSI
import nl.teqplay.aisengine.shiphistorymigrator.service.nats.NatsStatsService
import nl.teqplay.aisengine.shiphistorymigrator.service.nats.NatsTmpStorageService
import nl.teqplay.aisengine.shiphistorymigrator.service.steps.StepReadPerDayService
import nl.teqplay.aisengine.shiphistorymigrator.service.steps.StepTrackMonthIndexService
import nl.teqplay.aisengine.shiphistorymigrator.service.steps.StepTrackMonthProgressService
import nl.teqplay.aisengine.shiphistorymigrator.service.steps.StepWritePerMonthService
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.never
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.LocalDate
import java.time.YearMonth

@TestInstance(TestInstance.Lifecycle.PER_METHOD)
class RunOneCycleServiceTest {

    private val stepTrackMonthIndexService = mock<StepTrackMonthIndexService>()
    private val stepTrackMonthProgressService = mock<StepTrackMonthProgressService>()
    private val stepReadPerDayService = mock<StepReadPerDayService>()
    private val stepWritePerMonthService = mock<StepWritePerMonthService>()
    private val natsTmpStorageService = mock<NatsTmpStorageService>()
    private val natsStatsService = mock<NatsStatsService>()
    private val runOneCycleService = RunOneCycleService(
        stepTrackMonthIndexService,
        stepTrackMonthProgressService,
        stepReadPerDayService,
        stepWritePerMonthService,
        natsTmpStorageService,
        natsStatsService,
    )

    @Test
    fun `runOneCycle - nothing to run`() {
        whenever(stepTrackMonthProgressService.getDatesToRead()).thenReturn(null)
        assertFalse(runOneCycleService.runOneCycle())
        verify(natsTmpStorageService, never()).purge()
        verify(stepReadPerDayService, never()).readAndStore(any())
        verify(stepWritePerMonthService, never()).write(any())
        verify(stepTrackMonthProgressService, never()).progressToNext()
    }

    @Test
    fun `runOneCycle - reuse previous index`() {
        val expectedIndex = ConcurrentMapOfSubjectToFileType(
            mapOf(
                "0" to S3FileType("0", BY_MMSI),
                "1" to S3FileType("1", BY_MMSI),
                "2" to S3FileType("2", BY_MMSI),
            )
        )

        whenever(stepTrackMonthIndexService.getIndex()).thenReturn(expectedIndex)

        whenever(stepTrackMonthProgressService.getDatesToRead())
            .thenReturn(
                YearMonth.of(2023, 6) to listOf(
                    LocalDate.parse("2023-06-01"),
                    LocalDate.parse("2023-06-02"),
                    LocalDate.parse("2023-06-03"),
                )
            )
            .thenReturn(null)

        whenever(stepTrackMonthProgressService.progressToNext())
            .thenReturn(true)
            .thenThrow(Error("called too many times"))

        assertTrue(runOneCycleService.runOneCycle())
        assertFalse(runOneCycleService.runOneCycle())

        verify(natsTmpStorageService, never()).purge()
        verify(stepReadPerDayService, never()).readAndStore(any())

        verify(stepWritePerMonthService, times(1)).write(eq(expectedIndex))
        verify(stepTrackMonthProgressService, times(1)).progressToNext()

        verify(natsStatsService, never()).saveStatsPerDay(any(), any())
        verify(natsStatsService, never()).saveStatsPerMonth(any(), any())
    }

    @Test
    fun runOneCycle() {
        whenever(stepTrackMonthProgressService.getDatesToRead())
            .thenReturn(
                YearMonth.of(2023, 6) to listOf(
                    LocalDate.parse("2023-06-01"),
                    LocalDate.parse("2023-06-02"),
                    LocalDate.parse("2023-06-03"),
                )
            )
            .thenReturn(null)

        whenever(stepTrackMonthProgressService.progressToNext())
            .thenReturn(true)
            .thenThrow(Error("called too many times"))

        whenever(stepReadPerDayService.readAndStore(any()))
            .thenReturn(ConcurrentMapOfSubjectToFileType(mapOf("0" to S3FileType("0", BY_MMSI))))
            .thenReturn(ConcurrentMapOfSubjectToFileType(mapOf("1" to S3FileType("1", BY_MMSI))))
            .thenReturn(ConcurrentMapOfSubjectToFileType(mapOf("2" to S3FileType("2", BY_MMSI))))
            .thenThrow(Error("called too many times"))

        assertTrue(runOneCycleService.runOneCycle())
        assertFalse(runOneCycleService.runOneCycle())

        verify(natsTmpStorageService, times(1)).purge()

        verify(stepReadPerDayService, times(3)).readAndStore(any())
        verify(stepReadPerDayService, times(1)).readAndStore(eq(LocalDate.parse("2023-06-01")))
        verify(stepReadPerDayService, times(1)).readAndStore(eq(LocalDate.parse("2023-06-02")))
        verify(stepReadPerDayService, times(1)).readAndStore(eq(LocalDate.parse("2023-06-03")))

        val expectedIndex = ConcurrentMapOfSubjectToFileType(
            mapOf(
                "0" to S3FileType("0", BY_MMSI),
                "1" to S3FileType("1", BY_MMSI),
                "2" to S3FileType("2", BY_MMSI),
            )
        )
        verify(stepWritePerMonthService, times(1)).write(eq(expectedIndex))

        verify(stepTrackMonthProgressService, times(1)).progressToNext()

        verify(natsStatsService, times(3)).saveStatsPerDay(any(), any())
        verify(natsStatsService, times(1)).saveStatsPerMonth(any(), any())
    }
}
