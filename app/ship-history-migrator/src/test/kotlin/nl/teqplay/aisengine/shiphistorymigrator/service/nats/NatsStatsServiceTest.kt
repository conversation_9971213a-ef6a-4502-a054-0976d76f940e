package nl.teqplay.aisengine.shiphistorymigrator.service.nats

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.aisengine.shiphistorymigrator.ConcurrentMapOfSubjectToFileType
import nl.teqplay.aisengine.shiphistorymigrator.model.S3FileType
import nl.teqplay.aisengine.shiphistorymigrator.model.S3FileType.Enum.BY_AREA
import nl.teqplay.aisengine.shiphistorymigrator.model.S3FileType.Enum.BY_MMSI
import nl.teqplay.aisengine.shiphistorymigrator.service.nats.NatsStatsService.Stats
import nl.teqplay.skeleton.common.config.NatsProperties
import nl.teqplay.skeleton.nats.NatsClientBuilder
import nl.teqplay.skeleton.nats.NatsClientMock
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.time.LocalDate
import java.time.YearMonth

class NatsStatsServiceTest {

    private val objectMapper = mock<ObjectMapper>()
    private val kv = NatsClientMock().keyValueBucket<Stats>(
        name = "stats",
        serializer = objectMapper::writeValueAsBytes,
        deserializer = objectMapper::readValue,
    )
    private val natsClientBuilder = mock<NatsClientBuilder>().apply {
        whenever(keyValueBucket<Stats>(any(), any(), any(), any(), anyOrNull(), anyOrNull(), anyOrNull()))
            .thenReturn(kv)
    }
    private val config = mock<NatsProperties>()
    private val natsStatsService = NatsStatsService(
        natsClientBuilder,
        config,
        objectMapper,
    )

    @Test
    fun saveStatsPerDay() {
        val date = LocalDate.EPOCH
        val store = ConcurrentMapOfSubjectToFileType()
        store["byMmsi1"] = S3FileType("byMmsi", BY_MMSI)
        store["byMmsi2"] = S3FileType("byMmsi", BY_MMSI)
        store["byArea"] = S3FileType("byArea", BY_AREA)

        assertNull(kv.get(date.toString()))

        natsStatsService.saveStatsPerDay(date, store)

        val entry = kv.get(date.toString())
        assertNotNull(entry)
        assertEquals(2, entry?.value?.countByMmsi)
        assertEquals(1, entry?.value?.countByArea)
    }

    @Test
    fun saveStatsPerMonth() {
        val date = LocalDate.EPOCH
        val yearMonth = YearMonth.of(date.year, date.monthValue)
        val index = ConcurrentMapOfSubjectToFileType()
        index["byMmsi1"] = S3FileType("byMmsi", BY_MMSI)
        index["byMmsi2"] = S3FileType("byMmsi", BY_MMSI)
        index["byArea"] = S3FileType("byArea", BY_AREA)

        assertNull(kv.get(yearMonth.toString()))

        natsStatsService.saveStatsPerMonth(yearMonth, index)

        val entry = kv.get(yearMonth.toString())
        assertNotNull(entry)
        assertEquals(2, entry?.value?.countByMmsi)
        assertEquals(1, entry?.value?.countByArea)
    }
}
