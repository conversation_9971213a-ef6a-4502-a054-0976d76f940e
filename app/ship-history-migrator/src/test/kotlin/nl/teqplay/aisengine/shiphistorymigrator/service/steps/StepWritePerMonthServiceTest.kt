package nl.teqplay.aisengine.shiphistorymigrator.service.steps

import nl.teqplay.aisengine.shiphistorymigrator.ConcurrentMapOfSubjectToFileType
import nl.teqplay.aisengine.shiphistorymigrator.MapOfSubjectToRawData
import nl.teqplay.aisengine.shiphistorymigrator.model.S3FileType
import nl.teqplay.aisengine.shiphistorymigrator.service.nats.NatsTmpStorageService
import nl.teqplay.aisengine.shiphistorymigrator.service.s3.S3StorageService
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.LocalDate
import java.util.concurrent.LinkedBlockingQueue

class StepWritePerMonthServiceTest {

    private val s3StorageService = mock<S3StorageService>()
    private val natsTmpStorageService = mock<NatsTmpStorageService>()
    private val stepWritePerMonthService = StepWritePerMonthService(
        s3StorageService,
        natsTmpStorageService,
    )

    @Test
    fun write() {
        val subject = "244060924"
        val data = mapOf(LocalDate.EPOCH.toString() to "[]".toByteArray())
        val s3FileType = S3FileType(key = "2023-06,244060924", enum = S3FileType.Enum.BY_MMSI)
        val index = ConcurrentMapOfSubjectToFileType(mapOf(subject to s3FileType))

        whenever(s3StorageService.lock(any(), any())).then {
            it.getArgument<() -> Unit>(1).invoke()
        }
        whenever(natsTmpStorageService.readAndEnqueue(any())).then {
            val queue = it.getArgument<LinkedBlockingQueue<MapOfSubjectToRawData>>(0)
            queue.put(mapOf(subject to data))
        }

        stepWritePerMonthService.write(index)

        verify(natsTmpStorageService, times(1)).readAndEnqueue(any())
        verify(s3StorageService, times(1)).write(eq(s3FileType), eq(data))
    }
}
