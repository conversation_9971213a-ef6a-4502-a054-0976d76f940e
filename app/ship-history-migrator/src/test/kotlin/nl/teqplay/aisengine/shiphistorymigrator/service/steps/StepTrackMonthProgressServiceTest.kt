package nl.teqplay.aisengine.shiphistorymigrator.service.steps

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.aisengine.shiphistorymigrator.service.steps.StepTrackMonthProgressService.Progress
import nl.teqplay.skeleton.common.config.NatsProperties
import nl.teqplay.skeleton.nats.NatsClientBuilder
import nl.teqplay.skeleton.nats.NatsClientMock
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.time.LocalDate
import java.time.YearMonth

class StepTrackMonthProgressServiceTest {

    private val objectMapper = mock<ObjectMapper>()
    private val kv = NatsClientMock().keyValueBucket<Progress>(
        name = "progress",
        serializer = objectMapper::writeValueAsBytes,
        deserializer = objectMapper::readValue,
    )
    private val natsClientBuilder = mock<NatsClientBuilder>().apply {
        whenever(keyValueBucket<Progress>(any(), any(), any(), any(), anyOrNull(), anyOrNull(), anyOrNull()))
            .thenReturn(kv)
    }
    private val config = mock<NatsProperties>()
    private val stepTrackMonthProgressService = StepTrackMonthProgressService(
        natsClientBuilder,
        config,
        objectMapper,
    )

    @Test
    fun `getDatesToRead & progressToNext - past-to-future`() {
        // key doesn't exist
        assertThrows<Exception> { stepTrackMonthProgressService.getDatesToRead() }

        // progress two months
        val progress = Progress(
            from = YearMonth.of(2023, 6),
            toInclusive = YearMonth.of(2023, 7),
            current = YearMonth.of(2023, 6),
        )
        kv.put("progress", progress)

        // progress first month
        var expectedDates = LocalDate.parse("2023-06-01")
            .datesUntil(LocalDate.parse("2023-07-01"))
            .toList()
        var output = stepTrackMonthProgressService.getDatesToRead()!!
        var actualYearMonth = output.first
        var actualDates = output.second
        assertEquals(YearMonth.of(2023, 6), actualYearMonth)
        assertEquals(expectedDates, actualDates)
        assertEquals(30, actualDates.size)

        // second month left to progress to
        assertTrue(stepTrackMonthProgressService.progressToNext())

        // progress second month
        expectedDates = LocalDate.parse("2023-07-01")
            .datesUntil(LocalDate.parse("2023-08-01"))
            .toList()
        output = stepTrackMonthProgressService.getDatesToRead()!!
        actualYearMonth = output.first
        actualDates = output.second
        assertEquals(YearMonth.of(2023, 7), actualYearMonth)
        assertEquals(expectedDates, actualDates)
        assertEquals(31, actualDates.size)

        // no months left to progress to
        assertFalse(stepTrackMonthProgressService.progressToNext())
        assertNull(stepTrackMonthProgressService.getDatesToRead())
    }

    @Test
    fun `getDatesToRead & progressToNext - future-to-past`() {
        // key doesn't exist
        assertThrows<Exception> { stepTrackMonthProgressService.getDatesToRead() }

        // progress two months
        val progress = Progress(
            from = YearMonth.of(2023, 7),
            toInclusive = YearMonth.of(2023, 6),
            current = YearMonth.of(2023, 7),
        )
        kv.put("progress", progress)

        // progress first month
        var expectedDates = LocalDate.parse("2023-07-01")
            .datesUntil(LocalDate.parse("2023-08-01"))
            .toList()
        var output = stepTrackMonthProgressService.getDatesToRead()!!
        var actualYearMonth = output.first
        var actualDates = output.second
        assertEquals(YearMonth.of(2023, 7), actualYearMonth)
        assertEquals(expectedDates, actualDates)
        assertEquals(31, actualDates.size)

        // second month left to progress to
        assertTrue(stepTrackMonthProgressService.progressToNext())

        // progress second month
        expectedDates = LocalDate.parse("2023-06-01")
            .datesUntil(LocalDate.parse("2023-07-01"))
            .toList()
        output = stepTrackMonthProgressService.getDatesToRead()!!
        actualYearMonth = output.first
        actualDates = output.second
        assertEquals(YearMonth.of(2023, 6), actualYearMonth)
        assertEquals(expectedDates, actualDates)
        assertEquals(30, actualDates.size)

        // no months left to progress to
        assertFalse(stepTrackMonthProgressService.progressToNext())
        assertNull(stepTrackMonthProgressService.getDatesToRead())
    }
}
