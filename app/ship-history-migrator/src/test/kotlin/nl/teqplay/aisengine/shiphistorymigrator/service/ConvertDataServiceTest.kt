package nl.teqplay.aisengine.shiphistorymigrator.service

import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import nl.teqplay.aisengine.platform.model.LegacyHistoricShipInfo
import nl.teqplay.aisengine.shiphistorymigrator.ConcurrentMapOfSubjectToFileType
import nl.teqplay.aisengine.shiphistorymigrator.model.PublishData
import nl.teqplay.aisengine.shiphistorymigrator.model.S3FileType
import nl.teqplay.aisengine.shiphistorymigrator.model.S3FileType.Enum.BY_AREA
import nl.teqplay.aisengine.shiphistorymigrator.model.S3FileType.Enum.BY_MMSI
import nl.teqplay.aisengine.shiphistorymigrator.service.nats.NatsTmpStorageService
import nl.teqplay.aisengine.shiphistorymigrator.service.s3.S3ReadStorageService.QueuedRead
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.time.LocalDate

class ConvertDataServiceTest {

    @Test
    fun convertAndWriteToTmpStorage() {
        val objectMapper = jacksonObjectMapper()
            .registerModule(JavaTimeModule())
            .registerKotlinModule()
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)

        val oldData = objectMapper.readValue<List<LegacyHistoricShipInfo>>(
            this.javaClass.classLoader.getResource("./conversion/old_history.json")!!
        )

        val byMmsiExpected = objectMapper.readValue<PublishData>(
            this.javaClass.classLoader.getResource("./conversion/new_history_by_mmsi.json")!!
        )
        val byAreaExpectedMap = objectMapper.readValue<Map<String, PublishData>>(
            this.javaClass.classLoader.getResource("./conversion/new_history_by_area.json")!!
        )

        val natsTmpStorageService = mock<NatsTmpStorageService>().apply {
            whenever(publish(any())).then {
                val publishData = it.getArgument<PublishData>(0)
                when (publishData.fileType.enum) {
                    BY_MMSI -> assertEquals(byMmsiExpected, publishData)
                    BY_AREA -> {
                        val byAreaExpected = byAreaExpectedMap[publishData.fileType.key]!!
                        assertEquals(byAreaExpected, publishData)
                    }
                }
            }
        }
        val convertDataService = ConvertDataService(
            natsTmpStorageService,
        )

        val index = ConcurrentMapOfSubjectToFileType()
        val item = QueuedRead(
            date = LocalDate.parse("2023-06-01"),
            fileName = "ship_2023-06-01,244060924.zip",
            archiveBucketId = "2023-06-01,244060924",
            index = index
        )

        convertDataService.convertAndWriteToTmpStorage(item, oldData)

        assertEquals(
            ConcurrentMapOfSubjectToFileType(
                mapOf(
                    "244060924" to S3FileType(key = "2023-06,244060924", enum = BY_MMSI),
                    "4,4_51,8" to S3FileType(key = "2023-06,4.4,51.8", enum = BY_AREA),
                    "4,3_51,8" to S3FileType(key = "2023-06,4.3,51.8", enum = BY_AREA),
                    "4,2_51,8" to S3FileType(key = "2023-06,4.2,51.8", enum = BY_AREA),
                    "4,0_51,9" to S3FileType(key = "2023-06,4.0,51.9", enum = BY_AREA),
                    "4,2_51,9" to S3FileType(key = "2023-06,4.2,51.9", enum = BY_AREA),
                    "3,9_51,9" to S3FileType(key = "2023-06,3.9,51.9", enum = BY_AREA),
                    "4,1_51,9" to S3FileType(key = "2023-06,4.1,51.9", enum = BY_AREA),
                )
            ),
            index,
        )
    }
}
