package nl.teqplay.aisengine.shiphistorymigrator.service.s3

import nl.teqplay.aisengine.bucketing.storage.archive.ArchiveReadStorageImpl
import nl.teqplay.aisengine.platform.model.LegacyHistoricShipInfo
import nl.teqplay.aisengine.shiphistorymigrator.ConcurrentMapOfSubjectToFileType
import nl.teqplay.aisengine.shiphistorymigrator.config.PlatformConfig
import nl.teqplay.aisengine.shiphistorymigrator.service.ConvertDataService
import nl.teqplay.aisengine.shiphistorymigrator.service.nats.NatsTmpStorageService
import nl.teqplay.platform.model.Location
import org.junit.jupiter.api.Test
import org.mockito.ArgumentMatchers.anyString
import org.mockito.kotlin.any
import org.mockito.kotlin.doNothing
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.never
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.LocalDate

class S3ReadStorageServiceTest {

    private val natsTmpStorageService = mock<NatsTmpStorageService>()
    private val convertDataService = mock<ConvertDataService>()
    private val platform = mock<PlatformConfig>()
    private val s3ReadStorageService = S3ReadStorageService(
        natsTmpStorageService,
        convertDataService,
        platform,
    )

    private val item = S3ReadStorageService.QueuedRead(
        date = LocalDate.EPOCH,
        fileName = "fileName",
        archiveBucketId = "archiveBucketId",
        index = ConcurrentMapOfSubjectToFileType()
    )

    @Test
    fun `readAndWriteToTmpStorage - no data`() {
        val archiveReadStorage = mock<ArchiveReadStorageImpl<LegacyHistoricShipInfo>>().apply {
            whenever(get(anyString())).thenReturn(null)
        }
        whenever(platform.archiveReadStorage).thenReturn(archiveReadStorage)

        s3ReadStorageService.readAndWriteToTmpStorage(item)

        verify(convertDataService, never()).convertAndWriteToTmpStorage(any(), any())
    }

    @Test
    fun readAndWriteToTmpStorage() {
        val historicShipInfo = LegacyHistoricShipInfo(
            mmsi = "mmsi",
            timeLastUpdate = 0,
            location = Location(0.0, 0.0)
        )

        val archiveReadStorage = mock<ArchiveReadStorageImpl<LegacyHistoricShipInfo>>().apply {
            whenever(get(anyString())).thenReturn(mutableListOf(historicShipInfo))
        }
        whenever(platform.archiveReadStorage).thenReturn(archiveReadStorage)

        s3ReadStorageService.readAndWriteToTmpStorage(item)

        verify(convertDataService, times(1)).convertAndWriteToTmpStorage(eq(item), any())
    }

    @Test
    fun blockUntilFinished() {
        doNothing().whenever(natsTmpStorageService).waitForQueue()
        s3ReadStorageService.blockUntilFinished()
        verify(natsTmpStorageService, times(1)).waitForQueue()
    }
}
