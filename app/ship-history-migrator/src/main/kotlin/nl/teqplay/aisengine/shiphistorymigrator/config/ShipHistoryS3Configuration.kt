package nl.teqplay.aisengine.shiphistorymigrator.config

import com.fasterxml.jackson.databind.ObjectMapper
import nl.teqplay.aisengine.bucketing.factory.AisHistoricDiffMessageBucketFactoryByArea
import nl.teqplay.aisengine.bucketing.factory.AisHistoricDiffMessageBucketFactoryByMmsi
import nl.teqplay.aisengine.bucketing.model.AisHistoricOrderedDiffMessage
import nl.teqplay.aisengine.bucketing.model.AisHistoricUnorderedDiffMessage
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveGlobalProperties
import nl.teqplay.aisengine.bucketing.properties.PlatformShipHistoryByMmsiArchiveProperties
import nl.teqplay.aisengine.bucketing.properties.ShipHistoryByAreaArchiveProperties
import nl.teqplay.aisengine.bucketing.properties.ShipHistoryByMmsiArchiveProperties
import nl.teqplay.aisengine.bucketing.storage.archive.ArchiveClientService
import nl.teqplay.aisengine.bucketing.storage.archive.ArchiveWriteByMonthStorageImpl
import nl.teqplay.aisengine.bucketing.storage.bucket.AreaBucketId
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessage
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessageInterface
import nl.teqplay.skeleton.model.Location
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

data class ShipHistoryConfig(
    val archiveWriteStorageByMmsi: ArchiveWriteStorageByMmsi,
    val archiveWriteStorageByArea: ArchiveWriteStorageByArea,
)

typealias ArchiveWriteStorageByMmsi = ArchiveWriteByMonthStorageImpl<
    AisHistoricOrderedDiffMessage,
    AisHistoricUnorderedDiffMessage,
    AisHistoricMessageInterface,
    AisHistoricMessage,
    Int,
    BucketId<Int>
    >

typealias ArchiveWriteStorageByArea = ArchiveWriteByMonthStorageImpl<
    AisHistoricOrderedDiffMessage,
    AisHistoricUnorderedDiffMessage,
    AisHistoricMessageInterface,
    AisHistoricMessage,
    Location,
    AreaBucketId<Location>
    >

@Configuration
class ShipHistoryS3Configuration {

    @Bean
    fun shipHistoryS3Config(
        objectMapper: ObjectMapper,
        archiveGlobal: BucketArchiveGlobalProperties,
        platformShipHistoryByMmsiArchiveProperties: PlatformShipHistoryByMmsiArchiveProperties,
        shipHistoryByMmsiArchiveProperties: ShipHistoryByMmsiArchiveProperties,
        shipHistoryByAreaArchiveProperties: ShipHistoryByAreaArchiveProperties,
    ): ShipHistoryConfig {
        val archiveWriteStorageByMmsi = ArchiveWriteByMonthStorageImpl(
            archive = shipHistoryByMmsiArchiveProperties,
            objectMapper = objectMapper,
            factory = AisHistoricDiffMessageBucketFactoryByMmsi,
            archiveClientService = ArchiveClientService(shipHistoryByMmsiArchiveProperties, archiveGlobal)
        )

        val archiveWriteStorageByArea = ArchiveWriteByMonthStorageImpl(
            archive = shipHistoryByAreaArchiveProperties,
            objectMapper = objectMapper,
            factory = AisHistoricDiffMessageBucketFactoryByArea,
            archiveClientService = ArchiveClientService(shipHistoryByAreaArchiveProperties, archiveGlobal)
        )

        return ShipHistoryConfig(
            archiveWriteStorageByMmsi,
            archiveWriteStorageByArea,
        )
    }
}
