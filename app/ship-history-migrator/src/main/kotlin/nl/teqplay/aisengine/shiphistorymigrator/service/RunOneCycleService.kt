package nl.teqplay.aisengine.shiphistorymigrator.service

import nl.teqplay.aisengine.shiphistorymigrator.ConcurrentMapOfSubjectToFileType
import nl.teqplay.aisengine.shiphistorymigrator.LOG
import nl.teqplay.aisengine.shiphistorymigrator.service.nats.NatsStatsService
import nl.teqplay.aisengine.shiphistorymigrator.service.nats.NatsTmpStorageService
import nl.teqplay.aisengine.shiphistorymigrator.service.steps.StepReadPerDayService
import nl.teqplay.aisengine.shiphistorymigrator.service.steps.StepTrackMonthIndexService
import nl.teqplay.aisengine.shiphistorymigrator.service.steps.StepTrackMonthProgressService
import nl.teqplay.aisengine.shiphistorymigrator.service.steps.StepWritePerMonthService
import org.springframework.stereotype.Component

@Component
class RunOneCycleService(
    private val stepTrackMonthIndexService: StepTrackMonthIndexService,
    private val stepTrackMonthProgressService: StepTrackMonthProgressService,
    private val stepReadPerDayService: StepReadPerDayService,
    private val stepWritePerMonthService: StepWritePerMonthService,

    private val natsTmpStorageService: NatsTmpStorageService,
    private val natsStatsService: NatsStatsService,
) {

    /**
     * Runs one cycle, migrating days of history into history per month.
     *
     * @return whether there is a next cycle to run after this invocation
     */
    fun runOneCycle(): Boolean {
        val datesToRead = stepTrackMonthProgressService.getDatesToRead() ?: return false
        val (yearMonth, dates) = datesToRead

        // don't need to retry reading if we were already done with that
        val previousIndex = stepTrackMonthIndexService.getIndex()
        val index = previousIndex ?: ConcurrentMapOfSubjectToFileType()
        if (previousIndex == null) {
            // purge data
            stepTrackMonthIndexService.purgeIndex()
            natsTmpStorageService.purge()

            // read
            dates.forEach { date ->
                val readIndex = stepReadPerDayService.readAndStore(date)
                index += readIndex

                LOG.info { "Info: date=$date, index=${index.size}, readIndex=${readIndex.size}" }
                natsStatsService.saveStatsPerDay(date, readIndex)
            }

            // save index
            stepTrackMonthIndexService.saveIndex(index)
            natsStatsService.saveStatsPerMonth(yearMonth, index)
        }

        // write
        stepWritePerMonthService.write(index)

        // purge index and progress to next
        stepTrackMonthIndexService.purgeIndex()
        return stepTrackMonthProgressService.progressToNext()
    }
}
