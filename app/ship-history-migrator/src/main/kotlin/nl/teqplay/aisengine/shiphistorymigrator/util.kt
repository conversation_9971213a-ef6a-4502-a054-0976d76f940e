package nl.teqplay.aisengine.shiphistorymigrator

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.shiphistorymigrator.model.S3FileType
import java.time.Duration
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit

val LOG = KotlinLogging.logger { }

typealias MapOfFileNameToArchiveBucketId = Map<String, String>
typealias ConcurrentMapOfSubjectToFileType = ConcurrentHashMap<String, S3FileType>
typealias MapOfSubjectToRawData = Map<String, Map<String, ByteArray>>

/**
 * Times an [action], including a [title] in the log messages.
 */
inline fun <T> timed(
    title: String,
    action: () -> T,
): T {
    LOG.info { title }
    val start = System.currentTimeMillis()
    return action().also {
        val elapsed = System.currentTimeMillis() - start
        val duration = Duration.ofMillis(elapsed)
        LOG.info { "$title ($duration)" }
    }
}

/**
 * Infinitely retries [action] until it succeeds. Applying exponential backoff between retries.
 */
// TODO: health checks
inline fun <T> withInfiniteRetries(
    action: () -> T,
): T {
    var backoff = 2L
    while (true) {
        try {
            return action()
        } catch (e: Exception) {
            LOG.warn(e) { "Something went wrong, retrying after ${backoff}s" }
            TimeUnit.SECONDS.sleep(backoff)
            backoff = (backoff * 2).coerceAtMost(60)
        }
    }
}
