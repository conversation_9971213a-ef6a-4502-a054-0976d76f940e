package nl.teqplay.aisengine.shiphistorymigrator.service.nats

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.aisengine.bucketing.model.AisHistoricOrderedDiffMessage
import nl.teqplay.aisengine.shiphistorymigrator.service.GZipService
import org.springframework.stereotype.Component

@Component
class NatsPayloadService(
    private val gZipService: GZipService,
    private val objectMapper: ObjectMapper,
) {

    /**
     * Serializes the [data], by writing it to JSON with GZIP.
     */
    fun serialize(
        data: List<AisHistoricOrderedDiffMessage>,
    ): ByteArray {
        return gZipService.zip(objectMapper.writeValueAsString(data))
    }

    /**
     * Deserializes the [value], which might contain multiple appended JSON arrays.
     */
    fun deserialize(
        value: ByteArray,
    ): List<AisHistoricOrderedDiffMessage> {
        val unzipped = gZipService.unzip(value)
            .replace("}][{", "},{") // fix for appended JSON lists
        return objectMapper.readValue<List<AisHistoricOrderedDiffMessage>>(unzipped)
    }
}
