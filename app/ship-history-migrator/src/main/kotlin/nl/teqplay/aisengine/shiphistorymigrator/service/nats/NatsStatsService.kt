package nl.teqplay.aisengine.shiphistorymigrator.service.nats

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.aisengine.shiphistorymigrator.ConcurrentMapOfSubjectToFileType
import nl.teqplay.aisengine.shiphistorymigrator.model.S3FileType
import nl.teqplay.skeleton.common.config.NatsProperties
import nl.teqplay.skeleton.nats.NatsClientBuilder
import org.springframework.stereotype.Component
import java.time.LocalDate
import java.time.YearMonth

@Component
class NatsStatsService(
    natsClientBuilder: NatsClientBuilder,
    config: NatsProperties,
    private val objectMapper: ObjectMapper,
) {

    private val statsName = "stats"

    data class Stats(
        val countByMmsi: Int,
        val countByArea: Int,
    )

    private val kv = requireNotNull(
        natsClientBuilder.keyValueBucket<Stats>(
            config = config,
            name = statsName,
            serializer = objectMapper::writeValueAsBytes,
            deserializer = objectMapper::readValue,
            storeOnDisk = true
        )
    )

    /**
     * Save stats of the [index] per [date]
     */
    fun saveStatsPerDay(
        date: LocalDate,
        index: ConcurrentMapOfSubjectToFileType
    ) {
        val stats = getStats(index)
        kv.put(date.toString(), stats)
    }

    /**
     * Save stats of the [index] per [yearMonth]
     */
    fun saveStatsPerMonth(
        yearMonth: YearMonth,
        index: ConcurrentMapOfSubjectToFileType
    ) {
        val stats = getStats(index)
        kv.put(yearMonth.toString(), stats)
    }

    private fun getStats(
        store: ConcurrentMapOfSubjectToFileType,
    ): Stats {
        val countByMmsi = store.count { it.value.enum == S3FileType.Enum.BY_MMSI }
        val countByArea = store.count { it.value.enum == S3FileType.Enum.BY_AREA }
        return Stats(countByMmsi, countByArea)
    }
}
