package nl.teqplay.aisengine.shiphistorymigrator.service.steps

import nl.teqplay.aisengine.shiphistorymigrator.ConcurrentMapOfSubjectToFileType
import nl.teqplay.aisengine.shiphistorymigrator.LOG
import nl.teqplay.aisengine.shiphistorymigrator.MapOfSubjectToRawData
import nl.teqplay.aisengine.shiphistorymigrator.service.nats.NatsTmpStorageService
import nl.teqplay.aisengine.shiphistorymigrator.service.s3.S3StorageService
import org.springframework.stereotype.Component
import java.util.concurrent.CountDownLatch
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.concurrent.thread

@Component
class StepWritePerMonthService(
    private val s3StorageService: S3StorageService,
    private val natsTmpStorageService: NatsTmpStorageService,
) {

    /**
     * Write data from the [index] by piping data from [natsTmpStorageService] into the [s3StorageService].
     */
    fun write(index: ConcurrentMapOfSubjectToFileType) = s3StorageService.lock("Writing data") {
        LOG.info { "Index size: ${index.size}" }

        val queue = LinkedBlockingQueue<MapOfSubjectToRawData>(1)
        val running = AtomicBoolean(true)
        val completeLatch = CountDownLatch(1)

        thread {
            while (queue.isNotEmpty() || running.get()) {
                val sequence = queue.poll(5, TimeUnit.SECONDS) ?: continue
                sequence.forEach { (subject, data) ->
                    val fileType = index[subject]!!
                    s3StorageService.write(fileType, data)
                }
            }
            completeLatch.countDown()
        }
        natsTmpStorageService.readAndEnqueue(queue)
        running.set(false)
        completeLatch.await()
    }
}
