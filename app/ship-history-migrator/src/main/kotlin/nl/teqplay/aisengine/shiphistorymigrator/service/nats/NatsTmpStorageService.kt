package nl.teqplay.aisengine.shiphistorymigrator.service.nats

import nl.teqplay.aisengine.shiphistorymigrator.MapOfSubjectToRawData
import nl.teqplay.aisengine.shiphistorymigrator.model.PublishData
import nl.teqplay.aisengine.shiphistorymigrator.timed
import org.springframework.stereotype.Component
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.TimeUnit
import kotlin.math.absoluteValue

@Component
class NatsTmpStorageService(
    private val natsTmpStorageContextService: NatsTmpStorageContextService,
) {

    private val streamsAmount = 64
    private val contexts = (0 until streamsAmount).map { index ->
        natsTmpStorageContextService.createContext(index)
    }

    /**
     * Schedule [data] to be published to tmp storage.
     */
    fun publish(data: PublishData) {
        val streamIndex = getStreamIndex(data.subject)
        val context = contexts[streamIndex]
        context.incomingQueue.put(data)
    }

    /**
     * Partitions the data into different streams based on the [subject] and total [streamsAmount].
     */
    private fun getStreamIndex(subject: String): Int {
        return subject.hashCode().absoluteValue.mod(streamsAmount)
    }

    /**
     * Waits for the queues to write to tmp storage to be empty.
     */
    fun waitForQueue() = timed("Waiting for queue") {
        contexts.forEach { it.empty = false }
        while (contexts.any { !it.empty }) {
            TimeUnit.SECONDS.sleep(2)
        }
    }

    /**
     * Reads data from tmp storage and enqueues it into the [queue].
     */
    fun readAndEnqueue(
        queue: LinkedBlockingQueue<MapOfSubjectToRawData>,
    ) {
        contexts.forEach { context ->
            context.readAndEnqueue(queue)
        }
    }

    /**
     * Purge all data from tmp storage.
     */
    fun purge() {
        contexts.forEach { context ->
            context.purge()
        }
    }
}
