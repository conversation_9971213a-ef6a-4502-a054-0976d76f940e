package nl.teqplay.aisengine.shiphistorymigrator.service.nats

import io.nats.client.Dispatcher
import io.nats.client.JetStream
import io.nats.client.MessageHandler
import io.nats.client.PublishOptions
import io.nats.client.PushSubscribeOptions
import io.nats.client.StreamContext
import io.nats.client.impl.Headers
import io.nats.client.impl.NatsMessage
import nl.teqplay.aisengine.shiphistorymigrator.MapOfSubjectToRawData
import nl.teqplay.aisengine.shiphistorymigrator.model.PublishData
import nl.teqplay.aisengine.shiphistorymigrator.service.nats.NatsPublishService.PublishMessage
import nl.teqplay.aisengine.shiphistorymigrator.timed
import java.time.Duration
import java.util.concurrent.CountDownLatch
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.TimeUnit
import kotlin.concurrent.thread

class NatsTmpStorageContext(
    private val js: JetStream,
    private val streamIndex: Int,
    private val stream: StreamContext,
    private val dispatcher: Dispatcher,
    private var lastSequence: Long,
    private val natsPayloadService: NatsPayloadService,
    private val natsPublishService: NatsPublishService,

    val incomingQueue: LinkedBlockingQueue<PublishData>,
    var empty: Boolean,
) {

    companion object {
        const val streamName = "data"
        const val headerDate = "Date"
    }

    private val maxCapacity = 200

    init {
        thread(block = ::runInThread)
    }

    private fun runInThread() {
        val messagesBuffer = ArrayList<Pair<PublishData, ByteArray>>(maxCapacity)
        val messagesBufferOverflow = ArrayList<Pair<PublishData, ByteArray>>(maxCapacity)
        val messages = ArrayList<PublishMessage>(maxCapacity)

        val maxTimeout = Duration.ofSeconds(5).toMillis()

        while (true) {
            // empty queue up to limit and timeout
            val startTime = System.currentTimeMillis()
            while (messagesBuffer.size < maxCapacity) {
                val timeout = System.currentTimeMillis() - startTime + maxTimeout
                val publishData = incomingQueue.poll(timeout, TimeUnit.MILLISECONDS) ?: break

                val inputStream = natsPayloadService.serialize(publishData.data).inputStream()

                natsPublishService.chunk(inputStream).forEach { out ->
                    // messages buffer may not exceed max size, others overflow
                    if (messagesBuffer.size < maxCapacity) {
                        messagesBuffer.add(publishData to out)
                    } else {
                        messagesBufferOverflow.add(publishData to out)
                    }
                }
            }

            empty = messagesBuffer.isEmpty()
            if (empty) {
                continue
            }

            // clear message buffer by publishing all of it
            while (messagesBuffer.isNotEmpty()) {
                while (messagesBuffer.isNotEmpty() && messages.size < maxCapacity) {
                    val (message, body) = messagesBuffer.removeFirst()
                    val expectedLastSequence = lastSequence++
                    val subject = toStreamSubject(message.subject)
                    val natsMessage = NatsMessage.builder()
                        .subject(subject)
                        .data(body)
                        .headers(Headers().add(headerDate, message.date.toString()))
                        .build()
                    // use expected last sequence, to ensure writing in-order, and to otherwise fail and retry
                    val options = PublishOptions.builder()
                        .messageId(expectedLastSequence.toString())
                        .expectedLastSequence(expectedLastSequence)
                        .build()
                    messages.add(PublishMessage(natsMessage, options))
                }

                natsPublishService.publish(js, messages)
            }

            // transfer the overflown messages into the buffer
            while (messagesBufferOverflow.isNotEmpty() && messagesBuffer.size < maxCapacity) {
                messagesBuffer.add(messagesBufferOverflow.removeFirst())
            }
        }
    }

    private fun toStreamSubject(subject: String): String {
        return "$streamName.$streamIndex.$subject"
    }

    private fun toStreamSubjectWildcard(streamIndex: Int): String {
        return "$streamName.$streamIndex.>"
    }

    private fun toPlainSubject(subject: String): String {
        return subject.substringAfterLast(".")
    }

    fun readAndEnqueue(
        queue: LinkedBlockingQueue<MapOfSubjectToRawData>,
    ) {
        // can skip when there is no data
        if (stream.streamInfo.streamState.msgCount == 0L) {
            return
        }

        val data = mutableMapOf<String, MutableMap<String, ByteArray>>()
        timed("[context $streamIndex]: consume") {
            val countDownLatch = CountDownLatch(1)

            val options = PushSubscribeOptions.builder()
                .ordered(true)
                .stream(stream.streamName)
                .build()
            val handler = MessageHandler { message ->
                val date = message.headers.getFirst(headerDate)
                data.getOrPut(toPlainSubject(message.subject), ::mutableMapOf)
                    .compute(date) { _, old -> (old ?: ByteArray(0)) + message.data }
                if (message.metaData().pendingCount() == 0L) {
                    countDownLatch.countDown()
                }
            }
            val subscription = js.subscribe(
                toStreamSubjectWildcard(streamIndex),
                dispatcher,
                handler,
                false,
                options
            )

            countDownLatch.await()
            dispatcher.unsubscribe(subscription)
            stream.deleteConsumer(subscription.consumerName)
        }

        timed("[context $streamIndex]: enqueue") {
            queue.put(data)
            // the queue only allows one entry, an empty entry ensures the next
            // put has to wait for the data of this context to be completed
            queue.put(emptyMap())
        }
    }

    fun purge() {
        stream.purge()
    }
}
