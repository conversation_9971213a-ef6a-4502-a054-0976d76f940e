package nl.teqplay.aisengine.shiphistorymigrator.service.steps

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.skeleton.common.config.NatsProperties
import nl.teqplay.skeleton.nats.NatsClientBuilder
import nl.teqplay.skeleton.nats.NatsKeyValueBucketEntry
import org.springframework.stereotype.Component
import java.time.LocalDate
import java.time.YearMonth

@Component
class StepTrackMonthProgressService(
    natsClientBuilder: NatsClientBuilder,
    config: NatsProperties,
    private val objectMapper: ObjectMapper,
) {

    private val progressName = "progress"

    data class Progress(
        val from: YearMonth,
        val toInclusive: YearMonth,
        val current: YearMonth,
    )

    /**
     * Get all dates for the month that needs to be processed.
     * Or none, if no dates are left to be read.
     */
    fun getDatesToRead(): Pair<YearMonth, List<LocalDate>>? {
        val progress = getProgress()
        val value = requireNotNull(progress.value)
        if (!hasNext(value)) {
            return null
        }
        val start = value.current.atDay(1)
        val end = value.current.plusMonths(1).atDay(1)
        return YearMonth.of(start.year, start.monthValue) to start.datesUntil(end).toList()
    }

    /**
     * Progresses the cursor to the next month, and returns whether there are dates left to read.
     */
    fun progressToNext(): Boolean {
        val progress = getProgress()
        val value = requireNotNull(progress.value)
        val direction = getProgressDirection(value)
        val newValue = value.copy(current = value.current.plusMonths(direction))
        kv.update(progressName, newValue, progress.kvEntry.revision)
        return hasNext(newValue)
    }

    private val kv = requireNotNull(
        natsClientBuilder.keyValueBucket<Progress>(
            config = config,
            name = progressName,
            serializer = objectMapper::writeValueAsBytes,
            deserializer = objectMapper::readValue,
            storeOnDisk = true
        )
    )

    private fun getProgress(): NatsKeyValueBucketEntry<Progress> =
        kv.get(progressName)?.takeIf { it.value != null } ?: throw Exception("Progress not specified")

    private fun getProgressDirection(progress: Progress): Long {
        return if (progress.from <= progress.toInclusive) 1 else -1
    }

    private fun hasNext(progress: Progress): Boolean {
        val range = when {
            progress.from <= progress.toInclusive -> progress.from..progress.toInclusive
            else -> progress.toInclusive..progress.from
        }
        return progress.current in range
    }
}
