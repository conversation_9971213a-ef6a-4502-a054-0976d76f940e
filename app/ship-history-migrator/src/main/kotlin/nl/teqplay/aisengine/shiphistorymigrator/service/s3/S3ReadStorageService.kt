package nl.teqplay.aisengine.shiphistorymigrator.service.s3

import nl.teqplay.aisengine.shiphistorymigrator.ConcurrentMapOfSubjectToFileType
import nl.teqplay.aisengine.shiphistorymigrator.config.PlatformConfig
import nl.teqplay.aisengine.shiphistorymigrator.service.ConvertDataService
import nl.teqplay.aisengine.shiphistorymigrator.service.nats.NatsTmpStorageService
import nl.teqplay.aisengine.shiphistorymigrator.withInfiniteRetries
import org.springframework.stereotype.Component
import java.time.LocalDate

@Component
class S3ReadStorageService(
    private val natsTmpStorageService: NatsTmpStorageService,
    private val convertDataService: ConvertDataService,
    private val platform: PlatformConfig,
) {

    data class QueuedRead(
        val date: LocalDate,
        val fileName: String,
        val archiveBucketId: String,
        val index: ConcurrentMapOfSubjectToFileType,
    ) : S3StorageService.QueuedItem

    /**
     * Reads data for [item] and writes it into tmp storage.
     */
    fun readAndWriteToTmpStorage(
        item: QueuedRead,
    ) {
        val oldData = withInfiniteRetries { platform.archiveReadStorage.get(item.fileName) }
            // some data in 2018 didn't have a location, for this fix to work
            // the historic ship info needs to get a nullable location
            ?.filter { it.location != null }
            ?.ifEmpty { null }
        if (oldData != null) {
            convertDataService.convertAndWriteToTmpStorage(item, oldData)
        }
    }

    /**
     * Blocks until all write actions have been completed.
     */
    fun blockUntilFinished() {
        natsTmpStorageService.waitForQueue()
    }
}
