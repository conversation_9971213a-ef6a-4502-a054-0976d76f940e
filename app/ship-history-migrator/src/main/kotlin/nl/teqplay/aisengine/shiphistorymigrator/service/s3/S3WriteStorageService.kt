package nl.teqplay.aisengine.shiphistorymigrator.service.s3

import com.fasterxml.jackson.databind.ObjectMapper
import nl.teqplay.aisengine.bucketing.model.AisHistoricMessageResolver
import nl.teqplay.aisengine.bucketing.model.AisHistoricOrderedDiffMessage
import nl.teqplay.aisengine.bucketing.model.bucket.OrderedBucket
import nl.teqplay.aisengine.shiphistorymigrator.LOG
import nl.teqplay.aisengine.shiphistorymigrator.config.ShipHistoryConfig
import nl.teqplay.aisengine.shiphistorymigrator.model.S3FileType
import nl.teqplay.aisengine.shiphistorymigrator.model.S3FileType.Enum.BY_AREA
import nl.teqplay.aisengine.shiphistorymigrator.model.S3FileType.Enum.BY_MMSI
import nl.teqplay.aisengine.shiphistorymigrator.service.nats.NatsPayloadService
import nl.teqplay.aisengine.shiphistorymigrator.withInfiniteRetries
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.time.LocalDate
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicLong
import kotlin.math.roundToInt

@Component
class S3WriteStorageService(
    private val shipHistory: ShipHistoryConfig,
    private val natsPayloadService: NatsPayloadService,
    private val objectMapper: ObjectMapper,
) {

    data class QueuedWrite(
        val fileType: S3FileType,
        val data: Map<String, ByteArray>,
    ) : S3StorageService.QueuedItem

    private val counter = AtomicLong()

    /**
     * Write [item] into S3 storage.
     */
    fun write(
        item: QueuedWrite,
    ) {
        val fileName = "${item.fileType.enum.prefix}${item.fileType.key}.zip"

        val data = mutableMapOf<LocalDate, ByteArray>()
        item.data.forEach { (key, value) ->
            val date = LocalDate.parse(key)
            val list = natsPayloadService.deserialize(value)
            val bucket = OrderedBucket<AisHistoricOrderedDiffMessage>("")
            bucket.addAll(list, AisHistoricMessageResolver)

            data[date] = objectMapper.writeValueAsBytes(bucket.data)
        }

        withInfiniteRetries {
            val success = when (item.fileType.enum) {
                BY_MMSI -> shipHistory.archiveWriteStorageByMmsi.overwriteDirect(fileName, data)
                BY_AREA -> shipHistory.archiveWriteStorageByArea.overwriteDirect(fileName, data)
            }

            if (!success) {
                throw Exception("Unsuccessful S3 write")
            }

            // finished writing, break out of infinite loop and continue to the next
            counter.addAndGet(1)
        }
    }

    @Scheduled(initialDelay = 0, fixedRate = 5, timeUnit = TimeUnit.SECONDS)
    fun stats() {
        val messages = counter.getAndSet(0)
        if (messages > 0) {
            LOG.info { "S3: ${(messages.toDouble() / 5).roundToInt()} mps" }
        }
    }
}
