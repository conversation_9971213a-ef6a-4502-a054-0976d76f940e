package nl.teqplay.aisengine.shiphistorymigrator.model

/**
 * A type storing information about a file in S3.
 */
data class S3FileType(
    /**
     * Key of the file, without the file extension or the [Enum.prefix].
     */
    val key: String,

    /**
     * The type of file.
     */
    val enum: Enum
) {
    enum class Enum(
        val prefix: String
    ) {
        BY_MMSI("mmsi,"),
        BY_AREA("area,"),
    }
}
