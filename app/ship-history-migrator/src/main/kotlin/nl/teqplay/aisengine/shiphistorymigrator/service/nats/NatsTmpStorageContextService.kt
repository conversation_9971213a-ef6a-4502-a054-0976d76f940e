package nl.teqplay.aisengine.shiphistorymigrator.service.nats

import io.nats.client.Connection
import io.nats.client.JetStream
import io.nats.client.StreamContext
import io.nats.client.api.RetentionPolicy
import io.nats.client.api.StorageType
import io.nats.client.api.StreamConfiguration
import nl.teqplay.aisengine.shiphistorymigrator.model.PublishData
import nl.teqplay.skeleton.common.config.NatsProperties
import nl.teqplay.skeleton.nats.NatsClientConnection
import org.springframework.stereotype.Component
import java.util.concurrent.LinkedBlockingQueue

@Component
class NatsTmpStorageContextService(
    private val natsClientConnection: NatsClientConnection,
    private val config: NatsProperties,
    private val natsPayloadService: NatsPayloadService,
    private val natsPublishService: NatsPublishService,
) {

    /**
     * Facilitates the creation and usage of a [NatsTmpStorageContext].
     */
    fun createContext(index: Int): NatsTmpStorageContext {
        val nc = natsClientConnection.getConnection(config)
        val js = nc.jetStream()
        val stream = initStream(nc, js, index)
        val dispatcher = nc.createDispatcher()
        val lastSequence = stream.streamInfo.streamState.lastSequence
        val queue = LinkedBlockingQueue<PublishData>(1_000)
        return NatsTmpStorageContext(
            js = js,
            streamIndex = index,
            stream = stream,
            dispatcher = dispatcher,
            lastSequence = lastSequence,
            incomingQueue = queue,
            natsPayloadService = natsPayloadService,
            natsPublishService = natsPublishService,
            empty = false,
        )
    }

    private fun initStream(
        nc: Connection,
        js: JetStream,
        index: Int,
    ): StreamContext {
        val name = "${NatsTmpStorageContext.streamName}_$index"
        val jsm = nc.jetStreamManagement()
        if (name !in jsm.streamNames) {
            jsm.addStream(
                StreamConfiguration.builder()
                    .name(name)
                    .subjects("${NatsTmpStorageContext.streamName}.$index.>")
                    .storageType(StorageType.File)
                    .retentionPolicy(RetentionPolicy.Limits)
                    .build()
            )
        }
        return js.getStreamContext(name)
    }
}
