package nl.teqplay.aisengine.shiphistorymigrator.service

import nl.teqplay.aisengine.bucketing.factory.AisHistoricDiffMessageBucketFactoryByArea
import nl.teqplay.aisengine.bucketing.factory.AisHistoricDiffMessageBucketFactoryByMmsi
import nl.teqplay.aisengine.bucketing.model.AisHistoricOrderedDiffMessage
import nl.teqplay.aisengine.bucketing.util.convert
import nl.teqplay.aisengine.platform.historic.toAisHistoricMessage
import nl.teqplay.aisengine.platform.model.LegacyHistoricShipInfo
import nl.teqplay.aisengine.shiphistorymigrator.model.PublishData
import nl.teqplay.aisengine.shiphistorymigrator.model.S3FileType
import nl.teqplay.aisengine.shiphistorymigrator.model.S3FileType.Enum.BY_AREA
import nl.teqplay.aisengine.shiphistorymigrator.model.S3FileType.Enum.BY_MMSI
import nl.teqplay.aisengine.shiphistorymigrator.service.nats.NatsTmpStorageService
import nl.teqplay.aisengine.shiphistorymigrator.service.s3.S3ReadStorageService.QueuedRead
import nl.teqplay.skeleton.model.Location
import org.springframework.stereotype.Component
import java.time.LocalDate
import java.time.YearMonth

@Component
class ConvertDataService(
    private val natsTmpStorageService: NatsTmpStorageService,
) {

    /**
     * Converts [oldData] and publishes it into [natsTmpStorageService], as well as
     * populating the [QueuedRead.index] to retrieve this data after.
     */
    fun convertAndWriteToTmpStorage(
        item: QueuedRead,
        oldData: List<LegacyHistoricShipInfo>,
    ) {
        // provide fresh copies to both, since the model is mutable
        writeByMmsi(item, convert(oldData))
        writeByArea(item, convert(oldData))
    }

    private fun convert(
        oldData: List<LegacyHistoricShipInfo>,
    ): List<AisHistoricOrderedDiffMessage> = oldData.map {
        val message = it.toAisHistoricMessage()
        AisHistoricOrderedDiffMessage(
            mmsi = message.mmsi,
            messageTime = message.messageTime,
            receptionTime = null,
            historic = message.historic,
            source = it.source ?: "UNKNOWN",
            subSource = null,
            messageType = null,
            lat = message.location.lat,
            lon = message.location.lon,
            heading = message.heading,
            speedOverGround = message.speedOverGround,
            courseOverGround = message.courseOverGround,
            status = message.status,
            imo = message.imo,
            shipType = message.shipType,
            draught = message.draught,
            eta = message.eta,
            destination = message.destination,
            transponderPosition = message.transponderPosition?.convert()
        )
    }

    private fun writeByMmsi(
        item: QueuedRead,
        data: List<AisHistoricOrderedDiffMessage>,
    ) {
        val publishData = AisHistoricDiffMessageBucketFactoryByMmsi.bucketFormatter.zip(data)
        val publishByMmsi = toPublishData(item.archiveBucketId, item.date, BY_MMSI, publishData)
        natsTmpStorageService.publish(publishByMmsi)
        item.index.putIfAbsent(
            publishByMmsi.subject,
            publishByMmsi.fileType,
        )
    }

    private fun writeByArea(
        item: QueuedRead,
        data: List<AisHistoricOrderedDiffMessage>,
    ) {
        val areaArchivedBuckets = mutableMapOf<String, MutableList<AisHistoricOrderedDiffMessage>>()
        data.forEach {
            val lat = it.lat ?: return@forEach
            val lon = it.lon ?: return@forEach
            val key = Location(lat, lon)

            val bucketId = AisHistoricDiffMessageBucketFactoryByArea
                .bucketIdentifier.id.getBucketId(item.date, key)
            val archiveBucketId = AisHistoricDiffMessageBucketFactoryByArea
                .bucketIdentifier.archive.getArchiveBucketId(bucketId)

            areaArchivedBuckets.getOrPut(archiveBucketId, ::mutableListOf).add(it)
        }

        areaArchivedBuckets.forEach { (key, data) ->
            val publishData = AisHistoricDiffMessageBucketFactoryByArea.bucketFormatter.zip(data)
            val publishByArea = toPublishData(key, item.date, BY_AREA, publishData)
            natsTmpStorageService.publish(publishByArea)
            item.index.putIfAbsent(
                publishByArea.subject,
                publishByArea.fileType,
            )
        }
    }

    private fun toPublishData(
        bucketId: String,
        date: LocalDate,
        s3FileTypeEnum: S3FileType.Enum,
        bucketData: List<AisHistoricOrderedDiffMessage>,
    ): PublishData {
        val yearMonth = YearMonth.of(date.year, date.monthValue)

        val split = bucketId.split(",")
        val (key, subject) = when (split.size) {
            3 -> {
                val (_, lon, lat) = split
                // replace the '.' as that is a subject delimiter in NATS
                val area = "${lon.replace(".", ",")}_${lat.replace(".", ",")}"
                "$yearMonth,$lon,$lat" to area
            }

            else -> {
                val (_, mmsi) = split
                "$yearMonth,$mmsi" to mmsi
            }
        }

        return PublishData(
            date = date,
            fileType = S3FileType(key, s3FileTypeEnum),
            subject = subject,
            data = bucketData
        )
    }
}
