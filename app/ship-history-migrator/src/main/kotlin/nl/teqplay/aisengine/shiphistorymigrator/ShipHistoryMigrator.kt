package nl.teqplay.aisengine.shiphistorymigrator

import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication
import org.springframework.scheduling.annotation.EnableScheduling

@SpringBootApplication(exclude = [MongoAutoConfiguration::class])
@EnableScheduling
@ConfigurationPropertiesScan
class ShipHistoryMigrator

fun main(args: Array<String>) {
    runApplication<ShipHistoryMigrator>(*args)
}
