package nl.teqplay.aisengine.shiphistorymigrator.config

import com.fasterxml.jackson.databind.ObjectMapper
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveConfiguration
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveGlobalProperties
import nl.teqplay.aisengine.bucketing.properties.PlatformShipHistoryByMmsiArchiveProperties
import nl.teqplay.aisengine.bucketing.storage.archive.ArchiveClientService
import nl.teqplay.aisengine.bucketing.storage.archive.ArchiveReadStorageImpl
import nl.teqplay.aisengine.platform.model.LegacyHistoricShipInfo
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

data class PlatformConfig(
    val archive: BucketArchiveConfiguration,
    val archiveClientService: ArchiveClientService,
    val archiveReadStorage: ArchiveReadStorageImpl<LegacyHistoricShipInfo>,
)

@Configuration
class PlatformConfiguration {

    @Bean
    fun platformConfig(
        objectMapper: ObjectMapper,
        platformShipHistoryByMmsiArchiveProperties: PlatformShipHistoryByMmsiArchiveProperties,
        archiveGlobal: BucketArchiveGlobalProperties,
    ): PlatformConfig {
        val archiveClientService = ArchiveClientService(platformShipHistoryByMmsiArchiveProperties, archiveGlobal)
        val archiveReadStorage = ArchiveReadStorageImpl(
            dataClass = LegacyHistoricShipInfo::class.java,
            archive = platformShipHistoryByMmsiArchiveProperties,
            objectMapper = objectMapper,
            archiveClientService = archiveClientService
        )

        return PlatformConfig(
            platformShipHistoryByMmsiArchiveProperties,
            archiveClientService,
            archiveReadStorage,
        )
    }
}
