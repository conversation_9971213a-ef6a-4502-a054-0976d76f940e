package nl.teqplay.aisengine.shiphistorymigrator.service

import org.springframework.stereotype.Component
import java.io.ByteArrayOutputStream
import java.util.zip.GZIPInputStream
import java.util.zip.GZIPOutputStream

@Component
class GZipService {

    /**
     * Zips the [content] using GZIP
     */
    fun zip(content: String): ByteArray {
        val bos = ByteArrayOutputStream()
        GZIPOutputStream(bos)
            .bufferedWriter(Charsets.UTF_8)
            .use { it.write(content) }
        return bos.toByteArray()
    }

    /**
     * Unzips the [content] using GZIP.
     */
    fun unzip(content: ByteArray): String {
        return GZIPInputStream(content.inputStream())
            .bufferedReader(Charsets.UTF_8)
            .use { it.readText() }
    }
}
