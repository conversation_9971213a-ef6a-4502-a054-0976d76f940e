package nl.teqplay.aisengine.shiphistorymigrator.service.s3

import nl.teqplay.aisengine.shiphistorymigrator.ConcurrentMapOfSubjectToFileType
import nl.teqplay.aisengine.shiphistorymigrator.MapOfFileNameToArchiveBucketId
import nl.teqplay.aisengine.shiphistorymigrator.model.S3FileType
import nl.teqplay.aisengine.shiphistorymigrator.service.s3.S3ReadStorageService.QueuedRead
import nl.teqplay.aisengine.shiphistorymigrator.service.s3.S3WriteStorageService.QueuedWrite
import nl.teqplay.aisengine.shiphistorymigrator.timed
import org.springframework.stereotype.Component
import java.time.LocalDate
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.TimeUnit
import kotlin.concurrent.thread

@Component
class S3StorageService(
    private val s3ReadStorageService: S3ReadStorageService,
    private val s3WriteStorageService: S3WriteStorageService,
) {

    sealed interface QueuedItem

    private val queue = LinkedBlockingQueue<QueuedItem>(5_000)

    private val threadAmount = 56
    private val threadEmpty = ConcurrentHashMap<Int, Boolean>()

    init {
        repeat(threadAmount) { index ->
            threadEmpty[index] = false
            thread { runInThread(index) }
        }
    }

    /**
     * Read [files] from S3 and write them to tmp storage.
     */
    fun readAndStore(
        date: LocalDate,
        files: MapOfFileNameToArchiveBucketId,
    ): ConcurrentMapOfSubjectToFileType {
        val index = ConcurrentMapOfSubjectToFileType()
        lock("Reading buckets") {
            files.forEach { (fileName, archiveBucketId) ->
                queue.put(QueuedRead(date, fileName, archiveBucketId, index))
            }
        }
        return index
    }

    /**
     * Write [data] for the given [fileType].
     */
    fun write(
        fileType: S3FileType,
        data: Map<String, ByteArray>
    ) {
        queue.put(QueuedWrite(fileType, data))
    }

    private fun runInThread(index: Int) {
        while (true) {
            val item = queue.poll(5, TimeUnit.SECONDS)
            threadEmpty[index] = item == null
            if (item == null) {
                continue
            }
            when (item) {
                is QueuedRead -> s3ReadStorageService.readAndWriteToTmpStorage(item)
                is QueuedWrite -> s3WriteStorageService.write(item)
            }
        }
    }

    /**
     * Blocks and returns after all threads and writing are finished.
     */
    fun lock(title: String, action: () -> Unit) = timed(title) {
        action()

        // wait for all queued actions
        threadEmpty.forEach { threadEmpty[it.key] = false }
        while (threadEmpty.any { !it.value }) {
            TimeUnit.SECONDS.sleep(2)
        }

        // during reading, we also write to tmp storage, block until it's finished
        s3ReadStorageService.blockUntilFinished()
    }
}
