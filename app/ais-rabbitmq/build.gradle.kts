buildscript {
    project.extra.set("baseVersion", "2.1.0")
}

plugins {
    id("ais-engine.kotlin-app-webmvc-conventions")
    id("org.cyclonedx.bom") version cyclonedxBomVersion
}

dependencies {
    implementation(project(":lib:ais-stream-history-consumer"))
    implementation(project(":lib:platform"))

    implementation("nl.teqplay.skeleton:datasource2:$skeletonVersion")
    implementation("nl.teqplay.skeleton:rabbitmq:$skeletonVersion")
}

tasks.cyclonedxBom {
    setIncludeConfigs(listOf("runtimeClasspath"))
    setOutputFormat("json")
    setOutputName("bom")
}
