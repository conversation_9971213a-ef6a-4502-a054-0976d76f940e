package nl.teqplay.aisengine.aisrabbitmqconverter.datasource

import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.datasource.MinimalStateDataSourceImpl
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class MinimalStateDataSourceConfiguration {

    @Bean
    fun minimalStateDataSource(mongoDatabase: MongoDatabase) = MinimalStateDataSourceImpl(mongoDatabase)
}
