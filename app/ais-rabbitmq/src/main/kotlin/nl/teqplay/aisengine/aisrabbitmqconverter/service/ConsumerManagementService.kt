package nl.teqplay.aisengine.aisrabbitmqconverter.service

import jakarta.annotation.PostConstruct
import nl.teqplay.aisengine.service.ConsumerService
import org.springframework.stereotype.Component

@Component
class ConsumerManagementService(
    private val consumerService: ConsumerService
) {

    @PostConstruct
    fun initialize() {
        start()
    }

    fun start() = consumerService.start()
    fun stop() = consumerService.stop()
}
