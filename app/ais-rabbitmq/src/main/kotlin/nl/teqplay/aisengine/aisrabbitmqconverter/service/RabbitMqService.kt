package nl.teqplay.aisengine.aisrabbitmqconverter.service

import com.fasterxml.jackson.databind.ObjectMapper
import nl.teqplay.aisengine.aisrabbitmqconverter.Config
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Service

@Service
class RabbitMqService(
    private val rabbitMqEventSender: RabbitMqEventSender,
    @Qualifier("rmqMapper")
    private val rmqMapper: ObjectMapper,
    private val config: Config
) {
    fun sendStreamingUpdates(updates: StreamingAisUpdates) {
        rabbitMqEventSender.send(config.exchange, rmqMapper.writeValueAsBytes(updates))
    }
}
