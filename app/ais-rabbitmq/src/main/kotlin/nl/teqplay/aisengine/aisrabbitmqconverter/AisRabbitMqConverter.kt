package nl.teqplay.aisengine.aisrabbitmqconverter

import nl.teqplay.skeleton.common.logging.EnableIncomingRequestLogging
import nl.teqplay.skeleton.common.logging.EnableOutgoingRequestLogging
import nl.teqplay.skeleton.rabbitmq.RabbitMqAutoconfiguration
import org.springframework.boot.autoconfigure.ImportAutoConfiguration
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer
import org.springframework.scheduling.annotation.EnableScheduling

@SpringBootApplication(exclude = [MongoAutoConfiguration::class])
@EnableIncomingRequestLogging
@EnableOutgoingRequestLogging
@ConfigurationPropertiesScan
@ImportAutoConfiguration(exclude = [RabbitMqAutoconfiguration::class])
@EnableScheduling
class AisRabbitMqConverter : SpringBootServletInitializer()

fun main(args: Array<String>) {
    runApplication<AisRabbitMqConverter>(*args)
}
