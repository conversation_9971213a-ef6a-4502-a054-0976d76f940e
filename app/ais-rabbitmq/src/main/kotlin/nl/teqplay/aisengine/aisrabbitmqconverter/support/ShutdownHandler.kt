package nl.teqplay.aisengine.aisrabbitmqconverter.support

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.aisrabbitmqconverter.service.ShipStateService
import org.springframework.context.event.ContextClosedEvent
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

private val LOG = KotlinLogging.logger {}

@Component
class ShutdownHandler(
    private val stateService: ShipStateService
) {
    @EventListener(ContextClosedEvent::class)
    fun shutdown(event: ContextClosedEvent) {
        LOG.info { "Shutting down" }

        try {
            stateService.shutdown()
        } catch (e: Throwable) {
            LOG.warn(e) { "Error shutting down ais rabbit mq converter" }
        }

        LOG.info { "Shutdown complete" }
    }
}
