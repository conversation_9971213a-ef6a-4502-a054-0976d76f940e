package nl.teqplay.aisengine.aisstream

import com.fasterxml.jackson.module.kotlin.readValue
import io.github.oshai.kotlinlogging.KotlinLogging
import io.nats.client.Nats
import io.nats.client.PushSubscribeOptions
import nl.teqplay.aisengine.ObjectMapperConfiguration
import nl.teqplay.aisengine.aisstream.model.AisWrapper
import nl.teqplay.aisengine.aisstream.model.BaseAisMessage
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.slf4j.LoggerFactory
import org.testcontainers.containers.GenericContainer
import org.testcontainers.containers.MongoDBContainer
import org.testcontainers.containers.Network
import org.testcontainers.containers.output.Slf4jLogConsumer
import org.testcontainers.containers.wait.strategy.Wait
import org.testcontainers.junit.jupiter.Container
import org.testcontainers.junit.jupiter.Testcontainers
import org.testcontainers.utility.DockerImageName
import java.io.InputStreamReader
import java.net.ServerSocket
import java.net.SocketException
import java.time.Duration
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.TimeUnit
import kotlin.concurrent.thread

private val LOG = KotlinLogging.logger {}
// Not sure if this logger works, as these tests don't work at all at the moment (docker image issues; SHAs not lining up)
private val slf4jLogger = LoggerFactory.getLogger("Experiment")

@Testcontainers
class Experiment {
    private lateinit var app: GenericContainer<*>

    private val network = Network.newNetwork()

    @Container
    private val mongo = MongoDBContainer(
        DockerImageName.parse("mongo:4.4.12")
    )
        .withNetwork(network)
        .withNetworkAliases("mongo")

    @Container
    private val nats = GenericContainer(
        DockerImageName.parse("nats:latest")
    )
        .withLogConsumer(Slf4jLogConsumer(slf4jLogger))
        .withNetwork(network)
        .withNetworkAliases("nats")
        .withExposedPorts(4222)
        .withCommand("-js")

    private val mapper = ObjectMapperConfiguration().objectMapper()

    private lateinit var sock: ServerSocket

    private val sendQueue = LinkedBlockingQueue<String>(100)

    @BeforeEach
    fun setup() {
        sock = ServerSocket(15000)
        thread(block = ::handler)
        org.testcontainers.Testcontainers.exposeHostPorts(15000)
        app = GenericContainer(
            DockerImageName.parse("050356841556.dkr.ecr.eu-west-1.amazonaws.com/ais-engine/ais-stream:latest")
        )
            .withLogConsumer(Slf4jLogConsumer(slf4jLogger))
            .withNetwork(network)
            .dependsOn(mongo)
            .dependsOn(nats)
            .waitingFor(Wait.forLogMessage(".*StartupInfoLogger: Started AisStreamKt in.*", 1))
            .withEnv("MONGODB_HOST", "mongo")
            .withEnv("MONGODB_PORT", "27017")
            .withEnv("NATS_AIS_STREAM_ENABLED", "true")
            .withEnv("NATS_AIS_STREAM_URL", "nats://nats:4222")
            .withEnv("AIS_STREAM_SOURCES_0_NAME", "INTEST")
            .withEnv("AIS_STREAM_SOURCES_0_HOSTNAME", "host.testcontainers.internal")
            .withEnv("AIS_STREAM_SOURCES_0_TYPE", "tcp")
            .withEnv("AIS_STREAM_SOURCES_0_PORT", "15000")
            .withEnv("AIS_STREAM_HISTORY_DELAY", "PT5S")
            .withEnv("AIS_STREAM_HISTORY_PROCESSING_INTERVAL", "PT5S")
            .withEnv("AIS_STREAM_GHOST_SHIP_FILTER_ENABLED", "false")

        app.start()
    }

    private fun handler() {
        try {
            while (true) {
                val conn = sock.accept()
                LOG.info { "Connection received" }
                val chan = conn.getOutputStream()
                thread {
                    while (conn.isConnected) {
                        val line = sendQueue.poll(1, TimeUnit.SECONDS)
                        if (line != null) {
                            chan.write("$line\r\n".encodeToByteArray())
                            chan.flush()
                        } else {
                            chan.write("\r\n".encodeToByteArray())
                            chan.flush()
                        }
                    }
                }
            }
        } catch (e: SocketException) { /* stop the thread */ }
    }

    @AfterEach
    fun teardown() {
        sock.close()
        app.stop()
    }

    @Test
    fun `it runs`() {
        assertTrue(app.isRunning)

        val aismsgs = javaClass.getResourceAsStream("/spire.txt")
        assertNotNull(aismsgs, "Can't read spire.txt")
        aismsgs?.let { InputStreamReader(it).forEachLine { sendQueue.put(it) } }

        var i = 5
        while (i > 0 && sendQueue.isNotEmpty()) {
            Thread.sleep(1000)
            i--
        }
        if (sendQueue.isNotEmpty()) {
            LOG.warn { "Not all messages were sent" }
        }

        val msgs = getStream()

        LOG.info { "We have received ${msgs.size} messages!!" }

        assertTrue(msgs.isNotEmpty(), "we should have received messages")
    }

    private fun getStream(): List<AisWrapper<out BaseAisMessage>> {
        val nc = Nats.connect("nats://${nats.host}:${nats.getMappedPort(4222)}")
        val js = nc.jetStream()
        val so = PushSubscribeOptions.builder()
            .stream("ais-stream:history")
            .build()

        val sub = js.subscribe(">", so)
        nc.flush(Duration.ofSeconds(5))

        val msgs = mutableListOf<AisWrapper<out BaseAisMessage>>()
        while (true) {
            val delay = if (msgs.isEmpty()) Duration.ofSeconds(120) else Duration.ofSeconds(7)
            val msg = sub.nextMessage(delay) ?: break

            val aismsg = mapper.readValue<AisWrapper<out BaseAisMessage>>(msg.data)
            msgs.add(aismsg)
            msg.ack()
        }

        return msgs
    }
}
