package nl.teqplay.aisengine.aisstream.model

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.kotlin.mock
import java.time.Instant
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class StateTest {

    data class GetTimestampByTypeTestData(
        val message: String,
        val state: State,
        val wrapper: AisWrapper<out BaseAisMessage>,
        val expected: Instant
    )

    @ParameterizedTest
    @MethodSource("getTimestampByTypeTestData")
    fun getTimestampByType(data: GetTimestampByTypeTestData) {
        val actual = data.state.getTimestampByType(data.wrapper)
        assertThat(actual).isEqualTo(data.expected)
    }

    private fun getTimestampByTypeTestData() = Stream.of(
        GetTimestampByTypeTestData(
            message = "position",
            state = State(
                position = positionWrapper(Instant.EPOCH)
            ),
            wrapper = positionWrapper(Instant.MIN),
            expected = Instant.EPOCH
        ),
        GetTimestampByTypeTestData(
            message = "longrange",
            state = State(
                longrange = longRangeWrapper(Instant.EPOCH)
            ),
            wrapper = longRangeWrapper(Instant.MIN),
            expected = Instant.EPOCH
        ),
        GetTimestampByTypeTestData(
            message = "position & longrange, take maximum (position)",
            state = State(
                position = positionWrapper(Instant.EPOCH.plusSeconds(1)),
                longrange = longRangeWrapper(Instant.EPOCH)
            ),
            wrapper = longRangeWrapper(Instant.MIN),
            expected = Instant.EPOCH.plusSeconds(1)
        ),
        GetTimestampByTypeTestData(
            message = "position & longrange, take maximum (longrange)",
            state = State(
                position = positionWrapper(Instant.EPOCH),
                longrange = longRangeWrapper(Instant.EPOCH.plusSeconds(1))
            ),
            wrapper = positionWrapper(Instant.MIN),
            expected = Instant.EPOCH.plusSeconds(1)
        ),
        GetTimestampByTypeTestData(
            message = "static",
            state = State(
                static = staticWrapper(Instant.EPOCH)
            ),
            wrapper = staticWrapper(Instant.MIN),
            expected = Instant.EPOCH
        ),
        GetTimestampByTypeTestData(
            message = "station",
            state = State(
                station = stationWrapper(Instant.EPOCH)
            ),
            wrapper = stationWrapper(Instant.MIN),
            expected = Instant.EPOCH
        ),
    )

    private fun positionWrapper(timestamp: Instant) = AisPositionWrapper(timestamp, timestamp, "", null, mock())
    private fun longRangeWrapper(timestamp: Instant) = AisLongRangeWrapper(timestamp, timestamp, "", null, mock())
    private fun staticWrapper(timestamp: Instant) = AisStaticWrapper(timestamp, timestamp, "", null, mock())
    private fun stationWrapper(timestamp: Instant) = AisStationWrapper(timestamp, timestamp, "", null, mock())
}
