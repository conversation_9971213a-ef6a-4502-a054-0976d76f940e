package nl.teqplay.aisengine.aisstream.service

import dk.dma.ais.message.AisMessage
import dk.dma.ais.sentence.Vdm
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.aisstream.AisStreamBaseTest
import nl.teqplay.aisengine.aisstream.model.AisPositionWrapper
import nl.teqplay.aisengine.aisstream.properties.AisStreamProperties.Source
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.bean.override.mockito.MockitoBean
import java.time.Clock
import java.time.Instant
import java.time.temporal.ChronoUnit

@ContextConfiguration
@MockitoBean(
    types = [ShipStateService::class]
)
class AisTcpReceiverTest(
    private val receiver: AisTcpReceiver,
    private val shipStateService: ShipStateService
) : AisStreamBaseTest() {

    @TestConfiguration
    class Config(
        private val registry: MeterRegistry
    ) {
        val source = Source(
            "AIS HUB",
            "tcp",
            "amqp",
            61619,
            true,
            "1234",
            null
        )

        @Bean
        fun receiver(shipStateService: ShipStateService) = AisTcpReceiver(shipStateService, source, Clock.systemUTC(), registry)
    }

    @Test
    fun `The received AisMessage has no timestamp`() {
        // message setup
        val vdm = Vdm()
        vdm.parse("!AIVDM,1,1,,,13EfMmP01EwVHqrHteS<29Qd2>@<,0*60")
        val aisMessage = AisMessage.getInstance(vdm)

        // update function setup
        var receivedMessage: AisPositionWrapper? = null
        whenever(shipStateService.update(any())).then {
            it.getArgument<AisPositionWrapper>(0).let { wrapper -> receivedMessage = wrapper }
        }

        // ingest the message
        receiver.accept(aisMessage)
        assertNotNull(receivedMessage)

        if (receivedMessage != null) {
            assertEquals(
                Instant.ofEpochMilli(System.currentTimeMillis()).truncatedTo(ChronoUnit.MINUTES),
                receivedMessage!!.timestamp.truncatedTo(ChronoUnit.MINUTES)
            )
        }
    }

    @Test
    fun `The received AisMessage has a timestamp and a subSource`() {
        // message setup
        val vdm = Vdm()
        vdm.parse("\\c:1655330398*5C\\!AIVDM,1,1,,,13EfMmP01EwVHqrHteS<29Qd2>@<,0*60")
        vdm.commentBlock.addString("s", "FM102")
        val aisMessage = AisMessage.getInstance(vdm)

        // update function setup
        var receivedMessage: AisPositionWrapper? = null
        whenever(shipStateService.update(any())).then {
            it.getArgument<AisPositionWrapper>(0).let { wrapper -> receivedMessage = wrapper }
        }

        // ingest the message
        receiver.accept(aisMessage)
        assertNotNull(receivedMessage)

        if (receivedMessage != null) {
            assertEquals(
                Instant.ofEpochSecond(1655330398L),
                receivedMessage!!.timestamp
            )
            assertEquals(
                "s102",
                receivedMessage!!.subSource
            )
        }
    }
}
