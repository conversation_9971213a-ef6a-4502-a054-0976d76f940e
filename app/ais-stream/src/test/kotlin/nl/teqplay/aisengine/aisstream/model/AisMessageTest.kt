package nl.teqplay.aisengine.aisstream.model

import dk.dma.ais.message.AisMessage
import dk.dma.ais.sentence.Vdm
import nl.teqplay.aisengine.aisstream.util.getSubSource
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class AisMessageTest {

    private fun testAisSpireSubSourceMessages(): Stream<Arguments> {
        val vdm = Vdm()
        vdm.parse("\\c:***********5C\\!AIVDM,1,1,,,13EfMmP01EwVHqrHteS<29Qd2>@<,0*60")

        return Stream.of(
            Arguments.of(AisMessage.getInstance(vdm), "terrestrial", "t"),
            Arguments.of(AisMessage.getInstance(vdm), null, "t"),
            Arguments.of(AisMessage.getInstance(vdm), "dynamic", "d"),
            Arguments.of(AisMessage.getInstance(vdm), "FM102", "s102"),
            Arguments.of(AisMessage.getInstance(vdm), "FM", "s"),
            Arguments.of(AisMessage.getInstance(vdm), "unknown", "u"),
        )
    }

    @ParameterizedTest
    @MethodSource("testAisSpireSubSourceMessages")
    fun getSubSourceTest(
        source: AisMessage,
        comment: String?,
        subSource: String
    ) {
        val message = source.apply { vdm.commentBlock.addString("s", comment) }
        val messageSubSource = message.getSubSource()

        assertEquals(subSource, messageSubSource)
    }
}
