package nl.teqplay.aisengine.aisstream.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.aisstream.AisStreamBaseTest
import nl.teqplay.aisengine.aisstream.model.AisPositionWrapper
import nl.teqplay.aisengine.aisstream.model.FinnishAisMessage
import nl.teqplay.aisengine.aisstream.properties.AisStreamProperties.Source
import org.eclipse.paho.client.mqttv3.MqttMessage
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.bean.override.mockito.MockitoBean
import java.time.Clock
import java.time.Instant
import java.time.temporal.ChronoUnit

@ContextConfiguration
@MockitoBean(
    types = [ShipStateService::class]
)
class FinnishAisReceiverTest(
    private val receiver: FinnishAisReceiver,
    private val shipStateService: ShipStateService
) : AisStreamBaseTest() {

    @TestConfiguration
    class Config(
        private val registry: MeterRegistry
    ) {
        val source = Source(
            "FINNISH AIS",
            "wss://meri.digitraffic.fi:61619/mqtt",
            "mqtt-digitraffic",
            61619,
            false,
            username = "username",
            password = "password"
        )

        @Bean
        fun receiver(shipStateService: ShipStateService) = FinnishAisReceiver(shipStateService, source, Clock.systemUTC(), registry)
    }

    @Test
    fun `A valid digitraffic mqtt message is ingested`() {
        // message setup
        val objectMapper = ObjectMapper().registerKotlinModule()
        val aisMessage = objectMapper.readFileFromResources<FinnishAisMessage>("./Digitraffic_mqtt_message.json")
        val mqttMessage = MqttMessage(ObjectMapper().writeValueAsString(aisMessage).toByteArray())

        // update function setup
        var receivedMessage: AisPositionWrapper? = null
        whenever(shipStateService.update(any())).then {
            it.getArgument<AisPositionWrapper>(0).let { wrapper -> receivedMessage = wrapper }
        }

        // ingest the message
        receiver.messageArrived("vessels-v2/666666666/location", mqttMessage)
        assertNotNull(receivedMessage)
    }

    @Test
    fun `should parse mqtt topic correctly to mmsi`() {
        val topic = receiver.getMmsiFromTopic("vessels-v2/666666666/location")
        val expected = 666666666

        assertEquals(expected, topic)
    }

    @Test
    fun `should parse history message timestamp correctly`() {
        val expected = Instant.now()
            .minus(1, ChronoUnit.DAYS)
            .truncatedTo(ChronoUnit.SECONDS)
        val timestamp = receiver.getTimestamp(expected.epochSecond)

        assertEquals(expected, timestamp)
    }
}
