package nl.teqplay.aisengine.aisstream.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.mongodb.kotlin.client.FindIterable
import com.mongodb.kotlin.client.MongoCursor
import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.aisstream.AisStreamBaseTest
import nl.teqplay.aisengine.aisstream.datasource.ShipStateDataSource
import nl.teqplay.aisengine.aisstream.datasource.ShipStateWrapper
import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.aisstream.model.AisPositionMessage
import nl.teqplay.aisengine.aisstream.model.AisPositionWrapper
import nl.teqplay.aisengine.aisstream.model.AisStaticMessage
import nl.teqplay.aisengine.aisstream.model.AisStaticWrapper
import nl.teqplay.aisengine.aisstream.model.AisWrapper
import nl.teqplay.aisengine.aisstream.model.BaseAisMessage
import nl.teqplay.aisengine.aisstream.properties.AisStreamProperties
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.nats.NatsClientMock
import nl.teqplay.skeleton.nats.NatsProducerStream
import nl.teqplay.skeleton.nats.queue
import org.awaitility.kotlin.await
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.reset
import org.mockito.kotlin.whenever
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.test.annotation.DirtiesContext
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.bean.override.mockito.MockitoBean
import java.time.Clock
import java.time.Duration
import java.time.Instant
import kotlin.concurrent.thread

@ContextConfiguration
@MockitoBean(
    types = [
        ShipStateDataSource::class,
        AisMessageInvalidationService::class,
        AisMessageDedupeService::class,
        GhostShipFilterService::class
    ]
)
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
class ShipStateServiceTest(
    private val natsClientMock: NatsClientMock,
    private val shipStateService: ShipStateService,
    private val aisMessageInvalidationService: AisMessageInvalidationService,
    private val aisMessageDedupeService: AisMessageDedupeService,
    private val clock: Clock
) : AisStreamBaseTest() {

    companion object {
        const val HISTORY_STREAM = "ais-stream:history"
        const val DIFF_STREAM = "ais-stream:diff"
        const val DIFF_SUBJECT = "ais-stream.diff.>"
        const val HISTORY_SUBJECT = "ais-stream.history.>"
    }

    @TestConfiguration
    class Config {

        @Bean
        fun natsAisDiffProducerStream(
            natsClientMock: NatsClientMock,
            objectMapper: ObjectMapper
        ): NatsProducerStream<AisDiffMessage> {
            return natsClientMock.producerStream(
                stream = DIFF_STREAM,
                subjects = listOf(DIFF_SUBJECT),
                serializer = { input -> objectMapper.writeValueAsBytes(input) }
            )
        }

        @Bean
        fun natsAisHistoryProducerStream(
            natsClientMock: NatsClientMock,
            objectMapper: ObjectMapper
        ): NatsProducerStream<AisWrapper<out BaseAisMessage>> {
            return natsClientMock.producerStream(
                stream = HISTORY_STREAM,
                subjects = listOf(HISTORY_SUBJECT),
                serializer = { input -> objectMapper.writeValueAsBytes(input) }
            )
        }

        @Bean
        fun aisTransponderFilterService(): AisTransponderFilterService {
            return AisTransponderFilterService()
        }

        private val cursor = mock<MongoCursor<ShipStateWrapper>>()
        private val findIterable = mock<FindIterable<ShipStateWrapper>>().also {
            whenever(it.cursor()).thenReturn(cursor)
        }
        private val shipStateDataSource = mock<ShipStateDataSource>().also {
            whenever(it.getOlderState(any())).thenReturn(findIterable)
            whenever(it.getInitialState(any())).thenReturn(findIterable)
            whenever(it.findStateById(any())).thenReturn(null)
        }

        @Bean
        fun shipStateService(
            registry: MeterRegistry,
            diffStream: NatsProducerStream<AisDiffMessage>,
            historyStream: NatsProducerStream<AisWrapper<out BaseAisMessage>>,
            aisMessageInvalidationService: AisMessageInvalidationService,
            aisMessageDedupeService: AisMessageDedupeService,
            ghostShipFilterService: GhostShipFilterService,
            aisTransponderFilterService: AisTransponderFilterService,
            clock: Clock
        ) = ShipStateService(
            shipStateDataSource,
            registry,
            historyStream,
            diffStream,
            aisMessageInvalidationService,
            aisMessageDedupeService,
            ghostShipFilterService,
            aisTransponderFilterService,
            AisStreamProperties(
                ghostShipFilterEnabled = true,
                transponderFilterEnabled = true,
                syncInterval = Duration.ofMinutes(5),
                dedupeWindow = Duration.ofMinutes(3),
                ignoreChangeRelevancyAge = Duration.ofMinutes(15),
                restartCheckInterval = Duration.ofMinutes(3),
                history = AisStreamProperties.HistorySettings(
                    rateLimitInterval = Duration.ofSeconds(20),
                    processingInterval = Duration.ofSeconds(1), // Force minimal duration, to not wait.
                    delay = Duration.ofSeconds(1),
                    bufferSize = 1_000_000
                ),
                stateAge = Duration.ofDays(60),
                sources = listOf(
                    AisStreamProperties.Source(
                        name = "unit-test",
                        type = "test",
                        hostname = "localhost",
                        port = 0,
                        priority = 1
                    ),
                    AisStreamProperties.Source(
                        name = "unit-test-lowprio",
                        type = "test",
                        hostname = "localhost",
                        port = 0,
                        priority = 5
                    ),
                    AisStreamProperties.Source(
                        name = "unit-test-lowprio2",
                        type = "test",
                        hostname = "localhost",
                        port = 0,
                        priority = 5
                    ),
                )
            ),
            clock
        )

        @Bean
        fun natsClientMock() = NatsClientMock()

        @Bean
        fun clock(): Clock {
            val clock = mock<Clock>()

            whenever(clock.instant()).thenAnswer { Instant.now() }

            return clock
        }
    }

    @BeforeEach
    fun clearQueues() {
        natsClientMock.producerStreams.values.forEach { it.values.clear() }
        whenever(aisMessageInvalidationService.isInvalid(any())).thenReturn(false)
        whenever(aisMessageDedupeService.isDuplicate(any())).thenReturn(false)
    }

    @Test
    fun `all updates are sent to the history queue`() {
        val positionUpdate = AisPositionWrapper(
            timestamp = Instant.parse("2022-01-01T12:00:00Z"),
            receptionTimestamp = Instant.parse("2022-01-01T12:00:00Z"),
            source = "unit-test",
            subSource = "ut",
            message = AisPositionMessage(mmsi = 123456789, location = Location(12.34, 56.78)),
            historic = false
        )
        val staticUpdate = AisStaticWrapper(
            timestamp = Instant.parse("2022-01-01T12:01:00Z"),
            receptionTimestamp = Instant.parse("2022-01-01T12:01:00Z"),
            source = "unit-test",
            subSource = "ut",
            message = AisStaticMessage(mmsi = 123456789, name = "FIXUT MARIS"),
            historic = false
        )

        sendData(
            listOf(
                MockData(positionUpdate.timestamp, positionUpdate),
                MockData(staticUpdate.timestamp, staticUpdate)
            )
        )

        shipStateService.processFullUpdates()

        val historyQueue = natsClientMock.queue<AisWrapper<out BaseAisMessage>>(HISTORY_STREAM)
        assertEquals(2, historyQueue.size, "not all updates sent to queue")
        assertEquals(
            positionUpdate,
            historyQueue.map { it.second }.filterIsInstance<AisPositionWrapper>().first()
        )
        assertEquals(
            staticUpdate,
            historyQueue.map { it.second }.filterIsInstance<AisStaticWrapper>().first()
        )
    }

    @Test
    fun `diff updates are sent to the diff queue`() {
        val position1 = AisPositionWrapper(
            Instant.parse("2022-01-01T12:00:00Z"),
            Instant.parse("2022-01-01T12:00:00Z"),
            "unit-test",
            "ut",
            AisPositionMessage(mmsi = 123456789, location = Location(12.34, 56.78))
        )
        val position2 = AisPositionWrapper(
            Instant.parse("2022-01-01T12:02:00Z"),
            Instant.parse("2022-01-01T12:02:00Z"),
            "unit-test",
            "ut",
            AisPositionMessage(mmsi = 123456789, location = Location(12.35, 56.79))
        )

        sendData(MockData(position1.timestamp, position1))
        shipStateService.processDiff()
        sendData(MockData(position2.timestamp, position2))
        shipStateService.processDiff()

        val diffQueue = natsClientMock.queue<AisDiffMessage>(DIFF_STREAM)
        assertEquals(2, diffQueue.size, "not all updates sent to queue")

        val update = diffQueue.last().second
        assertEquals(position1.message.location, update.location.old, "Old position in update does not match")
        assertEquals(position2.message.location, update.location.changed?.new, "New position in update does not match")
    }

    @Test
    fun `past data is not sent to the full update publisher`() {
        val position1 = AisPositionWrapper(
            Instant.parse("2022-01-01T12:00:00Z"),
            Instant.parse("2022-01-01T12:00:00Z"),
            "unit-test",
            "ut",
            AisPositionMessage(mmsi = 123456789, location = Location(12.34, 56.78))
        )
        val position2 = AisPositionWrapper(
            Instant.parse("2022-01-01T12:02:00Z"),
            Instant.parse("2022-01-01T12:02:00Z"),
            "unit-test",
            "ut",
            AisPositionMessage(mmsi = 123456789, location = Location(12.35, 56.79))
        )

        sendData(MockData(position2.timestamp, position2))
        flushAll()
        sendData(MockData(Instant.parse("2022-01-01T12:10:00Z"), position1))
        flushAll()

        val historyQueue = natsClientMock.queue<AisWrapper<out BaseAisMessage>>(HISTORY_STREAM)

        assertEquals(1, historyQueue.size, "only live updates should have been sent to the full queue")
    }

    @Test
    fun `past data is sent using the historic update publisher, and contains historic=true`() {
        val position1 = AisPositionWrapper(
            timestamp = Instant.parse("2022-01-01T12:00:00Z"),
            receptionTimestamp = Instant.parse("2022-01-01T12:00:00Z"),
            source = "unit-test",
            subSource = "ut",
            message = AisPositionMessage(mmsi = 123456789, location = Location(12.34, 56.78)),
            historic = false
        )
        val position2 = AisPositionWrapper(
            timestamp = Instant.parse("2022-01-01T12:02:00Z"),
            receptionTimestamp = Instant.parse("2022-01-01T12:02:00Z"),
            source = "unit-test",
            subSource = "ut",
            message = AisPositionMessage(mmsi = 123456789, location = Location(12.35, 56.79)),
            historic = false
        )

        sendData(MockData(position2.timestamp, position2))
        sendData(MockData(position1.timestamp, position1))

        whenever(clock.instant()).thenAnswer { Instant.now() }
        shipStateService.startup()

        val historyQueue = natsClientMock.queue<AisWrapper<out BaseAisMessage>>(HISTORY_STREAM)
        await.until { historyQueue.size >= 2 }

        shipStateService.shutdown()

        assertEquals(2, historyQueue.size, "should contain both messages")

        val (_, wrapper) = historyQueue.find { (_, wrapper) -> wrapper.timestamp == position1.timestamp }!!
        assertTrue(wrapper.historic)
    }

    @Test
    fun `warp speed updates are filtered`() {
        val position1 = AisPositionWrapper(
            Instant.parse("2022-01-01T12:00:00Z"),
            Instant.parse("2022-01-01T12:00:00Z"),
            "unit-test",
            "ut",
            AisPositionMessage(mmsi = 123456789, location = Location(12.34, 56.78))
        )
        val position2 = AisPositionWrapper(
            Instant.parse("2022-01-01T12:02:00Z"),
            Instant.parse("2022-01-01T12:02:00Z"),
            "unit-test",
            "ut",
            AisPositionMessage(mmsi = 123456789, location = Location(14.35, 59.79))
        )

        shipStateService.update(position1)
        flushAll()
        shipStateService.update(position2)
        flushAll()

        val historyQueue = natsClientMock.queue<AisWrapper<out BaseAisMessage>>(HISTORY_STREAM)
        assertEquals(1, historyQueue.size, "second update should be ignored")
    }

    @Test
    fun `updates with incorrect heading are filtered`() {
        val position1 = AisPositionWrapper(
            Instant.parse("2022-01-01T12:00:00Z"),
            Instant.parse("2022-01-01T12:00:00Z"),
            "unit-test",
            "ut",
            AisPositionMessage(
                mmsi = 123456789,
                location = Location(12.34, 56.78),
                speedOverGround = 10.0f,
                heading = 90
            )
        )
        val position2 = AisPositionWrapper(
            Instant.parse("2022-01-01T12:02:00Z"),
            Instant.parse("2022-01-01T12:02:00Z"),
            "unit-test",
            "ut",
            AisPositionMessage(
                mmsi = 123456789,
                location = Location(12.34, 56.79),
                speedOverGround = 10.0f,
                heading = -90
            )
        )

        shipStateService.update(position1)
        flushAll()
        shipStateService.update(position2)
        flushAll()

        val historyQueue = natsClientMock.queue<AisWrapper<out BaseAisMessage>>(HISTORY_STREAM)
        assertEquals(1, historyQueue.size, "second update should be ignored")
    }

    @Test
    fun `updates are rate-limited`() {
        val position1 = AisPositionWrapper(
            Instant.parse("2022-01-01T12:00:00Z"),
            Instant.parse("2022-01-01T12:00:00Z"),
            "unit-test",
            "ut",
            AisPositionMessage(mmsi = 123456789, location = Location(12.34, 56.78))
        )
        val position2 = AisPositionWrapper(
            Instant.parse("2022-01-01T12:00:19Z"),
            Instant.parse("2022-01-01T12:00:19Z"),
            "unit-test",
            "ut",
            AisPositionMessage(mmsi = 123456789, location = Location(12.341, 56.781))
        )
        val position3 = AisPositionWrapper(
            Instant.parse("2022-01-01T12:00:21Z"),
            Instant.parse("2022-01-01T12:00:21Z"),
            "unit-test",
            "ut",
            AisPositionMessage(mmsi = 123456789, location = Location(12.342, 56.782))
        )

        sendData(MockData(position1.timestamp, position1))
        flushAll(awaitDiff = 1)
        sendData(MockData(position2.timestamp, position2))
        flushAll()
        sendData(MockData(position3.timestamp, position3))
        flushAll(awaitDiff = 1)

        val historyQueue = natsClientMock.queue<AisWrapper<out BaseAisMessage>>(HISTORY_STREAM)
        val diffQueue = natsClientMock.queue<AisDiffMessage>(DIFF_STREAM)
        assertEquals(2, diffQueue.size, "the middle update should be rate-limited")
        assertEquals(2, historyQueue.size, "the middle update should be rate-limited")
    }

    @Test
    fun `updates are rate-limited regardless of the update type but still update the state`() {
        val position1 = AisPositionWrapper(
            Instant.parse("2022-01-01T12:00:00Z"),
            Instant.parse("2022-01-01T12:00:00Z"),
            "unit-test",
            "ut",
            AisPositionMessage(mmsi = 123456789, location = Location(12.34, 56.78))
        )
        val position2 = AisPositionWrapper(
            Instant.parse("2022-01-01T12:00:19Z"),
            Instant.parse("2022-01-01T12:00:19Z"),
            "unit-test",
            "ut",
            AisPositionMessage(mmsi = 123456789, location = Location(12.34, 56.78))
        )
        val static1 = AisStaticWrapper(
            Instant.parse("2022-01-01T12:00:19Z"),
            Instant.parse("2022-01-01T12:00:19Z"),
            "unit-test",
            "ut",
            AisStaticMessage(mmsi = 123456789, name = "FIXUT MARIS")
        )
        val position3 = AisPositionWrapper(
            Instant.parse("2022-01-01T12:00:21Z"),
            Instant.parse("2022-01-01T12:00:21Z"),
            "unit-test",
            "ut",
            AisPositionMessage(mmsi = 123456789, location = Location(12.341, 56.781))
        )

        sendData(MockData(position1.timestamp, position1))
        flushAll(awaitDiff = 1)
        sendData(MockData(position2.timestamp, position2))
        flushAll()
        sendData(MockData(static1.timestamp, static1))
        flushAll()
        sendData(MockData(position3.timestamp, position3))
        flushAll(awaitDiff = 1)

        val historyQueue = natsClientMock.queue<AisWrapper<out BaseAisMessage>>(HISTORY_STREAM)
        val diffQueue = natsClientMock.queue<AisDiffMessage>(DIFF_STREAM)

        assertEquals(2, diffQueue.size, "the second position update and static update should be rate-limited")
        assertEquals(3, historyQueue.size, "the second position update should be rate-limited")
        assertEquals("FIXUT MARIS", diffQueue.last().second.name.changed?.new)
    }

    @Test
    fun `static update should be applied, even if IMO gets reset`() {
        val position = AisPositionWrapper(
            Instant.parse("2022-01-01T12:00:00Z"),
            Instant.parse("2022-01-01T12:00:00Z"),
            "unit-test",
            "ut",
            AisPositionMessage(mmsi = 123456789, location = Location(12.34, 56.78))
        )
        val static = AisStaticWrapper(
            Instant.parse("2022-01-01T12:01:00Z"),
            Instant.parse("2022-01-01T12:01:00Z"),
            "unit-test",
            "ut",
            AisStaticMessage(mmsi = 123456789, name = "FIXUT MARIS", imo = -1)
        )
        sendData(MockData(position.timestamp, position))
        flushAll(awaitDiff = 1)
        sendData(MockData(static.timestamp, static))
        flushAll(awaitDiff = 1)

        val historyQueue = natsClientMock.queue<AisWrapper<out BaseAisMessage>>(HISTORY_STREAM)
        val diffQueue = natsClientMock.queue<AisDiffMessage>(DIFF_STREAM)

        assertEquals(2, diffQueue.size)
        assertEquals(2, historyQueue.size)
        assertEquals("FIXUT MARIS", diffQueue.last().second.name.changed?.new)
    }

    @Test
    fun `low-prio update within switch interval is dropped`() {
        val position1 = AisPositionWrapper(
            Instant.parse("2022-01-01T12:00:00Z"),
            Instant.parse("2022-01-01T12:00:00Z"),
            "unit-test",
            "ut",
            AisPositionMessage(mmsi = 123456789, location = Location(12.34, 56.78))
        )
        val position2 = AisPositionWrapper(
            Instant.parse("2022-01-01T12:00:30Z"),
            Instant.parse("2022-01-01T12:00:30Z"),
            "unit-test-lowprio",
            "ut",
            AisPositionMessage(mmsi = 123456789, location = Location(12.34, 56.7801))
        )

        shipStateService.update(position1)
        flushAll()
        shipStateService.update(position2)
        flushAll()

        val historyQueue = natsClientMock.queue<AisWrapper<out BaseAisMessage>>(HISTORY_STREAM)
        assertEquals(1, historyQueue.size, "second update should be ignored")
    }

    @Test
    fun `high-prio update within switch interval is not dropped`() {
        val position1 = AisPositionWrapper(
            Instant.parse("2022-01-01T12:00:00Z"),
            Instant.parse("2022-01-01T12:00:00Z"),
            "unit-test-lowprio",
            "ut",
            AisPositionMessage(mmsi = 123456789, location = Location(12.34, 56.78))
        )
        val position2 = AisPositionWrapper(
            Instant.parse("2022-01-01T12:00:30Z"),
            Instant.parse("2022-01-01T12:00:30Z"),
            "unit-test",
            "ut",
            AisPositionMessage(mmsi = 123456789, location = Location(12.34, 56.781))
        )

        shipStateService.update(position1)
        flushAll()
        shipStateService.update(position2)
        flushAll()

        val historyQueue = natsClientMock.queue<AisWrapper<out BaseAisMessage>>(HISTORY_STREAM)
        assertEquals(2, historyQueue.size, "second update should not be ignored")
    }

    @Test
    fun `same-prio update within switch interval is not dropped`() {
        val position1 = AisPositionWrapper(
            Instant.parse("2022-01-01T12:00:00Z"),
            Instant.parse("2022-01-01T12:00:00Z"),
            "unit-test-lowprio",
            "ut",
            AisPositionMessage(mmsi = 123456789, location = Location(12.34, 56.78))
        )
        val position2 = AisPositionWrapper(
            Instant.parse("2022-01-01T12:00:30Z"),
            Instant.parse("2022-01-01T12:00:30Z"),
            "unit-test-lowprio2",
            "ut",
            AisPositionMessage(mmsi = 123456789, location = Location(12.34, 56.781))
        )

        shipStateService.update(position1)
        flushAll()
        shipStateService.update(position2)
        flushAll()

        val historyQueue = natsClientMock.queue<AisWrapper<out BaseAisMessage>>(HISTORY_STREAM)
        assertEquals(2, historyQueue.size, "second update should not be ignored")
    }

    private fun sendData(data: MockData) = sendData(listOf(data))

    private fun sendData(data: List<MockData>) {
        reset(clock)

        data.forEach { elem ->
            shipStateService.update(elem.data)
        }
    }

    private fun withTimeout(timeout: Long = 50, block: () -> Unit) {
        val thread = thread(start = true) {
            try {
                block()
            } catch (_: InterruptedException) {}
        }
        thread.join(timeout)
        if (thread.isAlive) thread.interrupt()
    }

    private fun flushAll(awaitDiff: Int = 0) {
        val diffQueue = natsClientMock.queue<AisDiffMessage>(DIFF_STREAM)
        val currentSize = diffQueue.size

        withTimeout(block = shipStateService::processFullUpdates)
        withTimeout(block = shipStateService::processDiff)

        if (awaitDiff > 0) {
            await.until { diffQueue.size - currentSize >= awaitDiff }
        }
    }

    private data class MockData(
        val receptionTimestamp: Instant,
        val data: AisWrapper<out BaseAisMessage>
    )
}
