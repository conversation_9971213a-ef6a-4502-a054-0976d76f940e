package nl.teqplay.aisengine.aisstream.service

import nl.teqplay.aisengine.aisstream.model.AisPositionMessage
import nl.teqplay.aisengine.aisstream.model.AisPositionWrapper
import nl.teqplay.aisengine.aisstream.model.AisStaticMessage
import nl.teqplay.aisengine.aisstream.model.AisStaticWrapper
import nl.teqplay.aisengine.aisstream.model.AisWrapper
import nl.teqplay.aisengine.aisstream.model.BaseAisMessage
import nl.teqplay.aisengine.aisstream.properties.AisStreamProperties
import nl.teqplay.skeleton.model.Location
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.time.Clock
import java.time.Duration
import java.time.Instant
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
internal class AisMessageDedupeServiceTest {

    private val config = AisStreamProperties(
        ghostShipFilterEnabled = true,
        transponderFilterEnabled = true,
        syncInterval = Duration.ofMinutes(5),
        dedupeWindow = Duration.ofMinutes(3),
        ignoreChangeRelevancyAge = Duration.ofMinutes(15),
        restartCheckInterval = Duration.ofMinutes(3),
        history = AisStreamProperties.HistorySettings(
            rateLimitInterval = Duration.ofSeconds(20),
            processingInterval = Duration.ofSeconds(10),
            delay = Duration.ofMinutes(1),
            bufferSize = 1_000_000
        ),
        stateAge = Duration.ofDays(60)
    )

    data class DedupeTestData(
        val message: String,
        val data: List<Message>
    )

    data class Message(
        val duplicate: Boolean,
        val time: Instant,
        val wrapper: AisWrapper<out BaseAisMessage>
    )

    private fun isDuplicateTestData(): Stream<DedupeTestData> = Stream.of(
        DedupeTestData(
            message = "simple messages, one ship",
            data = listOf(
                Message(
                    duplicate = false,
                    time = Instant.EPOCH,
                    wrapper = AisPositionMessage(0).wrap()
                ),
                Message(
                    duplicate = true,
                    time = Instant.EPOCH,
                    wrapper = AisPositionMessage(0).wrap()
                ),
                Message(
                    duplicate = true,
                    time = Instant.EPOCH
                        .plusMillis(config.dedupeWindow.toMillis()),
                    wrapper = AisPositionMessage(0).wrap()
                ),
                Message(
                    duplicate = false,
                    time = Instant.EPOCH
                        .plusMillis(config.dedupeWindow.toMillis())
                        .plusMillis(1),
                    wrapper = AisPositionMessage(0).wrap()
                )
            )
        ),
        DedupeTestData(
            message = "multiple messages, different ships",
            data = listOf(
                Message(
                    duplicate = false,
                    time = Instant.EPOCH,
                    wrapper = AisPositionMessage(0).wrap()
                ),
                Message(
                    duplicate = false,
                    time = Instant.EPOCH,
                    wrapper = AisPositionMessage(1).wrap()
                ),
                Message(
                    duplicate = true,
                    time = Instant.EPOCH.plusSeconds(2),
                    wrapper = AisPositionMessage(0).wrap()
                ),
                Message(
                    duplicate = true,
                    time = Instant.EPOCH.plusSeconds(2),
                    wrapper = AisPositionMessage(1).wrap()
                )
            )
        ),
        DedupeTestData(
            message = "messages for multiple types, one ship",
            data = messagesForMultipleTypes(false, Instant.EPOCH) +
                messagesForMultipleTypes(true, Instant.EPOCH.plusMillis(config.dedupeWindow.toMillis())) +
                messagesForMultipleTypes(false, Instant.EPOCH.plusMillis(config.dedupeWindow.toMillis() + 1))
        ),
    )

    private fun messagesForMultipleTypes(
        duplicate: Boolean,
        time: Instant
    ) = listOf(
        Message(
            duplicate = duplicate,
            time = time,
            wrapper = AisPositionMessage(0).wrap()
        ),
        Message(
            duplicate = duplicate,
            time = time,
            wrapper = AisStaticMessage(0).wrap()
        ),
        Message(
            duplicate = duplicate,
            time = time,
            wrapper = AisPositionMessage(0, location = Location(52.0, 4.0)).wrap()
        ),
        Message(
            duplicate = duplicate,
            time = time,
            wrapper = AisStaticMessage(0, destination = "NLRTM").wrap()
        )
    )

    private fun AisPositionMessage.wrap() = AisPositionWrapper(
        timestamp = Instant.EPOCH,
        source = "unit-test",
        subSource = "ut",
        message = this
    )

    private fun AisStaticMessage.wrap() = AisStaticWrapper(
        timestamp = Instant.EPOCH,
        source = "unit-test",
        subSource = "ut",
        message = this
    )

    @ParameterizedTest
    @MethodSource("isDuplicateTestData")
    fun isDuplicate(test: DedupeTestData) {
        val clock = mock<Clock>()
        val aisMessageDedupeService = AisMessageDedupeService(config, clock)

        test.data.withIndex().forEach { (index, data) ->
            whenever(clock.instant()).thenReturn(data.time)
            assertEquals(
                data.duplicate,
                aisMessageDedupeService.isDuplicate(data.wrapper),
                "${test.message}, index: $index"
            )
        }
    }
}
