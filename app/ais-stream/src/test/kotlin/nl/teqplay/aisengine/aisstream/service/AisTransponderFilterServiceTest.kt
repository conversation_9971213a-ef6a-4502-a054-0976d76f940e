package nl.teqplay.aisengine.aisstream.service

import nl.teqplay.aisengine.aisstream.model.AisMessage
import nl.teqplay.aisengine.aisstream.model.AisStaticMessage
import nl.teqplay.aisengine.aisstream.model.AisStaticWrapper
import nl.teqplay.aisengine.aisstream.model.State
import nl.teqplay.aisengine.aisstream.model.TransponderPosition
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import java.time.Instant
import java.util.concurrent.ConcurrentHashMap
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class AisTransponderFilterServiceTest {
    companion object {
        private const val TEST_MMSI = 111111111
        private const val TEST_MMSI_2 = 222222222
        private const val TEST_IMO = 1234567
        private const val TEST_IMO_2 = 2345678
        private const val TEST_IMO_INVALID = 1
        private val TEST_TRANSPONDER = TransponderPosition(10, 10, 2, 2)
        private val TEST_TRANSPONDER_2 = TransponderPosition(8, 12, 2, 2)
        private val TEST_TRANSPONDER_INVALID = TransponderPosition(2, 2, 10, 10)
        private val TEST_AIS_VERSION = AisMessage.AisVersionIndicator.ITU_R_M1371_1
        private val TEST_AIS_VERSION_2 = AisMessage.AisVersionIndicator.ITU_R_M1371_5
    }

    lateinit var service: AisTransponderFilterService

    @BeforeEach
    fun setup() {
        service = AisTransponderFilterService()

        val staticWrapper = AisStaticWrapper(
            message = AisStaticMessage(
                imo = TEST_IMO,
                transponderPosition = TEST_TRANSPONDER,
                aisVersion = TEST_AIS_VERSION,
                mmsi = TEST_MMSI
            ),
            source = "TEST",
            subSource = null,
            timestamp = Instant.now()
        )

        service.onStartup(
            state = ConcurrentHashMap(
                mapOf(
                    TEST_MMSI to State(static = staticWrapper)
                )
            )
        )
    }

    @Test
    fun `should allow message from ship without known transponder`() {
        val message = createTestStaticMessage(TEST_MMSI_2)
        assertTrue(service.validate(message))
    }

    @Test
    fun `should allow message from ship with same transponder`() {
        val message = createTestStaticMessage()
        assertTrue(service.validate(message))
    }

    private fun changedStaticMessages(): Stream<AisStaticMessage> {
        return Stream.of(
            createTestStaticMessage(imo = TEST_IMO_2),
            createTestStaticMessage(imo = null),
            createTestStaticMessage(transponder = TEST_TRANSPONDER_2),
            createTestStaticMessage(transponder = null),
            createTestStaticMessage(aisVersion = AisMessage.AisVersionIndicator.ITU_R_M1371_5),
            createTestStaticMessage(aisVersion = null)
        )
    }

    @ParameterizedTest
    @MethodSource("changedStaticMessages")
    fun `should not allow message on different value in static message`(message: AisStaticMessage) {
        assertFalse(service.validate(message))
    }

    private fun incorrectStaticMessages(): Stream<AisStaticMessage> {
        return Stream.of(
            createTestStaticMessage(imo = TEST_IMO_INVALID),
            createTestStaticMessage(mmsi = TEST_MMSI_2, imo = TEST_IMO_INVALID),
            createTestStaticMessage(transponder = TEST_TRANSPONDER_INVALID),
            createTestStaticMessage(mmsi = TEST_MMSI_2, transponder = TEST_TRANSPONDER_INVALID)
        )
    }

    @ParameterizedTest
    @MethodSource("incorrectStaticMessages")
    fun `should not allow message on incorrect static info`(message: AisStaticMessage) {
        assertFalse(service.validate(message))
    }

    @Test
    fun `should allow new message from new transponder after switching and not allow old one`() {
        val messageWithOld = createTestStaticMessage()
        val messageWithNew = createTestStaticMessage(
            imo = TEST_IMO_2,
            transponder = TEST_TRANSPONDER_2,
            aisVersion = TEST_AIS_VERSION_2
        )

        // Should ignore the first 19 messages
        repeat(19) {
            assertFalse(service.validate(messageWithNew))
        }

        // 20th message, should start using new transponder
        assertTrue(service.validate(messageWithNew))

        // 21st message, should not allow old transponder
        assertFalse(service.validate(messageWithOld))

        // 22nd message, should still allow new transponder
        assertTrue(service.validate(messageWithNew))
    }

    @Test
    fun `should allow new message from new transponder after switching with counter reset`() {
        val messageWithOld = createTestStaticMessage()
        val messageWithNew = createTestStaticMessage(
            imo = TEST_IMO_2,
            transponder = TEST_TRANSPONDER_2,
            aisVersion = TEST_AIS_VERSION_2
        )

        // Should ignore first 5 message
        repeat(5) {
            assertFalse(service.validate(messageWithNew))
        }

        // reset message, should allow old transponder
        assertTrue(service.validate(messageWithOld))

        // Should ignore the following 19 messages as counter should be reset
        repeat(19) {
            assertFalse(service.validate(messageWithNew))
        }

        // 20th message after counter reset, should start using new transponder
        assertTrue(service.validate(messageWithNew))

        // Should not allow old transponder as we just switched to the new one
        assertFalse(service.validate(messageWithOld))
    }

    private fun createTestStaticMessage(
        mmsi: Int = TEST_MMSI,
        imo: Int? = TEST_IMO,
        transponder: TransponderPosition? = TEST_TRANSPONDER,
        aisVersion: AisMessage.AisVersionIndicator? = TEST_AIS_VERSION
    ): AisStaticMessage {
        return AisStaticMessage(
            mmsi = mmsi,
            imo = imo,
            transponderPosition = transponder,
            aisVersion = aisVersion
        )
    }
}
