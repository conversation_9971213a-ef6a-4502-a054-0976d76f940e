package nl.teqplay.aisengine.aisstream.service

import nl.teqplay.aisengine.aisstream.AisStreamBaseTest
import nl.teqplay.aisengine.aisstream.model.AisPositionMessage
import nl.teqplay.aisengine.aisstream.model.AisPositionWrapper
import nl.teqplay.aisengine.aisstream.model.wrapIt
import nl.teqplay.skeleton.model.Location
import org.junit.jupiter.api.Assertions.assertAll
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.test.annotation.DirtiesContext
import org.springframework.test.context.ContextConfiguration
import java.time.Instant

@ContextConfiguration
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
class GhostShipFilterServiceTest(private val ghostShipFilterService: GhostShipFilterService) : AisStreamBaseTest() {

    @TestConfiguration
    class Config {
        @Bean
        fun ghostShipFilterService() = GhostShipFilterService()
    }

    private val wrapper = wrapIt(
        Instant.MIN, Instant.MIN, "unit-test", "ut",
        AisPositionMessage(mmsi = 1, location = Location(0.0, 0.0))
    ) as AisPositionWrapper

    @Test
    fun `All of the updates sent to isGhostShip() are valid`() {
        val results = (0L..110L step 10L).map { time ->
            ghostShipFilterService.isGhostShip(wrapper.copy(timestamp = wrapper.timestamp.plusSeconds(time)))
        }

        assertAll(
            { assertEquals(true, results[0]) },
            { assertEquals(true, results[9]) },
            {
                assertEquals(
                    false, results[10],
                    "The check should only return false after the count of valid updates has ticked up to 10"
                )
            },
            {
                assertEquals(
                    true, results[11],
                    "The count of valid updates should reset to 1 if an update comes in after the iteration where the" +
                        " ship is removed from quarantine. For the sake of the unit test this " +
                        "means that the ship will again be recognized as a ghost ship after that iteration. " +
                        " In practice this shouldn't happen because any ship that's present in the ship state will" +
                        " never have its associated updates sent to the ghost ship filter."
                )
            },
        )
    }

    @Test
    fun `One of the updates sent to isGhostShip() was sent too long after the previous update`() {
        val results = (0L..210L step 10L).map { time ->
            when (time > 85L) {
                false -> ghostShipFilterService.isGhostShip(wrapper.copy(timestamp = wrapper.timestamp.plusSeconds(time)))
                true -> ghostShipFilterService.isGhostShip(wrapper.copy(timestamp = wrapper.timestamp.plusSeconds(time + 1800L)))
            }
        }

        assertAll(
            { assertEquals(true, results[0]) },
            { assertEquals(true, results[9]) },
            { assertEquals(true, results[10]) },
            { assertEquals(true, results[11]) },
            {
                assertEquals(
                    false, results[19],
                    "The time discrepancy being too high should reset the valid update counter, so it should take ten " +
                        "more updates before the check resolves to false"
                )
            },
            {
                assertEquals(
                    true, results[20],
                    "The count of valid updates should reset to 1 if an update comes in after the iteration where the" +
                        " ship is removed from quarantine. For the sake of the unit test this " +
                        "means that the ship will again be recognized as a ghost ship after that iteration. " +
                        " In practice this shouldn't happen because any ship that's present in the ship state will" +
                        " never have its associated updates sent to the ghost ship filter."
                )
            },
        )
    }

    @Test
    fun `One of the updates sent to isGhostShip() has an invalid distance difference`() {
        val results = (0L..210L step 10L).map { time ->
            when (time) {
                90L -> ghostShipFilterService.isGhostShip(
                    wrapper.copy(
                        timestamp = wrapper.timestamp.plusSeconds(time),
                        message = wrapper.message.copy(location = Location(100.0, 100.0))
                    )
                )

                else -> ghostShipFilterService.isGhostShip(wrapper.copy(timestamp = wrapper.timestamp.plusSeconds(time)))
            }
        }

        assertAll(
            { assertEquals(true, results[0]) },
            { assertEquals(true, results[9]) },
            { assertEquals(true, results[10]) },
            { assertEquals(true, results[11]) },
            { assertEquals(true, results[19]) },
            {
                assertEquals(
                    false, results[20],
                    "The location being too far away from that of the previous update " +
                        "should reset the valid update counter,and the update following after it should also cause a " +
                        "reset because the distanceDiff would still be too large. This means that it should take eleven " +
                        "more updates before the check resolves to false"
                )
            },
            {
                assertEquals(
                    true, results[21],
                    "The count of valid updates should reset to 1 if an update comes in after the iteration where the" +
                        " ship is removed from quarantine. For the sake of the unit test this " +
                        "means that the ship will again be recognized as a ghost ship after that iteration. " +
                        " In practice this shouldn't happen because any ship that's present in the ship state will" +
                        " never have its associated updates sent to the ghost ship filter."
                )
            },
        )
    }

    @Test
    fun `One of the updates sent to isGhostShip() is missing location info`() {
        val results = (0L..210L step 10L).map { time ->
            when (time) {
                90L -> ghostShipFilterService.isGhostShip(
                    wrapper.copy(
                        timestamp = wrapper.timestamp.plusSeconds(time),
                        message = wrapper.message.copy(location = null)
                    )
                )

                else -> ghostShipFilterService.isGhostShip(wrapper.copy(timestamp = wrapper.timestamp.plusSeconds(time)))
            }
        }

        assertAll(
            { assertEquals(true, results[0]) },
            { assertEquals(true, results[9]) },
            { assertEquals(true, results[10]) },
            { assertEquals(true, results[11]) },
            {
                assertEquals(
                    false, results[12],
                    "Update with index=9 resulted in no counter increase, since the location was null. " +
                        "The next one also didn't increase the counter, since the location of the previous was still null."
                )
            },
            { assertEquals(true, results[19]) },
            {
                assertEquals(
                    true, results[20],
                    "The location being null doesn't reset the valid update counter"
                )
            },
            {
                assertEquals(
                    true, results[21],
                    "The count of valid updates should reset to 1 if an update comes in after the iteration where the" +
                        " ship is removed from quarantine. For the sake of the unit test this " +
                        "means that the ship will again be recognized as a ghost ship after that iteration. " +
                        " In practice this shouldn't happen because any ship that's present in the ship state will" +
                        " never have its associated updates sent to the ghost ship filter."
                )
            },
        )
    }
}
