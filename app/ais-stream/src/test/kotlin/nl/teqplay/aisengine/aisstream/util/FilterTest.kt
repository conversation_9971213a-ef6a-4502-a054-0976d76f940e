package nl.teqplay.aisengine.aisstream.util

import nl.teqplay.aisengine.aisstream.model.AisPositionMessage
import nl.teqplay.aisengine.aisstream.model.AisPositionWrapper
import nl.teqplay.skeleton.model.Location
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class FilterTest {
    private val defaultTime = Instant.ofEpochMilli(1690848000000)
    private val defaultTime2 = defaultTime.plus(1, ChronoUnit.HOURS)
    private val defaultTime3 = defaultTime.plus(1, ChronoUnit.DAYS)

    private val NLRTM_LOCATION = Location(51.95442214470791, 4.073181152343731)
    private val NLAMS_LOCATION = Location(52.40116206466774, 4.876556396484324)
    private val NLEEM_LOCATION = Location(53.450944962775516, 6.838817596435547)
    private val DEBRV_LOCATION = Location(53.55951261876575, 8.562820619675353)

    private fun `IMO numbers`() = Stream.of(
        Arguments.of(9311701L, true),
        Arguments.of(9311702L, false),
        Arguments.of(12345678L, false),
        Arguments.of(123456L, false)
    )

    private fun aisPositionTestCases() = Stream.of(
        Arguments.of(
            false,
            createAisPositionWrapper(defaultTime, createPositionMessage(location = NLRTM_LOCATION)),
            createAisPositionWrapper(defaultTime2, createPositionMessage(location = DEBRV_LOCATION))
        ),
        Arguments.of(
            false,
            createAisPositionWrapper(defaultTime, createPositionMessage(location = NLRTM_LOCATION)),
            createAisPositionWrapper(defaultTime2, createPositionMessage(location = NLEEM_LOCATION))
        ),
        Arguments.of(
            false,
            createAisPositionWrapper(defaultTime, createPositionMessage(location = NLRTM_LOCATION)),
            createAisPositionWrapper(defaultTime.plus(5, ChronoUnit.HOURS), createPositionMessage(location = NLEEM_LOCATION))
        ),
        Arguments.of(
            true,
            createAisPositionWrapper(defaultTime, createPositionMessage(location = NLRTM_LOCATION)),
            createAisPositionWrapper(defaultTime.plus(6, ChronoUnit.HOURS), createPositionMessage(location = NLEEM_LOCATION))
        ),
        Arguments.of(
            true,
            createAisPositionWrapper(defaultTime, createPositionMessage(location = NLRTM_LOCATION)),
            createAisPositionWrapper(defaultTime2, createPositionMessage(location = NLAMS_LOCATION))
        ),
        Arguments.of(
            true,
            createAisPositionWrapper(),
            createAisPositionWrapper(defaultTime3, createPositionMessage(location = Location(1.0, 1.0)))
        )
    )

    @ParameterizedTest
    @MethodSource("IMO numbers")
    fun `test valid and invalid IMO numbers`(imo: Long, valid: Boolean) {
        assertEquals(valid, isValidImo(imo))
    }

    @ParameterizedTest
    @MethodSource("aisPositionTestCases")
    fun `test validate distance`(
        expected: Boolean,
        old: AisPositionWrapper,
        new: AisPositionWrapper,
    ) {
        assertEquals(expected, validDistance(old, new))
    }

    private fun createAisPositionWrapper(
        time: Instant = defaultTime,
        message: AisPositionMessage = createPositionMessage(),
    ): AisPositionWrapper {
        return AisPositionWrapper(time, source = "test", subSource = null, message = message)
    }

    private fun createPositionMessage(
        mmsi: Int = 111111111,
        location: Location = Location(0.0, 0.0)
    ): AisPositionMessage {
        return AisPositionMessage(
            mmsi,
            location
        )
    }
}
