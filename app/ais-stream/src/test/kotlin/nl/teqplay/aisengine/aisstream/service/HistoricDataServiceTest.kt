package nl.teqplay.aisengine.aisstream.service

import nl.teqplay.aisengine.aisstream.model.AisPositionMessage
import nl.teqplay.aisengine.aisstream.model.AisPositionWrapper
import nl.teqplay.aisengine.aisstream.model.AisStaticMessage
import nl.teqplay.aisengine.aisstream.model.AisStaticWrapper
import nl.teqplay.aisengine.aisstream.model.AisWrapper
import nl.teqplay.aisengine.aisstream.model.BaseAisMessage
import nl.teqplay.aisengine.aisstream.properties.AisStreamProperties
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.time.Clock
import java.time.Duration
import java.time.Instant

class HistoricDataServiceTest {
    private val clock = mock<Clock>()

    @Test
    fun `normal rate data is passed through`() {
        val service = instance()

        // Messages spaced 4 minutes apart
        val msgs = listOf(
            msgAt("12:00:00"),
            msgAt("12:01:00"),
            msgAt("12:02:00"),
            msgAt("12:03:00"),
        )

        val output = mutableListOf<AisWrapper<out BaseAisMessage>>()

        at("12:10:00")
        service.processHistory(msgs.poller(), output::add)

        assertEquals(0, output.size, "messages should be kept at least 1 iteration")

        at("12:10:00")
        service.processHistory(msgs.poller(), output::add)

        assertEquals(4, output.size, "all messages should pass through")
    }

    @Test
    fun `data is delayed for a minute`() {
        val service = instance()

        // Messages spaced 4 minutes apart, all received at the same time
        val msgs = listOf(
            msgAt("12:00:00", "12:10:00"),
            msgAt("12:01:00", "12:10:00"),
            msgAt("12:02:00", "12:10:00"),
            msgAt("12:03:00", "12:10:00"),
        )

        val output = mutableListOf<AisWrapper<out BaseAisMessage>>()

        at("12:10:01")
        service.processHistory(msgs.poller(), output::add)

        assertEquals(0, output.size, "messages should be kept at least 1 iteration")

        at("12:10:05")
        service.processHistory(msgs.poller(), output::add)

        assertEquals(0, output.size, "not enough time has elapsed, messages should be kept")

        at("12:11:02")
        service.processHistory(msgs.poller(), output::add)

        assertEquals(4, output.size, "more than a minute has elapsed, messages should pass through")
    }

    @Test
    fun `out of order data in single batch is rate-limited`() {
        val service = instance()

        val msgs = listOf(
            msgAt("12:00:27", "12:15:00"),
            msgAt("12:00:09", "12:15:00"),
            msgAt("12:00:36", "12:15:00"),
            msgAt("12:00:18", "12:15:00"),
            msgAt("12:00:00", "12:15:00"),
            msgAt("12:00:45", "12:15:00"),
        )

        val output = mutableListOf<AisWrapper<out BaseAisMessage>>()

        at("12:16:00")
        service.processHistory(msgs.poller(), output::add)
        at("12:16:20")
        service.processHistory({ null }, output::add)

        assertEquals(2, output.size, "messages should be rate limited")
    }

    @Test
    fun `out of order data in multiple batches is rate-limited`() {
        val service = instance()

        var msgs = listOf(
            msgAt("12:00:27", "12:15:00"),
            msgAt("12:00:09", "12:15:00"),
            msgAt("12:00:36", "12:15:00"),
        )

        val output = mutableListOf<AisWrapper<out BaseAisMessage>>()

        at("12:15:00")
        service.processHistory(msgs.poller(), output::add)

        assertEquals(0, output.size, "messages should still be kept in buffer")

        msgs = listOf(
            msgAt("12:00:18", "12:15:20"),
            msgAt("12:00:00", "12:15:20"),
            msgAt("12:00:45", "12:15:20"),
        )

        at("12:15:20")
        service.processHistory(msgs.poller(), output::add)

        assertEquals(0, output.size, "messages should still be kept in buffer")

        at("12:17:00")
        service.processHistory({ null }, output::add)

        assertEquals(2, output.size, "messages should be rate limited")
    }

    @Test
    fun `data for separate mmsis is rate-limited separately`() {
        val service = instance()

        val msgs = listOf(
            msgAt("12:00:27", "12:15:00", mmsi = 1),
            msgAt("12:00:09", "12:15:00", mmsi = 1),
            msgAt("12:00:36", "12:15:00", mmsi = 1),
            msgAt("12:00:18", "12:15:00", mmsi = 1),
            msgAt("12:00:00", "12:15:00", mmsi = 1),
            msgAt("12:00:45", "12:15:00", mmsi = 1),

            msgAt("12:00:26", "12:15:00", mmsi = 2),
            msgAt("12:00:08", "12:15:00", mmsi = 2),
            msgAt("12:00:39", "12:15:00", mmsi = 2),
            msgAt("12:00:12", "12:15:00", mmsi = 2),
            msgAt("12:00:01", "12:15:00", mmsi = 2),
            msgAt("12:00:43", "12:15:00", mmsi = 2),
        )

        val output = mutableListOf<AisWrapper<out BaseAisMessage>>()

        at("12:16:00")
        service.processHistory(msgs.poller(), output::add)
        at("12:16:20")
        service.processHistory({ null }, output::add)

        assertEquals(4, output.size, "messages should be rate limited")
    }

    @Test
    fun `different message types are rate-limited individually`() {
        val service = instance()

        val msgs = listOf(
            msgAt("12:00:00"),
            staticMsgAt("12:00:05"),
            msgAt("12:00:15"),
            msgAt("12:00:30"),
            msgAt("12:00:32"),
            msgAt("12:00:34"),
            staticMsgAt("12:00:36")
        )

        val output = mutableListOf<AisWrapper<out BaseAisMessage>>()

        at("12:16:00")
        service.processHistory(msgs.poller(), output::add)
        at("12:16:20")
        service.processHistory({ null }, output::add)

        assertEquals(
            2,
            output.filter { it.message is AisPositionMessage }.size,
            "messages should be rate limited by type"
        )

        assertEquals(
            2,
            output.filter { it.message is AisStaticMessage }.size,
            "messages should be rate limited by type"
        )
    }

    private fun instance() = HistoricDataService(
        clock,
        AisStreamProperties(
            ghostShipFilterEnabled = true,
            transponderFilterEnabled = true,
            syncInterval = Duration.ofMinutes(5),
            dedupeWindow = Duration.ofMinutes(3),
            ignoreChangeRelevancyAge = Duration.ofMinutes(15),
            restartCheckInterval = Duration.ofMinutes(3),
            history = AisStreamProperties.HistorySettings(
                rateLimitInterval = Duration.ofSeconds(20),
                processingInterval = Duration.ofSeconds(10),
                delay = Duration.ofMinutes(1),
                bufferSize = 1_000_000
            ),
            stateAge = Duration.ofDays(60)
        )
    )

    private fun msgAt(
        transmissionTime: String,
        receptionTime: String? = null,
        mmsi: Int = 1
    ) = AisPositionWrapper(
        timestamp = Instant.parse("2022-01-01T$transmissionTime.000Z"),
        receptionTimestamp = Instant.parse("2022-01-01T${receptionTime ?: transmissionTime}.000Z"),
        source = "",
        subSource = null,
        message = AisPositionMessage(mmsi)
    )

    private fun staticMsgAt(
        transmissionTime: String,
        receptionTime: String? = null,
        mmsi: Int = 1
    ) = AisStaticWrapper(
        timestamp = Instant.parse("2022-01-01T$transmissionTime.000Z"),
        receptionTimestamp = Instant.parse("2022-01-01T${receptionTime ?: transmissionTime}.000Z"),
        source = "",
        subSource = null,
        message = AisStaticMessage(mmsi)
    )

    private fun at(time: String) {
        whenever(clock.instant()).thenReturn(Instant.parse("2022-01-01T$time.000Z"))
    }

    private fun <E> List<E>.poller(): () -> E? = with(iterator()) {
        { if (hasNext()) next() else null }
    }
}
