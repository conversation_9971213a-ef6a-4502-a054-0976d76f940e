package nl.teqplay.aisengine.aisstream.support

import nl.teqplay.aisengine.aisstream.AisStreamBaseTest
import nl.teqplay.aisengine.aisstream.service.ShipStateService
import org.junit.jupiter.api.Test
import org.mockito.kotlin.inOrder
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.ApplicationContext
import org.springframework.context.annotation.Bean
import org.springframework.context.event.ContextClosedEvent
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.bean.override.mockito.MockitoBean

@ContextConfiguration
@MockitoBean(
    types = [AisReceiverHandler::class, ShipStateService::class]
)
class LifecycleHandlerTest(
    private val lifecycleHandler: LifecycleHandler,
    private val AisReceiverHandler: AisReceiverHandler,
    private val shipStateService: ShipStateService,
    private val applicationContext: ApplicationContext
) : AisStreamBaseTest() {
    @TestConfiguration
    class Config {
        @Bean
        fun shutdownHandler(
            aisReceiverHandler: AisReceiverHandler,
            shipStateService: ShipStateService
        ) = LifecycleHandler(aisReceiverHandler, shipStateService)
    }

    @Test
    fun `shutdown hooks are called in the correct order`() {
        lifecycleHandler.shutdown(ContextClosedEvent(applicationContext))

        inOrder(AisReceiverHandler, shipStateService) {
            verify(AisReceiverHandler).shutdown()
            verify(shipStateService).shutdown()
        }
    }
}
