package nl.teqplay.aisengine.shiphistoryprocessor.service

import com.mongodb.kotlin.client.FindIterable
import com.mongodb.kotlin.client.MongoCursor
import nl.teqplay.aisengine.aisstream.model.AisLongRangeMessage
import nl.teqplay.aisengine.aisstream.model.AisLongRangeWrapper
import nl.teqplay.aisengine.aisstream.model.AisMessage
import nl.teqplay.aisengine.aisstream.model.AisPositionMessage
import nl.teqplay.aisengine.aisstream.model.AisPositionWrapper
import nl.teqplay.aisengine.aisstream.model.AisStaticMessage
import nl.teqplay.aisengine.aisstream.model.AisStaticWrapper
import nl.teqplay.aisengine.aisstream.model.TransponderPosition
import nl.teqplay.aisengine.bucketing.model.AisHistoricUnorderedDiffMessage
import nl.teqplay.aisengine.datasource.ShipHistoryStateDataSource
import nl.teqplay.aisengine.datasource.ShipHistoryStateWrapper
import nl.teqplay.aisengine.model.ShipHistoryState
import nl.teqplay.aisengine.shiphistoryprocessor.config.ShipHistoryWriteCache
import nl.teqplay.skeleton.model.Location
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.time.Instant
import nl.teqplay.aisengine.service.ShipStateService as CommonShipStateService

class ShipStateServiceTest {
    private val messages = mutableListOf<AisHistoricUnorderedDiffMessage>()

    private val cursor = mock<MongoCursor<ShipHistoryStateWrapper>>()
    private val findIterable = mock<FindIterable<ShipHistoryStateWrapper>>().also {
        whenever(it.cursor()).thenReturn(cursor)
    }
    private val shipHistoryStateDataSource = mock<ShipHistoryStateDataSource>().also {
        whenever(it.getOlderState(any())).thenReturn(findIterable)
        whenever(it.getInitialState(any())).thenReturn(findIterable)
        whenever(it.findStateById(any())).thenReturn(null)
    }

    private val commonShipStateService = mock<CommonShipStateService>()
    private val areaIndexService = mock<AreaIndexService>()

    private val mockWriteCache = mock<ShipHistoryWriteCache> {
        on { insert(any()) }.then { invocation ->
            (invocation.arguments[0] as? AisHistoricUnorderedDiffMessage)?.let { messages.add(it) }
        }
    }

    private val shipStateService = ShipStateService(
        commonShipStateService,
        listOf(mockWriteCache),
        areaIndexService,
        shipHistoryStateDataSource
    )

    @BeforeEach
    fun cleanup() {
        messages.clear()
    }

    @Test
    fun `basic update is processed`() {
        val pos = getPosition("2023-01-01T11:00:00Z", null, 1.0, 2.0, 3.0)

        shipStateService.process(pos, ShipHistoryState())

        assertEquals(1, messages.size)
        val msg = messages.first()
        assertEquals(pos.timestamp, msg.messageTime)
        assertEquals(pos.message.location?.lat, msg.lat)
        assertEquals(pos.message.location?.lon, msg.lon)
        assertEquals(pos.message.speedOverGround, msg.speedOverGround)
    }

    @Test
    fun `LR update is ignored if close`() {
        val pos = getPosition("2022-01-01T11:00:00Z", null, 12.0, 12.0, 0.0)
        val lrPos = getLrPosition("2022-01-01T11:02:00Z", null, 12.001, 12.0, 0.0)

        shipStateService.process(lrPos, ShipHistoryState(pos))

        assertTrue(messages.isEmpty())
    }

    @Test
    fun `LR update is converted if distant`() {
        val pos = getPosition("2022-01-01T11:00:00Z", null, 12.0, 12.0, 0.0)
        val lrPos = getLrPosition("2022-01-01T11:02:00Z", null, 12.01, 12.0, 0.0)

        shipStateService.process(lrPos, ShipHistoryState(pos))

        assertEquals(1, messages.size)
        val msg = messages.first()
        assertEquals(lrPos.message.location?.lat, msg.lat)
        assertEquals(lrPos.message.location?.lon, msg.lon)
        assertEquals(lrPos.timestamp, msg.messageTime)
    }

    @Test
    fun `LR update is ignored if old and non-moving`() {
        val pos = getPosition("2022-01-01T11:00:00Z", null, 12.0, 12.0, 0.0)
        val lrPos = getLrPosition("2022-01-01T10:40:00Z", "2022-01-01T11:02:00Z", 12.001, 12.0, 0.0)

        shipStateService.process(lrPos, ShipHistoryState(pos))

        assertTrue(messages.isEmpty())
    }

    @Test
    fun `LR update is converted if old but non-moving`() {
        val pos = getPosition("2022-01-01T11:00:00Z", null, 12.0, 12.0, 0.0)
        val lrPos = getLrPosition("2022-01-01T10:40:00Z", "2022-01-01T11:02:00Z", 12.001, 12.0, 5.0)

        shipStateService.process(lrPos, ShipHistoryState(pos))

        assertEquals(1, messages.size)
        val msg = messages.first()
        assertEquals(lrPos.message.location?.lat, msg.lat)
        assertEquals(lrPos.message.location?.lon, msg.lon)
        assertEquals(lrPos.timestamp, msg.messageTime)
    }

    @Test
    fun `old static update received, but real-time, merging with latest position`() {
        val pos = getPosition("2023-01-01T11:00:00Z", null, 12.0, 12.0, 0.0)
        val static = getStatic("2022-01-01T11:00:00Z", null, "NLRTM")

        shipStateService.process(static, ShipHistoryState(pos))

        assertEquals(1, messages.size)
        val msg = messages.first()
        assertEquals(static.message.destination, msg.destination)
        assertEquals(pos.message.location?.lat, msg.lat)
        assertEquals(pos.message.location?.lon, msg.lon)
        assertEquals(pos.timestamp, msg.messageTime)
    }

    @Test
    fun `old and historic position update received, NOT merging with latest static`() {
        val pos = getPosition("2022-01-01T11:00:00Z", null, 12.0, 12.0, 0.0)
            .copy(historic = true)
        val statePos = getPosition("2023-01-01T11:00:00Z", null, 0.0, 0.0, 0.0)
        val stateStatic = getStatic("2023-01-01T11:00:00Z", null, "NLRTM").let {
            it.copy(
                message = it.message.copy(
                    imo = 1,
                    shipType = AisMessage.ShipType.CARGO,
                    draught = 3.14f,
                    eta = Instant.EPOCH,
                    transponderPosition = TransponderPosition(0, 1, 2, 3)
                )
            )
        }

        shipStateService.process(pos, ShipHistoryState(statePos, stateStatic))

        assertEquals(1, messages.size)
        val msg = messages.first()

        // all static fields should be null
        assertNull(msg.imo)
        assertNull(msg.shipType)
        assertNull(msg.draught)
        assertNull(msg.eta)
        assertNull(msg.destination)
        assertNull(msg.transponderPosition)

        // position fields should remain
        assertEquals(pos.message.location?.lat, msg.lat)
        assertEquals(pos.message.location?.lon, msg.lon)
        assertEquals(pos.timestamp, msg.messageTime)
    }

    private fun getLrPosition(timestamp: String, receptionTimestamp: String?, lat: Double, lon: Double, speed: Double) = AisLongRangeWrapper(
        timestamp = Instant.parse(timestamp),
        receptionTimestamp = Instant.parse(receptionTimestamp ?: timestamp),
        source = "unit-test",
        subSource = null,
        AisLongRangeMessage(
            mmsi = 123456789,
            location = Location(lat, lon),
            speedOverGround = speed.toFloat()
        )
    )

    private fun getPosition(timestamp: String, receptionTimestamp: String?, lat: Double, lon: Double, speed: Double) = AisPositionWrapper(
        timestamp = Instant.parse(timestamp),
        receptionTimestamp = Instant.parse(receptionTimestamp ?: timestamp),
        source = "unit-test",
        subSource = null,
        AisPositionMessage(
            mmsi = 123456789,
            location = Location(lat, lon),
            speedOverGround = speed.toFloat()
        )
    )

    private fun getStatic(timestamp: String, receptionTimestamp: String?, destination: String?) = AisStaticWrapper(
        timestamp = Instant.parse(timestamp),
        receptionTimestamp = Instant.parse(receptionTimestamp ?: timestamp),
        source = "unit-test",
        subSource = null,
        message = AisStaticMessage(
            mmsi = 123456789,
            destination = destination
        )
    )
}
