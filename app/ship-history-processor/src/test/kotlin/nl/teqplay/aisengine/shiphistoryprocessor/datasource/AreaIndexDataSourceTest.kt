package nl.teqplay.aisengine.shiphistoryprocessor.datasource

import com.mongodb.client.MongoCursor
import com.mongodb.kotlin.client.FindIterable
import com.mongodb.kotlin.client.MongoCollection
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.shiphistoryprocessor.model.AreaIndex
import nl.teqplay.skeleton.datasource.kmongo.eq
import org.bson.conversions.Bson
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.time.Instant
import java.time.YearMonth
import java.time.ZoneOffset
import java.time.temporal.ChronoUnit
import com.mongodb.client.FindIterable as JFindIterable

class AreaIndexDataSourceTest {
    companion object {
        private const val TEST_LOCATION_KEY_1_MONGO = "4,0_52,0"
        private const val TEST_LOCATION_KEY_1_S3 = "4.0,52.0"
        private const val TEST_LOCATION_KEY_2_MONGO = "5,0_52,0"
        private const val TEST_LOCATION_KEY_2_S3 = "5.0,52.0"
        private const val MMSI_1 = 1
        private const val MMSI_2 = 2
        private const val MMSI_3 = 3
    }

    private val collection = mock<MongoCollection<AreaIndex>>().apply {
        whenever(this.createIndex(keys = anyOrNull<Bson>(), options = any()))
            .thenReturn("")
    }
    private val database = mock<MongoDatabase>().apply {
        whenever(getCollection<AreaIndex>(any(), any()))
            .thenReturn(collection)
    }
    private val dataSource = AreaIndexDataSource(database)

    @Test
    fun `should group area indexes correctly when having multiple for a day`() {
        val yesterday = Instant.now()
            .truncatedTo(ChronoUnit.DAYS)
            .minus(1, ChronoUnit.DAYS)
        val testData1 = AreaIndex(yesterday, data = mapOf(TEST_LOCATION_KEY_1_MONGO to setOf(MMSI_1)))
        val testData2 = AreaIndex(yesterday, data = mapOf(TEST_LOCATION_KEY_1_MONGO to setOf(MMSI_3)))
        val testData3 = AreaIndex(yesterday, data = mapOf(TEST_LOCATION_KEY_2_MONGO to setOf(MMSI_2, MMSI_3)))

        val iterable = mock<JFindIterable<AreaIndex>>().apply {
            val cursor = mock<MongoCursor<AreaIndex>>().apply {
                whenever(hasNext())
                    .thenReturn(true)
                    .thenReturn(true)
                    .thenReturn(true)
                    .thenReturn(false)

                whenever(next())
                    .thenReturn(testData1)
                    .thenReturn(testData2)
                    .thenReturn(testData3)
            }

            whenever(this.cursor()).thenReturn(cursor)
        }

        whenever(collection.find(eq(AreaIndex::date eq yesterday)))
            .thenReturn(FindIterable(iterable))

        val result = dataSource.getAllIndexesOfDay(yesterday)
        val resultGroupedIndex = dataSource.groupIndexesToFlush(result)
        val expected = AreaIndex(
            date = yesterday,
            data = mapOf(
                TEST_LOCATION_KEY_1_S3 to setOf(MMSI_1, MMSI_3),
                TEST_LOCATION_KEY_2_S3 to setOf(MMSI_2, MMSI_3)
            )
        )

        assertEquals(expected, resultGroupedIndex)
    }

    @Test
    fun `should group indexes together correctly`() {
        val testDay = YearMonth.of(2025, 1)
            .atDay(1)
            .atStartOfDay()
            .toInstant(ZoneOffset.UTC)

        val input = listOf(
            AreaIndex(testDay, data = mapOf(TEST_LOCATION_KEY_1_MONGO to setOf(MMSI_1))),
            AreaIndex(testDay, data = mapOf(TEST_LOCATION_KEY_1_MONGO to setOf(MMSI_3))),
            AreaIndex(testDay, data = mapOf(TEST_LOCATION_KEY_2_MONGO to setOf(MMSI_2, MMSI_3)))
        )

        val resultGroupedIndex = dataSource.groupIndexesToFlush(input)
        val expected = AreaIndex(
            date = testDay,
            data = mapOf(
                TEST_LOCATION_KEY_1_S3 to setOf(MMSI_1, MMSI_3),
                TEST_LOCATION_KEY_2_S3 to setOf(MMSI_2, MMSI_3)
            )
        )

        assertEquals(expected, resultGroupedIndex)
    }
}
