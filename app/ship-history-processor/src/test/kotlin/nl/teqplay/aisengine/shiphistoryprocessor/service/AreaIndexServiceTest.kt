package nl.teqplay.aisengine.shiphistoryprocessor.service

import nl.teqplay.aisengine.shiphistoryprocessor.datasource.AreaIndexDataSource
import nl.teqplay.aisengine.shiphistoryprocessor.model.AreaIndex
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.mockito.kotlin.any
import org.mockito.kotlin.doAnswer
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.never
import org.mockito.kotlin.reset
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.verifyNoMoreInteractions
import org.mockito.kotlin.whenever
import java.time.Instant
import java.time.temporal.ChronoUnit

class AreaIndexServiceTest {
    companion object {
        private const val TEST_LOCATION_KEY_MONGO = "4,0_52,0"
        private const val TEST_LOCATION_KEY_S3 = "4.0,52.0"
        private const val MMSI_1 = 1
        private const val MMSI_2 = 2
        private const val MMSI_3 = 3
    }

    private val areaIndexDataSource = mock<AreaIndexDataSource>()
    private val areaIndexS3Service = mock<AreaIndexS3Service>()

    private val service = AreaIndexService(
        areaIndexDataSource = areaIndexDataSource,
        areaIndexS3Service = areaIndexS3Service
    )

    @BeforeEach
    fun setUp() {
        reset(areaIndexDataSource, areaIndexS3Service)

        whenever(areaIndexDataSource.groupIndexesToFlush(any()))
            .thenCallRealMethod()
    }

    @Test
    fun `should flush correctly to S3`() {
        val day1 = Instant.now()
            .truncatedTo(ChronoUnit.DAYS)
            .minus(7, ChronoUnit.DAYS)
        val day2 = day1.plus(1, ChronoUnit.DAYS)
        val day3 = day2.plus(1, ChronoUnit.DAYS)
        val day4 = day3.plus(1, ChronoUnit.DAYS)
        val day5 = day4.plus(1, ChronoUnit.DAYS)
        val day6 = day5.plus(1, ChronoUnit.DAYS)
        val day7 = day6.plus(1, ChronoUnit.DAYS)

        val areaIndex1Mongo = AreaIndex(day1, data = mapOf(TEST_LOCATION_KEY_MONGO to setOf(MMSI_1, MMSI_2, MMSI_3)))
        val areaIndex2Mongo = AreaIndex(day2, data = mapOf(TEST_LOCATION_KEY_MONGO to setOf(MMSI_1, MMSI_2)))
        val areaIndex3Mongo = AreaIndex(day3, data = mapOf(TEST_LOCATION_KEY_MONGO to setOf(MMSI_1)))
        val areaIndex4Mongo = AreaIndex(day4, data = mapOf(TEST_LOCATION_KEY_MONGO to setOf(MMSI_2, MMSI_3)))
        val areaIndex5Mongo = AreaIndex(day5, data = mapOf(TEST_LOCATION_KEY_MONGO to setOf(MMSI_2)))
        val areaIndex6Mongo = AreaIndex(day6, data = mapOf(TEST_LOCATION_KEY_MONGO to setOf(MMSI_3)))
        val areaIndex7Mongo = AreaIndex(day7, data = mapOf(TEST_LOCATION_KEY_MONGO to setOf(MMSI_1, MMSI_2, MMSI_3)))
        val areaIndex1S3 = AreaIndex(day1, data = mapOf(TEST_LOCATION_KEY_S3 to setOf(MMSI_1, MMSI_2, MMSI_3)))
        val areaIndex2S3 = AreaIndex(day2, data = mapOf(TEST_LOCATION_KEY_S3 to setOf(MMSI_1, MMSI_2)))
        val areaIndex3S3 = AreaIndex(day3, data = mapOf(TEST_LOCATION_KEY_S3 to setOf(MMSI_1)))
        val areaIndex4S3 = AreaIndex(day4, data = mapOf(TEST_LOCATION_KEY_S3 to setOf(MMSI_2, MMSI_3)))
        val areaIndex5S3 = AreaIndex(day5, data = mapOf(TEST_LOCATION_KEY_S3 to setOf(MMSI_2)))
        val areaIndex6S3 = AreaIndex(day6, data = mapOf(TEST_LOCATION_KEY_S3 to setOf(MMSI_3)))
        val areaIndex7S3 = AreaIndex(day7, data = mapOf(TEST_LOCATION_KEY_S3 to setOf(MMSI_1, MMSI_2, MMSI_3)))

        whenever(areaIndexDataSource.getOldestIndex())
            .thenReturn(AreaIndex(date = day1, data = emptyMap()))

        whenever(areaIndexDataSource.getAllIndexesOfDay(eq(day1)))
            .thenReturn(listOf(areaIndex1Mongo))
        whenever(areaIndexDataSource.getAllIndexesOfDay(eq(day2)))
            .thenReturn(listOf(areaIndex2Mongo))
        whenever(areaIndexDataSource.getAllIndexesOfDay(eq(day3)))
            .thenReturn(listOf(areaIndex3Mongo))
        whenever(areaIndexDataSource.getAllIndexesOfDay(eq(day4)))
            .thenReturn(listOf(areaIndex4Mongo))
        whenever(areaIndexDataSource.getAllIndexesOfDay(eq(day5)))
            .thenReturn(listOf(areaIndex5Mongo))
        whenever(areaIndexDataSource.getAllIndexesOfDay(eq(day6)))
            .thenReturn(listOf(areaIndex6Mongo))
        whenever(areaIndexDataSource.getAllIndexesOfDay(eq(day7)))
            .thenReturn(listOf(areaIndex7Mongo))

        whenever(areaIndexS3Service.persist(any()))
            .thenReturn(true)

        service.flushToS3()

        verify(areaIndexS3Service).persist(eq(areaIndex1S3))
        verify(areaIndexS3Service).persist(eq(areaIndex2S3))
        verify(areaIndexS3Service).persist(eq(areaIndex3S3))
        verify(areaIndexS3Service).persist(eq(areaIndex4S3))
        verify(areaIndexS3Service).persist(eq(areaIndex5S3))
        verify(areaIndexS3Service).persist(eq(areaIndex6S3))
        verify(areaIndexS3Service).persist(eq(areaIndex7S3))
        verifyNoMoreInteractions(areaIndexS3Service)

        verify(areaIndexDataSource).delete(eq(day1))
        verify(areaIndexDataSource).delete(eq(day2))
        verify(areaIndexDataSource).delete(eq(day3))
        verify(areaIndexDataSource).delete(eq(day4))
        verify(areaIndexDataSource).delete(eq(day5))
        verify(areaIndexDataSource).delete(eq(day6))
        verify(areaIndexDataSource).delete(eq(day7))
    }

    @Test
    fun `should not flush to S3 when having no data`() {
        val day1 = Instant.now()
            .truncatedTo(ChronoUnit.DAYS)
            .minus(7, ChronoUnit.DAYS)

        val areaIndex1Mongo = AreaIndex(day1, data = mapOf(TEST_LOCATION_KEY_MONGO to setOf(MMSI_1, MMSI_2, MMSI_3)))
        val areaIndex1S3 = AreaIndex(day1, data = mapOf(TEST_LOCATION_KEY_S3 to setOf(MMSI_1, MMSI_2, MMSI_3)))

        whenever(areaIndexDataSource.getOldestIndex())
            .thenReturn(AreaIndex(date = day1, data = emptyMap()))

        whenever(areaIndexDataSource.getAllIndexesOfDay(any()))
            .thenReturn(emptyList())
        whenever(areaIndexDataSource.getAllIndexesOfDay(eq(day1)))
            .thenReturn(listOf(areaIndex1Mongo))

        whenever(areaIndexS3Service.persist(any()))
            .thenReturn(true)

        service.flushToS3()

        verify(areaIndexS3Service).persist(eq(areaIndex1S3))
        verifyNoMoreInteractions(areaIndexS3Service)

        verify(areaIndexDataSource, times(1)).delete(any())
    }

    @Test
    fun `should not flush anything when oldest index is today`() {
        val today = Instant.now().truncatedTo(ChronoUnit.DAYS)
        val todayAreaIndex = AreaIndex(today, data = emptyMap())

        whenever(areaIndexDataSource.getOldestIndex())
            .thenReturn(todayAreaIndex)

        service.flushToS3()

        verify(areaIndexS3Service, never()).persist(any())
        verify(areaIndexDataSource, never()).getAllIndexesOfDay(any())
        verify(areaIndexDataSource, never()).groupIndexesToFlush(any())
        verify(areaIndexDataSource, never()).insert(any())
        verify(areaIndexDataSource, never()).delete(any())
    }

    @Test
    fun `should stop with flushing when an exception is thrown`() {
        whenever(areaIndexDataSource.getOldestIndex())
            .doAnswer { throw Exception() }

        assertDoesNotThrow {
            service.flushToS3()
        }
    }
}
