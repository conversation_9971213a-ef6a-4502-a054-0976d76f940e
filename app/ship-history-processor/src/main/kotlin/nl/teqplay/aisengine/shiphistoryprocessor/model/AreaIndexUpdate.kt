package nl.teqplay.aisengine.shiphistoryprocessor.model

import java.time.Instant

/**
 * Indicates an update to an [AreaIndex] containing one item at a [time] within a specific [areaId], and for a [mmsi].
 */
data class AreaIndexUpdate(
    val time: Instant,
    val areaId: String,
    val mmsi: Int
) {
    companion object {
        private const val POISON_PILL_MARKER = "__poison_pill__"

        /**
         * Special marker object that marks the end of data in the queue.
         */
        val poisonPill = AreaIndexUpdate(Instant.EPOCH, POISON_PILL_MARKER, -1)
    }

    val isPoisonPill: <PERSON>olean
        get() = this == poisonPill
}
