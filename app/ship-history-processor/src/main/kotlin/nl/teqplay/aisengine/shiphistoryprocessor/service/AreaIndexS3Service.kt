package nl.teqplay.aisengine.shiphistoryprocessor.service

import com.amazonaws.services.s3.model.ObjectMetadata
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveGlobalProperties
import nl.teqplay.aisengine.bucketing.properties.ShipHistoryAreaIndexArchiveProperties
import nl.teqplay.aisengine.bucketing.storage.archive.ArchiveClientService
import nl.teqplay.aisengine.bucketing.storage.archive.readS3ObjectBytes
import nl.teqplay.aisengine.shiphistoryprocessor.model.AreaIndex
import nl.teqplay.aisengine.util.floorToLocalDate
import org.springframework.stereotype.Component
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.InputStream
import java.util.zip.ZipEntry
import java.util.zip.ZipInputStream
import java.util.zip.ZipOutputStream

private val LOG = KotlinLogging.logger { }

/**
 * Service that persists [AreaIndex] into S3, appending to it if the index already exists.
 */
@Component
class AreaIndexS3Service(
    private val archive: ShipHistoryAreaIndexArchiveProperties,
    archiveGlobal: BucketArchiveGlobalProperties,
    private val objectMapper: ObjectMapper,
) {
    companion object {
        /**
         * The file name of the area index in the zip file.
         */
        private const val FILE_NAME = "index.json"
    }

    private val archiveClientService = if (archive.enabled) {
        ArchiveClientService(archive, archiveGlobal)
    } else null

    /**
     * Persist [index] into S3, appending data if it already exists.
     * Returns whether the action was successful or needs to be retried if not.
     */
    fun persist(index: AreaIndex): Boolean {
        if (archiveClientService == null) {
            // Nothing to persist, mark as successful.
            LOG.info { "Archiving not enabled, marking as successful." }
            return true
        }

        val parsedDate = index.date.floorToLocalDate()
        val archiveKey = "area-index,$parsedDate.zip"
        LOG.info { "Starting to archive area index to S3 (bucket = ${archive.name}, key = $archiveKey)" }
        val data = mutableMapOf<String, Set<Int>>()
        data.putAll(index.data)
        LOG.info { "Having ${data.size} indexes after adding database index" }

        try {
            val originalBytes = readS3ObjectBytes(archiveClientService, archiveKey)
            if (originalBytes != null) {
                LOG.info { "We already have area index data for this key, adding new data to existing file (bucket = ${archive.name}, key = $archiveKey)" }
                ZipInputStream(originalBytes.inputStream()).use { zipInputStream ->
                    // We only need to get 1 file, so we can directly get the next entry
                    val entry = zipInputStream.nextEntry
                    if (entry != null && entry.name == FILE_NAME) {
                        LOG.info { "Zip contains index file, reading file content and adding to result (bucket = ${archive.name}, key = $archiveKey)" }
                        readExistingAreaIndexData(zipInputStream, data)
                        LOG.info { "Successfully read file content, having now a total of ${data.size} indexes" }
                    }
                }
            }
            writeAreaIndexFileToS3(archiveClientService, data, archiveKey)
            return true
        } catch (e: Exception) {
            LOG.error(e) { "Something went wrong while storing an object in the S3 archive" }
            return false
        }
    }

    private fun writeAreaIndexFileToS3(
        archiveClientService: ArchiveClientService,
        data: Map<String, Set<Int>>,
        archiveKey: String
    ) {
        ByteArrayOutputStream().use { outputStream ->
            ZipOutputStream(outputStream).use { zipOutputStream ->
                // Sort the values to improve compression due to partial repeating MMSIs.
                zipOutputStream.putNextEntry(ZipEntry(FILE_NAME))
                objectMapper.writeValue(
                    zipOutputStream,
                    data.mapValues { it.value.sorted() }.toSortedMap()
                )
            }

            val bytes = outputStream.toByteArray()
            ByteArrayInputStream(bytes).use { inputStream ->
                val metadata = ObjectMetadata()
                metadata.contentLength = bytes.size.toLong()
                metadata.contentType = "application/zip, application/octet-stream"

                LOG.info { "Putting single index file of ${data.size} areas to S3 bucket (bucket = ${archive.name}, key = $archiveKey)" }
                archiveClientService.s3Client.putObject(archive.name, archiveKey, inputStream, metadata)
                LOG.info { "Archiving successful (bucket = ${archive.name}, key = $archiveKey)" }
            }
        }
    }

    private fun readExistingAreaIndexData(
        inputStream: InputStream,
        data: MutableMap<String, Set<Int>>
    ) {
        val indexFileData = inputStream.readAllBytes()
        try {
            val s3Data = objectMapper.readValue<Map<String, Set<Int>>>(indexFileData)
            s3Data.forEach { (areaId, oldMmsis) ->
                val newMmsis = data[areaId] ?: emptySet()

                if (data.containsKey(areaId)) {
                    data[areaId] = oldMmsis + newMmsis
                } else {
                    data[areaId] = oldMmsis
                }
            }
        } catch (ex: Throwable) {
            LOG.error(ex) { "Something went wrong while reading index file content" }
        }
    }
}
