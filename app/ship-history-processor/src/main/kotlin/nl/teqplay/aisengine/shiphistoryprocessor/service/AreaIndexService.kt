package nl.teqplay.aisengine.shiphistoryprocessor.service

import io.github.oshai.kotlinlogging.KotlinLogging
import jakarta.annotation.PostConstruct
import nl.teqplay.aisengine.bucketing.factory.AisHistoricDiffMessageBucketFactoryByArea
import nl.teqplay.aisengine.bucketing.model.AisHistoricUnorderedDiffMessage
import nl.teqplay.aisengine.shiphistoryprocessor.datasource.AreaIndexDataSource
import nl.teqplay.aisengine.shiphistoryprocessor.model.AreaIndexUpdate
import nl.teqplay.aisengine.util.floorToLocalDate
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.time.Instant
import java.time.ZoneOffset
import java.time.temporal.ChronoUnit
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.TimeUnit
import java.util.stream.Collectors
import kotlin.concurrent.thread

private val LOG = KotlinLogging.logger { }

/**
 * Service managing the lifecycle of area indexes. Writing them to the database and flushing them to S3.
 */
@Component
class AreaIndexService(
    private val areaIndexDataSource: AreaIndexDataSource,
    private val areaIndexS3Service: AreaIndexS3Service,
) {

    private val factory = AisHistoricDiffMessageBucketFactoryByArea
    private val bucketIdentifier = factory.bucketIdentifier
    private val resolver = factory.resolver

    companion object {
        private const val BUFFER_WRITE_QUEUE_SIZE = 2000

        // Flushing the area index can only take 5 seconds
        private const val FLUSHER_MAX_TIMEOUT_MILLI = 5000
    }

    /**
     * Buffer to decouple handling incoming messages from writing them to Mongo.
     */
    private val bufferWriteQueue = LinkedBlockingQueue<AreaIndexUpdate>(BUFFER_WRITE_QUEUE_SIZE)

    private val bufferFlushThread = thread(
        block = ::bufferFlusher,
        name = "area-index-buffer-flusher",
        start = false
    )

    @PostConstruct
    fun startup() {
        LOG.info { "Starting buffer flush thread" }
        bufferFlushThread.start()
    }

    fun shutdown() {
        LOG.info { "Shutting down, flushing all data..." }
        bufferWriteQueue.put(AreaIndexUpdate.poisonPill)
    }

    /**
     * Insert [diffMessage] into the area index.
     */
    fun insert(diffMessage: AisHistoricUnorderedDiffMessage) {
        val bucketId = bucketIdentifier.getBucketIdForItem(diffMessage, resolver)
        val archiveBucketId = bucketIdentifier.archive.getArchiveBucketId(bucketId)
        val date = diffMessage.messageTime.floorToLocalDate()

        // The archiveBucketId will hold 1970-01-01,4.0,52.0 for example, date needs to be stripped.
        val areaId = archiveBucketId.removePrefix("$date,")
        bufferWriteQueue.put(AreaIndexUpdate(diffMessage.messageTime, areaId, diffMessage.mmsi))
    }

    /**
     * This thread is used to decouple the message handling from the database writes. In the message handler thread,
     * new data is written to the [bufferWriteQueue]. This thread flushes it to the database, periodically if the buffer
     * is not full, and continuously if the buffer is full.
     */
    private fun bufferFlusher() {
        var running = true
        val flushQueue = LinkedBlockingQueue<AreaIndexUpdate>(BUFFER_WRITE_QUEUE_SIZE)
        while (running || bufferWriteQueue.isNotEmpty() || flushQueue.isNotEmpty()) {
            try {
                val startTime = System.currentTimeMillis()
                while (flushQueue.remainingCapacity() > 0) {
                    val timeout = startTime + FLUSHER_MAX_TIMEOUT_MILLI - System.currentTimeMillis()
                    val bufferData = bufferWriteQueue.poll(timeout, TimeUnit.MILLISECONDS) ?: break
                    if (bufferData.isPoisonPill) {
                        running = false
                    } else {
                        flushQueue.put(bufferData)
                    }
                }

                if (flushQueue.isNotEmpty()) {
                    areaIndexDataSource.insert(flushQueue.toList())
                    flushQueue.clear()
                }
            } catch (e: Exception) {
                LOG.error(e) { "Exception while flushing buffered area index to mongo" }
            }
        }
    }

    @Scheduled(
        initialDelayString = "\${bucket.mongo.unordered.schedule.after-startup}",
        fixedRate = 1,
        timeUnit = TimeUnit.DAYS,
    )
    fun flushToS3() {
        LOG.info { "Flushing indexes to S3..." }
        try {
            val oldestDate = areaIndexDataSource.getOldestIndex()?.date

            val today = Instant.now()
                .truncatedTo(ChronoUnit.DAYS)
            if (oldestDate == today) {
                LOG.info { "Nothing to flush as the oldest index is today $oldestDate" }
                return
            }

            if (oldestDate != null) {
                val daysToFlush = oldestDate
                    .floorToLocalDate()
                    .datesUntil(today.floorToLocalDate())
                    .collect(Collectors.toList())

                LOG.info { "Flushing ${daysToFlush.count()} days of area indexes to S3 (from = ${daysToFlush.first()} = ${daysToFlush.last()})" }
                daysToFlush.forEach { dayToFlush ->
                    val dayToFlushAsInstant = dayToFlush.atStartOfDay()
                        .toInstant(ZoneOffset.UTC)
                    flushDayToS3(dayToFlushAsInstant)
                }
            }
            LOG.info { "Flushed indexes to S3." }
        } catch (ex: Throwable) {
            LOG.error(ex) { "Something went wrong while flushing the indexes to S3." }
        }
    }

    private fun flushDayToS3(dayToFlush: Instant) {
        val shortDayToFlush = dayToFlush.floorToLocalDate()
        LOG.info { "Trying to flush $shortDayToFlush to S3, looking up from database the area index" }
        val databaseIndexes = areaIndexDataSource.getAllIndexesOfDay(dayToFlush)

        if (databaseIndexes.isEmpty()) {
            LOG.info { "Nothing to flush, skipping day $dayToFlush" }
            return
        }

        LOG.info { "Found ${databaseIndexes.size} database indexes preparing them so it can be written to S3" }
        val groupedDatabaseIndex = areaIndexDataSource.groupIndexesToFlush(databaseIndexes)

        LOG.info { "Flushing index $shortDayToFlush to S3" }
        val flushed = areaIndexS3Service.persist(groupedDatabaseIndex)
        if (flushed) {
            LOG.info { "Done flushing deleting all indexes of $shortDayToFlush from mongo" }
            areaIndexDataSource.delete(groupedDatabaseIndex.date)
        }
    }
}
