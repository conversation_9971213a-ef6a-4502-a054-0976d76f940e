package nl.teqplay.aisengine.shiphistoryprocessor.service

import nl.teqplay.aisengine.service.ConsumerService
import org.springframework.context.event.ContextRefreshedEvent
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

@Component
class ConsumerManagementService(
    private val consumerService: ConsumerService,
    private val consumerEventService: ConsumerEventService,
) {
    @EventListener(ContextRefreshedEvent::class)
    fun initialize() {
        start()
    }

    fun start() {
        consumerService.start()
        consumerEventService.start()
    }

    fun stop() {
        consumerService.stop()
        consumerEventService.stop()
    }
}
