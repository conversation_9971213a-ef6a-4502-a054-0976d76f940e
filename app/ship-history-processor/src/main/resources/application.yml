nats:
  enabled: true

  ais-stream:
    enabled: true
    url:
    username:
    password:

  event-stream:
    enabled: true
    url:
    username:
    password:

mongodb:
  db: ship-history

bucket:
  archive:
    ship:
      mmsi:
        enabled: false
        name: ship-history
      area:
        enabled: false
        name: ship-history
      area-index:
        enabled: false
        name: ship-history

# Prometheus JVM stats exposing
management:
  metrics:
    enable:
      jvm: true
    tags:
      component: ship-history-processor
  endpoints:
    web:
      exposure:
        include:
          - health
          - prometheus
