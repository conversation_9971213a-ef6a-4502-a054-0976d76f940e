package nl.teqplay.aisengine.shiphistorybucketcopier.properties

import nl.teqplay.skeleton.model.TimeWindow
import org.springframework.boot.context.properties.ConfigurationProperties
import java.time.Instant

@ConfigurationProperties(prefix = "bucket-copy")
data class BucketCopyProperties(
    val start: Instant,
    val end: Instant,
) {
    fun toTimeWindow(): TimeWindow = TimeWindow(start, end)
}
