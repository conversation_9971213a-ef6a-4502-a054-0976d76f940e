package nl.teqplay.aisengine.shiphistorybucketcopier.service

import nl.teqplay.aisengine.bucketing.properties.BucketProperties
import nl.teqplay.aisengine.shiphistorybucketcopier.LOG
import nl.teqplay.aisengine.shiphistorybucketcopier.config.MongoDbSourceReadConfiguration.SourceShipHistoryByArea
import nl.teqplay.aisengine.shiphistorybucketcopier.config.MongoDbSourceReadConfiguration.SourceShipHistoryByMmsi
import nl.teqplay.aisengine.shiphistorybucketcopier.config.MongoDbTargetWriteConfiguration.TargetShipHistoryByArea
import nl.teqplay.aisengine.shiphistorybucketcopier.config.MongoDbTargetWriteConfiguration.TargetShipHistoryByMmsi
import nl.teqplay.aisengine.shiphistorybucketcopier.config.SourceMongoDbReadStorage
import nl.teqplay.aisengine.shiphistorybucketcopier.config.TargetMongoDbWriteStorage
import nl.teqplay.aisengine.shiphistorybucketcopier.datasource.BucketCopyTrackingDataSource
import nl.teqplay.aisengine.shiphistorybucketcopier.model.BucketCopyKeys
import org.springframework.stereotype.Component

@Component
class BucketCopyProcessor(
    private val bucketProperties: BucketProperties,
    private val bucketConverter: BucketConverter,
    private val bucketCopyTrackingDataSource: BucketCopyTrackingDataSource,

    @SourceShipHistoryByMmsi private val sourceShipHistoryByMmsi: SourceMongoDbReadStorage,
    @SourceShipHistoryByArea private val sourceShipHistoryByArea: SourceMongoDbReadStorage,

    @TargetShipHistoryByMmsi private val targetShipHistoryByMmsi: TargetMongoDbWriteStorage,
    @TargetShipHistoryByArea private val targetShipHistoryByArea: TargetMongoDbWriteStorage,
) {

    fun process(bucketCopyKeys: BucketCopyKeys) {
        copyToTarget("by mmsi", bucketCopyKeys.byMmsi, sourceShipHistoryByMmsi, targetShipHistoryByMmsi)
        copyToTarget("by area", bucketCopyKeys.byArea, sourceShipHistoryByArea, targetShipHistoryByArea)
    }

    private fun copyToTarget(
        description: String,
        bucketCopyKeys: List<String>,
        source: SourceMongoDbReadStorage,
        target: TargetMongoDbWriteStorage,
    ) {
        val chunks = bucketCopyKeys.chunked(bucketProperties.mongo.unordered.bulkWrite.maxCount)

        var copiedChunks = 0
        LOG.info { "Copying ${chunks.size} chunks $description..." }

        chunks.forEach { chunk ->
            val orderedBuckets = source.load(chunk)
            val unorderedBuckets = bucketConverter.convert(orderedBuckets)
            target.insertMany(unorderedBuckets)

            bucketCopyTrackingDataSource.completed(chunk)

            // Give the target mongo some breathing room.
            Thread.sleep(bucketProperties.mongo.unordered.bulkWrite.sleep.toMillis())
            copiedChunks++

            if (copiedChunks % 10 == 0) {
                LOG.info { "Copied $copiedChunks/${chunks.size} chunks $description" }
            }
        }

        LOG.info { "Finishing copying ${chunks.size} chunks $description" }
    }
}
