package nl.teqplay.aisengine.shiphistorybucketcopier.service

import nl.teqplay.aisengine.shiphistorybucketcopier.LOG
import nl.teqplay.aisengine.shiphistorybucketcopier.config.MongoDbSourceReadConfiguration.SourceShipHistoryByArea
import nl.teqplay.aisengine.shiphistorybucketcopier.config.MongoDbSourceReadConfiguration.SourceShipHistoryByMmsi
import nl.teqplay.aisengine.shiphistorybucketcopier.config.SourceMongoDbReadStorage
import nl.teqplay.aisengine.shiphistorybucketcopier.datasource.BucketCopyTrackingDataSource
import nl.teqplay.aisengine.shiphistorybucketcopier.model.BucketCopyKeys
import nl.teqplay.aisengine.shiphistorybucketcopier.properties.BucketCopyProperties
import org.springframework.stereotype.Component

@Component
class BucketCopyInitializer(
    private val bucketCopyTrackingDataSource: BucketCopyTrackingDataSource,
    private val bucketCopyProperties: BucketCopyProperties,

    @SourceShipHistoryByMmsi private val sourceShipHistoryByMmsi: SourceMongoDbReadStorage,
    @SourceShipHistoryByArea private val sourceShipHistoryByArea: SourceMongoDbReadStorage,
) {

    fun init(): BucketCopyKeys {
        if (bucketCopyTrackingDataSource.isAlreadyInitialized()) {
            LOG.info { "Already initialized, fetching remaining keys..." }
            return bucketCopyTrackingDataSource.getRemainingKeys()
        }

        LOG.info { "Not yet initialized, initializing..." }
        val window = bucketCopyProperties.toTimeWindow()
        val byMmsi = sourceShipHistoryByMmsi.findExistingBucketIds(window).asSequence().toList()
        val byArea = sourceShipHistoryByArea.findExistingBucketIds(window).asSequence().toList()

        LOG.info { "Persisting buckets to copy..." }
        val bucketCopyKeys = BucketCopyKeys(byMmsi, byArea)
        bucketCopyTrackingDataSource.save(bucketCopyKeys)
        return bucketCopyKeys
    }
}
