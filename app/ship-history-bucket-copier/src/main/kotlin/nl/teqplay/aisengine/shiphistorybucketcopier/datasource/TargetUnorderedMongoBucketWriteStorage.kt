package nl.teqplay.aisengine.shiphistorybucketcopier.datasource

import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.bucketing.model.bucket.UnorderedBucket
import nl.teqplay.aisengine.bucketing.storage.BucketFactoryDual
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.mongo.MongoBucketStorageQuery
import nl.teqplay.aisengine.bucketing.storage.mongo.unordered.UnorderedMongoBucketStorageImpl

interface TargetUnorderedMongoBucketWriteStorage<UnorderedData : Any> {
    fun insertMany(
        buckets: List<UnorderedBucket<UnorderedData>>,
    )
}

class TargetUnorderedMongoBucketWriteStorageImpl<
    OrderedData : Data,
    UnorderedData : Data,
    Data : Any,
    Output : Data,
    Key : Any,
    ID : BucketId<Key>,
    >(
    private val mongoBucketStorageQuery: MongoBucketStorageQuery.Unordered,
    mongoDatabase: MongoDatabase,
    collectionName: String,
    unorderedCollectionSuffix: String,
    factory: BucketFactoryDual<OrderedData, UnorderedData, Data, Output, Key, ID>,
) : TargetUnorderedMongoBucketWriteStorage<UnorderedData>,
    UnorderedMongoBucketStorageImpl<UnorderedData>(
        mongoDatabase = mongoDatabase,
        collectionName = collectionName,
        // Make it read-only to not create the indexes, we will still be able to write to it later.
        collectionReadOnly = true,
        unorderedCollectionSuffix = unorderedCollectionSuffix,
        unorderedJavaType = factory.unorderedBucketJavaType(),
        bufferJavaType = factory.bufferBucketJavaType(),
    ) {

    override fun insertMany(
        buckets: List<UnorderedBucket<UnorderedData>>,
    ) {
        mongoBucketStorageQuery.insertMany(collection, buckets)
    }
}
