package nl.teqplay.aisengine.shiphistorybucketcopier.datasource

import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.shiphistorybucketcopier.config.MongoDbConfiguration.MongoDbSourceDatabase
import nl.teqplay.aisengine.shiphistorybucketcopier.model.BucketCopyKeys
import nl.teqplay.skeleton.datasource.kmongo.ensureIndex
import nl.teqplay.skeleton.datasource.kmongo.eq
import nl.teqplay.skeleton.datasource.kmongo.`in`
import nl.teqplay.skeleton.datasource.kmongo.set
import nl.teqplay.skeleton.datasource.kmongo.setTo
import org.springframework.stereotype.Component

@Component
class BucketCopyTrackingDataSource(
    @MongoDbSourceDatabase mongoDatabase: MongoDatabase,
) {

    data class TrackingData(
        val bucketId: String,
        val by: By,
        val copied: Boolean = false,
    ) {
        enum class By { MMSI, AREA }
    }

    private val collection = mongoDatabase.getCollection<TrackingData>("_.bucket_copy_tracking").apply {
        ensureIndex(TrackingData::copied)
    }

    fun isAlreadyInitialized(): Boolean {
        // If the collection has documents, we assume initialized.
        return collection.countDocuments() > 0
    }

    fun getRemainingKeys(): BucketCopyKeys {
        val remaining = collection.find(TrackingData::copied eq false).groupBy({ it.by }, { it.bucketId })
        val byMmsi = remaining[TrackingData.By.MMSI] ?: emptyList()
        val byArea = remaining[TrackingData.By.AREA] ?: emptyList()
        return BucketCopyKeys(byMmsi, byArea)
    }

    fun save(bucketCopyKeys: BucketCopyKeys) {
        val models = bucketCopyKeys.byMmsi.toModel(TrackingData.By.MMSI) +
            bucketCopyKeys.byArea.toModel(TrackingData.By.AREA)

        if (models.isEmpty()) return
        collection.insertMany(models)
    }

    private fun List<String>.toModel(by: TrackingData.By): List<TrackingData> = map { TrackingData(it, by) }

    fun completed(ids: List<String>) {
        collection.updateMany(TrackingData::bucketId `in` ids, set(TrackingData::copied setTo true))
    }
}
