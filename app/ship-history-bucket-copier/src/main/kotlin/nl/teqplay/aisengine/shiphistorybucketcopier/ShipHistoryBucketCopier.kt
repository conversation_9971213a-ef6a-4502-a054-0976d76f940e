package nl.teqplay.aisengine.shiphistorybucketcopier

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.shiphistorybucketcopier.service.BucketCopyInitializer
import nl.teqplay.aisengine.shiphistorybucketcopier.service.BucketCopyProcessor
import org.springframework.boot.CommandLineRunner
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication

val LOG = KotlinLogging.logger { }

@SpringBootApplication(exclude = [MongoAutoConfiguration::class])
@ConfigurationPropertiesScan
class ShipHistoryBucketCopier(
    private val bucketCopyInitializer: BucketCopyInitializer,
    private val bucketCopyProcessor: BucketCopyProcessor,
) : CommandLineRunner {

    override fun run(vararg args: String?) {
        val bucketCopyKeys = bucketCopyInitializer.init()
        bucketCopyProcessor.process(bucketCopyKeys)
    }
}

fun main(args: Array<String>) {
    runApplication<ShipHistoryBucketCopier>(*args)
}
