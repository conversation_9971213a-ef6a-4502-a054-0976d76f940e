package nl.teqplay.aisengine.shiphistorybucketcopier.service

import nl.teqplay.aisengine.bucketing.model.AisHistoricOrderedDiffMessage
import nl.teqplay.aisengine.bucketing.model.AisHistoricUnorderedDiffMessage
import nl.teqplay.aisengine.bucketing.model.bucket.OrderedBucket
import nl.teqplay.aisengine.bucketing.model.bucket.UnorderedBucket
import nl.teqplay.aisengine.bucketing.storage.ship.ShipHistoryBucketFormatter
import nl.teqplay.aisengine.shiphistorybucketcopier.properties.BucketCopyProperties
import org.springframework.stereotype.Component

@Component
class BucketConverter(
    private val bucketCopyProperties: BucketCopyProperties,
) {

    fun convert(
        orderedBuckets: Iterator<OrderedBucket<AisHistoricOrderedDiffMessage>>,
    ): List<UnorderedBucket<AisHistoricUnorderedDiffMessage>> {
        val window = bucketCopyProperties.toTimeWindow()
        return orderedBuckets.asSequence().map { orderedBucket ->
            val data = ShipHistoryBucketFormatter.unzipAsSequence(orderedBucket.data.asSequence())
                .filter { window.from <= it.messageTime && it.messageTime < window.to }
                .map { it.convertToUnordered() }
                .toMutableList()

            UnorderedBucket(
                bucketId = orderedBucket.bucketId,
                archiveBucketId = orderedBucket.archiveBucketId,
                data = data
            )
        }.toList()
    }

    fun AisHistoricOrderedDiffMessage.convertToUnordered(): AisHistoricUnorderedDiffMessage =
        AisHistoricUnorderedDiffMessage(
            mmsi = mmsi,
            messageTime = messageTime,
            receptionTime = receptionTime,

            historic = historic ?: true,
            source = source ?: "",
            subSource = subSource,
            messageType = messageType,

            lat = lat ?: -1.0,
            lon = lon ?: -1.0,
            heading = heading,
            speedOverGround = speedOverGround,
            courseOverGround = courseOverGround,
            status = status,

            imo = imo,
            shipType = shipType,
            draught = draught,
            eta = eta,
            destination = destination,
            transponderPosition = transponderPosition,
        )
}
