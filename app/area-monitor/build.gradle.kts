buildscript {
    project.extra.set("baseVersion", "2.1.0")
}

plugins {
    id("ais-engine.kotlin-app-webmvc-conventions")
    id("org.cyclonedx.bom") version cyclonedxBomVersion
}

dependencies {
    implementation(project(":api:nats-stream-ais-consume-diff"))
    implementation(project(":api:nats-stream-event"))

    implementation("com.github.davidmoten:rtree:$rtreeVersion")

    implementation("nl.teqplay.poma:api:$pomaVersion")

    implementation("nl.teqplay.skeleton:models:$skeletonVersion")
    implementation("nl.teqplay.skeleton:datasource2:$skeletonVersion")
    implementation("org.springdoc:springdoc-openapi-starter-webmvc-ui:$springdocVersion")
    implementation("nl.teqplay.skeleton:util-location2:$skeletonVersion")
    implementation("nl.teqplay.skeleton:auth-credentials:$skeletonVersion")
    implementation("nl.teqplay.skeleton:auth-credentials-keycloak-s2s-common:$skeletonVersion")
    implementation("nl.teqplay.skeleton:auth-credentials-keycloak-s2s-client:$skeletonVersion")
    implementation("nl.teqplay.skeleton:auth-credentials-keycloak-s2s-mongo2:$skeletonVersion")
    testImplementation("nl.teqplay.skeleton:nats-test:$skeletonVersion")

    implementation("org.springframework.boot:spring-boot-starter-security")
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("org.springframework.security:spring-security-test")

    testImplementation("org.awaitility:awaitility:$awaitilityVersion")
    testImplementation("org.awaitility:awaitility-kotlin:$awaitilityVersion")
}

tasks.cyclonedxBom {
    setIncludeConfigs(listOf("runtimeClasspath"))
    setOutputFormat("json")
    setOutputName("bom")
}
