settings:
  enable-state-events-for-area-type:
    - BERTH
    - ANCHOR
  fetch:
    size: 10_000
    cron: '-'
  admin:
    maximum-input-size: 100_000
    prune-queue: P30D

persist:
  interval: PT5M

mongodb:
  host: localhost
  port: 27017
  authDb: admin
  username:
  password:
  db: area-monitor

nats:
  event-stream:
    enabled: true
    url: nats://localhost:4222
    username: event-publish-area-monitor
    password:

  ais-stream:
    enabled: true
    url: nats://localhost:4222
    username: ais-stream-consume-area-monitor
    password:

auth-credentials-keycloak-s2s:
  domain: keycloakdev.teqplay.nl
  realm: dev
  audience: area-monitor

poma:
  url: https://backendpoma.dev.teqplay.com
  domain: keycloakdev.teqplay.nl
  realm: dev
  client-id: area-monitor
  client-secret: