package nl.teqplay.aisengine.areamonitor.service

import nl.teqplay.aisengine.areamonitor.datasource.config.ConfigDataSource
import nl.teqplay.aisengine.areamonitor.model.Area
import nl.teqplay.aisengine.areamonitor.model.AreaResourceIdentifier
import nl.teqplay.aisengine.areamonitor.model.config.Config
import nl.teqplay.aisengine.areamonitor.service.config.AreaConfigService
import nl.teqplay.aisengine.areamonitor.service.config.PomaAreaResourceService
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.skeleton.common.BaseTest
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.Polygon
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.test.context.TestConstructor
import java.time.YearMonth
import java.time.ZoneOffset
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestConstructor(autowireMode = TestConstructor.AutowireMode.ALL)
class AreaSearchServiceTest(
    private val searchService: AreaSearchService
) : BaseTest() {

    @TestConfiguration
    class Configuration {
        private val testFetchTime = YearMonth.of(2024, 1)
            .atDay(1)
            .atStartOfDay()
            .toInstant(ZoneOffset.UTC)
        private val testConfig = Config(
            _id = "config._id",
            area = Area(
                id = "E57297BAEF6EE30BCDE04E8DFAED7E389A8AF210",
                type = AreaIdentifier.AreaType.PORT,
                polygon = Polygon(
                    Location(lat = 51.9947465395093, lon = 4.04826474340414),
                    Location(lat = 51.9866839306227, lon = 4.04231244263564),
                    Location(lat = 51.9949386108018, lon = 3.98723515896162),
                    Location(lat = 51.95980032137, lon = 3.95805466885168),
                    Location(lat = 51.9191834072515, lon = 3.98794645269768),
                    Location(lat = 51.8200309258343, lon = 4.46901215535474),
                    Location(lat = 51.644428155759, lon = 4.5508418808848),
                    Location(lat = 51.6640010303137, lon = 4.62900311805812),
                    Location(lat = 51.8379683191972, lon = 4.69874612574227),
                    Location(lat = 51.9170309140714, lon = 4.53819944844208),
                    Location(lat = 51.9947465395093, lon = 4.04826474340414)
                ),
                outerPolygon = Polygon(
                    Location(lat = 52.0713592577847, lon = 4.05319787160119),
                    Location(lat = 51.9879673525711, lon = 4.10850140925267),
                    Location(lat = 51.9232766895276, lon = 4.56314815050509),
                    Location(lat = 51.8452094821278, lon = 4.71783351591977),
                    Location(lat = 51.6662326576568, lon = 4.71248337595095),
                    Location(lat = 51.6219215411548, lon = 4.52599454245279),
                    Location(lat = 51.8071476975151, lon = 4.42697272416327),
                    Location(lat = 51.9134655543609, lon = 3.97962538685049),
                    Location(lat = 51.9004935546928, lon = 3.68730286533299),
                    Location(lat = 52.0608395950234, lon = 3.69719948636429),
                    Location(lat = 52.0922919270824, lon = 3.86570751850351),
                    Location(lat = 52.0713592577847, lon = 4.05319787160119)
                ),
                name = "PORT OF ROTTERDAM",
                unlocode = "NLRTM",
                berth = null
            ),
            resource = AreaResourceIdentifier.Resource.POMA,
            fetched = testFetchTime,
            since = testFetchTime
        )

        @Bean
        fun testConfitDataSource(): ConfigDataSource {
            return mock<ConfigDataSource>().apply {
                whenever(this.getAll()).thenReturn(
                    listOf(testConfig)
                )
            }
        }

        @Bean
        fun testPomaAreaResourceService(): PomaAreaResourceService {
            return mock<PomaAreaResourceService>().apply {
                whenever(this.getAll()).thenReturn(listOf(testConfig.area))
            }
        }

        @Bean
        fun testConfigService(
            @Qualifier("testConfitDataSource") dataSource: ConfigDataSource,
            pomaAreaResourceService: PomaAreaResourceService,
        ) = AreaConfigService(dataSource)

        @Bean
        fun testSearchService(@Qualifier("testConfigService") configService: AreaConfigService) = AreaSearchService(configService)
    }

    data class RouteLocation(
        val message: String,
        val location: Location,
        val expected: Expected = Expected()
    )

    data class Expected(
        val isInsideInnerArea: Boolean = false,
        val isInsideOuterArea: Boolean = false,
        val enteredInnerArea: Boolean = false,
        val leftOuterArea: Boolean = false
    )

    private fun routeLocations(): List<RouteLocation> = listOf(
        RouteLocation(
            message = "outside outer area",
            location = Location(52.03137322244006, 3.5472106933593746)
        ),
        RouteLocation(
            message = "outside outer area, before moving into outer area",
            location = Location(52.032218104145294, 3.6481475830078125)
        ),
        RouteLocation(
            message = "inside outer area, just moved in",
            location = Location(52.03179566528824, 3.72711181640625),
            expected = Expected(
                isInsideOuterArea = true
            )
        ),
        RouteLocation(
            message = "inside outer area, before moving into inner area",
            location = Location(51.99883313659224, 4.033355712890625),
            expected = Expected(
                isInsideOuterArea = true
            )
        ),
        RouteLocation(
            message = "inside inner area, just moved in",
            location = Location(51.988685933842774, 4.0587615966796875),
            expected = Expected(
                isInsideOuterArea = true,
                isInsideInnerArea = true,
                enteredInnerArea = true
            )
        ),
        RouteLocation(
            message = "inside inner area, before leaving",
            location = Location(51.986571643810834, 4.0793609619140625),
            expected = Expected(
                isInsideOuterArea = true,
                isInsideInnerArea = true
            )
        ),
        RouteLocation(
            message = "inside outer area, just left inner area",
            location = Location(51.998410382390325, 4.067344665527344),
            expected = Expected(
                isInsideOuterArea = true
            )
        ),
        RouteLocation(
            message = "inside outer area, before leaving",
            location = Location(52.01996575683673, 4.074897766113281),
            expected = Expected(
                isInsideOuterArea = true
            )
        ),
        RouteLocation(
            message = "outside outer area, just left",
            location = Location(52.03813182908344, 4.088630676269531),
            expected = Expected(
                leftOuterArea = true
            )
        ),
        RouteLocation(
            message = "outside out area",
            location = Location(52.06051241654061, 4.112663269042969)
        )
    )

    data class Route(
        val from: RouteLocation,
        val to: RouteLocation
    )

    private fun route(): Stream<Route> {
        val locations = routeLocations()
        val route = locations.zipWithNext { from, to ->
            if (to != null) {
                Route(from, to)
            } else {
                null
            }
        }.filterNotNull()
        return route.stream()
    }

    @ParameterizedTest
    @MethodSource("route")
    fun getMovedIntoAreas(route: Route) {
        val innerAreas = searchService.getMovedIntoAreas(route.from.location, route.to.location, false)
        val outerAreas = searchService.getMovedIntoAreas(route.to.location, route.from.location, true)

        assertEquals(
            route.to.expected.enteredInnerArea,
            innerAreas.isNotEmpty(),
            "moved into inside area check failed: ${route.to.message}"
        )
        assertEquals(
            route.to.expected.leftOuterArea,
            outerAreas.isNotEmpty(),
            "moved into outer area check failed: ${route.to.message}"
        )
    }

    @ParameterizedTest
    @MethodSource("routeLocations")
    fun getInsideAreas(routeLocation: RouteLocation) {
        val innerAreas = searchService.getInsideAreas(routeLocation.location, false)
        val outerAreas = searchService.getInsideAreas(routeLocation.location, true)

        assertEquals(
            routeLocation.expected.isInsideInnerArea,
            innerAreas.isNotEmpty(),
            "inside area check failed: ${routeLocation.message}"
        )
        assertEquals(
            routeLocation.expected.isInsideOuterArea,
            outerAreas.isNotEmpty(),
            "outer area check failed: ${routeLocation.message}"
        )
    }
}
