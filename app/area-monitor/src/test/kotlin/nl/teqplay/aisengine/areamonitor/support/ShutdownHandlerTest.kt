package nl.teqplay.aisengine.areamonitor.support

import nl.teqplay.aisengine.areamonitor.service.ConsumerService
import nl.teqplay.aisengine.areamonitor.service.StateService
import org.junit.jupiter.api.Test
import org.mockito.kotlin.doNothing
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

class ShutdownHandlerTest {

    private val consumerService = mock<ConsumerService>()
    private val stateService = mock<StateService>()
    private val shutdownHandler = ShutdownHandler(consumerService, stateService)

    @Test
    fun shutdown() {
        whenever(consumerService.isFrozen())
            .thenReturn(true)
            .thenReturn(true)
            .thenReturn(false)
        doNothing().whenever(consumerService).shutdown()
        doNothing().whenever(stateService).shutdown()

        shutdownHandler.shutdown(mock())

        verify(consumerService).shutdown()
        verify(stateService).shutdown()
    }

    @Test
    fun `shutdown - error flow`() {
        whenever(consumerService.isFrozen())
            .thenReturn(true)
            .thenThrow(Error())
        whenever(consumerService.shutdown()).thenThrow(Error())
        whenever(stateService.shutdown()).thenThrow(Error())

        shutdownHandler.shutdown(mock())

        verify(consumerService).shutdown()
        verify(stateService).shutdown()
    }
}
