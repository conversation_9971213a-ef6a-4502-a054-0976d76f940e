package nl.teqplay.aisengine.areamonitor

import com.mongodb.kotlin.client.FindIterable
import com.mongodb.kotlin.client.MongoCollection
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.skeleton.common.BaseTest
import nl.teqplay.skeleton.nats.NatsClientBuilderMock
import org.bson.conversions.Bson
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Import
import org.springframework.context.annotation.Primary

@SpringBootTest
class AreaMonitorTest : BaseTest() {
    @Configuration
    @Import(AreaMonitor::class)
    class Config {
        @Bean
        @Primary
        fun mongoDatabase() = getMockMongoDB()

        @Primary
        @Bean
        fun natsClientBuilderMock() = NatsClientBuilderMock()

        private fun <T : Any> getMockIterable() = mock<FindIterable<T>>()

        fun getMockMongoDB(): MongoDatabase {
            return mock {
                val mockCollection = mock<MongoCollection<Any>> {
                    val iterable = getMockIterable<Any>()
                    on { createIndex(any<Bson>(), any()) } doReturn ""
                    on { find() } doReturn iterable
                    on { find(any<Bson>()) } doReturn iterable
                }
                on { getCollection(any(), any<Class<Any>>()) } doReturn mockCollection
                val iterable = getMockIterable<String>()
                on { listCollectionNames() } doReturn iterable
            }
        }
    }

    @Test
    fun contextLoads() {
    }
}
