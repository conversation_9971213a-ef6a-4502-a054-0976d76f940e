package nl.teqplay.aisengine.areamonitor.service

import nl.teqplay.aisengine.aisstream.model.AisDiffField
import nl.teqplay.aisengine.areamonitor.config
import nl.teqplay.aisengine.areamonitor.datasource.state.StateDataSource
import nl.teqplay.aisengine.areamonitor.diff
import nl.teqplay.aisengine.areamonitor.mmsi
import nl.teqplay.aisengine.areamonitor.model.State
import nl.teqplay.aisengine.areamonitor.service.config.AreaConfigService
import nl.teqplay.aisengine.areamonitor.startEventId
import nl.teqplay.skeleton.model.Location
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.doNothing
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

internal class StateServiceTest {

    private val stateDataSource = mock<StateDataSource>()
    private val configService = mock<AreaConfigService>()
    private val stateService = StateService(stateDataSource, configService)

    @Test
    fun persistOnSchedule() {
        doNothing().whenever(stateDataSource).save(any())
        stateService.persistOnSchedule()
        stateService.shutdown()
        stateService.persist()
        verify(stateDataSource, times(3)).save(any())
    }

    @Test
    fun saveGetAndDeleteState() {
        stateService.saveStartEvent(config, diff, startEventId)

        assertEquals(startEventId, stateService.getStartEventId(config, mmsi))
        assertEquals(
            listOf(State(config._id, mapOf(mmsi to State.StartEvent(diff, startEventId, null)), emptySet())),
            stateService.getStateList()
        )

        val updatedDiff = diff.copy(
            messageTime = diff.messageTime.plusSeconds(10),
            oldMessageTime = diff.messageTime,
            location = AisDiffField(Location(52.0, 4.0), null),
        )

        // update stored diff data
        stateService.update(updatedDiff)

        // simulate a state event being triggered in the meantime
        stateService.saveLastStateEventSentTime(config, updatedDiff, updatedDiff.messageTime)

        assertEquals(startEventId, stateService.getStartEventId(config, mmsi))
        assertEquals(
            listOf(State(config._id, mapOf(mmsi to State.StartEvent(updatedDiff, startEventId, updatedDiff.messageTime)), emptySet())),
            stateService.getStateList()
        )

        stateService.deleteStartEvent(config, diff)

        assertNull(stateService.getStartEventId(config, mmsi))
        // Simulate the persist state by getting both ongoing and cleared state
        assertEquals(listOf(State(config._id, emptyMap(), setOf(mmsi))), stateService.getOngoingAndClearedStateList())

        stateService.deleteOutsideEvent(config, diff)

        assertNull(stateService.getStartEventId(config, mmsi))
        // Simulate the persist state by getting both ongoing and cleared state
        assertEquals(listOf(State(config._id, emptyMap(), emptySet())), stateService.getOngoingAndClearedStateList())
    }

    @Test
    fun loadLocallyAndDelete() {
        whenever(stateDataSource.list(any()))
            .thenReturn(listOf(State(config._id, mapOf(mmsi to State.StartEvent(diff, startEventId)), emptySet())))
        doNothing().whenever(stateDataSource).delete(any())

        // load
        stateService.loadLocally(setOf(config._id))

        // verify loaded
        assertEquals(startEventId, stateService.getStartEventId(config, mmsi))
        assertEquals(
            listOf(State(config._id, mapOf(mmsi to State.StartEvent(diff, startEventId)), emptySet())),
            stateService.getStateList()
        )

        // delete
        stateService.delete(setOf(config._id))

        // verify deleted from memory
        assertNull(stateService.getStartEventId(config, mmsi))
        assertEquals(emptyList<State>(), stateService.getStateList())

        // verify deleted from database
        verify(stateDataSource).delete(any())
    }
}
