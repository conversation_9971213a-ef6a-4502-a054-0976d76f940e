package nl.teqplay.aisengine.areamonitor.service

import nl.teqplay.aisengine.aisstream.model.AisDiffField
import nl.teqplay.aisengine.areamonitor.config
import nl.teqplay.aisengine.areamonitor.diff
import nl.teqplay.aisengine.event.model.AreaEndEvent
import nl.teqplay.aisengine.event.model.AreaStartEvent
import nl.teqplay.aisengine.event.model.StateAreaInsideEvent
import nl.teqplay.aisengine.event.model.StateAreaOutsideEvent
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.skeleton.model.Location
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.Instant

internal class EventCreationServiceTest {

    private val eventCreationService = EventCreationService()

    @Test
    fun createStartEvent() {
        val event = eventCreationService.createStartEvent(config, diff)
            .copy(_id = "", createdTime = Instant.EPOCH)
        assertEquals(
            AreaStartEvent(
                _id = "",
                ship = AisShipIdentifier(1, 2),
                area = AreaIdentifier(
                    id = "NLRTM",
                    type = AreaIdentifier.AreaType.PORT,
                    name = "PORT OF ROTTERDAM",
                    unlocode = "NLRTM"
                ),
                berth = null,
                heading = null,
                draught = 12.3f,
                speedOverGround = 4.5f,
                location = Location(0.0, 0.0),
                actualTime = Instant.EPOCH,
                createdTime = Instant.EPOCH
            ),
            event
        )
    }

    @Test
    fun createEndEvent() {
        val startEventId = "startEventId"
        val event = eventCreationService.createEndEvent(config, diff, startEventId)
            .copy(_id = "", createdTime = Instant.EPOCH)
        assertEquals(
            AreaEndEvent(
                _id = "",
                startEventId = startEventId,
                ship = AisShipIdentifier(1, 2),
                area = AreaIdentifier(
                    id = "NLRTM",
                    type = AreaIdentifier.AreaType.PORT,
                    name = "PORT OF ROTTERDAM",
                    unlocode = "NLRTM"
                ),
                berth = null,
                heading = null,
                draught = 12.3f,
                speedOverGround = 4.5f,
                location = Location(0.0, 0.0),
                actualTime = Instant.EPOCH,
                createdTime = Instant.EPOCH
            ),
            event
        )
    }

    @Test
    fun createStateAreaInsideEvent() {
        val diffWithChanges = diff.copy(
            location = AisDiffField(Location(0.0, 0.0), AisDiffField.Change(Location(1.0, 1.0))),
            heading = AisDiffField(1, null),
            speedOverGround = AisDiffField(0.0f, AisDiffField.Change(1.1f)),
            draught = AisDiffField(2f, null)
        )
        val event = eventCreationService.createStateAreaInsideEvent(config, diffWithChanges)
            .copy(_id = "", createdTime = Instant.EPOCH)
        assertEquals(
            StateAreaInsideEvent(
                _id = "",
                ship = AisShipIdentifier(1, 2),
                area = AreaIdentifier(
                    id = "NLRTM",
                    type = AreaIdentifier.AreaType.PORT,
                    name = "PORT OF ROTTERDAM",
                    unlocode = "NLRTM"
                ),
                berth = null,
                heading = 1,
                draught = 2f,
                location = Location(1.0, 1.0),
                speedOverGround = 1.1f,
                actualTime = Instant.EPOCH,
                createdTime = Instant.EPOCH
            ),
            event
        )
    }

    @Test
    fun createStateAreaOutsideEvent() {
        val diffWithChanges = diff.copy(
            location = AisDiffField(Location(0.0, 0.0), AisDiffField.Change(Location(1.0, 1.0))),
            heading = AisDiffField(1, null),
            speedOverGround = AisDiffField(0.0f, AisDiffField.Change(1.1f)),
            draught = AisDiffField(2f, null)
        )
        val event = eventCreationService.createStateAreaOutsideEvent(config, diffWithChanges)
            .copy(_id = "", createdTime = Instant.EPOCH)
        assertEquals(
            StateAreaOutsideEvent(
                _id = "",
                ship = AisShipIdentifier(1, 2),
                area = AreaIdentifier(
                    id = "NLRTM",
                    type = AreaIdentifier.AreaType.PORT,
                    name = "PORT OF ROTTERDAM",
                    unlocode = "NLRTM"
                ),
                berth = null,
                heading = 1,
                draught = 2f,
                location = Location(1.0, 1.0),
                speedOverGround = 1.1f,
                actualTime = Instant.EPOCH,
                createdTime = Instant.EPOCH
            ),
            event
        )
    }
}
