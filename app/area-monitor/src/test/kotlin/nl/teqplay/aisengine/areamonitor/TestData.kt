package nl.teqplay.aisengine.areamonitor

import nl.teqplay.aisengine.aisstream.model.AisDiffField
import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.areamonitor.model.Area
import nl.teqplay.aisengine.areamonitor.model.AreaResourceIdentifier
import nl.teqplay.aisengine.areamonitor.model.AreaResourceIdentifier.Resource.POMA
import nl.teqplay.aisengine.areamonitor.model.config.Config
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier.AreaType.PORT
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.Polygon
import java.time.Instant

const val mmsi = 1
const val startEventId = "startEventId"

val identifier = AreaResourceIdentifier(POMA, PORT, "NLRTM")

val config = Config(
    area = Area(
        id = "NLRTM",
        type = PORT,
        // it's an invalid polygon, but that doesn't matter for event creation
        polygon = Polygon(
            Location(53.0, 53.0),
            Location(51.0, 53.0),
            Location(51.0, 51.0),
            Location(53.0, 51.0),
            Location(53.0, 53.0),
        ),
        outerPolygon = Polygon(
            Location(54.0, 54.0),
            Location(50.0, 54.0),
            Location(50.0, 50.0),
            Location(54.0, 50.0),
            Location(54.0, 54.0),
        ),
        name = "PORT OF ROTTERDAM",
        unlocode = "NLRTM",
        berth = null
    ),
    resource = POMA,
    fetched = Instant.EPOCH,
    since = Instant.EPOCH
)

val diff = AisDiffMessage(
    mmsi = 1,
    messageTime = Instant.EPOCH,
    oldMessageTime = null,
    sources = emptySet(),
    imo = AisDiffField(2, null),
    eni = emptyDiffField(),
    name = emptyDiffField(),
    callSign = emptyDiffField(),
    location = AisDiffField(Location(0.0, 0.0), null),
    destination = emptyDiffField(),
    eta = emptyDiffField(),
    speedOverGround = AisDiffField(4.5f, null),
    courseOverGround = emptyDiffField(),
    rateOfTurn = emptyDiffField(),
    heading = emptyDiffField(),
    draught = AisDiffField(12.3f, null),
    positionAccuracy = emptyDiffField(),
    transponderPosition = emptyDiffField(),
    positionSensorType = emptyDiffField(),
    aisVersion = emptyDiffField(),
    shipType = emptyDiffField(),
    status = emptyDiffField(),
    specialManeuverStatus = emptyDiffField(),
    usingDataTerminal = emptyDiffField()
)

private inline fun <reified T, reified U : T> emptyDiffField() = AisDiffField<T?, U>(null, null)
