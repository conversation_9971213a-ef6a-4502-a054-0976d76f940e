package nl.teqplay.aisengine.areamonitor.service

import nl.teqplay.aisengine.aisstream.model.AisDiffField
import nl.teqplay.aisengine.areamonitor.config
import nl.teqplay.aisengine.areamonitor.diff
import nl.teqplay.aisengine.areamonitor.model.State
import nl.teqplay.aisengine.areamonitor.model.config.Config
import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.model.AreaEndEvent
import nl.teqplay.aisengine.event.model.AreaStartEvent
import nl.teqplay.aisengine.event.model.StateAreaInsideEvent
import nl.teqplay.aisengine.event.model.StateAreaOutsideEvent
import nl.teqplay.skeleton.model.Location
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
internal class EventGenerationServiceTest {

    private val searchService = mock<AreaSearchService>()
    private val stateService = mock<StateService>()

    private val eventCreationService = EventCreationService()
    private val eventGenerationService = EventGenerationService(searchService, stateService, eventCreationService)

    private val diffWithLocationUpdate = diff.copy(
        location = AisDiffField(Location(0.0, 0.0), AisDiffField.Change(Location(1.0, 1.0)))
    )

    private val startEventId = "startEventId"

    @ParameterizedTest
    @MethodSource("processTestData")
    fun process(data: ProcessTestData) {
        whenever(stateService.getConfigsAndStatusForShip(any())).thenReturn(emptyList())
        whenever(searchService.getMovedIntoAreas(any(), any(), any()))
            .thenReturn(data.outerAreas)
            .thenReturn(data.innerAreas)

        if (data.startEventIds.isEmpty()) {
            whenever(stateService.getStartEventId(any(), any())).thenReturn(null)
        } else {
            data.startEventIds.fold(
                whenever(stateService.getStartEventId(any(), any()))
            ) { acc, curr -> acc.thenReturn(curr) }
        }

        val events = eventGenerationService.process(diffWithLocationUpdate)
            .map { (_, event) -> event }

        assertEquals(data.output.fix(), events.fix(), data.message)
    }

    @Test
    fun `create state inside event if no state inside event has been sent prior`() {
        whenever(stateService.getConfigsAndStatusForShip(any())).thenReturn(
            listOf(StateService.ConfigShipStatus(config, State.StartEvent(diffWithLocationUpdate, startEventId), false))
        )
        whenever(searchService.getMovedIntoAreas(any(), any(), any()))
            .thenReturn(mutableSetOf())

        val events = eventGenerationService.process(diffWithLocationUpdate)
            .map { (_, event) -> event }

        assertEquals(
            listOf(eventCreationService.createStateAreaInsideEvent(config, diffWithLocationUpdate)).fix(),
            events.fix()
        )
    }

    @Test
    fun `don't create state inside event if area end event is fired`() {
        whenever(stateService.getConfigsAndStatusForShip(any())).thenReturn(
            listOf(StateService.ConfigShipStatus(config, State.StartEvent(diffWithLocationUpdate, startEventId), false))
        )
        whenever(searchService.getMovedIntoAreas(any(), any(), any()))
            .thenReturn(mutableSetOf(config))
            .thenReturn(mutableSetOf())
        whenever(stateService.getStartEventId(any(), any())).thenReturn(startEventId)

        val events = eventGenerationService.process(diffWithLocationUpdate)
            .map { (_, event) -> event }

        assertEquals(
            listOf(eventCreationService.createEndEvent(config, diffWithLocationUpdate, startEventId)).fix(),
            events.fix()
        )
    }

    @Test
    fun `create state inside event if exceeds state inside event interval`() {
        whenever(stateService.getConfigsAndStatusForShip(any()))
            .thenReturn(
                listOf(
                    // should generate a state event, it has been 2 minutes
                    StateService.ConfigShipStatus(
                        config,
                        State.StartEvent(
                            diff = diffWithLocationUpdate,
                            startEventId = startEventId,
                            lastStateEventSentTime = Instant.EPOCH
                                .minus(2, ChronoUnit.MINUTES)
                        ),
                        false
                    ),
                    // should not generate a state event, has not been 2 minutes yet
                    StateService.ConfigShipStatus(
                        config,
                        State.StartEvent(
                            diff = diffWithLocationUpdate,
                            startEventId = startEventId,
                            lastStateEventSentTime = Instant.EPOCH
                                .minus(2, ChronoUnit.MINUTES)
                                .plusNanos(1)
                        ),
                        false
                    ),
                )
            )
        whenever(searchService.getMovedIntoAreas(any(), any(), any()))
            .thenReturn(mutableSetOf())

        val events = eventGenerationService.process(diffWithLocationUpdate)
            .map { (_, event) -> event }

        assertEquals(
            listOf(eventCreationService.createStateAreaInsideEvent(config, diffWithLocationUpdate)).fix(),
            events.fix()
        )
    }

    @Test
    fun `don't create state outside event if area start event is fired`() {
        whenever(stateService.getConfigsAndStatusForShip(any())).thenReturn(
            listOf(StateService.ConfigShipStatus(config, null, true))
        )
        whenever(searchService.getMovedIntoAreas(any(), any(), any()))
            .thenReturn(mutableSetOf())
            .thenReturn(mutableSetOf(config))
        whenever(stateService.getStartEventId(any(), any())).thenReturn(null)

        val events = eventGenerationService.process(diffWithLocationUpdate)
            .map { (_, event) -> event }

        assertEquals(
            listOf(eventCreationService.createStartEvent(config, diffWithLocationUpdate)).fix(),
            events.fix()
        )
    }

    @Test
    fun `create state outside event after leaving area`() {
        whenever(stateService.getConfigsAndStatusForShip(any())).thenReturn(
            listOf(StateService.ConfigShipStatus(config, null, true))
        )
        whenever(searchService.getMovedIntoAreas(any(), any(), any()))
            .thenReturn(mutableSetOf())
        whenever(stateService.getStartEventId(any(), any())).thenReturn(null)

        val events = eventGenerationService.process(diffWithLocationUpdate)
            .map { (_, event) -> event }

        assertEquals(
            listOf(eventCreationService.createStateAreaOutsideEvent(config, diffWithLocationUpdate)).fix(),
            events.fix()
        )
    }

    private fun List<Event>.fix() = map { event ->
        when (event) {
            is AreaStartEvent -> event.copy(_id = "", createdTime = Instant.EPOCH)
            is AreaEndEvent -> event.copy(_id = "", createdTime = Instant.EPOCH)
            is StateAreaInsideEvent -> event.copy(_id = "", createdTime = Instant.EPOCH)
            is StateAreaOutsideEvent -> event.copy(_id = "", createdTime = Instant.EPOCH)
            else -> event
        }
    }

    data class ProcessTestData(
        val message: String,
        val outerAreas: MutableSet<Config>,
        val innerAreas: MutableSet<Config>,
        val startEventIds: List<String?>,
        val output: List<AreaEvent>,
    )

    private fun processTestData() = Stream.of(
        ProcessTestData(
            message = "trigger no events",
            outerAreas = mutableSetOf(),
            innerAreas = mutableSetOf(),
            startEventIds = emptyList(),
            output = emptyList()
        ),

        ProcessTestData(
            message = "trigger no event, ship left outer area",
            outerAreas = mutableSetOf(config),
            innerAreas = mutableSetOf(),
            startEventIds = listOf(null),
            output = emptyList()
        ),

        ProcessTestData(
            message = "trigger start event",
            outerAreas = mutableSetOf(),
            innerAreas = mutableSetOf(config),
            startEventIds = listOf(null),
            output = listOf(eventCreationService.createStartEvent(config, diffWithLocationUpdate))
        ),

        ProcessTestData(
            message = "trigger no events, ship moved into inner area again",
            outerAreas = mutableSetOf(),
            innerAreas = mutableSetOf(config),
            startEventIds = listOf(startEventId),
            output = emptyList()
        ),

        ProcessTestData(
            message = "trigger end event",
            outerAreas = mutableSetOf(config),
            innerAreas = mutableSetOf(),
            startEventIds = listOf(startEventId),
            output = listOf(eventCreationService.createEndEvent(config, diffWithLocationUpdate, startEventId))
        ),
    )
}
