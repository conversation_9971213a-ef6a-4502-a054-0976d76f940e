package nl.teqplay.aisengine.areamonitor.service

import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.areamonitor.model.config.Config
import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.aisengine.event.model.AreaEndEvent
import nl.teqplay.aisengine.event.model.AreaStartEvent
import nl.teqplay.skeleton.model.Location
import org.springframework.stereotype.Component
import java.time.Duration

/**
 * Service to generate [AreaEvent] for multiple areas.
 */
@Component
class EventGenerationService(
    private val searchService: AreaSearchService,
    private val stateService: StateService,
    private val eventCreationService: EventCreationService
) {

    companion object {
        /**
         * When a ship is inside an area, state events should be sent.
         * These state events should have a minimal interval of 2 minutes between ship updates.
         */
        private val STATE_EVENT_INTERVAL = Duration.ofMinutes(2)
    }

    /**
     * Process a [diff] for a specific movement between two locations
     *
     * Returns 0-N results, with the related area [Config] and start/end/state [AreaEvent]
     */
    fun process(
        diff: AisDiffMessage
    ): List<Pair<Config, AreaEvent>> {
        val stateEvents = createStateEvents(diff)

        val change = diff.location.changed ?: return stateEvents
        val from = diff.location.old
        val to = change.new

        // when moving backwards, get outer areas we enter (i.e. which outer areas did we leave)
        val outerAreas = searchService.getMovedIntoAreas(to, from, includeOuter = true)

        // when moving forwards, get inner areas we enter
        val innerAreas = searchService.getMovedIntoAreas(from, to, includeOuter = false)

        val areaEvents = createAreaEvents(outerAreas, innerAreas, diff)
        return areaEvents + stateEvents.filter { (stateConfig, _) ->
            // don't return state events where area start/end events for the same area exist
            areaEvents.none { (config, event) ->
                config._id == stateConfig._id && (event is AreaStartEvent || event is AreaEndEvent)
            }
        }
    }

    private fun createStateEvents(
        diff: AisDiffMessage
    ): List<Pair<Config, AreaEvent>> {
        val statuses = stateService.getConfigsAndStatusForShip(diff)
        return statuses.mapNotNull { status ->
            when {
                status.startEvent != null -> {
                    val lastStateEventSentTime = status.startEvent.lastStateEventSentTime
                    if (
                        lastStateEventSentTime == null ||
                        Duration.between(lastStateEventSentTime, diff.messageTime) >= STATE_EVENT_INTERVAL
                    ) {
                        status.config to eventCreationService.createStateAreaInsideEvent(status.config, diff)
                    } else null
                }

                status.outside -> {
                    status.config to eventCreationService.createStateAreaOutsideEvent(status.config, diff)
                }

                else -> null
            }
        }
    }

    /**
     * Process a [diff] for a specific location [location]
     *
     * Returns 0-N results, with the related area [Config] and start/end [AreaEvent]
     */
    fun processCurrentLocation(
        diff: AisDiffMessage,
        location: Location
    ): List<Pair<Config, AreaEvent>> {
        val innerAreas = searchService.getInsideAreas(location, includeOuter = false)
        return createAreaEvents(mutableSetOf(), innerAreas, diff)
    }

    private fun createAreaEvents(
        outerAreas: MutableSet<Config>,
        innerAreas: MutableSet<Config>,
        diff: AisDiffMessage
    ): List<Pair<Config, AreaEvent>> {
        val startEventIdMap = (outerAreas + innerAreas)
            .associateWith { config -> stateService.getStartEventId(config, diff.mmsi) }

        outerAreas.removeIf { startEventIdMap[it] == null } // ship left area, but has never entered
        innerAreas.removeIf { startEventIdMap[it] != null } // ship entered inner area again, but hasn't left

        if (outerAreas.isEmpty() && innerAreas.isEmpty()) {
            return emptyList()
        }

        val events = mutableListOf<Pair<Config, AreaEvent>>()

        outerAreas.forEach { config ->
            val event = eventCreationService.createEndEvent(config, diff, startEventIdMap[config])
            events.add(config to event)
        }

        innerAreas.forEach { config ->
            val event = eventCreationService.createStartEvent(config, diff)
            events.add(config to event)
        }

        return events
    }
}
