package nl.teqplay.aisengine.areamonitor.service

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.areamonitor.model.config.Config
import nl.teqplay.aisengine.areamonitor.properties.AreaMonitorSettingsProperties
import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.aisengine.event.interfaces.StateEvent
import nl.teqplay.aisengine.event.model.AreaEndEvent
import nl.teqplay.aisengine.event.model.AreaStartEvent
import nl.teqplay.aisengine.event.model.StateAreaInsideEvent
import nl.teqplay.aisengine.event.model.StateAreaOutsideEvent
import nl.teqplay.aisengine.nats.stream.EventStreamService
import org.springframework.stereotype.Component

/**
 * The service that publishes 0-N [AreaStartEvent] or [AreaEndEvent].
 */
@Component
class ConsumerPublishService(
    private val stateService: StateService,
    private val eventStreamService: EventStreamService,
    private val properties: AreaMonitorSettingsProperties,
) {

    private val log = KotlinLogging.logger { }

    /**
     * Publish the [events] for the [diff] and ensures the state is updated.
     * State is only updated if event publishing succeeds to maintain consistency.
     */
    fun publish(
        diff: AisDiffMessage,
        events: List<Pair<Config, AreaEvent>>
    ) {
        val failedEvents = mutableListOf<Pair<Config, AreaEvent>>()

        for ((config, event) in events) {
            try {
                // Only publish if it's not a StateEvent or if StateEvents are enabled for this area type
                val shouldPublish = event !is StateEvent || event.area.type in properties.enableStateEventsForAreaType

                if (shouldPublish) {
                    eventStreamService.publish(event)
                    log.debug { "Successfully published ${event::class.simpleName} for MMSI ${event.ship.mmsi} in area ${event.area.id}" }
                }

                // Only save state if publishing succeeded (or was skipped for StateEvents)
                save(diff, config, event)

            } catch (e: Exception) {
                log.error(e) { "Failed to publish ${event::class.simpleName} for MMSI ${event.ship.mmsi} in area ${event.area.id}. State will not be updated." }
                failedEvents.add(config to event)

                // For critical events like AreaEndEvent, we should be more aggressive about logging
                if (event is AreaEndEvent) {
                    log.error { "CRITICAL: AreaEndEvent failed to publish - vessel ${event.ship.mmsi} exit from area ${event.area.id} not recorded!" }
                }
            }
        }

        // Log summary of failed events for monitoring
        if (failedEvents.isNotEmpty()) {
            log.warn { "Failed to publish ${failedEvents.size} events for MMSI ${diff.mmsi}. Events: ${failedEvents.map { it.second::class.simpleName }}" }
        }
    }

    /**
     * Save the [event] for an area.
     */
    fun save(
        diff: AisDiffMessage,
        config: Config,
        event: AreaEvent
    ) {
        when (event) {
            is AreaStartEvent -> stateService.saveStartEvent(config, diff, event._id)
            is AreaEndEvent -> stateService.deleteStartEvent(config, diff)
            is StateAreaInsideEvent -> stateService.saveLastStateEventSentTime(config, diff, event.actualTime)
            is StateAreaOutsideEvent -> stateService.deleteOutsideEvent(config, diff)
        }
    }
}
