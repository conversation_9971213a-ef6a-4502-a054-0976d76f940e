package nl.teqplay.aisengine.areamonitor.service

import io.github.oshai.kotlinlogging.KotlinLogging
import io.micrometer.core.instrument.MeterRegistry
import jakarta.annotation.PostConstruct
import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.areamonitor.service.intercept.ConsumerInterceptService
import nl.teqplay.aisengine.event.model.AreaEndEvent
import nl.teqplay.aisengine.event.model.AreaStartEvent
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.aisengine.nats.stream.EventStreamService.MessageContext
import nl.teqplay.aisengine.nats.stream.revents.model.ReventsAisStreamOptions
import nl.teqplay.skeleton.metrics.Metric
import nl.teqplay.skeleton.metrics.MetricRegistry
import nl.teqplay.skeleton.nats.NatsConsumerStream
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.time.Duration
import java.time.Instant
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicLong
import java.util.concurrent.atomic.AtomicReference

/**
 * The service that actually consumes [AisDiffMessage] and publishes 0-N [AreaStartEvent] or [AreaEndEvent] for all
 * monitored areas.
 */
@Component
class ConsumerService(
    meterRegistry: MeterRegistry,
    private val stateService: StateService,
    private val stream: NatsConsumerStream<AisDiffMessage>,
    private val eventStreamService: EventStreamService,
    private val eventGenerationService: EventGenerationService,
    private val consumerPublishService: ConsumerPublishService,
    private val consumerInterceptService: ConsumerInterceptService
) {

    private val log = KotlinLogging.logger { }

    private val registry = MetricRegistry.of<ConsumerService>(meterRegistry)

    private val counterTotalMsgIn = registry.createGauge(Metric.MESSAGE_COUNT_INPUT, AtomicLong())
    private val counterTotalMsgOut = registry.createGauge(Metric.MESSAGE_COUNT_OUTPUT, AtomicLong())
    private val counterTotalMsgDropped = registry.createGauge(Metric.MESSAGE_COUNT_DROPPED, AtomicLong())
    private val counterMsgIn = AtomicLong()
    private val counterMsgDropped = AtomicLong()
    private val counterMsgOut = AtomicLong()

    private val running = AtomicBoolean()
    private val frozen = AtomicBoolean()
    private val freezeStreamSequence = AtomicReference<Long?>(null)
    private val freezeFirstHit = AtomicBoolean()
    private val freezeOnCatchupComplete = AtomicReference<(() -> Unit)?>(null)
    private val lastStreamSequence = AtomicLong(0)
    private val lastStreamTimestamp = AtomicReference(Instant.MIN)

    private val allowFreeze = Duration.ofSeconds(5)
    private val redeliveryDelay = Duration.ofSeconds(2)

    @Scheduled(initialDelay = 15000, fixedDelay = 15000)
    fun stats() {
        val msgIn = counterMsgIn.getAndSet(0)
        val msgDropped = counterMsgDropped.getAndSet(0)
        val dropPercentage = msgDropped / msgIn.toFloat() * 100
        val msgOut = counterMsgOut.getAndSet(0)
        val ingestRate = (msgIn - msgDropped) / 15f

        val messageAge = getNewestMessageAge()

        log.info { "$msgIn messages received, dropped $msgDropped ($dropPercentage%), ${ingestRate}m/s, age: $messageAge, sent $msgOut messages" }
    }

    private fun getNewestMessageAge(): Duration = Duration.between(lastStreamTimestamp.get(), Instant.now()).abs()

    /**
     * Returns whether the consumer is allowed to freeze.
     */
    fun canFreeze(): Boolean {
        val messageAge = getNewestMessageAge()
        return messageAge <= allowFreeze && freezeStreamSequence.get() == null
    }

    /**
     * Freezes message consumption, will check if the consumer can be frozen before freezing.
     *
     * While frozen it performs an action, specified by [onFrozen]
     */
    fun onFreeze(
        onFrozen: (Instant) -> Unit
    ): Instant? {
        val (_, timestamp) = freeze() ?: return null
        try {
            onFrozen(timestamp)
        } catch (e: Exception) {
            log.error(e) { "Something went wrong while performing action during freeze" }
        } finally {
            log.info { "Unfreezing consumer" }
            resume()
        }
        return timestamp
    }

    /**
     * Freezes message consumption, will check if the consumer can be frozen before freezing.
     */
    fun freeze(): Pair<Long, Instant>? {
        // already frozen or cannot freeze
        if (isFrozen() || !canFreeze()) {
            return null
        }

        log.info { "Commencing freeze..." }

        try {
            shutdown()

            log.info { "Consumer frozen at lastStreamSequence=${lastStreamSequence.get()}" }
            frozen.set(true)
        } catch (e: Exception) {
            log.error(e) { "Something went wrong during freezing" }
            start()
            return null
        }

        return lastStreamSequence.get() to lastStreamTimestamp.get()
    }

    /**
     * Returns if the consumer is currently frozen, or is about to be frozen
     */
    fun isFrozen(): Boolean = frozen.get() || freezeStreamSequence.get() != null

    /**
     * Returns the last stream sequence
     */
    fun getLastStreamSequence(): Long = lastStreamSequence.get()

    @PostConstruct
    fun init() {
        start()
    }

    /**
     * Start message consumption
     */
    fun start() {
        if (!running.compareAndSet(false, true)) {
            // already running
            return
        }

        log.info { "Start consuming messages..." }

        eventStreamService.consume(
            stream = stream,
            // remnant of when area-monitor supported multiple instances,
            // this suffix must be kept to keep reusing the original consumer
            suffix = "0",
            revents = ReventsAisStreamOptions(
                includeServiceVessels = false
            )
        ) { diff, message ->
            counterMsgIn.incrementAndGet()
            counterTotalMsgIn.incrementAndGet()

            val metadata = message.metaData()
            val sequence = metadata.streamSequence()
            val timestamp = metadata.timestamp().toInstant()

            val freezeStreamSequence = freezeStreamSequence.get()
            if (freezeStreamSequence != null && sequence > freezeStreamSequence) {
                // "freeze" message
                message.nakWithDelay(redeliveryDelay)
                if (freezeFirstHit.compareAndSet(false, true)) {
                    shutdown()
                }
            } else {
                // update the last stream sequence and timestamp
                lastStreamSequence.getAndUpdate { previous ->
                    if (sequence > previous) {
                        lastStreamTimestamp.set(timestamp)
                        sequence
                    } else {
                        previous
                    }
                }
                consumeMessage(diff, message)
            }
        }
    }

    private fun consumeMessage(
        diff: AisDiffMessage,
        message: MessageContext
    ) {
        try {
            // update the state of all areas the ship is already in
            stateService.update(diff)

            // intercept, we might want to trigger on the diff beforehand
            consumerInterceptService.intercept(diff)

            val events = eventGenerationService.process(diff)
            consumerPublishService.publish(diff, events)

            // update counters
            val change = diff.location.changed
            if (change == null) {
                counterMsgDropped.incrementAndGet()
                counterTotalMsgDropped.incrementAndGet()
            } else {
                counterMsgOut.addAndGet(events.size.toLong())
                counterTotalMsgOut.addAndGet(events.size.toLong())
            }

            // acknowledge only if all processing succeeded
            message.ack()

        } catch (e: Exception) {
            log.error(e) { "Failed to process message for MMSI ${diff.mmsi}. Message will be redelivered." }

            // Don't acknowledge the message so it will be redelivered
            // Use nakWithDelay to avoid immediate redelivery and potential tight loops
            message.nakWithDelay(redeliveryDelay)

            // Update error counters
            counterMsgDropped.incrementAndGet()
            counterTotalMsgDropped.incrementAndGet()
        }
    }

    /**
     * Shutdown the consumer by draining
     */
    fun shutdown() {
        log.info { "Draining..." }
        try {
            // wait for drain to complete
            stream.drain()

            val onComplete = freezeOnCatchupComplete.getAndSet(null)
            onComplete?.invoke()

            running.set(false)
            resetFreeze()

            log.info { "Finished draining" }
        } catch (e: Exception) {
            log.error(e) { "Something went wrong while draining the consumer" }
        }
    }

    /**
     * Resume message consumption, but stop after being caught up with the specified [sequence]
     */
    fun catchupToSequence(
        sequence: Long,
        onComplete: () -> Unit
    ) {
        resetFreeze()
        freezeStreamSequence.set(sequence)
        freezeOnCatchupComplete.set(onComplete)
        start()
    }

    /**
     * Resets all values related to freezing message consumption
     */
    private fun resetFreeze() {
        frozen.set(false)
        freezeStreamSequence.set(null)
        freezeFirstHit.set(false)
        freezeOnCatchupComplete.set(null)
    }

    /**
     * Resumes message consumption, and ensures [resetFreeze]
     */
    fun resume() {
        resetFreeze()
        start()
    }
}
