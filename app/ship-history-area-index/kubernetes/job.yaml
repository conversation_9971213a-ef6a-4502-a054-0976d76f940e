apiVersion: batch/v1
kind: Job
metadata:
  name: ship-history-area-index-$DATE
  namespace: revents-jobs
spec:
  template:
    spec:
      serviceAccountName: ship-history-area-index-app
      containers:
        - image: ************.dkr.ecr.eu-west-1.amazonaws.com/ais-engine/ship-history-area-index
          name: ship-history-area-index-$DATE
          args:
            - $DATE
          resources:
            limits:
              cpu: '8.0'
              memory: 16G
          env:
            - name: SPRING_CLOUD_KUBERNETES_ENABLED
              value: 'true'
            - name: SPRING_CLOUD_KUBERNETES_CONFIG_NAME
              value: ship-history-area-index-app
            - name: SPRING_CLOUD_KUBERNETES_CONFIG_NAMESPACE
              value: revents-jobs
            - name: SPRING_CLOUD_KUBERNETES_CONFIG_ENABLED
              value: 'true'
            - name: SPRING_CLOUD_KUBERNETES_CONFIG_ENABLE_API
              value: 'true'
      restartPolicy: Never
