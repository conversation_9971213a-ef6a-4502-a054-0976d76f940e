# ship-history-area-index

> Command line application used to back-fill the area index into S3.

Install required role and service account.

```shell
kubectl apply -f app/ship-history-area-index/kubernetes/role.yaml
kubectl apply -f app/ship-history-area-index/kubernetes/rolebinding.yaml
kubectl apply -f app/ship-history-area-index/kubernetes/serviceaccount.yaml
```

Run the following command for a given `DATE` to spawn a new Job within the `revents-jobs` namespace (since it supports
running serverless Jobs with Fargate).

```shell
DATE=2022-01 envsubst < app/ship-history-area-index/kubernetes/job.yaml | kubectl apply -f -
```

The application uses a `ConfigMap` with the name `ship-history-area-index-app` as its config.
