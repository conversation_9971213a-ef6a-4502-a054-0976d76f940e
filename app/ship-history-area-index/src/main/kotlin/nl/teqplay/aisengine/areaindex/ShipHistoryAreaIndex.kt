package nl.teqplay.aisengine.areaindex

import com.amazonaws.auth.AWSStaticCredentialsProvider
import com.amazonaws.auth.BasicAWSCredentials
import com.amazonaws.regions.Regions
import com.amazonaws.services.s3.AmazonS3
import com.amazonaws.services.s3.AmazonS3ClientBuilder
import com.amazonaws.services.s3.model.ListObjectsV2Request
import com.amazonaws.services.s3.model.ObjectMetadata
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.ObjectMapper
import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveGlobalProperties
import nl.teqplay.aisengine.bucketing.storage.archive.readS3ObjectBytes
import nl.teqplay.aisengine.util.floorLatitudeToDecimals
import nl.teqplay.aisengine.util.floorLongitudeToDecimals
import nl.teqplay.aisengine.util.withLimitedRetries
import org.springframework.boot.CommandLineRunner
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.time.Duration
import java.time.Instant
import java.time.LocalDate
import java.time.YearMonth
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.CountDownLatch
import java.util.concurrent.atomic.AtomicInteger
import java.util.zip.ZipEntry
import java.util.zip.ZipInputStream
import java.util.zip.ZipOutputStream
import kotlin.concurrent.thread

private val LOG = KotlinLogging.logger { }

@SpringBootApplication
@ConfigurationPropertiesScan
class ShipHistoryAreaIndex(
    private val archiveGlobal: BucketArchiveGlobalProperties,
    private val objectMapper: ObjectMapper,
) : CommandLineRunner {

    private val s3Client: AmazonS3 = AmazonS3ClientBuilder
        .standard()
        .withRegion(Regions.EU_WEST_1)
        .enableForceGlobalBucketAccess()
        .withCredentials()
        .build()

    private fun AmazonS3ClientBuilder.withCredentials() = archiveGlobal.credentials?.let { credentials ->
        withCredentials(
            AWSStaticCredentialsProvider(
                with(credentials) { BasicAWSCredentials(accessKeyId, secretKey) }
            )
        )
    } ?: this

    companion object {
        private const val SHIP_HISTORY_S3_BUCKET = "ship-history"
    }

    override fun run(vararg args: String?) {
        val start = Instant.now()

        val dateArg = requireNotNull(args.first()) { "DATE argument not provided." }
        val yearMonth = YearMonth.parse(dateArg)
        val mmsiFiles = listFiles(yearMonth)

        val mmsisInAreaByDate = ConcurrentHashMap<LocalDate, ConcurrentHashMap<String, MutableSet<Int>>>()

        val mmsiCount = AtomicInteger(0)
        val queue = ConcurrentLinkedQueue(mmsiFiles)
        val threadCount = 32
        val latch = CountDownLatch(threadCount)
        repeat(threadCount) {
            thread {
                runQueueWorker(mmsiCount, mmsiFiles.size, yearMonth, queue, mmsisInAreaByDate, latch)
            }
        }
        latch.await()

        LOG.info { "Uploading files..." }

        mmsisInAreaByDate.forEach { (date, data) ->
            val outputStream = ByteArrayOutputStream()
            val zipOutputStream = ZipOutputStream(outputStream)
            zipOutputStream.putNextEntry(ZipEntry("index.json"))
            // Sort the values to improve compression due to partial repeating MMSIs.
            zipOutputStream.write(objectMapper.writeValueAsBytes(data.mapValues { it.value.sorted() }.toSortedMap()))
            zipOutputStream.closeEntry()
            zipOutputStream.close()
            zipOutputStream.flush()
            zipOutputStream.close()

            val zipBytes = outputStream.toByteArray()
            val inputStream = ByteArrayInputStream(zipBytes)

            val metadata = ObjectMetadata()
            metadata.contentLength = zipBytes.size.toLong()
            metadata.contentType = "application/zip, application/octet-stream"

            withLimitedRetries {
                s3Client.putObject(SHIP_HISTORY_S3_BUCKET, "area-index,$date.zip", inputStream, metadata)
            }
        }

        val elapsed = Duration.between(start, Instant.now())
        LOG.info { "Migration took $elapsed." }
    }

    private fun runQueueWorker(
        mmsiCount: AtomicInteger,
        mmsiMax: Int,
        yearMonth: YearMonth,
        queue: ConcurrentLinkedQueue<String>,
        mmsisInAreaByDate: ConcurrentHashMap<LocalDate, ConcurrentHashMap<String, MutableSet<Int>>>,
        latch: CountDownLatch
    ) {
        while (queue.isNotEmpty()) {
            val mmsiFile = queue.poll() ?: break

            val mmsi = mmsiFile
                .removePrefix("mmsi,$yearMonth,")
                .removeSuffix(".zip")
                .toInt()

            val bytes = withLimitedRetries {
                readS3ObjectBytes(s3Client, SHIP_HISTORY_S3_BUCKET, mmsiFile)
            }
            if (bytes == null) {
                continue
            }

            val zipInputStream = ZipInputStream(bytes.inputStream())
            do {
                val entry = zipInputStream.nextEntry
                if (entry != null) {
                    val fileDate = LocalDate.parse(entry.name.removeSuffix(".json"))
                    val content = zipInputStream.readAllBytes()
                    val iterator = objectMapper
                        .readerFor(Update::class.java)
                        .readValues<Update>(content)
                    val areaIdPairs = mutableSetOf<Pair<Int, Int>>()
                    val update = Update()
                    for (it in iterator) {
                        if (it.lat == null && it.lon == null) {
                            continue
                        }

                        if (it.lat != null) update.lat = it.lat
                        if (it.lon != null) update.lon = it.lon

                        if (update.lat == null || update.lon == null) {
                            continue
                        }

                        val fLon = update.lon!!.times(10).toInt()
                        val fLat = update.lat!!.times(10).toInt()
                        val pair = fLon to fLat
                        areaIdPairs.add(pair)
                    }

                    mmsisInAreaByDate.compute(fileDate) { _, currentMap ->
                        val mmsisByArea = currentMap ?: ConcurrentHashMap<String, MutableSet<Int>>()
                        areaIdPairs.forEach { areaIdPair ->
                            val (fLon, fLat) = areaIdPair
                            val lon = floorLongitudeToDecimals(fLon.div(10.0), 1)
                            val lat = floorLatitudeToDecimals(fLat.div(10.0), 1)
                            val areaId = "$lon,$lat"
                            mmsisByArea.compute(areaId) { _, currentSet ->
                                val mmsis = currentSet ?: mutableSetOf()
                                mmsis.apply { add(mmsi) }
                            }
                        }
                        mmsisByArea
                    }
                }
            } while (entry != null)
            zipInputStream.close()

            val loaded = mmsiCount.addAndGet(1)
            if (loaded % 250 == 0) {
                LOG.info { "Processed $loaded/$mmsiMax files..." }
            }
        }
        latch.countDown()
    }

    private fun listFiles(yearMonth: YearMonth): List<String> {
        val request = ListObjectsV2Request()
        request.bucketName = SHIP_HISTORY_S3_BUCKET
        request.prefix = "mmsi,$yearMonth,"

        var listed = 0
        val files = mutableListOf<String>()
        var listResult = withLimitedRetries { s3Client.listObjectsV2(request) }
        do {
            listResult.objectSummaries.forEach {
                val fileName = it.key
                files.add(fileName)
            }

            val isTruncated = listResult.isTruncated
            if (isTruncated) {
                request.continuationToken = listResult.nextContinuationToken
                listResult = withLimitedRetries { s3Client.listObjectsV2(request) }
            }
            if (++listed % 10 == 0) {
                LOG.info { "Listed ${files.size} files..." }
            }
        } while (isTruncated)

        return files
    }

    data class Update(
        @JsonProperty("lat") var lat: Double? = null,
        @JsonProperty("lon") var lon: Double? = null,
    )
}

fun main(args: Array<String>) {
    runApplication<ShipHistoryAreaIndex>(*args)
}
