global:
  storageClass: "gp2"
  namespaceOverride: "teqplay-app"

replicaCount: ~
deployStrategy: RollingUpdate

preStopHookEnable: true
podDisruptionBudget:
  enabled: true

image:
  repository: 050356841556.dkr.ecr.eu-west-1.amazonaws.com/ais-engine/event-history

resources:
  requests:
    cpu: 100m
    memory: 4Gi
  limits:
    memory: 4Gi

nodeSelector:
  app.teqplay.nl/nodegroup: ais-engine

tolerations:
  - key: nodegroup
    operator: Equal
    value: ais-engine
    effect: NoSchedule

podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/path: /actuator/prometheus
  prometheus.io/port: "8080"

mongodb:
  enabled: false
