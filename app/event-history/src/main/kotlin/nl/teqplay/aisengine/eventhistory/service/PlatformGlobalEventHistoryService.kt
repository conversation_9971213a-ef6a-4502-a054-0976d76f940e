package nl.teqplay.aisengine.eventhistory.service

import nl.teqplay.aisengine.bucketing.properties.PlatformBucketProperties
import nl.teqplay.aisengine.bucketing.storage.event.area.implementation.EventByAreaReadCache
import nl.teqplay.aisengine.bucketing.storage.event.area.implementation.PlatformTeqplayEventByAreaReadCache
import nl.teqplay.aisengine.bucketing.storage.event.ship.implementation.EventByShipReadCache
import nl.teqplay.aisengine.bucketing.storage.event.ship.implementation.PlatformTeqplayEventByShipReadCache
import nl.teqplay.aisengine.eventhistory.config.PlatformGlobalEventByArea
import nl.teqplay.aisengine.eventhistory.config.PlatformGlobalEventByShip
import nl.teqplay.aisengine.platform.converter.BerthInformationResolver
import org.springframework.stereotype.Service

@Service
class PlatformGlobalEventHistoryService(
    eventByShipReadCache: EventByShipReadCache,
    eventByAreaReadCache: EventByAreaReadCache,
    @PlatformGlobalEventByShip platformTeqplayEventByShipReadCache: PlatformTeqplayEventByShipReadCache?,
    @PlatformGlobalEventByArea platformTeqplayEventByAreaReadCache: PlatformTeqplayEventByAreaReadCache?,
    csiService: CsiService,
    platformBucketProperties: PlatformBucketProperties,
    validationService: ValidationService,
    berthConverter: BerthInformationResolver
) : PlatformEventHistoryService(
    eventByShipReadCache,
    eventByAreaReadCache,
    platformTeqplayEventByShipReadCache,
    platformTeqplayEventByAreaReadCache,
    csiService,
    platformBucketProperties,
    validationService,
    berthConverter
)
