package nl.teqplay.aisengine.eventhistory.controller

import nl.teqplay.aisengine.event.interfaces.AreaBasedEvent
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.eventhistory.model.EventHistoryQuery
import nl.teqplay.aisengine.eventhistory.service.EventHistoryBaseService
import nl.teqplay.aisengine.eventhistory.service.ValidationService
import nl.teqplay.aisengine.platform.toLocation
import nl.teqplay.platform.model.event.TeqplayEvent
import nl.teqplay.skeleton.common.exception.BadRequestException
import nl.teqplay.skeleton.model.BoundingBox
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.Polygon
import nl.teqplay.skeleton.model.TimeWindow
import reactor.core.publisher.Flux
import reactor.kotlin.core.publisher.toFlux
import kotlin.reflect.KFunction1
import kotlin.streams.asStream

/**
 * Base controller containing all requests that are used by the [EventHistoryController] and [PlatformEventHistoryController]
 *  and only have a different return type.
 */
abstract class EventHistoryBaseController<T : Any>(
    private val service: EventHistoryBaseService<T>,
    private val validationService: ValidationService
) {
    protected fun byMmsi(
        mmsi: Int,
        sort: Boolean,
        window: TimeWindow,
        maxDays: Int?,
        includeEvents: Set<String>?,
        excludeEvents: Set<String>?,
        includeAreaTypes: Set<AreaIdentifier.AreaType>?,
        excludeAreaTypes: Set<AreaIdentifier.AreaType>?
    ): Flux<T> {
        validationService.validateMaxDays(window, maxDays)
        val events = service.findHistoryByMmsi(mmsi, sort, window)
        return filterEventsIfNeeded(events, includeEvents, excludeEvents, includeAreaTypes, excludeAreaTypes).asStream().toFlux()
    }

    protected fun byImo(
        imo: Int,
        sort: Boolean,
        window: TimeWindow,
        maxDays: Int?,
        includeEvents: Set<String>?,
        excludeEvents: Set<String>?,
        includeAreaTypes: Set<AreaIdentifier.AreaType>?,
        excludeAreaTypes: Set<AreaIdentifier.AreaType>?
    ): Flux<T> {
        validationService.validateMaxDays(window, maxDays)
        val events = service.findHistoryByImo(imo, sort, window)
        return filterEventsIfNeeded(events, includeEvents, excludeEvents, includeAreaTypes, excludeAreaTypes).asStream().toFlux()
    }

    protected fun byMmsis(
        mmsis: List<Int>,
        sort: Boolean,
        window: TimeWindow,
        maxDays: Int?,
        includeEvents: Set<String>?,
        excludeEvents: Set<String>?,
        includeAreaTypes: Set<AreaIdentifier.AreaType>?,
        excludeAreaTypes: Set<AreaIdentifier.AreaType>?
    ): Flux<T> {
        validationService.validateMaxDays(window, maxDays)
        val events = service.findHistoryByMmsis(mmsis.toSet(), sort, window)
        return filterEventsIfNeeded(events, includeEvents, excludeEvents, includeAreaTypes, excludeAreaTypes).asStream().toFlux()
    }

    protected fun byImos(
        imos: List<Int>,
        sort: Boolean,
        window: TimeWindow,
        maxDays: Int?,
        includeEvents: Set<String>?,
        excludeEvents: Set<String>?,
        includeAreaTypes: Set<AreaIdentifier.AreaType>?,
        excludeAreaTypes: Set<AreaIdentifier.AreaType>?
    ): Flux<T> {
        validationService.validateMaxDays(window, maxDays)
        val events = service.findHistoryByImos(imos.toSet(), sort, window)
        return filterEventsIfNeeded(events, includeEvents, excludeEvents, includeAreaTypes, excludeAreaTypes).asStream().toFlux()
    }

    protected fun inBoundingBox(
        boundingBox: BoundingBox,
        sort: Boolean,
        window: TimeWindow,
        maxDays: Int?,
        maxAreaInKm2: Double?,
        includeEvents: Set<String>?,
        excludeEvents: Set<String>?,
        includeAreaTypes: Set<AreaIdentifier.AreaType>?,
        excludeAreaTypes: Set<AreaIdentifier.AreaType>?
    ): Flux<T> {
        validationService.validateMaxDays(window, maxDays)
        validationService.validateMaxBoundingBoxArea(boundingBox, maxAreaInKm2, sort)
        val events = service.findHistoryInBoundingBox(boundingBox, sort, window)
        return filterEventsIfNeeded(events, includeEvents, excludeEvents, includeAreaTypes, excludeAreaTypes).asStream().toFlux()
    }

    protected fun inPolygon(
        polygon: Polygon,
        sort: Boolean,
        window: TimeWindow,
        maxDays: Int?,
        maxAreaInKm2: Double?,
        includeEvents: Set<String>?,
        excludeEvents: Set<String>?,
        includeAreaTypes: Set<AreaIdentifier.AreaType>?,
        excludeAreaTypes: Set<AreaIdentifier.AreaType>?
    ): Flux<T> {
        validationService.validateMaxDays(window, maxDays)
        validationService.validateMaxPolygonArea(polygon, maxAreaInKm2)
        val events = service.findHistoryInPolygon(polygon, sort, window)
        return filterEventsIfNeeded(events, includeEvents, excludeEvents, includeAreaTypes, excludeAreaTypes).asStream().toFlux()
    }

    protected fun inCircle(
        center: Location,
        radiusInKm: Double,
        sort: Boolean,
        window: TimeWindow,
        maxDays: Int?,
        maxAreaInKm2: Double?,
        includeEvents: Set<String>?,
        excludeEvents: Set<String>?,
        includeAreaTypes: Set<AreaIdentifier.AreaType>?,
        excludeAreaTypes: Set<AreaIdentifier.AreaType>?
    ): Flux<T> {
        validationService.validateMaxDays(window, maxDays)
        validationService.validateMaxCircleArea(radiusInKm, maxAreaInKm2)
        val events = service.findHistoryInCircle(center, radiusInKm, sort, window)
        return filterEventsIfNeeded(events, includeEvents, excludeEvents, includeAreaTypes, excludeAreaTypes).asStream().toFlux()
    }

    protected fun byQuery(query: EventHistoryQuery): Flux<T> {
        val polygon = query.polygon?.let { locations ->
            Polygon(locations.map { it.toLocation() })
        }

        val fieldsCount = listOfNotNull(query.polygon, query.circle, query.mmsis, query.imos).size
        if (fieldsCount > 1) {
            throw BadRequestException(
                "More than one of the mutually exclusive fields " +
                    "'polygon', 'circle', 'mmsis', and 'imos' is defined. " +
                    "Ony one of these fields can be defined."
            )
        }

        val window = TimeWindow(query.from.toInstant(), query.to.toInstant())
        validationService.validateMaxDays(window, query.maxDays?.toInt())

        when {
            polygon != null -> validationService.validateMaxPolygonArea(polygon, query.maxAreaInKm2?.toDouble())
            query.circle != null -> validationService.validateMaxCircleArea(query.circle.radiusInKm, query.maxAreaInKm2?.toDouble())
        }

        return service.findHistoryByQuery(
            window = window,
            polygon = polygon,
            circle = query.circle,
            mmsis = query.mmsis?.mapNotNull { it.toIntOrNull() },
            imos = query.imos?.mapNotNull { it.toIntOrNull() },
            sort = query.sort ?: true,
            filter = query.filter
        ) ?: throw BadRequestException(
            "Missing one of the required fields 'polygon', 'circle', 'mmsis', or 'imos'. " +
                "Exactly one of these fields must be defined."
        )
    }

    /**
     * Filter the found events based on the [includeEvents] or [excludeEvents] set of event types.
     *
     * @param events All the events found in the event buckets
     * @param includeEvents A set of event types we should keep
     * @param excludeEvents A set of event types we should filter out
     * @param includeAreaTypes A set of AreaType we should keep
     * @param excludeAreaTypes A set of AreaType we should filter out
     * @return filtered out events
     */
    private fun filterEventsIfNeeded(
        events: Sequence<T>,
        includeEvents: Set<String>?,
        excludeEvents: Set<String>?,
        includeAreaTypes: Set<AreaIdentifier.AreaType>?,
        excludeAreaTypes: Set<AreaIdentifier.AreaType>?
    ): Sequence<T> {
        val filteredEventsOnType = when {
            !includeEvents.isNullOrEmpty() -> filterByEventType(includeEvents, events::filter)
            !excludeEvents.isNullOrEmpty() -> filterByEventType(excludeEvents, events::filterNot)
            else -> events
        }

        val filteredEvents = when {
            !includeAreaTypes.isNullOrEmpty() -> filterByEventAreaType(includeAreaTypes, filteredEventsOnType::filter)
            !excludeAreaTypes.isNullOrEmpty() -> filterByEventAreaType(excludeAreaTypes, filteredEventsOnType::filterNot)
            else -> filteredEventsOnType
        }

        return filteredEvents
    }

    /**
     * Filter an event based on the type.
     *
     * @param eventTypes A set of events types we should either keep or filter out
     * @param filter The filter function applied to the [Sequence] of [T]
     * @return A [Sequence] of [T] containing the filtered events
     */
    private fun filterByEventType(
        eventTypes: Set<String>,
        filter: KFunction1<(T) -> Boolean, Sequence<T>>,
    ): Sequence<T> {
        return filter { event ->
            val eventType = getEventType(event)
            eventTypes.contains(eventType)
        }
    }

    /**
     * Filter an event based on the type.
     *
     * @param eventTypes A set of events types we should either keep or filter out
     * @param filter The filter function applied to the [Sequence] of [T]
     * @return A [Sequence] of [T] containing the filtered events
     */
    private fun filterByEventAreaType(
        areaTypes: Set<AreaIdentifier.AreaType>,
        filter: KFunction1<(T) -> Boolean, Sequence<T>>,
    ): Sequence<T> {
        return filter { event ->
            val areaType = getAreaType(event)
            areaTypes.contains(areaType)
        }
    }

    /**
     * Get the type of the event.
     *
     * @return either [TeqplayEvent.type], [Event.getType] or null depending on the type of [T]
     */
    private fun <T> getEventType(event: T): String? {
        return when (event) {
            is Event -> event.getType()
            is TeqplayEvent -> event.type
            else -> null
        }
    }

    private fun <T> getAreaType(event: T): AreaIdentifier.AreaType? {
        return when (event) {
            is AreaBasedEvent -> event.area.type
            is TeqplayEvent -> event.getAreaIdentifierType()
            else -> null
        }
    }

    private fun TeqplayEvent.getAreaIdentifierType(): AreaIdentifier.AreaType? {
        if (!this.type.startsWith(TeqplayEvent.AREA)) {
            // Not an area event
            return null
        }

        return AreaIdentifier.AreaType.values().find { this.type.startsWith(TeqplayEvent.AREA + "." + it.name.lowercase()) }
    }
}
