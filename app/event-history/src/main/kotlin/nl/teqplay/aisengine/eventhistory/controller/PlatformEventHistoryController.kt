package nl.teqplay.aisengine.eventhistory.controller

import com.fasterxml.jackson.annotation.JsonView
import nl.teqplay.aisengine.eventhistory.model.EventHistoryQuery
import nl.teqplay.aisengine.eventhistory.service.PlatformEventHistoryService
import nl.teqplay.aisengine.eventhistory.service.ValidationService
import nl.teqplay.aisengine.eventhistory.util.convert
import nl.teqplay.aisengine.platform.toLocation
import nl.teqplay.platform.model.Location
import nl.teqplay.platform.model.event.ComposedEventView
import nl.teqplay.platform.model.event.TeqplayEvent
import nl.teqplay.platform.model.serialization.Views
import nl.teqplay.platform.resource.params.TimeWindow
import nl.teqplay.skeleton.model.Polygon
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestParam
import reactor.core.publisher.Flux

const val DEFAULT_EVENT_LIMIT = 100000L
const val DEFAULT_EVENT_SORT = "asc"

/**
 * Endpoints used to get event history, both by mmsi and by area, from platform's database.
 */
abstract class PlatformEventHistoryController(
    private val service: PlatformEventHistoryService,
    validationService: ValidationService,
) : EventHistoryBaseController<TeqplayEvent>(service, validationService) {
    @GetMapping("/eventtypes/{type}")
    fun getEventTypes(
        @PathVariable type: String,
    ): List<TeqplayEvent.EventType> {
        return when {
            type.startsWith("shipship") -> TeqplayEvent.getAllShipShipEventTypes()
            type.startsWith("area") -> TeqplayEvent.getAllAreaEventTypes()
            type.startsWith("planned") -> TeqplayEvent.getAllSpecificPlanningEventsTypes()
            type.startsWith("singleship") -> TeqplayEvent.getSingleShipEventsTypes()
            else -> TeqplayEvent.EventType.values().filter { it != TeqplayEvent.EventType.ALL_EVENTS }
        }
    }

    @GetMapping("/composed/mmsi/{mmsi}")
    fun findComposedHistoryByMmsi(
        @PathVariable mmsi: Int,
        window: TimeWindow,
        @RequestParam(required = false) maxDays: Int?,
        @RequestParam(defaultValue = DEFAULT_EVENT_LIMIT.toString()) limit: Long
    ): Flux<ComposedEventView> = service.getComposedEvents(window, maxDays) {
        service.findHistoryByMmsi(mmsi, false, it)
    }.take(limit, true)

    @GetMapping("/composed/imo/{imo}")
    fun findComposedHistoryByImo(
        @PathVariable imo: Int,
        window: TimeWindow,
        @RequestParam(required = false) maxDays: Int?,
        @RequestParam(defaultValue = DEFAULT_EVENT_LIMIT.toString()) limit: Long
    ): Flux<ComposedEventView> = service.getComposedEvents(window, maxDays) {
        service.findHistoryByImo(imo, false, it)
    }.take(limit, true)

    @PostMapping("/composed/list/mmsi")
    fun findComposedHistoryByMmsis(
        @RequestBody mmsis: List<Int>,
        window: TimeWindow,
        @RequestParam(required = false) maxDays: Int?,
        @RequestParam(defaultValue = DEFAULT_EVENT_LIMIT.toString()) limit: Long
    ): Flux<ComposedEventView> = service.getComposedEvents(window, maxDays) {
        service.findHistoryByMmsis(mmsis.toSet(), false, it)
    }.take(limit, true)

    @PostMapping("/composed/list/imo")
    fun findComposedHistoryByImos(
        @RequestBody imos: List<Int>,
        window: TimeWindow,
        @RequestParam(required = false) maxDays: Int?,
        @RequestParam(defaultValue = DEFAULT_EVENT_LIMIT.toString()) limit: Long
    ): Flux<ComposedEventView> = service.getComposedEvents(window, maxDays) {
        service.findHistoryByImos(imos.toSet(), false, it)
    }.take(limit, true)

    @JsonView(Views.PublicView::class)
    @GetMapping("/mmsi/{mmsi}")
    fun findHistoryByMmsi(
        @PathVariable mmsi: Int,
        window: TimeWindow,
        @RequestParam(required = false) includeEvents: Set<String>?,
        @RequestParam(required = false) excludeEvents: Set<String>?,
        @RequestParam(required = false) maxDays: Int?,
        @RequestParam(defaultValue = DEFAULT_EVENT_SORT) sort: String,
        @RequestParam(defaultValue = DEFAULT_EVENT_LIMIT.toString()) limit: Long
    ): Flux<TeqplayEvent> = byMmsi(
        mmsi = mmsi,
        sort = false,
        window = window.convert(),
        maxDays = maxDays,
        includeEvents = includeEvents,
        excludeEvents = excludeEvents,
        includeAreaTypes = null,
        excludeAreaTypes = null
    ).applyDescSortIfNeeded(sort)
        .take(limit, true)

    @JsonView(Views.PublicView::class)
    @PostMapping("/list/mmsi")
    fun findHistoryByMmsis(
        @RequestBody mmsis: List<Int>,
        window: TimeWindow,
        @RequestParam(required = false) includeEvents: Set<String>?,
        @RequestParam(required = false) excludeEvents: Set<String>?,
        @RequestParam(required = false) maxDays: Int?,
        @RequestParam(defaultValue = DEFAULT_EVENT_SORT) sort: String,
        @RequestParam(defaultValue = DEFAULT_EVENT_LIMIT.toString()) limit: Long
    ): Flux<TeqplayEvent> = byMmsis(
        mmsis = mmsis,
        sort = false,
        window = window.convert(),
        maxDays = maxDays,
        includeEvents = includeEvents,
        excludeEvents = excludeEvents,
        includeAreaTypes = null,
        excludeAreaTypes = null
    ).applyDescSortIfNeeded(sort)
        .take(limit, true)

    @JsonView(Views.PublicView::class)
    @GetMapping("/imo/{imo}")
    fun findHistoryByImo(
        @PathVariable imo: Int,
        window: TimeWindow,
        @RequestParam(required = false) includeEvents: Set<String>?,
        @RequestParam(required = false) excludeEvents: Set<String>?,
        @RequestParam(required = false) maxDays: Int?,
        @RequestParam(defaultValue = DEFAULT_EVENT_SORT) sort: String,
        @RequestParam(defaultValue = DEFAULT_EVENT_LIMIT.toString()) limit: Long
    ): Flux<TeqplayEvent> = byImo(
        imo = imo,
        sort = false,
        window = window.convert(),
        maxDays = maxDays,
        includeEvents = includeEvents,
        excludeEvents = excludeEvents,
        includeAreaTypes = null,
        excludeAreaTypes = null
    ).applyDescSortIfNeeded(sort)
        .take(limit, true)

    @JsonView(Views.PublicView::class)
    @PostMapping("/area")
    fun findHistoryInPolygon(
        @RequestBody platformPolygon: List<Location>,
        window: TimeWindow,
        @RequestParam(required = false) includeEvents: Set<String>?,
        @RequestParam(required = false) excludeEvents: Set<String>?,
        @RequestParam(required = false) maxDays: Int?,
        @RequestParam("maxArea", required = false) maxAreaInKm2: Double?,
        @RequestParam(defaultValue = DEFAULT_EVENT_SORT) sort: String,
        @RequestParam(defaultValue = DEFAULT_EVENT_LIMIT.toString()) limit: Long
    ): Flux<TeqplayEvent> = inPolygon(
        polygon = Polygon(platformPolygon.map { it.toLocation() }),
        sort = false,
        window = window.convert(),
        maxDays = maxDays,
        maxAreaInKm2 = maxAreaInKm2,
        includeEvents = includeEvents,
        excludeEvents = excludeEvents,
        includeAreaTypes = null,
        excludeAreaTypes = null
    ).applyDescSortIfNeeded(sort)
        .take(limit, true)

    @JsonView(Views.PublicView::class)
    @PostMapping("/query")
    fun findHistoryQuery(
        @RequestBody query: EventHistoryQuery,
    ): Flux<TeqplayEvent> = byQuery(query)

    private fun Flux<TeqplayEvent>.applyDescSortIfNeeded(sort: String): Flux<TeqplayEvent> {
        return if (sort == "desc") {
            this.sort(compareByDescending(TeqplayEvent::getDatetime))
        } else {
            this
        }
    }
}
