package nl.teqplay.aisengine.eventhistory

import io.swagger.v3.oas.annotations.OpenAPIDefinition
import nl.teqplay.aisengine.ObjectMapperConfiguration
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication
import org.springframework.context.annotation.Import
import org.springframework.scheduling.annotation.EnableScheduling

@SpringBootApplication(exclude = [MongoAutoConfiguration::class])
@ConfigurationPropertiesScan
@OpenAPIDefinition
@Import(ObjectMapperConfiguration::class) // TODO should be auto configured, not working in reactive mode, see PRA-613
@EnableScheduling
class EventHistory

fun main(args: Array<String>) {
    runApplication<EventHistory>(*args)
}
