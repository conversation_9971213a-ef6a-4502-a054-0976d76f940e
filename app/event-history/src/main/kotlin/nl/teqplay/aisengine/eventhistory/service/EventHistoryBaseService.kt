package nl.teqplay.aisengine.eventhistory.service

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.bucketing.properties.PlatformBucketProperties
import nl.teqplay.aisengine.bucketing.storage.event.area.implementation.EventByAreaReadCache
import nl.teqplay.aisengine.bucketing.storage.event.area.implementation.PlatformTeqplayEventByAreaReadCache
import nl.teqplay.aisengine.bucketing.storage.event.ship.implementation.EventByShipReadCache
import nl.teqplay.aisengine.bucketing.storage.event.ship.implementation.PlatformTeqplayEventByShipReadCache
import nl.teqplay.aisengine.event.interfaces.ActualEvent
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.eventhistory.model.PlatformCircleQuery
import nl.teqplay.aisengine.platform.converter.convertToAisEngineEvent
import nl.teqplay.aisengine.platform.converter.convertToPlatformEvent
import nl.teqplay.aisengine.platform.toLocation
import nl.teqplay.platform.model.event.TeqplayEvent
import nl.teqplay.skeleton.model.BoundingBox
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.Polygon
import nl.teqplay.skeleton.model.TimeWindow
import reactor.core.publisher.Flux
import reactor.kotlin.core.publisher.toFlux
import java.time.ZoneOffset
import kotlin.reflect.KClass
import kotlin.streams.asStream

abstract class EventHistoryBaseService<T : Any>(
    private val desiredClass: KClass<T>,
    private val eventByShipReadCache: EventByShipReadCache,
    private val eventByAreaReadCache: EventByAreaReadCache,
    private val platformTeqplayEventByShipReadCache: PlatformTeqplayEventByShipReadCache?,
    private val platformTeqplayEventByAreaReadCache: PlatformTeqplayEventByAreaReadCache?,
    private val csiService: CsiService,
    platformBucketProperties: PlatformBucketProperties
) {
    private val log = KotlinLogging.logger { }

    private val platformDataEdge = platformBucketProperties.edge
        ?.atStartOfDay(ZoneOffset.UTC)
        ?.toInstant()

    private fun findHistorySequenceByMmsi(
        mmsi: Int,
        sort: Boolean,
        window: TimeWindow,
    ): Sequence<T> = mergeHistory(
        window = window,
        getTeqplayEvents = {
            platformTeqplayEventByShipReadCache?.findHistory(
                window = it,
                shipId = mmsi,
                sort = sort
            ) ?: emptySequence()
        },
        getEvents = {
            eventByShipReadCache.findHistoryByMmsi(
                window = it,
                mmsi = mmsi
            )
        }
    )

    private fun findHistorySequenceByImo(
        imo: Int,
        window: TimeWindow,
    ): Sequence<T> = mergeHistory(
        window = window,
        getTeqplayEvents = {
            // Platform doesn't support ship buckets on imo numbers, only mmsi, so we can just return an empty result
            emptySequence()
        },
        getEvents = {
            eventByShipReadCache.findHistoryByImo(
                window = it,
                imo = imo
            )
        }
    )

    fun findHistoryByMmsi(
        mmsi: Int,
        sort: Boolean,
        window: TimeWindow,
    ): Sequence<T> {
        val imos = csiService.getRelevantImosByMmsi(mmsi, window)

        val eventsByMmsi = findHistorySequenceByMmsi(mmsi, sort, window)

        // Return the result if we don't have an imo related to the provided mmsi
        if (imos.isEmpty()) {
            return eventsByMmsi
        }

        var result = emptySequence<T>()

        // We have mmsis related to this imo, so we search all possible events
        imos.forEach { imo ->
            result += findHistorySequenceByImo(imo, window)
        }

        result += eventsByMmsi

        // Return the result as is if we don't want to sort
        if (!sort) {
            return result
        }

        // TODO in-memory sort, replace with a stream-proof way of loading events if it becomes a problem
        return result.sortedWith(eventComparator())
    }

    fun findHistoryByImo(
        imo: Int,
        sort: Boolean,
        window: TimeWindow,
    ): Sequence<T> {
        val mmsis = csiService.getRelevantMmsisByImo(imo, window)

        // Try to find the event history using its IMO, we might have a vessel without an MMSI
        val eventsByImo = findHistorySequenceByImo(imo, window)

        // Return the result if we don't have a mmsi related to the provided imo
        if (mmsis.isEmpty()) {
            return eventsByImo
        }

        var result = emptySequence<T>()

        // We have mmsis related to this imo, so we search all possible events
        mmsis.forEach { mmsi ->
            result += findHistorySequenceByMmsi(mmsi, sort, window)
        }

        result += eventsByImo

        // Return the result as is if we don't want to sort
        if (!sort) {
            return result
        }

        // TODO in-memory sort, replace with a stream-proof way of loading events if it becomes a problem
        return result.sortedWith(eventComparator())
    }

    fun findHistoryByMmsis(
        mmsis: Set<Int>,
        sort: Boolean,
        window: TimeWindow,
    ): Sequence<T> {
        return mmsis.map { mmsi -> findHistoryByMmsi(mmsi, sort, window) }
            .asSequence()
            .flatten()
    }

    fun findHistoryByImos(
        imos: Set<Int>,
        sort: Boolean,
        window: TimeWindow,
    ): Sequence<T> {
        return imos.map { imo -> findHistoryByImo(imo, sort, window) }
            .asSequence()
            .flatten()
    }

    fun findHistoryInBoundingBox(
        boundingBox: BoundingBox,
        sort: Boolean,
        window: TimeWindow,
    ): Sequence<T> = mergeHistory(
        window = window,
        getTeqplayEvents = { platformTeqplayEventByAreaReadCache?.findHistoryInBoundingBox(it, boundingBox, sort) ?: emptySequence() },
        getEvents = { eventByAreaReadCache.findHistoryInBoundingBox(it, boundingBox, sort) }
    )

    fun findHistoryInPolygon(
        polygon: Polygon,
        sort: Boolean,
        window: TimeWindow,
    ): Sequence<T> = mergeHistory(
        window = window,
        getTeqplayEvents = { platformTeqplayEventByAreaReadCache?.findHistoryInPolygon(it, polygon, sort) ?: emptySequence() },
        getEvents = { eventByAreaReadCache.findHistoryInPolygon(it, polygon, sort) }
    )

    fun findHistoryInCircle(
        center: Location,
        radius: Double,
        sort: Boolean,
        window: TimeWindow,
    ): Sequence<T> = mergeHistory(
        window = window,
        getTeqplayEvents = { platformTeqplayEventByAreaReadCache?.findHistoryInCircle(it, center, radius, sort) ?: emptySequence() },
        getEvents = { eventByAreaReadCache.findHistoryInCircle(it, center, radius, sort) }
    )

    fun findHistoryByQuery(
        window: TimeWindow,
        polygon: Polygon?,
        circle: PlatformCircleQuery?,
        mmsis: List<Int>?,
        imos: List<Int>?,
        sort: Boolean = true,
        filter: String?,
    ): Flux<T>? {
        val events: Flux<T> = when {
            polygon != null -> {
                findHistoryInPolygon(
                    polygon = polygon,
                    sort = sort,
                    window = window
                )
            }
            circle != null -> {
                findHistoryInCircle(
                    center = circle.center.toLocation(),
                    radius = circle.radiusInKm,
                    sort = sort,
                    window = window
                )
            }
            mmsis?.isNotEmpty() == true -> {
                findHistoryByMmsis(
                    mmsis = mmsis.toSet(),
                    sort = sort,
                    window = window
                )
            }
            imos?.isNotEmpty() == true -> {
                findHistoryByImos(
                    imos = imos.toSet(),
                    sort = sort,
                    window = window
                )
            }
            else -> {
                return null
            }
        }.asStream().toFlux()

        if (filter != null) {
            log.warn { "Sql query found:$filter" }
        }

        return events
    }

    /**
     * Merge [getTeqplayEvents] and [getEvents] depending on the defined [window]
     */
    private fun mergeHistory(
        window: TimeWindow,
        getTeqplayEvents: (TimeWindow) -> Sequence<TeqplayEvent>,
        getEvents: (TimeWindow) -> Sequence<Event>,
    ): Sequence<T> = when {
        platformDataEdge == null || window.from >= platformDataEdge -> {
            getAisEngineEvents(window, getEvents)
        }
        window.to <= platformDataEdge -> {
            getPlatformEvents(window, getTeqplayEvents)
        }
        else -> {
            val platformWindow = TimeWindow(window.from, platformDataEdge)
            val shipHistoryWindow = TimeWindow(platformDataEdge, window.to)

            val platformData = getPlatformEvents(platformWindow, getTeqplayEvents)
            val eventsData = getAisEngineEvents(shipHistoryWindow, getEvents)

            (platformData + eventsData)
        }
    }

    protected open fun convertToPlatformCompatibleEvent(event: Event): TeqplayEvent? {
        return convertToPlatformEvent(event, null)
    }

    /**
     * Get all AisEngine events in a given [window] and convert them to the desired [T] if any changes are needed.
     *
     * @param window The time window used to find events
     * @param getEvents Lambda function to get the AisEngine events
     * @return A [Sequence] of [T] containing the events received from the EventHistory
     */
    private fun getAisEngineEvents(
        window: TimeWindow,
        getEvents: (TimeWindow) -> Sequence<Event>,
    ): Sequence<T> = getAndCastEvents<Event, TeqplayEvent>(window, getEvents, ::convertToPlatformCompatibleEvent)

    /**
     * Get all Platform events in a given [window] and convert them to the desired [T] if any changes are needed.
     *
     * @param window The time window used to find events
     * @param getEvents Lambda function to get the Platform events
     * @return A [Sequence] of [T] containing the events received from Platform
     */
    private fun getPlatformEvents(
        window: TimeWindow,
        getEvents: (TimeWindow) -> Sequence<TeqplayEvent>,
    ): Sequence<T> = getAndCastEvents<TeqplayEvent, Event>(window, getEvents, ::convertToAisEngineEvent)

    /**
     * Get all events in a given [window] and convert them to the desired [T] if any changes are needed.
     *
     * @param A The type we retrieve the events from the buckets
     * @param B The other event type
     * @param window The time window used to find events
     * @param getEvents Lambda function to get the type [A] events
     * @param convertEvent Lambda function to convert the type [A] event to type [B]
     * @return A [Sequence] of [T] containing the events received from Platform
     */
    private inline fun <reified A, reified B> getAndCastEvents(
        window: TimeWindow,
        getEvents: (TimeWindow) -> Sequence<A>,
        crossinline convertEvent: (A) -> B?,
    ): Sequence<T> {
        val foundEvents = getEvents.invoke(window)

        val convertedEvents = when (desiredClass) {
            A::class -> foundEvents
            B::class -> foundEvents.mapNotNull { convertEvent(it) }
            else -> emptySequence<T>()
        }

        @Suppress("UNCHECKED_CAST")
        return convertedEvents as Sequence<T>
    }

    /**
     * A [Comparator] used to sort events based on their correct timestamp.
     */
    private fun eventComparator(): Comparator<T> {
        return compareBy { event ->
            when (event) {
                is ActualEvent -> event.actualTime.toEpochMilli()
                is Event -> event.createdTime.toEpochMilli()
                is TeqplayEvent -> event.datetime
                else -> throw Exception("Can not sort event because it is not a supported model")
            }
        }
    }
}
