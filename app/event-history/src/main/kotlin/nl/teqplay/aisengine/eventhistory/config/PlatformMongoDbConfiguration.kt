package nl.teqplay.aisengine.eventhistory.config

import com.fasterxml.jackson.databind.ObjectMapper
import com.mongodb.kotlin.client.MongoClient
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.eventhistory.properties.PlatformGlobalMongoDbProperties
import nl.teqplay.aisengine.eventhistory.properties.PlatformProntoMongoDbProperties
import nl.teqplay.skeleton.datasource.MongoDbBuilder
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@ConditionalOnProperty(prefix = "bucket.platform", name = ["enabled"], havingValue = "true")
@Configuration
class PlatformMongoDbConfiguration(
    private val prontoProperties: PlatformProntoMongoDbProperties,
    private val globalProperties: PlatformGlobalMongoDbProperties
) {
    @Bean
    @PlatformProntoMongoClient
    fun platformProntoMongoClient(objectMapper: ObjectMapper): MongoClient = MongoDbBuilder.mongoClient(prontoProperties, MongoDbBuilder.configureObjectMapper(objectMapper))

    @Bean
    @PlatformProntoMongoDatabase
    fun platformProntoMongoDatabase(
        @PlatformProntoMongoClient client: MongoClient
    ): MongoDatabase = MongoDbBuilder.mongoDatabase(client, prontoProperties)

    @Bean
    @PlatformGlobalMongoClient
    fun platformGlobalMongoClient(objectMapper: ObjectMapper): MongoClient = MongoDbBuilder.mongoClient(globalProperties, MongoDbBuilder.configureObjectMapper(objectMapper))

    @Bean
    @PlatformGlobalMongoDatabase
    fun platformGlobalMongoDatabase(
        @PlatformGlobalMongoClient client: MongoClient
    ): MongoDatabase = MongoDbBuilder.mongoDatabase(client, globalProperties)
}

const val PLATFORM_PRONTO_MONGO_CLIENT = "platformProntoMongoClient"
const val PLATFORM_PRONTO_MONGO_DATABASE = "platformProntoMongoDatabase"
const val PLATFORM_GLOBAL_MONGO_CLIENT = "platformGlobalMongoClient"
const val PLATFORM_GLOBAL_MONGO_DATABASE = "platformGlobalMongoDatabase"

@Qualifier(PLATFORM_PRONTO_MONGO_CLIENT)
annotation class PlatformProntoMongoClient

@Qualifier(PLATFORM_PRONTO_MONGO_DATABASE)
annotation class PlatformProntoMongoDatabase

@Qualifier(PLATFORM_GLOBAL_MONGO_CLIENT)
annotation class PlatformGlobalMongoClient

@Qualifier(PLATFORM_GLOBAL_MONGO_DATABASE)
annotation class PlatformGlobalMongoDatabase
