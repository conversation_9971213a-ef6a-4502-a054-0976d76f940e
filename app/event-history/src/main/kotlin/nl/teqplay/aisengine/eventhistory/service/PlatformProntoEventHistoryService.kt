package nl.teqplay.aisengine.eventhistory.service

import nl.teqplay.aisengine.bucketing.properties.PlatformBucketProperties
import nl.teqplay.aisengine.bucketing.storage.event.area.implementation.EventByAreaReadCache
import nl.teqplay.aisengine.bucketing.storage.event.area.implementation.PlatformTeqplayEventByAreaReadCache
import nl.teqplay.aisengine.bucketing.storage.event.ship.implementation.EventByShipReadCache
import nl.teqplay.aisengine.bucketing.storage.event.ship.implementation.PlatformTeqplayEventByShipReadCache
import nl.teqplay.aisengine.eventhistory.config.PlatformProntoEventByArea
import nl.teqplay.aisengine.eventhistory.config.PlatformProntoEventByShip
import nl.teqplay.aisengine.platform.converter.BerthInformationResolver
import org.springframework.stereotype.Service

@Service
class PlatformProntoEventHistoryService(
    eventByShipReadCache: EventByShipReadCache,
    eventByAreaReadCache: EventByAreaReadCache,
    @PlatformProntoEventByShip platformTeqplayEventByShipReadCache: PlatformTeqplayEventByShipReadCache?,
    @PlatformProntoEventByArea platformTeqplayEventByAreaReadCache: PlatformTeqplayEventByAreaReadCache?,
    csiService: CsiService,
    platformBucketProperties: PlatformBucketProperties,
    validationService: ValidationService,
    berthConverter: BerthInformationResolver
) : PlatformEventHistoryService(
    eventByShipReadCache,
    eventByAreaReadCache,
    platformTeqplayEventByShipReadCache,
    platformTeqplayEventByAreaReadCache,
    csiService,
    platformBucketProperties,
    validationService,
    berthConverter
)
