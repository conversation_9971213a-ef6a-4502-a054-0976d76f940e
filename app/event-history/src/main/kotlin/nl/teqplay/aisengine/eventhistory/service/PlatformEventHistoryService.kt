package nl.teqplay.aisengine.eventhistory.service

import nl.teqplay.aisengine.bucketing.properties.PlatformBucketProperties
import nl.teqplay.aisengine.bucketing.storage.event.area.implementation.EventByAreaReadCache
import nl.teqplay.aisengine.bucketing.storage.event.area.implementation.PlatformTeqplayEventByAreaReadCache
import nl.teqplay.aisengine.bucketing.storage.event.ship.implementation.EventByShipReadCache
import nl.teqplay.aisengine.bucketing.storage.event.ship.implementation.PlatformTeqplayEventByShipReadCache
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.eventhistory.util.convert
import nl.teqplay.aisengine.platform.composeEvents
import nl.teqplay.aisengine.platform.converter.BerthInformationResolver
import nl.teqplay.aisengine.platform.converter.convertToPlatformEvent
import nl.teqplay.aisengine.platform.getAllAsComposedEventView
import nl.teqplay.platform.model.event.ComposedEventView
import nl.teqplay.platform.model.event.TeqplayEvent
import nl.teqplay.skeleton.model.TimeWindow
import reactor.core.publisher.Flux
import reactor.kotlin.core.publisher.toFlux
import kotlin.streams.asStream
import nl.teqplay.platform.resource.params.TimeWindow as PlatformTimeWindow

abstract class PlatformEventHistoryService(
    eventByShipReadCache: EventByShipReadCache,
    eventByAreaReadCache: EventByAreaReadCache,
    platformTeqplayEventByShipReadCache: PlatformTeqplayEventByShipReadCache?,
    platformTeqplayEventByAreaReadCache: PlatformTeqplayEventByAreaReadCache?,
    csiService: CsiService,
    platformBucketProperties: PlatformBucketProperties,
    private val validationService: ValidationService,
    private val berthIdConverter: BerthInformationResolver
) : EventHistoryBaseService<TeqplayEvent>(
    TeqplayEvent::class,
    eventByShipReadCache,
    eventByAreaReadCache,
    platformTeqplayEventByShipReadCache,
    platformTeqplayEventByAreaReadCache,
    csiService,
    platformBucketProperties
) {
    override fun convertToPlatformCompatibleEvent(event: Event): TeqplayEvent? {
        return convertToPlatformEvent(event, berthIdConverter)
    }

    /**
     * Get all events using the provided [window], compose them to ComposedTeqplayEvents and convert them into [ComposedEventView]s.
     *
     * @param window Time window used to find all platform events in
     * @param maxDays The max days of events we want to query
     * @param getPlatformEvents lambda function to get all platform events given the converted [TimeWindow]
     *
     * @return All found events composed as [ComposedEventView]s
     */
    fun getComposedEvents(
        window: PlatformTimeWindow,
        maxDays: Int?,
        getPlatformEvents: (TimeWindow) -> Sequence<TeqplayEvent>,
    ): Flux<ComposedEventView> {
        val convertedWindow = window.convert()
        validationService.validateMaxDays(convertedWindow, maxDays)

        val events = getPlatformEvents(convertedWindow)

        return composeEvents(events)
            .getAllAsComposedEventView()
            .asStream()
            .toFlux()
    }
}
