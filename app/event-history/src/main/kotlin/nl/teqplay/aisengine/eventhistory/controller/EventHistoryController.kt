package nl.teqplay.aisengine.eventhistory.controller

import nl.teqplay.aisengine.aisstream.client.OffsetTimeWindow
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.eventhistory.model.EventHistoryQuery
import nl.teqplay.aisengine.eventhistory.service.EventHistoryService
import nl.teqplay.aisengine.eventhistory.service.ValidationService
import nl.teqplay.skeleton.model.BoundingBox
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.Polygon
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import reactor.core.publisher.Flux

/**
 * Endpoints used to get ship history, both by mmsi and by area.
 */
@RestController
@RequestMapping("/v1/event/history", produces = [MediaType.APPLICATION_NDJSON_VALUE, MediaType.APPLICATION_JSON_VALUE])
class EventHistoryController(
    service: EventHistoryService,
    validationService: ValidationService
) : EventHistoryBaseController<Event>(service, validationService) {

    @GetMapping("/{mmsi}")
    fun findHistoryByMmsi(
        @PathVariable mmsi: Int,
        window: OffsetTimeWindow,
        @RequestParam(required = false, defaultValue = "true") sort: Boolean,
        @RequestParam(required = false) includeEvents: Set<String>? = null,
        @RequestParam(required = false) excludeEvents: Set<String>? = null,
        @RequestParam(required = false) includeAreaTypes: Set<AreaIdentifier.AreaType>?,
        @RequestParam(required = false) excludeAreaTypes: Set<AreaIdentifier.AreaType>?,
        @RequestParam(required = false) maxDays: Int?
    ): Flux<Event> = byMmsi(mmsi, sort, window.toTimeWindow(), maxDays, includeEvents, excludeEvents, includeAreaTypes, excludeAreaTypes)

    @GetMapping("/imo/{imo}")
    fun findHistoryByImo(
        @PathVariable imo: Int,
        window: OffsetTimeWindow,
        @RequestParam(required = false, defaultValue = "true") sort: Boolean,
        @RequestParam(required = false) includeEvents: Set<String>? = null,
        @RequestParam(required = false) excludeEvents: Set<String>? = null,
        @RequestParam(required = false) includeAreaTypes: Set<AreaIdentifier.AreaType>?,
        @RequestParam(required = false) excludeAreaTypes: Set<AreaIdentifier.AreaType>?,
        @RequestParam(required = false) maxDays: Int?
    ): Flux<Event> = byImo(imo, sort, window.toTimeWindow(), maxDays, includeEvents, excludeEvents, includeAreaTypes, excludeAreaTypes)

    @PostMapping("/mmsilist")
    fun findHistoryByMmsis(
        @RequestBody mmsis: List<Int>,
        window: OffsetTimeWindow,
        @RequestParam(required = false, defaultValue = "true") sort: Boolean,
        @RequestParam(required = false) includeEvents: Set<String>? = null,
        @RequestParam(required = false) excludeEvents: Set<String>? = null,
        @RequestParam(required = false) includeAreaTypes: Set<AreaIdentifier.AreaType>?,
        @RequestParam(required = false) excludeAreaTypes: Set<AreaIdentifier.AreaType>?,
        @RequestParam(required = false) maxDays: Int?
    ): Flux<Event> = byMmsis(mmsis, sort, window.toTimeWindow(), maxDays, includeEvents, excludeEvents, includeAreaTypes, excludeAreaTypes)

    @PostMapping("/imolist")
    fun findHistoryByImos(
        @RequestBody imos: List<Int>,
        window: OffsetTimeWindow,
        @RequestParam(required = false, defaultValue = "true") sort: Boolean,
        @RequestParam(required = false) includeEvents: Set<String>? = null,
        @RequestParam(required = false) excludeEvents: Set<String>? = null,
        @RequestParam(required = false) includeAreaTypes: Set<AreaIdentifier.AreaType>?,
        @RequestParam(required = false) excludeAreaTypes: Set<AreaIdentifier.AreaType>?,
        @RequestParam(required = false) maxDays: Int?
    ): Flux<Event> = byImos(imos, sort, window.toTimeWindow(), maxDays, includeEvents, excludeEvents, includeAreaTypes, excludeAreaTypes)

    @PostMapping("/boundingBox")
    fun findHistoryInBoundingBox(
        @RequestBody boundingBox: BoundingBox,
        window: OffsetTimeWindow,
        @RequestParam(required = false, defaultValue = "true") sort: Boolean,
        @RequestParam(required = false) includeEvents: Set<String>? = null,
        @RequestParam(required = false) excludeEvents: Set<String>? = null,
        @RequestParam(required = false) includeAreaTypes: Set<AreaIdentifier.AreaType>?,
        @RequestParam(required = false) excludeAreaTypes: Set<AreaIdentifier.AreaType>?,
        @RequestParam(required = false) maxDays: Int?,
        @RequestParam(required = false) maxAreaInKm2: Double?
    ): Flux<Event> = inBoundingBox(boundingBox, sort, window.toTimeWindow(), maxDays, maxAreaInKm2, includeEvents, excludeEvents, includeAreaTypes, excludeAreaTypes)

    @PostMapping("/polygon")
    fun findHistoryInPolygon(
        @RequestBody polygon: Polygon,
        window: OffsetTimeWindow,
        @RequestParam(required = false, defaultValue = "true") sort: Boolean,
        @RequestParam(required = false) includeEvents: Set<String>? = null,
        @RequestParam(required = false) excludeEvents: Set<String>? = null,
        @RequestParam(required = false) includeAreaTypes: Set<AreaIdentifier.AreaType>?,
        @RequestParam(required = false) excludeAreaTypes: Set<AreaIdentifier.AreaType>?,
        @RequestParam(required = false) maxDays: Int?,
        @RequestParam(required = false) maxAreaInKm2: Double?
    ): Flux<Event> = inPolygon(polygon, sort, window.toTimeWindow(), maxDays, maxAreaInKm2, includeEvents, excludeEvents, includeAreaTypes, excludeAreaTypes)

    @PostMapping("/circle")
    fun findHistoryInCircle(
        @RequestBody center: Location,
        @RequestParam radiusInKm: Double,
        window: OffsetTimeWindow,
        @RequestParam(required = false, defaultValue = "true") sort: Boolean,
        @RequestParam(required = false) includeEvents: Set<String>? = null,
        @RequestParam(required = false) excludeEvents: Set<String>? = null,
        @RequestParam(required = false) includeAreaTypes: Set<AreaIdentifier.AreaType>?,
        @RequestParam(required = false) excludeAreaTypes: Set<AreaIdentifier.AreaType>?,
        @RequestParam(required = false) maxDays: Int?,
        @RequestParam(required = false) maxAreaInKm2: Double?
    ): Flux<Event> = inCircle(center, radiusInKm, sort, window.toTimeWindow(), maxDays, maxAreaInKm2, includeEvents, excludeEvents, includeAreaTypes, excludeAreaTypes)

    @PostMapping("/query")
    fun findHistoryQuery(
        @RequestBody query: EventHistoryQuery
    ): Flux<Event> = byQuery(query)
}
