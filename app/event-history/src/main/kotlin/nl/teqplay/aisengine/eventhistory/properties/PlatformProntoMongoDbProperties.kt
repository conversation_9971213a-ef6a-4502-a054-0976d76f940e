package nl.teqplay.aisengine.eventhistory.properties

import nl.teqplay.skeleton.common.config.MongoDbConfiguration
import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = "mongodb.platform.pronto")
data class PlatformProntoMongoDbProperties(
    override val host: String = "localhost",
    override val port: Int = 27017,
    override val authDb: String = "",
    override val username: String = "",
    override val password: String = "",
    override val db: String = "",
    override val hosts: List<String> = emptyList(),
    override val replicaSetName: String = "",
) : MongoDbConfiguration
