package nl.teqplay.aisengine.eventhistory.service

import nl.teqplay.skeleton.common.exception.BadRequestException
import nl.teqplay.skeleton.model.BoundingBox
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.Polygon
import nl.teqplay.skeleton.model.TimeWindow
import nl.teqplay.skeleton.util.calculatePolygonSurfaceAreaInMeters2
import org.springframework.stereotype.Component
import java.time.Duration
import kotlin.math.PI
import kotlin.math.roundToLong

// TODO move this to some library that can be used by both event history and ais history
@Component
class ValidationService {

    companion object {
        const val DEFAULT_MAX_DAYS = 60

        const val DEFAULT_MAX_AREA_IN_KM2 = 100.0 // km^2
    }

    fun validateMaxDays(
        window: TimeWindow,
        maxDays: Int?
    ) {
        val max = maxDays ?: DEFAULT_MAX_DAYS
        val duration = Duration.between(window.from, window.to)
        val actualDays = duration.toDays()
        if (actualDays > max) {
            throw BadRequestException(
                "Time window exceeds maximum duration of $max days (actual duration: $actualDays days). " +
                    "Set a larger value for 'maxDays' in order to query more days."
            )
        }
    }

    fun validateMaxBoundingBoxArea(
        boundingBox: BoundingBox,
        maxAreaInKm2: Double?,
        sort: Boolean
    ) {
        val polygon = with(boundingBox) {
            Polygon(
                locations = listOf(
                    Location(bottomleft.lat, bottomleft.lon),
                    Location(bottomleft.lat, topright.lon),
                    Location(topright.lat, topright.lon),
                    Location(topright.lat, bottomleft.lon)
                )
            )
        }
        validateMaxPolygonArea(polygon, maxAreaInKm2)
    }

    fun validateMaxPolygonArea(polygon: Polygon, maxAreaInKm2: Double?) {
        if (polygon.locations.isEmpty()) {
            throw BadRequestException("Polygon is empty")
        }

        val actualAreaInKm2 = calculatePolygonSurfaceAreaInMeters2(polygon) / 1e6
        validateActualArea(actualAreaInKm2, maxAreaInKm2)
    }

    fun validateMaxCircleArea(
        radiusInKm: Double,
        maxAreaInKm2: Double?
    ) {
        if (radiusInKm <= 0) {
            throw BadRequestException(
                "Invalid circle radius, must be a positive number. " +
                    "Actual value: $radiusInKm kilometer."
            )
        }

        val actualAreaInKm2 = PI * radiusInKm * radiusInKm
        validateActualArea(actualAreaInKm2, maxAreaInKm2)
    }

    private fun validateActualArea(
        actualAreaInKm2: Double,
        maxAreaInKm2: Double?
    ) {
        val max = maxAreaInKm2 ?: DEFAULT_MAX_AREA_IN_KM2
        if (actualAreaInKm2 > max) {
            throw BadRequestException(
                "Exceeds maximum area of ${max.roundToLong()} km^2 " +
                    "(actual area: ${actualAreaInKm2.roundToLong()} km^2). " +
                    "Set a larger value for the 'maxAreaInKm2' in order to query larger areas."
            )
        }
    }
}
