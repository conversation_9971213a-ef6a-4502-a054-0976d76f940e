package nl.teqplay.aisengine.eventhistory.model

import nl.teqplay.platform.model.Location
import java.util.Date

data class EventHistoryQuery(
    val from: Date,
    val to: Date,
    val polygon: List<Location>? = null,
    val circle: PlatformCircleQuery? = null,
    val mmsis: List<String>? = null,
    val imos: List<String>? = null,
    val filter: String? = null,
    val maxDays: Long? = null,
    val maxAreaInKm2: Long? = null,
    val sort: Boolean? = null
)
