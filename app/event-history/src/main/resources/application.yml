cache:
  ttl:
    imo-mmsi-mapping: PT24H

server:
  error:
    include-message: ALWAYS
  shutdown: GRACEFUL
  compression:
    enabled: true

spring:
  main:
    web-application-type: REACTIVE
  codec:
    # allows for greater input size when receiving (JSON) data in an endpoint
    max-in-memory-size: 10MB

# Prometheus JVM stats exposing
management:
  metrics:
    enable:
      jvm: true
    tags:
      component: event-history-processor
  endpoints:
    web:
      exposure:
        include:
          - health
          - prometheus

springdoc:
  swagger-ui:
    path: /swagger-ui/index.html

auth-credentials-keycloak-s2s:
  domain: keycloakdev.teqplay.nl
  realm: dev
  audience: event-history

auth:
  platform:
    keycloak-url: https://keycloakdev.teqplay.nl
    keycloak-realm: platform

mongodb:
  db: event-history

  platform:
    pronto:
      host:
      auth-db:
      username:
      password:
      db: aiscom
    global:
      host:
      auth-db:
      username:
      password:
      db: aiscom

bucket:
  platform:
    # We don't use platform anymore to get events from the database
    enabled: false

  archive:
    event:
      ship:
        enabled: false
        name: event-history.teqplay
      area:
        enabled: false
        name: event-history.teqplay

csi:
  url: https://csibackend.dev.teqplay.com
  domain: keycloakdev.teqplay.nl
  realm: dev
  client-id: event-history
  client-secret:

poma:
  url:
  domain:
  realm:
  client-id:
  client-secret:
