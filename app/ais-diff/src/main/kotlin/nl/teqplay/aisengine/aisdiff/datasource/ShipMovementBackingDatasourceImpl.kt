package nl.teqplay.aisengine.aisdiff.datasource

import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.aisdiff.model.ShipStartMovementData
import nl.teqplay.aisengine.revents.NotReventsProfile
import nl.teqplay.skeleton.datasource.kmongo.deleteOneById
import nl.teqplay.skeleton.datasource.kmongo.save
import org.springframework.stereotype.Component

@Component
@NotReventsProfile
class ShipMovementBackingDatasourceImpl(
    mongoDatabase: MongoDatabase
) : ShipMovementBackingDatasource {
    private val collection = mongoDatabase.getCollection<ShipStartMovementData>("shipMovement")

    override fun list(): Iterable<ShipStartMovementData> = collection.find().toList()

    override fun save(shipStartMovementData: ShipStartMovementData) {
        collection.save(shipStartMovementData)
    }

    override fun delete(mmsi: Int) {
        collection.deleteOneById(mmsi)
    }
}
