package nl.teqplay.aisengine.aisdiff.datasource

import nl.teqplay.aisengine.aisdiff.model.ShipStartMovementData
import org.springframework.stereotype.Component

/**
 * Data source to persist the [ShipStartMovementData].
 */
@Component
class ShipMovementDatasource(
    private val shipMovementBackingDatasource: ShipMovementBackingDatasource
) {

    private val shipMovementDataMap = shipMovementBackingDatasource.list()
        .associateBy { it.mmsi }
        .toMutableMap()

    fun getStartShipMovement(mmsi: Int): ShipStartMovementData? = shipMovementDataMap[mmsi]

    fun saveStartShipMovement(shipStartMovementData: ShipStartMovementData) {
        shipMovementDataMap[shipStartMovementData.mmsi] = shipStartMovementData
        shipMovementBackingDatasource.save(shipStartMovementData)
    }

    fun deleteStartShipMovement(mmsi: Int) {
        shipMovementDataMap.remove(mmsi)
        shipMovementBackingDatasource.delete(mmsi)
    }
}
