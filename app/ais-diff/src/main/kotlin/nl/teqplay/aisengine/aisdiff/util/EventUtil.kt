package nl.teqplay.aisengine.aisdiff.util

import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.aisstream.model.AisMessage
import nl.teqplay.aisengine.event.model.AisDestinationChangedEvent
import nl.teqplay.aisengine.event.model.AisDraughtChangedEvent
import nl.teqplay.aisengine.event.model.AisEtaChangedEvent
import nl.teqplay.aisengine.event.model.AisStatusChangedEvent
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import java.time.Instant
import java.util.UUID

fun createAisDestinationChangedEvent(
    message: AisDiffMessage,
    newValue: String?
): AisDestinationChangedEvent {
    return AisDestinationChangedEvent(
        _id = UUID.randomUUID().toString(),
        actualTime = message.messageTime,
        ship = AisShipIdentifier(message.mmsi, message.imo.latest()),
        location = message.location.latest(),
        oldValue = message.destination.old,
        newValue = newValue
    )
}

fun createAisDraughtChangedEvent(
    message: AisDiffMessage,
    newValue: Float?
): AisDraughtChangedEvent {
    return AisDraughtChangedEvent(
        _id = UUID.randomUUID().toString(),
        actualTime = message.messageTime,
        ship = AisShipIdentifier(message.mmsi, message.imo.latest()),
        location = message.location.latest(),
        oldValue = message.draught.old,
        newValue = newValue
    )
}

fun createAisEtaChangedEvent(
    message: AisDiffMessage,
    newValue: Instant?
): AisEtaChangedEvent {
    return AisEtaChangedEvent(
        _id = UUID.randomUUID().toString(),
        actualTime = message.messageTime,
        ship = AisShipIdentifier(message.mmsi, message.imo.latest()),
        location = message.location.latest(),
        oldValue = message.eta.old,
        newValue = newValue
    )
}

fun createAisStatusChangedEvent(
    message: AisDiffMessage,
    newValue: AisMessage.ShipStatus
): AisStatusChangedEvent {
    return AisStatusChangedEvent(
        _id = UUID.randomUUID().toString(),
        actualTime = message.messageTime,
        ship = AisShipIdentifier(message.mmsi, message.imo.latest()),
        location = message.location.latest(),
        oldValue = message.status.old,
        newValue = newValue
    )
}
