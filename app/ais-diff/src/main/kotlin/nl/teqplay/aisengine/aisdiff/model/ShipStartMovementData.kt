package nl.teqplay.aisengine.aisdiff.model

import nl.teqplay.aisengine.aisdiff.model.ShipStartMovementData.MessageSnapshot
import nl.teqplay.skeleton.model.Location
import org.bson.codecs.pojo.annotations.BsonId
import java.time.Instant

/** Data class that hold ship start event reference of MMSI and startEventId with the state in the database. */
data class ShipStartMovementData(
    @BsonId
    val mmsi: Int,

    /**
     * Contains the [MessageSnapshot] at the initial time the speed was sufficient.
     */
    val snapshotAtSufficientSpeed: MessageSnapshot?,

    /**
     * Contains the [MessageSnapshot] at the initial time there was no speed.
     */
    val snapshotAtNoSpeed: MessageSnapshot?,

    /**
     * Contains the [startEventId] if a start event was fired.
     */
    val startEventId: String?
) {
    data class MessageSnapshot(
        val messageTime: Instant,
        val location: Location
    )
}
