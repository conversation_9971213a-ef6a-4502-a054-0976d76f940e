package nl.teqplay.aisengine.aisdiff.datasource

import nl.teqplay.aisengine.aisdiff.model.ShipStartMovementData
import nl.teqplay.aisengine.revents.ReventsProfile
import org.springframework.stereotype.Component

@Component
@ReventsProfile
class ShipMovementBackingDatasourceReventsImpl : ShipMovementBackingDatasource {

    override fun list(): Iterable<ShipStartMovementData> = emptyList<ShipStartMovementData>().asIterable()

    override fun save(shipStartMovementData: ShipStartMovementData) {
    }

    override fun delete(mmsi: Int) {
    }
}
