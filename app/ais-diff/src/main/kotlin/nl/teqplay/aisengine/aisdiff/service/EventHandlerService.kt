package nl.teqplay.aisengine.aisdiff.service

import io.micrometer.core.instrument.MeterRegistry
import jakarta.annotation.PostConstruct
import nl.teqplay.aisengine.aisdiff.util.createAisDestinationChangedEvent
import nl.teqplay.aisengine.aisdiff.util.createAisDraughtChangedEvent
import nl.teqplay.aisengine.aisdiff.util.createAisEtaChangedEvent
import nl.teqplay.aisengine.aisdiff.util.createAisStatusChangedEvent
import nl.teqplay.aisengine.aisstream.model.AisDiffField
import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.event.interfaces.AisDiffEvent
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.aisengine.nats.stream.revents.model.ReventsAisStreamOptions
import nl.teqplay.skeleton.metrics.Metric
import nl.teqplay.skeleton.metrics.MetricRegistry
import nl.teqplay.skeleton.nats.NatsConsumerStream
import org.springframework.stereotype.Component
import java.util.concurrent.atomic.AtomicLong
import kotlin.reflect.KFunction2
import kotlin.reflect.KProperty1

@Component
class EventHandlerService(
    private val eventStreamService: EventStreamService,
    private val shipMovementEventProcessor: AisShipMovementEventProcessor,
    private val diffConsumerStream: NatsConsumerStream<AisDiffMessage>,
    meterRegistry: MeterRegistry
) {

    private val registry = MetricRegistry.of<EventHandlerService>(meterRegistry, listOf("event-type"))
    private val aisDiffMessageInputMetric = registry.createGaugeWithClass(Metric.MESSAGE_COUNT_INPUT, AtomicLong(), AisDiffMessage::class)

    /**
     * This function pulls [AisDiffMessage] from nats' ais-stream:diff message stream at configurable interval
     */
    @PostConstruct
    fun receive() {
        eventStreamService.consume(
            stream = diffConsumerStream,
            revents = ReventsAisStreamOptions(
                includeServiceVessels = false
            )
        ) { aisDiffMessage, message ->
            onMessage(aisDiffMessage)
            message.ack()
        }
    }

    /**
     * This method processes all [AisDiffMessage] received from ais-stream:diff message stream
     */
    fun onMessage(message: AisDiffMessage) {
        aisDiffMessageInputMetric.incrementAndGet()

        message.publishDiffEventOnChanged(AisDiffMessage::destination, ::createAisDestinationChangedEvent)
            .publishDiffEventOnChanged(AisDiffMessage::draught, ::createAisDraughtChangedEvent)
            .publishDiffEventOnChanged(AisDiffMessage::eta, ::createAisEtaChangedEvent)
            .publishDiffEventOnChanged(AisDiffMessage::status, ::createAisStatusChangedEvent)

        shipMovementEventProcessor.onMessage(message)
            ?.also(eventStreamService::publish)
    }

    /**
     * Check if the value of the provided [diffField] has changed. If so create a new event via the [onChanged]
     *  lambda and publish it using the  [eventStreamService].
     *
     * @param T The input type
     * @param U The output type
     * @param diffField The field containing the potential value change
     * @param onCreateDiffEvent Lambda to create the diff event
     * @return The [AisDiffMessage] so we can chain multiple [publishDiffEventOnChanged] functions
     */
    fun <T, U : T> AisDiffMessage.publishDiffEventOnChanged(
        diffField: KProperty1<AisDiffMessage, AisDiffField<T, U>>,
        onCreateDiffEvent: KFunction2<AisDiffMessage, U, AisDiffEvent<*>>
    ): AisDiffMessage {
        val diffFieldValue = diffField.get(this)
        val hasChanged = diffFieldValue.changed

        if (hasChanged != null && diffFieldValue.old != hasChanged.new) {
            val event = onCreateDiffEvent(this, hasChanged.new)
            publishDiffEvent(event)
        }

        return this
    }

    fun <T : AisDiffEvent<*>> publishDiffEvent(event: T) {
        eventStreamService.publish(event)
        registry.getOrCreateGauge(Metric.MESSAGE_COUNT_OUTPUT, event.getSubject()).incrementAndGet()
    }
}
