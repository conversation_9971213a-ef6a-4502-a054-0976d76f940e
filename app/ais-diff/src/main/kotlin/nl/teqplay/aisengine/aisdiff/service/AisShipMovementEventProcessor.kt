package nl.teqplay.aisengine.aisdiff.service

import nl.teqplay.aisengine.aisdiff.datasource.ShipMovementDatasource
import nl.teqplay.aisengine.aisdiff.model.ShipStartMovementData
import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.model.ShipMovingEndEvent
import nl.teqplay.aisengine.event.model.ShipMovingStartEvent
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import org.springframework.stereotype.Component
import java.time.Duration
import java.util.UUID

/**
 * This event processor implementation class handles the processing of speedOverGround or location changes
 * in [AisDiffMessage] diff events
 */
@Component
class AisShipMovementEventProcessor(private val shipMovementDatasource: ShipMovementDatasource) {
    companion object {
        /**
         * The min allowed speed when considering if a ship started moving
         */
        const val MOVING_MIN_SPEED = 4.5f

        /**
         * The max allowed speed when considering if a ship stopped moving
         */
        const val STOPPING_MAX_SPEED = 1.0f

        /**
         * The minimum threshold when a movement increase is established
         */
        private val MOVEMENT_THRESHOLD: Duration = Duration.ofMinutes(5)
    }

    /**
     * This method processes [AisDiffMessage] to determine if there was a change in speedOverGround or location, if so, call the appropriate process..(...) method that'll
     * return an object of [ShipMovingStartEvent] or [ShipMovingEndEvent]; otherwise returns null
     */
    fun onMessage(message: AisDiffMessage): Event? {
        val state = shipMovementDatasource.getStartShipMovement(message.mmsi)
        if (state?.startEventId == null) {
            return processStartEvent(state, message)
        }
        return processEndEvent(state, message)
    }

    /**
     * This method processes [AisDiffMessage] for change in speedOverGround or location, and
     * return an object of [ShipMovingStartEvent] if speedOverGround meets the valid change predicate; otherwise returns null
     */
    fun processStartEvent(state: ShipStartMovementData?, message: AisDiffMessage): ShipMovingStartEvent? {
        // We can skip this diff event if this is the first message we received from this ship
        val oldMessageTime = message.oldMessageTime ?: return null

        val newMessageTime = message.messageTime
        val newSpeedOverGround = message.speedOverGround.latest()
        val oldSpeedOverGround = message.speedOverGround.old

        var snapshotAtSufficientSpeed = state?.snapshotAtSufficientSpeed
        if (isSpeedOverGroundFastEnough(oldSpeedOverGround) &&
            isSpeedOverGroundFastEnough(newSpeedOverGround)
        ) {
            if (snapshotAtSufficientSpeed == null) {
                snapshotAtSufficientSpeed = ShipStartMovementData.MessageSnapshot(
                    messageTime = oldMessageTime,
                    location = message.location.old,
                )
            }
            if (Duration.between(snapshotAtSufficientSpeed.messageTime, newMessageTime) >= MOVEMENT_THRESHOLD) {
                val startEventId = UUID.randomUUID().toString()

                shipMovementDatasource.saveStartShipMovement(
                    ShipStartMovementData(
                        mmsi = message.mmsi,
                        snapshotAtSufficientSpeed = null,
                        snapshotAtNoSpeed = null,
                        startEventId = startEventId
                    )
                )

                return ShipMovingStartEvent(
                    _id = startEventId,
                    ship = AisShipIdentifier(message.mmsi, message.imo.latest()),

                    // The old timestamp and location is taken here to be as close to the point where the ship started moving
                    actualTime = snapshotAtSufficientSpeed.messageTime,
                    location = snapshotAtSufficientSpeed.location
                )
            } else if (state?.snapshotAtSufficientSpeed == null) {
                shipMovementDatasource.saveStartShipMovement(
                    ShipStartMovementData(
                        mmsi = message.mmsi,
                        snapshotAtSufficientSpeed = snapshotAtSufficientSpeed,
                        snapshotAtNoSpeed = null,
                        startEventId = null
                    )
                )
            }
        } else if (state != null && snapshotAtSufficientSpeed != null) {
            // reset snapshot, since the threshold isn't held anymore
            shipMovementDatasource.saveStartShipMovement(state.copy(snapshotAtSufficientSpeed = null))
        }

        return null
    }

    /**
     * This method processes [AisDiffMessage] and returns an object of [ShipMovingEndEvent] when there's a pending [ShipMovingStartEvent] in the database with same [AisDiffMessage].mmsi, a valid change in speedOverGround and messageTime is not null,
     * otherwise return null
     */
    fun processEndEvent(
        state: ShipStartMovementData,
        message: AisDiffMessage
    ): ShipMovingEndEvent? {
        // We can skip this diff event if this is the first message we received from this ship
        val oldMessageTime = message.oldMessageTime ?: return null

        val newMessageTime = message.messageTime
        val newSpeedOverGround = message.speedOverGround.latest()
        val oldSpeedOverGround = message.speedOverGround.old

        var snapshotAtNoSpeed = state.snapshotAtNoSpeed
        if (isSpeedOverGroundSlowEnough(oldSpeedOverGround) &&
            isSpeedOverGroundSlowEnough(newSpeedOverGround)
        ) {
            if (snapshotAtNoSpeed == null) {
                snapshotAtNoSpeed = ShipStartMovementData.MessageSnapshot(
                    messageTime = oldMessageTime,
                    location = message.location.old,
                )
            }
            if (Duration.between(snapshotAtNoSpeed.messageTime, newMessageTime) >= MOVEMENT_THRESHOLD) {
                shipMovementDatasource.deleteStartShipMovement(message.mmsi)

                return ShipMovingEndEvent(
                    _id = UUID.randomUUID().toString(),
                    startEventId = state.startEventId,
                    ship = AisShipIdentifier(message.mmsi, message.imo.latest()),

                    // The old timestamp and location is taken here to be as close to the point where the ship started moving
                    actualTime = snapshotAtNoSpeed.messageTime,
                    location = snapshotAtNoSpeed.location
                )
            } else if (state.snapshotAtNoSpeed == null) {
                shipMovementDatasource.saveStartShipMovement(state.copy(snapshotAtNoSpeed = snapshotAtNoSpeed))
            }
        } else if (snapshotAtNoSpeed != null) {
            // reset snapshot, since the threshold isn't held anymore
            shipMovementDatasource.saveStartShipMovement(state.copy(snapshotAtNoSpeed = null))
        }
        return null
    }

    /**
     * Helper function to check if the ship is moving based on the speed over ground.
     *
     * @param speedOverGround The provided speed over ground of the ship.
     * @return true when the speed over ground of the ship is large enough to be considered as moving.
     */
    private fun isSpeedOverGroundFastEnough(speedOverGround: Float?): Boolean {
        if (speedOverGround == null) {
            return false
        }

        return speedOverGround >= MOVING_MIN_SPEED
    }

    /**
     * Helper function to check if the ship is not moving based on the speed over ground.
     *
     * @param speedOverGround The provided speed over ground of the ship.
     * @return true when the speed over ground of the ship is small enough to be considered as laying still.
     */
    private fun isSpeedOverGroundSlowEnough(speedOverGround: Float?): Boolean {
        if (speedOverGround == null) {
            return false
        }

        return speedOverGround <= STOPPING_MAX_SPEED
    }
}
