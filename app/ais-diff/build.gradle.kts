buildscript {
    project.extra.set("baseVersion", "2.1.0")
}

plugins {
    id("ais-engine.kotlin-app-webmvc-conventions")
    id("org.cyclonedx.bom") version cyclonedxBomVersion
}

dependencies {
    implementation(project(":api:nats-stream-ais-consume-diff"))
    implementation(project(":api:nats-stream-event"))

    implementation("nl.teqplay.skeleton:datasource2:$skeletonVersion")
    testImplementation("nl.teqplay.skeleton:nats-test:$skeletonVersion")
}

testing {
    suites {
        named("integrationTest", JvmTestSuite::class).configure {
            dependencies {
                implementation("org.testcontainers:mongodb:$testContainersVersion")
                implementation("io.nats:jnats:$jnatsVersion")
                implementation(project(":lib:common"))
                implementation("com.fasterxml.jackson.core:jackson-databind:$jacksonVersion")
                implementation("com.fasterxml.jackson.core:jackson-annotations:$jacksonVersion")
                implementation("com.fasterxml.jackson.module:jackson-module-kotlin:$jacksonVersion")
            }

            targets.configureEach {
                testTask.configure {
                    testLogging.showStandardStreams = true
                }
            }
        }
    }
}

tasks.cyclonedxBom {
    setIncludeConfigs(listOf("runtimeClasspath"))
    setOutputFormat("json")
    setOutputName("bom")
}
