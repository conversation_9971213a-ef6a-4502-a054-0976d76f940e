package nl.teqplay.aisengine.shiphistory.config

import com.fasterxml.jackson.databind.ObjectMapper
import com.mongodb.kotlin.client.MongoClient
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.shiphistory.properties.PlatformMongoDbProperties
import nl.teqplay.skeleton.datasource.MongoDbBuilder
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
@ConditionalOnProperty(prefix = "bucket.platform", name = ["enabled"], havingValue = "true")
class PlatformMongoDbConfiguration(
    private val platformMongoDbProperties: PlatformMongoDbProperties
) {

    @Bean(PLATFORM_MONGO_CLIENT)
    fun platformMongoClient(objectMapper: ObjectMapper): MongoClient {
        val mongoJackObjectMapper = MongoDbBuilder.configureObjectMapper(objectMapper.copy())
        return MongoDbBuilder.mongoClient(platformMongoDbProperties, mongoJackObjectMapper)
    }

    @Bean(PLATFORM_MONGO_DATABASE)
    fun platformMongoDatabase(
        @PlatformMongoClient client: MongoClient
    ): MongoDatabase = MongoDbBuilder.mongoDatabase(client, platformMongoDbProperties)
}

const val PLATFORM_MONGO_CLIENT = "platformMongoClient"
const val PLATFORM_MONGO_DATABASE = "platformMongoDatabase"

@Qualifier(PLATFORM_MONGO_CLIENT)
annotation class PlatformMongoClient

@Qualifier(PLATFORM_MONGO_DATABASE)
annotation class PlatformMongoDatabase
