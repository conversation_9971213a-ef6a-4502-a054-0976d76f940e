package nl.teqplay.aisengine.shiphistory

import io.swagger.v3.oas.annotations.OpenAPIDefinition
import nl.teqplay.aisengine.ObjectMapperConfiguration
import nl.teqplay.skeleton.common.logging.EnableOutgoingRequestLogging
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication
import org.springframework.context.annotation.Import

@SpringBootApplication(exclude = [MongoAutoConfiguration::class])
@ConfigurationPropertiesScan
@OpenAPIDefinition
@Import(ObjectMapperConfiguration::class) // TODO should be auto configured, not working in reactive mode, see PRA-613
@EnableOutgoingRequestLogging
class ShipHistory

fun main(args: Array<String>) {
    runApplication<ShipHistory>(*args)
}
