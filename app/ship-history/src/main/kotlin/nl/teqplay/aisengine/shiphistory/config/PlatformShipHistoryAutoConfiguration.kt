package nl.teqplay.aisengine.shiphistory.config

import com.fasterxml.jackson.databind.ObjectMapper
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveGlobalProperties
import nl.teqplay.aisengine.bucketing.properties.BucketProperties
import nl.teqplay.aisengine.bucketing.properties.PlatformBucketProperties
import nl.teqplay.aisengine.bucketing.properties.PlatformShipHistoryByAreaArchiveProperties
import nl.teqplay.aisengine.bucketing.properties.PlatformShipHistoryByMmsiArchiveProperties
import nl.teqplay.aisengine.bucketing.storage.cache.impl.ArchiveStorageFactory
import nl.teqplay.aisengine.bucketing.storage.cache.impl.BucketCacheFactory
import nl.teqplay.aisengine.bucketing.storage.ship.area.implementation.PlatformShipHistoryByAreaReadCache
import nl.teqplay.aisengine.bucketing.storage.ship.mmsi.implementation.PlatformShipHistoryByMmsiReadCache
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class PlatformShipHistoryAutoConfiguration {

    @Bean
    fun platformShipHistoryByMmsiReadCache(
        @PlatformMongoDatabase platformMongoDatabase: MongoDatabase?,
        objectMapper: ObjectMapper,
        bucket: BucketProperties,
        archive: PlatformShipHistoryByMmsiArchiveProperties,
        archiveGlobal: BucketArchiveGlobalProperties,
        platformBucketProperties: PlatformBucketProperties
    ): PlatformShipHistoryByMmsiReadCache? {
        if (platformMongoDatabase == null) {
            return null
        }
        return PlatformShipHistoryByMmsiReadCache(
            bucketCacheFactory = BucketCacheFactory,
            archiveStorageFactory = ArchiveStorageFactory,
            mongoDatabase = platformMongoDatabase,
            objectMapper = objectMapper,
            bucket = bucket,
            archive = archive,
            archiveGlobal = archiveGlobal,
            platformBucketProperties = platformBucketProperties
        )
    }

    @Bean
    fun platformShipHistoryByAreaReadCache(
        @PlatformMongoDatabase platformMongoDatabase: MongoDatabase?,
        objectMapper: ObjectMapper,
        bucket: BucketProperties,
        archive: PlatformShipHistoryByAreaArchiveProperties,
        archiveGlobal: BucketArchiveGlobalProperties,
        platformBucketProperties: PlatformBucketProperties
    ): PlatformShipHistoryByAreaReadCache? {
        if (platformMongoDatabase == null) {
            return null
        }
        return PlatformShipHistoryByAreaReadCache(
            bucketCacheFactory = BucketCacheFactory,
            archiveStorageFactory = ArchiveStorageFactory,
            mongoDatabase = platformMongoDatabase,
            objectMapper = objectMapper,
            bucket = bucket,
            archive = archive,
            archiveGlobal = archiveGlobal,
            platformBucketProperties = platformBucketProperties
        )
    }
}
