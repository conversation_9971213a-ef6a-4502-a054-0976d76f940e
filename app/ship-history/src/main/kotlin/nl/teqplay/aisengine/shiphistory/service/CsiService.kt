package nl.teqplay.aisengine.shiphistory.service

import io.github.oshai.kotlinlogging.KotlinLogging
import jakarta.annotation.PostConstruct
import nl.teqplay.csi.model.ship.info.component.ShipIdentifiers
import nl.teqplay.csi.model.ship.info.component.ShipRole
import nl.teqplay.platform.model.ShipInfo
import nl.teqplay.skeleton.csi.client.CsiShipClient
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.util.concurrent.TimeUnit
import java.util.concurrent.locks.ReentrantReadWriteLock
import kotlin.concurrent.read
import kotlin.concurrent.write
import kotlin.system.measureTimeMillis

private val LOG = KotlinLogging.logger { }

@Component
class CsiService(
    private val csiShipClient: CsiShipClient
) {
    private val csiLock = ReentrantReadWriteLock()

    private var rolesMap = emptyMap<String, ShipRole>()
    private var eniMap = emptyMap<String, String>()

    /**
     * Download data from CSI at startup and every 4 hours after that to receive updates.
     */
    @PostConstruct
    @Scheduled(fixedRate = 4, initialDelay = 4, timeUnit = TimeUnit.HOURS)
    fun updateRoles() {
        measureTimeMillis {
            val ships = csiShipClient.listShipRegisterInfoCache()
            LOG.info { "Loading CSI ships into state" }

            val newRolesMap = mutableMapOf<String, ShipRole>()
            val newEniMap = mutableMapOf<String, String>()

            ships.forEach {
                val mmsi = it.identifiers.mmsi?.ifBlank { null }
                if (mmsi != null) {
                    val role = it.types.role
                    if (role != null && role != ShipRole.NONE) newRolesMap[mmsi] = role

                    val eni = it.identifiers.eni?.ifBlank { null }
                    if (eni != null) newEniMap[mmsi] = eni
                }
            }

            csiLock.write {
                rolesMap = newRolesMap
                eniMap = newEniMap
            }
        }.also {
            LOG.info { "Finished loading CSI state in $it ms." }
        }
    }

    /**
     * Get the [ShipRole] for the vessel with the given [mmsi]. Will return [ShipRole.NONE] if the vessel is not known.
     */
    fun getRoleByMmsi(mmsi: String): ShipInfo.ShipRole {
        return csiLock.read {
            when (rolesMap[mmsi] ?: ShipRole.NONE) {
                ShipRole.PILOT -> ShipInfo.ShipRole.PILOT
                ShipRole.TUG -> ShipInfo.ShipRole.TUG
                ShipRole.WASTE -> ShipInfo.ShipRole.WASTE
                ShipRole.WATER -> ShipInfo.ShipRole.WATER
                ShipRole.BUNKER -> ShipInfo.ShipRole.BUNKER
                ShipRole.BOATMAN -> ShipInfo.ShipRole.BOATMAN
                ShipRole.AUTHORITIES -> ShipInfo.ShipRole.AUTHORITIES
                ShipRole.CUSTOMS -> ShipInfo.ShipRole.CUSTOMS
                ShipRole.PORTAUTHORITIES -> ShipInfo.ShipRole.PORTAUTHORITIES
                ShipRole.PORTPOLICE -> ShipInfo.ShipRole.PORTPOLICE
                ShipRole.COASTGUARD -> ShipInfo.ShipRole.COASTGUARD
                ShipRole.SWOG -> ShipInfo.ShipRole.SWOG
                ShipRole.TENDER -> ShipInfo.ShipRole.TENDER
                ShipRole.FENDER -> ShipInfo.ShipRole.FENDER
                ShipRole.CRANE -> ShipInfo.ShipRole.CRANE
                ShipRole.SUPPLYBARGE -> ShipInfo.ShipRole.SUPPLYBARGE
                ShipRole.CARGOBARGE -> ShipInfo.ShipRole.CARGOBARGE
                ShipRole.TANKERBARGE -> ShipInfo.ShipRole.TANKERBARGE
                ShipRole.PUSHBARGE -> ShipInfo.ShipRole.PUSHBARGE
                ShipRole.SURVEY -> ShipInfo.ShipRole.SURVEY
                ShipRole.BARGE_BUNKER -> ShipInfo.ShipRole.BARGE_BUNKER
                ShipRole.LUBES -> ShipInfo.ShipRole.LUBES
                ShipRole.DIVE -> ShipInfo.ShipRole.DIVE
                ShipRole.NONE -> ShipInfo.ShipRole.NONE
            }
        }
    }

    /**
     * Get the [ShipIdentifiers.eni] for the vessel with the given [mmsi].
     */
    fun getEniByMmsi(mmsi: String): String? {
        return csiLock.read {
            eniMap[mmsi]
        }
    }
}
