package nl.teqplay.aisengine.shiphistory.controller.ship.history

import nl.teqplay.aisengine.aisstream.client.OffsetTimeWindow
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessage
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessageMmsiQuery
import nl.teqplay.aisengine.shiphistory.service.AisHistoricMessageService
import nl.teqplay.aisengine.shiphistory.service.ShipStateSearchService
import nl.teqplay.aisengine.shiphistory.service.ValidationService
import nl.teqplay.skeleton.common.exception.NotFoundException
import nl.teqplay.skeleton.model.BoundingBox
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.Polygon
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import reactor.core.publisher.Flux

/**
 * Endpoints used to get ship history, both by mmsi and by area.
 */
@RestController
@RequestMapping("/v1/ship/history", produces = [MediaType.APPLICATION_NDJSON_VALUE, MediaType.APPLICATION_JSON_VALUE])
class AisHistoricMessageController(
    private val aisHistoricMessageService: AisHistoricMessageService,
    private val shipStateSearchService: ShipStateSearchService,
    private val validationService: ValidationService
) {

    @GetMapping("/mmsi/{mmsi}")
    fun findHistoryByMmsi(
        @PathVariable mmsi: Int,
        window: OffsetTimeWindow,
        @RequestParam(required = false) maxDays: Int?
    ): Flux<AisHistoricMessage> {
        if (shipStateSearchService.getByMmsi(mmsi) == null) {
            throw NotFoundException("Ship with mmsi $mmsi not found.")
        }
        val utcWindow = window.toTimeWindow()
        validationService.validateMaxDays(utcWindow, maxDays)
        return aisHistoricMessageService.findHistoryByMmsi(mmsi, utcWindow)
    }

    @PostMapping("/mmsi")
    fun findHistoryByMmsiQuery(
        @RequestBody query: List<AisHistoricMessageMmsiQuery>,
        @RequestParam(required = false) maxDays: Int?,
        @RequestParam(required = false, defaultValue = "true") sort: Boolean
    ): Flux<AisHistoricMessage> {
        validationService.validateMmsiQuery(query, maxDays, sort)

        val buckets = query
            .map { aisHistoricMessageService.findHistoryByMmsi(it.mmsi, it.window) }
            .toTypedArray()

        return when {
            sort -> Flux.mergeComparing(compareBy(AisHistoricMessage::messageTime), *buckets)
            else -> Flux.concat(*buckets)
        }
    }

    @PostMapping("/boundingBox")
    fun findHistoryInBoundingBox(
        @RequestBody boundingBox: BoundingBox,
        window: OffsetTimeWindow,
        @RequestParam(required = false) maxDays: Int?,
        @RequestParam(required = false) maxAreaInKm2: Double?,
        @RequestParam(required = false, defaultValue = "true") sort: Boolean
    ): Flux<AisHistoricMessage> {
        val utcWindow = window.toTimeWindow()
        validationService.validateMaxDays(utcWindow, maxDays)
        validationService.validateMaxBoundingBoxArea(boundingBox, maxAreaInKm2, sort)
        return aisHistoricMessageService.findHistoryInBoundingBox(boundingBox, utcWindow, sort)
    }

    @PostMapping("/polygon")
    fun findHistoryInPolygon(
        @RequestBody polygon: Polygon,
        window: OffsetTimeWindow,
        @RequestParam(required = false) maxDays: Int?,
        @RequestParam(required = false) maxAreaInKm2: Double?,
        @RequestParam(required = false, defaultValue = "true") sort: Boolean
    ): Flux<AisHistoricMessage> {
        val utcWindow = window.toTimeWindow()
        validationService.validateMaxDays(utcWindow, maxDays)
        validationService.validateMaxPolygonArea(polygon, maxAreaInKm2, sort)
        return aisHistoricMessageService.findHistoryInPolygon(polygon, utcWindow, sort)
    }

    @PostMapping("/circle")
    fun findHistoryInCircle(
        @RequestBody center: Location,
        @RequestParam radiusInKm: Double,
        window: OffsetTimeWindow,
        @RequestParam(required = false) maxDays: Int?,
        @RequestParam(required = false) maxAreaInKm2: Double?,
        @RequestParam(required = false, defaultValue = "true") sort: Boolean
    ): Flux<AisHistoricMessage> {
        val utcWindow = window.toTimeWindow()
        validationService.validateMaxDays(utcWindow, maxDays)
        validationService.validateMaxCircleArea(radiusInKm, maxAreaInKm2, sort)
        return aisHistoricMessageService.findHistoryInCircle(center, radiusInKm, utcWindow, sort)
    }
}
