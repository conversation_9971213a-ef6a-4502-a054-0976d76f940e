package nl.teqplay.aisengine.shiphistory.config

import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.context.annotation.Configuration
import org.springframework.web.server.ServerWebExchange
import org.springframework.web.server.WebFilter
import org.springframework.web.server.WebFilterChain
import reactor.core.publisher.Mono

@Configuration
class IncomingRequestLoggerWebFilter : WebFilter {
    private val log = KotlinLogging.logger {}

    override fun filter(exchange: ServerWebExchange, chain: WebFilterChain): Mono<Void> {
        val request = exchange.request

        val uri = request.uri.path
        log.info { "<== $uri" }
        val start = System.currentTimeMillis()

        // Execute the request, then we can check how long it took
        return chain.filter(exchange)
            .doOnTerminate {
                val duration = System.currentTimeMillis() - start
                val response = exchange.response

                log.info { "==> ${response.statusCode} $uri  (${duration}ms)" }
            }
    }
}
