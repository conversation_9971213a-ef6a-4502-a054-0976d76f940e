package nl.teqplay.aisengine.shiphistory.service

import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessageMmsiQuery
import nl.teqplay.skeleton.common.exception.BadRequestException
import nl.teqplay.skeleton.model.BoundingBox
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.Polygon
import nl.teqplay.skeleton.model.TimeWindow
import nl.teqplay.skeleton.util.calculatePolygonSurfaceAreaInMeters2
import org.springframework.stereotype.Component
import java.time.Duration
import kotlin.math.PI
import kotlin.math.roundToLong

@Component
class ValidationService {

    companion object {
        const val DEFAULT_MAX_DAYS = 60
        const val DEFAULT_MAX_SORTABLE_DAYS = 3000

        const val DEFAULT_MAX_AREA_IN_KM2 = 100.0 // km^2
        const val DEFAULT_MAX_SORTABLE_AREA_IN_KM2 = DEFAULT_MAX_AREA_IN_KM2
    }

    fun validateMaxDays(
        window: TimeWindow,
        maxDays: Int?
    ) {
        val max = maxDays ?: DEFAULT_MAX_DAYS
        val duration = Duration.between(window.from, window.to)
        val actualDays = duration.toDays()
        if (actualDays > max) {
            throw BadRequestException(
                "Time window exceeds maximum duration of $max days (actual duration: $actualDays days). " +
                    "Set a larger value for 'maxDays' in order to query more days."
            )
        }
    }

    fun validateMmsiQuery(
        query: List<AisHistoricMessageMmsiQuery>,
        maxDays: Int?,
        sort: Boolean
    ) {
        if (query.isEmpty()) {
            throw BadRequestException("query list is empty")
        }
        query.forEach { validateMaxDays(it.window, maxDays) }

        if (sort) {
            val totalDays = query.sumOf { Duration.between(it.window.from, it.window.to).toDays() }
            if (totalDays > DEFAULT_MAX_SORTABLE_DAYS) {
                throw BadRequestException(
                    "Exceeds maximum sortable amount of days. " +
                        "(actual days: $totalDays, maximum days: $DEFAULT_MAX_SORTABLE_DAYS). " +
                        "Disable sorting by setting 'sort' to false, or query a smaller number of ships or smaller time windows."
                )
            }
        }
    }

    fun validateMaxBoundingBoxArea(
        boundingBox: BoundingBox,
        maxAreaInKm2: Double?,
        sort: Boolean
    ) {
        val polygon = with(boundingBox) {
            Polygon(
                locations = listOf(
                    Location(bottomleft.lat, bottomleft.lon),
                    Location(bottomleft.lat, topright.lon),
                    Location(topright.lat, topright.lon),
                    Location(topright.lat, bottomleft.lon)
                )
            )
        }
        validateMaxPolygonArea(polygon, maxAreaInKm2, sort)
    }

    fun validateMaxPolygonArea(polygon: Polygon, maxAreaInKm2: Double?, sort: Boolean) {
        if (polygon.locations.isEmpty()) {
            throw BadRequestException("Polygon is empty")
        }

        val actualAreaInKm2 = calculatePolygonSurfaceAreaInMeters2(polygon) / 1e6
        validateActualArea(actualAreaInKm2, maxAreaInKm2, sort)
    }

    fun validateMaxCircleArea(
        radiusInKm: Double,
        maxAreaInKm2: Double?,
        sort: Boolean
    ) {
        if (radiusInKm <= 0) {
            throw BadRequestException(
                "Invalid circle radius, must be a positive number. " +
                    "Actual value: $radiusInKm kilometer."
            )
        }

        val actualAreaInKm2 = PI * radiusInKm * radiusInKm
        validateActualArea(actualAreaInKm2, maxAreaInKm2, sort)
    }

    private fun validateActualArea(
        actualAreaInKm2: Double,
        maxAreaInKm2: Double?,
        sort: Boolean
    ) {
        val max = maxAreaInKm2 ?: DEFAULT_MAX_AREA_IN_KM2
        if (actualAreaInKm2 > max) {
            throw BadRequestException(
                "Exceeds maximum area of ${max.roundToLong()} km^2 " +
                    "(actual area: ${actualAreaInKm2.roundToLong()} km^2). " +
                    "Set a larger value for the 'maxAreaInKm2' in order to query larger areas."
            )
        }

        if (sort && actualAreaInKm2 > DEFAULT_MAX_SORTABLE_AREA_IN_KM2) {
            throw BadRequestException(
                "Exceeds maximum sortable area of ${DEFAULT_MAX_SORTABLE_AREA_IN_KM2.roundToLong()} km^2 " +
                    "(actual area: ${actualAreaInKm2.roundToLong()} km^2). " +
                    "Disable sorting by setting 'sort' to false, or query a smaller area."
            )
        }
    }
}
