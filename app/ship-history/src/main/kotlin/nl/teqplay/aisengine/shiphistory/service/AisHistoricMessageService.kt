package nl.teqplay.aisengine.shiphistory.service

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.bucketing.properties.BucketProperties
import nl.teqplay.aisengine.bucketing.properties.PlatformBucketProperties
import nl.teqplay.aisengine.bucketing.storage.cache.BucketReadCache
import nl.teqplay.aisengine.bucketing.storage.ship.area.implementation.PlatformShipHistoryByAreaReadCache
import nl.teqplay.aisengine.bucketing.storage.ship.area.implementation.ShipHistoryByAreaReadCache
import nl.teqplay.aisengine.bucketing.storage.ship.mmsi.implementation.PlatformShipHistoryByMmsiReadCache
import nl.teqplay.aisengine.bucketing.storage.ship.mmsi.implementation.ShipHistoryByMmsiReadCache
import nl.teqplay.aisengine.platform.historic.toAisHistoricMessage
import nl.teqplay.aisengine.platform.model.LegacyHistoricShipInfo
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessage
import nl.teqplay.aisengine.shiphistory.util.flatSequenceToFlux
import nl.teqplay.aisengine.shiphistory.util.getBlockingInMono
import nl.teqplay.skeleton.model.BoundingBox
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.Polygon
import nl.teqplay.skeleton.model.TimeWindow
import org.springframework.stereotype.Component
import reactor.core.publisher.Flux
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneOffset

@Component
class AisHistoricMessageService(
    private val shipHistoryByMmsiReadCache: ShipHistoryByMmsiReadCache,
    private val shipHistoryByAreaReadCache: ShipHistoryByAreaReadCache,

    private val platformShipHistoryByMmsiReadCache: PlatformShipHistoryByMmsiReadCache?,
    private val platformShipHistoryByAreaReadCache: PlatformShipHistoryByAreaReadCache?,

    private val bucketProperties: BucketProperties,
    private val platformBucketProperties: PlatformBucketProperties
) {
    private val LOG = KotlinLogging.logger { }

    /**
     * Calculates the edge between fetching data from platform or from ship-history itself.
     *
     * If [PlatformBucketProperties.dynamicEdge] is set, the edge will be dynamically calculated based on
     * the locally stored short-term history.
     * Otherwise the manually set [PlatformBucketProperties.edge] is used.
     */
    private fun calculatePlatformDataEdge(): Instant? {
        if (!platformBucketProperties.enabled) {
            return null
        }

        val sweeperMaxAge = bucketProperties.sweeper.maxAge
        if (platformBucketProperties.dynamicEdge && sweeperMaxAge != null) {
            return LocalDate.now()
                .minusDays(sweeperMaxAge.toDays())
                .atStartOfDay(ZoneOffset.UTC)
                .toInstant()
        }

        return platformBucketProperties.edge
            ?.atStartOfDay(ZoneOffset.UTC)
            ?.toInstant()
    }

    /**
     * Find history for ship with [mmsi] within the given [window]
     */
    fun findHistoryByMmsi(
        mmsi: Int,
        window: TimeWindow
    ): Flux<AisHistoricMessage> = mergeHistory(
        window = window,
        platformHistory = platformShipHistoryByMmsiReadCache.conditional { cache, it ->
            cache.findHistory(it, mmsi)
        },
        shipHistory = { shipHistoryByMmsiReadCache.findHistory(it, mmsi) }
    )

    /**
     * Find history for ships within the [boundingBox] within the given [window]
     */
    fun findHistoryInBoundingBox(
        boundingBox: BoundingBox,
        window: TimeWindow,
        sort: Boolean
    ): Flux<AisHistoricMessage> = mergeHistory(
        window = window,
        platformHistory = platformShipHistoryByAreaReadCache.conditional { cache, it ->
            cache.findHistoryInBoundingBox(it, boundingBox, sort)
        },
        shipHistory = { shipHistoryByAreaReadCache.findHistoryInBoundingBox(it, boundingBox, sort) }
    )

    /**
     * Find history for ships within the [polygon] within the given [window]
     */
    fun findHistoryInPolygon(
        polygon: Polygon,
        window: TimeWindow,
        sort: Boolean
    ): Flux<AisHistoricMessage> = mergeHistory(
        window = window,
        platformHistory = platformShipHistoryByAreaReadCache.conditional { cache, it ->
            cache.findHistoryInPolygon(it, polygon, sort)
        },
        shipHistory = { shipHistoryByAreaReadCache.findHistoryInPolygon(it, polygon, sort) }
    )

    /**
     * Find history for ships within a circle, with a specified [center] and [radiusInKm], within the given [window]
     */
    fun findHistoryInCircle(
        center: Location,
        radiusInKm: Double,
        window: TimeWindow,
        sort: Boolean
    ): Flux<AisHistoricMessage> = mergeHistory(
        window = window,
        platformHistory = platformShipHistoryByAreaReadCache.conditional { cache, it ->
            cache.findHistoryInCircle(it, center, radiusInKm, sort)
        },
        shipHistory = { shipHistoryByAreaReadCache.findHistoryInCircle(it, center, radiusInKm, sort) }
    )

    /**
     * Merge [platformHistory] and [shipHistory] depending on the defined [window]
     */
    private fun mergeHistory(
        window: TimeWindow,
        platformHistory: ((TimeWindow) -> Sequence<LegacyHistoricShipInfo>)?,
        shipHistory: (TimeWindow) -> Sequence<AisHistoricMessage>
    ): Flux<AisHistoricMessage> {
        val platformDataEdge = calculatePlatformDataEdge()

        val blockingMono = getBlockingInMono {
            when {
                platformDataEdge == null ||
                    platformHistory == null ||
                    window.from >= platformDataEdge -> {

                    LOG.info { "Getting history from ship history" }
                    // Blocking operation
                    shipHistory.invoke(window)
                }

                window.to <= platformDataEdge -> {
                    LOG.info { "Getting history from platform" }
                    // Blocking operation
                    platformHistory.invoke(window)
                        .map { it.toAisHistoricMessage() }
                }

                else -> {
                    val platformWindow = TimeWindow(window.from, platformDataEdge)
                    val shipHistoryWindow = TimeWindow(platformDataEdge, window.to)

                    // First get the data from platform
                    LOG.info { "Getting history from ship history and platform" }
                    // Blocking operation
                    val platformSequence = platformHistory.invoke(platformWindow)
                        .map { it.toAisHistoricMessage() }

                    val shipHistorySequence = shipHistory.invoke(shipHistoryWindow)

                    platformSequence + shipHistorySequence
                }
            }
        }

        return blockingMono.flatSequenceToFlux()
            .doFinally {
                LOG.info { "Done getting ship history" }
            }
    }

    private fun <T : BucketReadCache<*, *, *>> T?.conditional(
        action: (T, TimeWindow) -> Sequence<LegacyHistoricShipInfo>
    ): ((TimeWindow) -> Sequence<LegacyHistoricShipInfo>)? {
        if (this == null) {
            return null
        }
        return { window -> action(this, window) }
    }
}
