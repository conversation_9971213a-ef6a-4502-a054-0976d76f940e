package nl.teqplay.aisengine.shiphistory.service

import com.github.davidmoten.rtree.Entries
import com.github.davidmoten.rtree.Entry
import com.github.davidmoten.rtree.RTree
import com.github.davidmoten.rtree.geometry.Geometries
import com.github.davidmoten.rtree.geometry.Point
import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.datasource.ShipHistoryStateDataSource
import nl.teqplay.aisengine.datasource.StateDataSource.Companion.INITIAL_LOADING_HOURS
import nl.teqplay.aisengine.service.ShipStateSubscriberService
import nl.teqplay.aisengine.shiphistory.exception.ShipStateNotFullyInitializedException
import nl.teqplay.aisengine.shiphistory.model.AisCurrentMessage
import nl.teqplay.aisengine.shiphistory.model.AisMessageCurrentQuery
import nl.teqplay.aisengine.shiphistory.util.flatIterableToFlux
import nl.teqplay.aisengine.shiphistory.util.getBlockingInFlux
import nl.teqplay.aisengine.shiphistory.util.getBlockingInMono
import nl.teqplay.aisengine.shiphistory.util.runInBackground
import nl.teqplay.aisengine.shiphistory.util.setUpLocationKeys
import nl.teqplay.aisengine.shiphistory.util.toFlux
import nl.teqplay.aisengine.util.getAisCurrentMessage
import nl.teqplay.aisengine.util.normalizeLongitude
import nl.teqplay.skeleton.model.BoundingBox
import nl.teqplay.skeleton.util.pointInBoundingBox
import org.springframework.stereotype.Component
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toFlux
import java.time.Duration
import java.time.Instant
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicReference
import kotlin.math.abs

@Component
class ShipStateSearchService(private val stateDataSource: ShipHistoryStateDataSource) : ShipStateSubscriberService {
    private val LOG = KotlinLogging.logger { }

    @Volatile
    override var isStateFullyLoaded = false

    private val shipsByMmsi = ConcurrentHashMap<Int, AisCurrentMessage>()
    private val shipsByImo = ConcurrentHashMap<Int, List<AisCurrentMessage>>()

    private val treeEntryByMmsi = ConcurrentHashMap<Int, Entry<Int, Point>>()
    private var tree = AtomicReference(RTree.star().create<Int, Point>())

    // We need to sort all messages in descending order as we want the newest messages first
    private val descendingMessageSort = Comparator.comparing(AisCurrentMessage::messageTime)
        .reversed()

    companion object {
        val MAX_AGE_DURATION: Duration = Duration.ofHours(3)
    }

    private val queryMatcher: List<Pair<(AisMessageCurrentQuery) -> String?, (AisCurrentMessage, String) -> Int?>> = listOf(
        AisMessageCurrentQuery::imo to { message, search ->
            message.imo?.toString()?.indexOf(search, ignoreCase = true)
        },
        AisMessageCurrentQuery::mmsi to { message, search ->
            message.mmsi.toString().indexOf(search, ignoreCase = true)
        },
        AisMessageCurrentQuery::eni to { message, search ->
            message.eni?.toString()?.indexOf(search, ignoreCase = true)
        },
        AisMessageCurrentQuery::name to { message, search ->
            message.name?.indexOf(search, ignoreCase = true)
        },
        AisMessageCurrentQuery::destination to { message, search ->
            message.destination?.indexOf(search, ignoreCase = true)
        },
        AisMessageCurrentQuery::shipType to { message, search ->
            message.shipType?.toString()?.indexOf(search, ignoreCase = true)
        },
    )

    /**
     * Get the state of a ship by its [mmsi].
     *
     * @param mmsi The MMSI of the Ship we want to find the state for.
     * @return The found state or an empty state when we couldn't find it in our database.
     */
    private fun getStateByMmsi(mmsi: Int): AisCurrentMessage? {
        // Use compute to ensure exclusive access to the state
        return shipsByMmsi.compute(mmsi) { _, value ->
            val state = if (value == null) {
                // Lazy load the state from the database
                val databaseState = stateDataSource.findStateById(mmsi)?.state
                    ?.let { getAisCurrentMessage(it) }

                // Update the other state maps as well when we found a ship from the database
                if (databaseState != null) {
                    updateImoState(new = databaseState, old = null)
                    updateAreaState(new = databaseState)
                }

                databaseState
            } else {
                value
            }

            state
        }
    }

    override fun containsMmsiInState(mmsi: Int): Boolean {
        return shipsByMmsi.containsKey(mmsi)
    }

    fun getByMmsi(mmsi: Int): Mono<AisCurrentMessage> {
        return getBlockingInMono {
            getStateByMmsi(mmsi)
        }.mapNotNull { it }
    }

    fun getByMmsiList(mmsiList: List<Int>): Flux<AisCurrentMessage> {
        return getBlockingInFlux(mmsiList) { mmsi ->
            getStateByMmsi(mmsi)
        }
    }

    fun getByImo(imo: Int): Flux<AisCurrentMessage> {
        val blockingGetStateByImo = getBlockingInMono {
            if (isStateFullyLoaded) {
                // Go via in-memory map if everything is loaded
                shipsByImo[imo]?.sortedByDescending { it.messageTime } ?: emptyList()
            } else {
                LOG.debug { "State not fully loaded, finding ship by imo via database (imo: $imo)" }

                // Always go to the database, don't do any lazy loading via imo
                stateDataSource.findStateByImo(imo)
                    .mapNotNull { wrapper -> getAisCurrentMessage(wrapper.state) }
            }
        }

        return blockingGetStateByImo.flatIterableToFlux()
            .sort(descendingMessageSort)
    }

    fun getByImoList(imoList: List<Int>): Flux<AisCurrentMessage> {
        return getBlockingInFlux(imoList) { imo -> getByImo(imo) }
            .flatMap { it }
            // Sort again as we might get data out of order when requesting for multiple IMOs
            .sort(descendingMessageSort)
    }

    private fun AisCurrentMessage.filterMaxAge(maxAge: Duration?): Boolean =
        maxAge == null || Duration.between(messageTime, Instant.now()) <= maxAge

    private fun determineMaxAgeDuration(
        showAged: Boolean?,
        maxAge: Duration?
    ): Duration? = when {
        maxAge != null -> maxAge
        showAged != true -> MAX_AGE_DURATION
        else -> null
    }

    fun getAll(
        showAged: Boolean?,
        maxAge: Duration?
    ): Flux<AisCurrentMessage> {
        val initialLoadingDuration = Duration.ofHours(INITIAL_LOADING_HOURS)
        val maxAgeSetting = determineMaxAgeDuration(showAged, maxAge)

        // We only have to throw when the max age is older than what we want to return.
        // Otherwise, the ones we've loaded on start up is already sufficient.
        if (!isStateFullyLoaded && maxAgeSetting != null && maxAgeSetting > initialLoadingDuration) {
            return Flux.error(ShipStateNotFullyInitializedException())
        }

        return shipsByMmsi.values.toFlux()
            .filter { message -> message.filterMaxAge(maxAgeSetting) }
    }

    /**
     * Gets all ships within the specified [boundingBox]
     *
     * @param boundingBox The bounding box we want to find all current messages for
     * @param showAged Indicate if aged vessels should be returned
     * @param maxAge How old the vessels are allowed ot be
     */
    fun getInBoundingBox(
        boundingBox: BoundingBox,
        showAged: Boolean?,
        maxAge: Duration?
    ): Flux<AisCurrentMessage> {
        val (latLeft, lonLeft) = boundingBox.bottomleft
        val (latRight, lonRight) = boundingBox.topright

        if (isStateFullyLoaded) {
            return getInBoundingBoxViaRTree(
                showAged = showAged,
                maxAge = maxAge,
                latLeft = latLeft,
                latRight = latRight,
                lonLeft = lonLeft,
                lonRight = lonRight
            )
        }

        // Get all possible location keys we need to find the current state for
        val locationKeys = setUpLocationKeys(lat1 = latLeft, lat2 = latRight, lon1 = lonLeft, lon2 = lonRight)
        return getInBoundingBoxViaDatabase(showAged, maxAge, boundingBox, locationKeys)
    }

    /**
     * Find all current AIS messages using the RTree
     */
    private fun getInBoundingBoxViaRTree(
        showAged: Boolean?,
        maxAge: Duration?,
        latLeft: Double,
        latRight: Double,
        lonLeft: Double,
        lonRight: Double
    ): Flux<AisCurrentMessage> {
        val maxAgeSetting = determineMaxAgeDuration(showAged, maxAge)

        /**
         * If the width is greater than 360, everything is returned, as the whole world is visible
         * Else if 160 to 200 is requested, normalization in [Geometries] makes the right one -160
         * to prevent everything between -160 and 160 being returned, two searches are concatenated:
         * 160 to 179.9999, and -180.0 to -160
         * It's 179.9999, as [Geometries] changes 180.0 to -180.0
         * And this precision as the double gets changed to a float (More 9s makes it a 180.0 again, then -180.0)
         */
        val searchedTree =
            if (abs(lonLeft - lonRight) >= 360) {
                tree.get().entries()
            } else if (normalizeLongitude(lonRight) < normalizeLongitude(lonLeft)) {
                tree.get().search(Geometries.rectangleGeographic(lonLeft, latLeft, 179.9999, latRight)).concatWith(
                    tree.get().search(Geometries.rectangleGeographic(-180.0, latLeft, lonRight, latRight))
                )
            } else {
                tree.get().search(Geometries.rectangleGeographic(lonLeft, latLeft, lonRight, latRight))
            }

        return searchedTree.toFlux()
            .mapNotNull<AisCurrentMessage> { treeEntry ->
                val mmsi = treeEntry.value()

                // Getting the state by MMSI can be a blocking operation if not loaded in yet
                val message = getStateByMmsi(mmsi)
                if (message != null && message.filterMaxAge(maxAgeSetting)) {
                    message
                } else {
                    null
                }
            }
            .runInBackground()
    }

    /**
     * Get all current ais messages directly from the database using the provided list of [locationKeys].
     */
    private fun getInBoundingBoxViaDatabase(
        showAged: Boolean?,
        maxAge: Duration?,
        boundingBox: BoundingBox,
        locationKeys: List<String>
    ): Flux<AisCurrentMessage> {
        LOG.debug { "State not fully loaded, finding ships by via database for location keys [$locationKeys]" }

        val maxAgeSetting = determineMaxAgeDuration(showAged, maxAge)

        return getBlockingInMono { stateDataSource.findStateByLocationKeys(locationKeys) }
            .flatIterableToFlux()
            .mapNotNull<AisCurrentMessage> { wrapper -> getAisCurrentMessage(wrapper.state) }
            .filter { pointInBoundingBox(boundingBox, it.location) && it.filterMaxAge(maxAgeSetting) }
    }

    /**
     * Returns all ships matching the specific [query]
     *
     * @param query filters every ship
     * @param limit the maximum amount of returned results
     * @param seaVessel dictates if a ship should have an IMO or not (or doesn't matter)
     * @param maxAge dictates if a ship should be included by having a recent age
     */
    fun getByQuery(
        query: AisMessageCurrentQuery,
        limit: Int?,
        seaVessel: Boolean?,
        showAged: Boolean?,
        maxAge: Duration?
    ): Flux<AisCurrentMessage> {
        val maxAgeSetting = determineMaxAgeDuration(showAged, maxAge)

        val resultingMono = getBlockingInMono<Pair<List<AisCurrentMessage>, List<AisCurrentMessage>>> {
            val startsWith = mutableListOf<AisCurrentMessage>()
            val contains = mutableListOf<AisCurrentMessage>()

            for (message in shipsByMmsi.values) {
                // if sea vessel is set, the IMO must either exist or not depending on its setting
                if (seaVessel != null && (message.imo != null) != seaVessel) {
                    continue
                } else if (!message.filterMaxAge(maxAgeSetting)) {
                    continue
                }

                val matchesIndex = queryMatcher.asSequence()
                    .mapNotNull { (search, index) ->
                        val searchText = search.invoke(query)?.ifBlank { null }
                        if (searchText != null) index.invoke(message, searchText)
                        else null
                    }
                    .firstOrNull { it != -1 }
                    ?: continue

                if (matchesIndex == 0) {
                    startsWith.add(message)
                } else if (matchesIndex > 0) {
                    contains.add(message)
                }
            }

            startsWith to contains
        }

        return resultingMono.map { (startsWith, contains) ->
            startsWith.sortedBy { it.name }
            contains.sortedBy { it.name }

            if (limit != null) {
                val output = startsWith.take(limit)
                output + contains.take((limit - output.size).coerceAtLeast(0))
            } else {
                startsWith + contains
            }
        }.flatIterableToFlux()
    }

    /**
     * Updates based on the given [message]
     */
    override fun update(message: AisCurrentMessage) {
        synchronized(shipsByMmsi) {
            shipsByMmsi.compute(message.mmsi) { _, old ->
                // short-circuit if the message is older
                if (old != null && message.messageTime < old.messageTime) {
                    return@compute old
                }

                // Also update the imo and area state
                updateImoState(message, old)
                updateAreaState(message)

                message
            }
        }
    }

    @Synchronized
    private fun updateImoState(new: AisCurrentMessage, old: AisCurrentMessage?) {
        val oldImo = old?.imo
        val imo = new.imo

        // remove from IMO mapping if IMO changed
        if (oldImo != null && oldImo != imo) {
            shipsByImo.computeIfPresent(oldImo) { _, value ->
                value.filterNot { it.mmsi == new.mmsi }
            }
        }

        // add to IMO mapping
        if (imo != null) {
            shipsByImo.compute(imo) { _, value ->
                if (value == null) {
                    listOf(new)
                } else {
                    // Filter out old message if mmsis are matching
                    val shipsWithDifferentMmsi = value.filterNot { it.mmsi == new.mmsi }
                    shipsWithDifferentMmsi + new
                }
            }
        }
    }

    @Synchronized
    private fun updateAreaState(new: AisCurrentMessage) {
        // add to search tree
        tree.getAndUpdate { rtree ->
            val oldEntry = treeEntryByMmsi[new.mmsi]
            val newEntry = Entries.entry(new.mmsi, Geometries.pointGeographic(new.location.lon, new.location.lat))
            treeEntryByMmsi[new.mmsi] = newEntry

            // Remove the old entry from the rtree if we had one
            if (oldEntry != null) {
                rtree.delete(oldEntry)
                    .add(newEntry)
            } else {
                rtree.add(newEntry)
            }
        }
    }

    /**
     * Deletes all data related to the given [mmsi]
     */
    override fun delete(mmsi: Int) {
        synchronized(shipsByMmsi) {
            shipsByMmsi.computeIfPresent(mmsi) { _, value ->
                // remove from IMO mapping
                val imo = value.imo
                if (imo != null) {
                    shipsByImo.computeIfPresent(imo) { _, imoValue ->
                        imoValue.filterNot { it.mmsi == mmsi }.ifEmpty { null }
                    }
                }

                // remove from search tree
                val entry = treeEntryByMmsi.remove(mmsi)
                if (entry != null) {
                    tree.getAndUpdate { it.delete(entry) }
                }
                null
            }
        }
    }
}
