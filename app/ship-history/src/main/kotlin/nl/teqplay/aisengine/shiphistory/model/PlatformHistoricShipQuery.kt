package nl.teqplay.aisengine.shiphistory.model

import nl.teqplay.platform.model.Location
import java.util.Date

data class PlatformHistoricShipQuery(
    val from: Date? = null,
    val to: Date? = null,

    val polygon: List<Location>? = null,
    val circle: PlatformCircleQuery? = null,
    val mmsis: List<String>? = null,
    val imos: List<String>? = null,

    val filter: String? = null, // SQL like query such as "speedOverGround > 8 AND destination = 'ROTTERDAM'"

    // TODO: not supported
    // val mergeStaticShipInfo: Boolean? = null,
    val includeFields: Set<String>? = null,

    val maxDays: Int? = null,
    val maxAreaInKm2: Long? = null,
    val sort: Boolean = true,

    // TODO: not supported
    // val includeRestSources: Boolean = false,
    // TODO: not supported
    // val revalidate: Boolean? = null
)
