package nl.teqplay.aisengine.shiphistory.controller.ship.history

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.aisstream.client.OffsetTimeWindow
import nl.teqplay.aisengine.platform.historic.toLegacyHistoricShipInfo
import nl.teqplay.aisengine.platform.model.LegacyHistoricShipInfo
import nl.teqplay.aisengine.platform.model.LegacyNamedHistoricShipInfo
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessage
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessageMmsiQuery
import nl.teqplay.aisengine.shiphistory.model.PlatformHistoricShipQuery
import nl.teqplay.aisengine.shiphistory.model.PlatformShipParameterMmsiQuery
import nl.teqplay.aisengine.shiphistory.service.CsiService
import nl.teqplay.aisengine.shiphistory.service.PlatformCompatibilityService
import nl.teqplay.aisengine.shiphistory.util.flatNestedFlux
import nl.teqplay.aisengine.shiphistory.util.throwOnEmpty
import nl.teqplay.skeleton.common.exception.BadRequestException
import nl.teqplay.skeleton.common.exception.NotFoundException
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.Polygon
import nl.teqplay.skeleton.model.TimeWindow
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import reactor.core.publisher.Flux
import java.time.Duration
import java.time.Instant
import java.time.ZoneOffset
import kotlin.reflect.full.memberProperties
import nl.teqplay.platform.model.Location as PlatformLocation
import nl.teqplay.platform.resource.params.TimeWindow as PlatformTimeWindow

/**
 * Endpoints used to get ship history, both by mmsi and by area, from platform's database.
 */
@RestController
@RequestMapping("/v0/ship/history", produces = [MediaType.APPLICATION_JSON_VALUE])
class PlatformShipHistoryController(
    private val aisHistoricMessageController: AisHistoricMessageController,
    private val platformCompatibilityService: PlatformCompatibilityService,
    private val csiService: CsiService
) {
    private val log = KotlinLogging.logger { }

    /**
     * Find history for ship with [mmsiString] within the given [platformWindow]
     */
    @GetMapping("/mmsi/{mmsiString}")
    fun findHistoryByMmsi(
        @PathVariable mmsiString: String,
        platformWindow: PlatformTimeWindow,
        @RequestParam(required = false) maxDays: Int?
    ): Flux<LegacyHistoricShipInfo> {
        val mmsi = mmsiString.toIntOrNull()
            ?: throw NotFoundException("Ship with mmsi $mmsiString not found.")
        val window = platformWindow.convert()
        return aisHistoricMessageController
            .findHistoryByMmsi(mmsi, window, maxDays)
            .convert()
    }

    /**
     * Find history for ships matching the [platformQuery]
     */
    @PostMapping("/mmsi")
    fun findHistoryByMmsiQuery(
        @RequestBody platformQuery: List<PlatformShipParameterMmsiQuery>,
        @RequestParam(required = false) maxDays: Int?,
        @RequestParam(required = false, defaultValue = "true") sort: Boolean
    ): Flux<LegacyHistoricShipInfo> {
        val query = platformQuery.convert()
        return aisHistoricMessageController
            .findHistoryByMmsiQuery(query, maxDays, sort)
            .convert()
    }

    /**
     * Find history for ship with [imoString] within the given [platformWindow]
     */
    @GetMapping("/imo/{imoString}")
    fun findHistoryByImo(
        @PathVariable imoString: String,
        platformWindow: PlatformTimeWindow,
        @RequestParam(required = false) maxDays: Int?
    ): Flux<LegacyHistoricShipInfo> {
        val currentMono = platformCompatibilityService.findCurrentByImo(imoString)
        val window = platformWindow.convert()

        return currentMono.throwOnEmpty(NotFoundException("Ship with imo $imoString not found."))
            .mapNotNull<Int> { shipInfo -> shipInfo.mmsi.toIntOrNull() }
            .map { mmsi ->
                aisHistoricMessageController
                    .findHistoryByMmsi(mmsi, window, maxDays)
                    .convert()
            }
            .flatNestedFlux()
    }

    /**
     * Find history for ships within the [platformPolygon] within the given [platformWindow]
     */
    @PostMapping("/polygon")
    fun findHistoryInPolygon(
        @RequestBody platformPolygon: List<PlatformLocation>,
        platformWindow: PlatformTimeWindow,
        @RequestParam(required = false) maxDays: Int?,
        @RequestParam(required = false, name = "maxArea") maxAreaInKm2: Double?,
        @RequestParam(required = false, defaultValue = "true") sort: Boolean
    ): Flux<LegacyNamedHistoricShipInfo> {
        val polygon = platformPolygon.convertToPolygon()
        val window = platformWindow.convert()
        return aisHistoricMessageController
            .findHistoryInPolygon(polygon, window, maxDays, maxAreaInKm2, sort)
            .convert()
            .convertToNamed()
    }

    /**
     * Find history for ships within a circle, with a specified center of [lat], [lon] and [radiusInMeters], within the given [platformWindow]
     */
    @GetMapping("/circle")
    fun findHistoryInCircle(
        @RequestParam lat: Double,
        @RequestParam lon: Double,
        @RequestParam(name = "radius") radiusInMeters: Double,
        platformWindow: PlatformTimeWindow,
        @RequestParam(required = false) maxDays: Int?,
        @RequestParam(required = false, name = "maxArea") maxAreaInKm2: Double?,
        @RequestParam(required = false, defaultValue = "true") sort: Boolean
    ): Flux<LegacyNamedHistoricShipInfo> {
        val window = platformWindow.convert()
        val center = Location(lat, lon)
        val radiusInKm = radiusInMeters / 1000
        return aisHistoricMessageController
            .findHistoryInCircle(center, radiusInKm, window, maxDays, maxAreaInKm2, sort)
            .convert()
            .convertToNamed()
    }

    /**
     * Find history for ships matching the [query]
     */
    @PostMapping("/query")
    fun findHistoryQuery(
        @RequestBody query: PlatformHistoricShipQuery
    ): Flux<Map<String, Any?>> {
        val platformWindow = PlatformTimeWindow(query.from, query.to)

        val fieldsCount = listOfNotNull(query.polygon, query.circle, query.mmsis, query.imos).size
        if (fieldsCount > 1) {
            throw BadRequestException(
                "More than one of the mutually exclusive fields " +
                    "'polygon', 'circle', 'mmsis', and 'imos' is defined. " +
                    "Ony one of these fields can be defined."
            )
        }

        val data = when {
            query.polygon != null -> findHistoryInPolygon(
                platformPolygon = query.polygon,
                platformWindow = platformWindow,
                maxDays = query.maxDays,
                maxAreaInKm2 = query.maxAreaInKm2?.toDouble(),
                sort = query.sort
            )

            query.circle != null -> findHistoryInCircle(
                lat = query.circle.center.latitude,
                lon = query.circle.center.longitude,
                radiusInMeters = query.circle.radiusInKm * 1000,
                platformWindow = platformWindow,
                maxDays = query.maxDays,
                maxAreaInKm2 = query.maxAreaInKm2?.toDouble(),
                sort = query.sort
            )

            query.mmsis != null -> {
                findHistoryByMmsiQuery(
                    platformQuery = query.mmsis.map { mmsi ->
                        PlatformShipParameterMmsiQuery(
                            mmsi = mmsi,
                            from = platformWindow.from,
                            to = platformWindow.to
                        )
                    },
                    maxDays = query.maxDays,
                    sort = query.sort
                )
            }
            query.imos != null -> {
                Flux.fromIterable(query.imos)
                    .map { imo -> findHistoryByImo(imo, platformWindow, query.maxDays) }
                    .flatMap { it }
            }

            else -> throw BadRequestException(
                "Missing one of the required fields 'polygon', 'circle', 'mmsis', or 'imos'. " +
                    "Exactly one of these fields must be defined."
            )
        }

        // add additional filtering via a SQL filter
        if (query.filter != null) {
            log.warn { "SQL query Found: '${query.filter}'" }
        }

        // optionally, drop fields that shouldn't be included
        val fields = LegacyHistoricShipInfo::class.memberProperties
        return data
            .map {
                val map = mutableMapOf<String, Any?>()
                fields.forEach { field ->
                    if (query.includeFields.isNullOrEmpty() || field.name in query.includeFields) {
                        map[field.name] = field.get(it)
                    }
                }
                map
            }
    }

    private fun Flux<AisHistoricMessage>.convert() = map { it.toLegacyHistoricShipInfo() }

    private fun Flux<LegacyHistoricShipInfo>.convertToNamed(): Flux<LegacyNamedHistoricShipInfo> {
        return map { historicShipInfo ->
            val shipInfoMono = platformCompatibilityService.findCurrentByMmsi(historicShipInfo.mmsi)
            val role = csiService.getRoleByMmsi(historicShipInfo.mmsi)

            shipInfoMono.map { shipInfo ->
                LegacyNamedHistoricShipInfo(
                    historicShipInfo = historicShipInfo,
                    name = shipInfo?.name,
                    shipType = shipInfo?.shipType,
                    role = role
                )
            }
        }.flatMap { it }
    }

    private fun PlatformTimeWindow.convert(): OffsetTimeWindow {
        if (from == null || to == null) {
            throw BadRequestException("No time window provided. Set 'from' and 'to'.")
        }
        return OffsetTimeWindow(
            from = Instant.ofEpochMilli(from).atOffset(ZoneOffset.UTC),
            to = Instant.ofEpochMilli(to).atOffset(ZoneOffset.UTC)
        )
    }

    private fun List<PlatformLocation>.convertToPolygon(): Polygon =
        Polygon(locations = map { Location(it.latitude, it.longitude) })

    private fun List<PlatformShipParameterMmsiQuery>.convert() = mapNotNull {
        val mmsi = it.mmsi.toIntOrNull() ?: return@mapNotNull null
        val window = when {
            it.from != null && it.to != null -> TimeWindow(
                from = Instant.ofEpochMilli(it.from),
                to = Instant.ofEpochMilli(it.to)
            )

            else -> TimeWindow(
                time = Instant.now(),
                duration = Duration.ofDays(-1)
            )
        }
        AisHistoricMessageMmsiQuery(mmsi, window)
    }
}
