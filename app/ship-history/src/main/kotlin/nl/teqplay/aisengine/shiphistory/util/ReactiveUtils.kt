package nl.teqplay.aisengine.shiphistory.util

import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.core.scheduler.Schedulers
import reactor.kotlin.core.publisher.switchIfEmpty
import rx.Observable
import rx.RxReactiveStreams

/**
 * Execute a blocking operation inside a [Mono] and make sure it is executed in a separate thread pool.
 */
inline fun <reified T> getBlockingInMono(crossinline blockingOperation: () -> T): Mono<T> {
    return Mono.fromCallable { blockingOperation() }
        // Make sure we run the blocking operations in a separate thread pool
        .runInBackground()
}

/**
 * Execute a blocking operation inside a [Flux] and make sure it is executed in a separate thread pool.
 *
 * Note that the [blockingOperation] is executed for every item in [items].
 */
inline fun <T, reified R> getBlockingInFlux(items: List<T>, crossinline blockingOperation: (T) -> R?): Flux<R> {
    return Flux.fromIterable(items)
        .mapNotNull<R> { blockingOperation(it) }
        .runInBackground()
}

/**
 * The amount of threads the [BLOCKING_SCHEDULER] will have.
 */
val BLOCKING_SCHEDULER_THREADS = 10 * Runtime.getRuntime().availableProcessors()

/**
 * Custom scheduler for blocking IO calls, for example, reading from S3 or requesting mongo.
 * [Schedulers.boundedElastic] doesn't seem to work, as doing a blocking call followed by a health actuator call would result in our backend not retrieving the call.
 */
val BLOCKING_SCHEDULER = Schedulers.newBoundedElastic(BLOCKING_SCHEDULER_THREADS, 100_000, "blocking")

inline fun <reified T> Flux<T>.runInBackground(): Flux<T> {
    return this.subscribeOn(BLOCKING_SCHEDULER)
}

inline fun <reified T> Mono<T>.runInBackground(): Mono<T> {
    return this.subscribeOn(BLOCKING_SCHEDULER)
}

/**
 * Map a Mono sequence to a Flux as required by some endpoints
 */
fun <T> Mono<Sequence<T>>.flatSequenceToFlux(): Flux<T> {
    return this.flatMapIterable { sequence -> sequence.toList() }
}

/**
 * Flat map a Mono containing an Iterable of [T] to a Flux of [T] as required by some endpoints
 */
fun <T, I : Iterable<T>> Mono<I>.flatIterableToFlux(): Flux<T> {
    return this.flatMapIterable { it }
}

/**
 * Map a Mono of Flux to a single Flux.
 */
fun <T> Mono<Flux<T>>.flatNestedFlux(): Flux<T> {
    return this.flatMapMany { it }
}

/**
 * Helper function to create a [Flux] from an RxJava [Observable] used by our RTree library.
 */
fun <T> Observable<T>.toFlux(): Flux<T> {
    return Flux.from(RxReactiveStreams.toPublisher(this))
}

/**
 * Switch the current [Mono] to an error of [exception].
 */
fun <T> Mono<T>.throwOnEmpty(exception: Throwable): Mono<T> {
    return this.switchIfEmpty {
        Mono.error(exception)
    }
}
