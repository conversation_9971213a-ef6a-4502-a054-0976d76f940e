package nl.teqplay.aisengine.shiphistory.util

import nl.teqplay.aisengine.util.floorLatitudeToDecimals
import nl.teqplay.aisengine.util.floorToDecimals
import nl.teqplay.aisengine.util.normalizeLongitude
import nl.teqplay.aisengine.util.normalizeZero

/**
 * Set up the locations keys for a given area.
 * When providing [0.0, 0.0], [2.0, 0.0] a list of all location keys should be returned with the in-between locations included.
 *
 * @param lat1 The bottom left latitude
 * @param lat2 The top right latitude
 * @param lon1 The bottom left longitude
 * @param lon2 The top right longitude
 * @return A list of all possible location keys
 */
fun setUpLocationKeys(
    lat1: Double,
    lat2: Double,
    lon1: Double,
    lon2: Double
): List<String> {
    val lowerLat1 = floorLatitudeToDecimals(lat1, 0).toInt()
    val lowerLon1 = normalizeZero(floorToDecimals(lon1, 0)).toInt()

    val upperLat2 = floorLatitudeToDecimals(lat2, 0).toInt()
    val upperLon2 = normalizeZero(floorToDecimals(lon2, 0)).toInt()

    val latRange = lowerLat1..upperLat2
    val lonRange = lowerLon1..upperLon2

    val locationKeys = mutableListOf<String>()

    for (lat in latRange) {
        for (lon in lonRange) {
            val normalizedLon = normalizeLongitude(lon.toDouble()).toInt()
            locationKeys.add("$lat,$normalizedLon")
        }
    }

    return locationKeys
}
