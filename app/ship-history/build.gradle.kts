import org.springframework.boot.gradle.tasks.bundling.BootBuildImage

buildscript {
    project.extra.set("baseVersion", "2.1.0")
}

plugins {
    id("ais-engine.kotlin-app-webflux-conventions")
    id("org.cyclonedx.bom") version cyclonedxBomVersion
}

dependencies {
    implementation(project(":api:client-common"))
    implementation(project(":lib:ship-state"))
    implementation(project(":lib:bucketing-ship"))
    implementation(project(":lib:ship-history-common"))
    implementation(project(":lib:keycloak-s2s-server-any-verified-user"))

    implementation("nl.teqplay.skeleton:metrics:$skeletonVersion")
    implementation("nl.teqplay.skeleton:util-location2:$skeletonVersion")
    implementation("nl.teqplay.skeleton:datasource-builder2:$skeletonVersion")
    implementation("nl.teqplay.skeleton:csi-client:$skeletonVersion")

    // SwaggerUI
    implementation("org.springdoc:springdoc-openapi-starter-webflux-api:$springdocVersion")
    implementation("org.springdoc:springdoc-openapi-starter-webflux-ui:$springdocVersion")

    implementation("com.github.davidmoten:rtree:$rtreeVersion")

    // rtree uses rxjava 1, this library adds support to the Reactive classes that Spring Webflux uses.
    implementation("io.reactivex:rxjava-reactive-streams:$rxjavaReactiveAdapter")

    testImplementation("io.projectreactor:reactor-test")

    // Authentication
    implementation("nl.teqplay.skeleton:auth-credentials-webflux:$skeletonVersion")
    implementation("org.springframework.boot:spring-boot-starter-security")
    implementation(project(":lib:platform-keycloak-auth"))
    implementation(project(":lib:keycloak-s2s-server-any-verified-user"))
}

tasks.named<BootBuildImage>("bootBuildImage") {
    // To avoid the JVM going OOM when doing large requests, give it some more direct memory.
    // Note: the leading space is here for a reason ;)
    environment.put("BPE_APPEND_JAVA_TOOL_OPTIONS", " -XX:MaxDirectMemorySize=64M")
}

tasks.cyclonedxBom {
    setIncludeConfigs(listOf("runtimeClasspath"))
    setOutputFormat("json")
    setOutputName("bom")
}
