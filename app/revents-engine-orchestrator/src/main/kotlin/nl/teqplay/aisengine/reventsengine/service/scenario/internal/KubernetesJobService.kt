package nl.teqplay.aisengine.reventsengine.service.scenario.internal

import io.fabric8.kubernetes.api.model.ConfigMapVolumeSourceBuilder
import io.fabric8.kubernetes.api.model.Container
import io.fabric8.kubernetes.api.model.ContainerBuilder
import io.fabric8.kubernetes.api.model.EnvVar
import io.fabric8.kubernetes.api.model.EnvVarSource
import io.fabric8.kubernetes.api.model.IntOrString
import io.fabric8.kubernetes.api.model.ObjectFieldSelector
import io.fabric8.kubernetes.api.model.PodList
import io.fabric8.kubernetes.api.model.Quantity
import io.fabric8.kubernetes.api.model.ResourceRequirementsBuilder
import io.fabric8.kubernetes.api.model.ServiceBuilder
import io.fabric8.kubernetes.api.model.ServicePortBuilder
import io.fabric8.kubernetes.api.model.VolumeMountBuilder
import io.fabric8.kubernetes.api.model.batch.v1.JobBuilder
import io.fabric8.kubernetes.client.KubernetesClientBuilder
import io.fabric8.kubernetes.client.readiness.Readiness
import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioEvent
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioPostProcessing
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioPostProcessing.VESSEL_VOYAGE_V2
import nl.teqplay.aisengine.reventsengine.properties.KubernetesProperties
import org.springframework.stereotype.Component

private val LOG = KotlinLogging.logger { }

/**
 * A service communicating with Kubernetes to schedule a Job.
 */
@Component
class KubernetesJobService(
    private val kubernetesProperties: KubernetesProperties
) {

    private val client = KubernetesClientBuilder().build()
    private val suffix = if (kubernetesProperties.useDataEnvironment) "-data" else ""

    private val REVENTS_ENGINE_APP = "revents-engine-app$suffix"
    private val REVENTS_ENGINE_APP_INDEX_LABEL = "$REVENTS_ENGINE_APP/index"

    // TODO Configurable
    companion object {
        private const val IMAGE_TAG_FIELD = "tag"
        private const val AIS_ENGINE_VERSION_CONFIGMAP = "ais-engine-version"
        private const val VESSELVOYAGE_VERSION_CONFIGMAP = "vesselvoyage-version"
        private const val NATS_CONFIGMAP_NAME = "nats-job-config"
        private const val NATS_CONFIG_VOLUME_NAME = "nats-config"
        private const val NATS_PORT = 4222
        private const val REVENTS_ENGINE_PORT = 8080
        private const val REVENTS_FIRST_APP_PORT = REVENTS_ENGINE_PORT + 1
        private const val VESSELVOYAGE_PORT = 9090
    }

    fun initialize(
        contextIndex: Int,
        events: Set<ScenarioEvent>,
        postProcessing: Set<ScenarioPostProcessing>
    ): Boolean {
        runCatching {
            val serviceApi = ServiceBuilder()
                .withNewMetadata()
                .withName(jobServiceApiName(contextIndex))
                .withNamespace(kubernetesProperties.namespace)
                .endMetadata()
                .withNewSpec()
                .addToSelector("job-name", jobName(contextIndex))
                .withPorts(
                    ServicePortBuilder()
                        .withProtocol("TCP")
                        .withPort(REVENTS_ENGINE_PORT)
                        .withTargetPort(IntOrString(REVENTS_ENGINE_PORT))
                        .build()
                )
                .endSpec()
                .build()

            val serviceNats = ServiceBuilder()
                .withNewMetadata()
                .withName(jobServiceNatsName(contextIndex))
                .withNamespace(kubernetesProperties.namespace)
                .endMetadata()
                .withNewSpec()
                .addToSelector("job-name", jobName(contextIndex))
                .withPorts(
                    ServicePortBuilder()
                        .withProtocol("TCP")
                        .withPort(NATS_PORT)
                        .withTargetPort(IntOrString(NATS_PORT))
                        .build()
                )
                .endSpec()
                .build()

            val job = JobBuilder()
                .withApiVersion("batch/v1")
                .addMetadata(contextIndex)
                .addJobSpec(contextIndex, events, postProcessing)
                .build()

            client.services().resource(serviceApi).create()
            client.services().resource(serviceNats).create()
            client.batch().v1().jobs().resource(job).create()
        }.onFailure { ex ->
            LOG.error(ex) { "Couldn't instantiate Job" }
            return false
        }
        return true
    }

    fun teardown(
        contextIndex: Int
    ): Boolean {
        runCatching {
            val namespace = kubernetesProperties.namespace
            client.services().inNamespace(namespace).withName(jobServiceApiName(contextIndex)).delete()
            client.services().inNamespace(namespace).withName(jobServiceNatsName(contextIndex)).delete()
            client.batch().v1().jobs().inNamespace(namespace).withName(jobName(contextIndex)).delete()
        }.onFailure { ex ->
            LOG.error(ex) { "Couldn't teardown Job" }
            return false
        }
        return true
    }

    fun waitUntilReady(contextIndex: Int): Boolean {
        val pods = listPods(contextIndex)
        return pods.items.isNotEmpty() && pods.items.all { pod -> Readiness.isPodReady(pod) }
    }

    private fun listPods(contextIndex: Int): PodList = client.pods()
        .inNamespace(kubernetesProperties.namespace)
        .withLabel(REVENTS_ENGINE_APP_INDEX_LABEL, contextIndex.toString())
        .list()

    private fun jobName(contextIndex: Int) = "revents-engine-job$suffix-$contextIndex"
    private fun jobServiceApiName(contextIndex: Int) = "${jobName(contextIndex)}-api"
    private fun jobServiceNatsName(contextIndex: Int) = "${jobName(contextIndex)}-nats"

    fun jobServiceApiUri(contextIndex: Int): String {
        val name = jobServiceApiName(contextIndex)
        return "http://$name.${kubernetesProperties.namespace}.svc.cluster.local:$REVENTS_ENGINE_PORT"
    }

    fun jobServiceNatsUri(contextIndex: Int): String {
        val name = jobServiceNatsName(contextIndex)
        return "nats://$name.${kubernetesProperties.namespace}.svc.cluster.local:$NATS_PORT"
    }

    private fun JobBuilder.addMetadata(
        contextIndex: Int
    ): JobBuilder {
        return withNewMetadata()
            .withName(jobName(contextIndex))
            .withNamespace(kubernetesProperties.namespace)
            .endMetadata()
    }

    /**
     * Specifies the Job's spec.
     * Adding labels, service account, containers, etc.
     */
    private fun JobBuilder.addJobSpec(
        contextIndex: Int,
        events: Set<ScenarioEvent>,
        postProcessing: Set<ScenarioPostProcessing>
    ): JobBuilder {
        val aisEngineImageTag = requireNotNull(
            client.configMaps()
                .inNamespace(kubernetesProperties.namespace)
                .withName(AIS_ENGINE_VERSION_CONFIGMAP)
                .get().data[IMAGE_TAG_FIELD]
        )

        val natsConfigMapSource = ConfigMapVolumeSourceBuilder()
            .withName(NATS_CONFIGMAP_NAME)
            .build()

        return withNewSpec()
            // Provide a reasonably high backoff limit, to allow for enough possible app restarts.
            // Allowing revents-engine-orchestrator to kill the process in-time before the Job is marked as failed.
            .withBackoffLimit(500)
            .withNewTemplate()
            .withNewMetadata()
            .addToLabels(REVENTS_ENGINE_APP_INDEX_LABEL, contextIndex.toString())
            .endMetadata()
            .withNewSpec()
            .withServiceAccountName(REVENTS_ENGINE_APP)
            .withRestartPolicy("OnFailure")
            .let {
                if (kubernetesProperties.kubernetesVersionSupportsSidecarContainers) {
                    it.withInitContainers(initContainers(aisEngineImageTag))
                        .withContainers(appContainers(aisEngineImageTag, events, postProcessing))
                } else {
                    it.withContainers(
                        initContainers(aisEngineImageTag) +
                            appContainers(aisEngineImageTag, events, postProcessing)
                    )
                }
            }
            .addNewVolume()
            .withName(NATS_CONFIG_VOLUME_NAME)
            .withConfigMap(natsConfigMapSource)
            .endVolume()
            .endSpec()
            .endTemplate()
            .endSpec()
    }

    private fun initContainers(
        aisEngineImageTag: String
    ): List<Container> = listOf(
        ContainerBuilder()
            .withName("nats")
            .withArgs("-c", "/etc/nats-config/nats.conf", "-js")
            .addNewPort()
            .withName("nats")
            .withContainerPort(NATS_PORT)
            .withProtocol("TCP")
            .endPort()
            .withImage(kubernetesProperties.containerImages.nats)
            .withImagePullPolicy("IfNotPresent")
            .withResources(kubernetesProperties.resources.nats)
            .withVolumeMounts(
                VolumeMountBuilder()
                    .withName(NATS_CONFIG_VOLUME_NAME)
                    .withMountPath("/etc/nats-config")
                    .build()
            )
            .let {
                if (kubernetesProperties.kubernetesVersionSupportsSidecarContainers) {
                    it.addToAdditionalProperties("restartPolicy", "Always")
                } else it
            }
            .build(),
        ContainerBuilder()
            .withName("revents-engine")
            .addNewPort()
            .withName("http")
            .withContainerPort(REVENTS_ENGINE_PORT)
            .withProtocol("TCP")
            .endPort()
            .withImage("${kubernetesProperties.containerImages.aisEngineBase}/revents-engine:$aisEngineImageTag")
            .withImagePullPolicy("IfNotPresent")
            .let {
                if (kubernetesProperties.kubernetesVersionSupportsSidecarContainers) {
                    it.addToAdditionalProperties("restartPolicy", "Always")
                } else it
            }
            .withResources(kubernetesProperties.resources.reventsEngine)
            .withStartupAndLivenessProbes()
            .withCustomEnvVars(REVENTS_ENGINE_PORT)
            .build(),
    )

    private fun ContainerBuilder.withResources(spec: KubernetesProperties.ResourceSpecification) =
        withResources(
            ResourceRequirementsBuilder()
                .addToRequests("cpu", Quantity.parse(spec.requests.cpu))
                .addToRequests("memory", Quantity.parse(spec.requests.memory))
                .let {
                    if (spec.limits.memory != null) {
                        it.addToLimits("memory", Quantity.parse(spec.limits.memory))
                    } else it
                }
                .build()
        )

    private fun ContainerBuilder.withStartupAndLivenessProbes(): ContainerBuilder =
        withNewStartupProbe()
            .withFailureThreshold(15)
            .withTimeoutSeconds(10)
            .withNewHttpGet()
            .withPath("/actuator/health")
            .withPort(IntOrString("http"))
            .endHttpGet()
            .endStartupProbe()
            .withNewLivenessProbe()
            .withFailureThreshold(2)
            .withTimeoutSeconds(10)
            .withNewHttpGet()
            .withPath("/actuator/health")
            .withPort(IntOrString("http"))
            .endHttpGet()
            .endLivenessProbe()

    val configMapName = "revents-engine-app-configmap$suffix"

    private fun ContainerBuilder.withCustomEnvVars(port: Int): ContainerBuilder =
        withEnv(
            EnvVar("SERVER_PORT", port.toString(), null),
            EnvVar("POD_NAME", null, EnvVarSource(null, ObjectFieldSelector(null, "metadata.name"), null, null)),
            EnvVar("POD_NAMESPACE", kubernetesProperties.namespace, null),
            EnvVar("SPRING_APPLICATION_NAME", configMapName, null),
            EnvVar("SPRING_PROFILES_ACTIVE", "revents", null),
            EnvVar("SPRING_CLOUD_KUBERNETES_ENABLED", "true", null),
            EnvVar("SPRING_CLOUD_KUBERNETES_CONFIG_NAME", configMapName, null),
            EnvVar("SPRING_CLOUD_KUBERNETES_CONFIG_NAMESPACE", kubernetesProperties.namespace, null),
            EnvVar("SPRING_CLOUD_KUBERNETES_CONFIG_ENABLED", "true", null),
            EnvVar("SPRING_CLOUD_KUBERNETES_CONFIG_ENABLE_API", "true", null),
            // Add some headroom as we are working with very high memory numbers,
            //  so the auto memory scaling doesn't work that well
            EnvVar("BPL_JVM_HEAD_ROOM", "2", null),
            // Use the parallel GC as we have a lot of threads
            EnvVar("JAVA_TOOL_OPTIONS", "-XX:+UseParallelGC", null)
        )

    private fun appContainers(
        aisEngineImageTag: String,
        events: Set<ScenarioEvent>,
        postProcessing: Set<ScenarioPostProcessing>
    ): List<Container> {
        val containers = events.mapIndexed { index, event ->
            val port = REVENTS_FIRST_APP_PORT + index
            ContainerBuilder()
                .withName(event.app)
                .addNewPort()
                .withName("http")
                .withContainerPort(port)
                .withProtocol("TCP")
                .endPort()
                .withImage("${kubernetesProperties.containerImages.aisEngineBase}/${event.app}:$aisEngineImageTag")
                .withImagePullPolicy("IfNotPresent")
                .withResources(requireNotNull(kubernetesProperties.resources.apps[event]))
                .withStartupAndLivenessProbes()
                .withCustomEnvVars(port)
                .build()
        }

        if (VESSEL_VOYAGE_V2 in postProcessing) {
            val vesselvoyageImageTag = requireNotNull(
                client.configMaps()
                    .inNamespace(kubernetesProperties.namespace)
                    .withName(VESSELVOYAGE_VERSION_CONFIGMAP)
                    .get().data[IMAGE_TAG_FIELD]
            )
            return containers + ContainerBuilder()
                .withName("vesselvoyage")
                .addNewPort()
                .withName("http")
                .withContainerPort(VESSELVOYAGE_PORT)
                .withProtocol("TCP")
                .endPort()
                .withImage("${kubernetesProperties.containerImages.vesselvoyage}:$vesselvoyageImageTag")
                .withImagePullPolicy("IfNotPresent")
                .withResources(requireNotNull(kubernetesProperties.resources.vesselvoyage))
                .withStartupAndLivenessProbes()
                .withCustomEnvVars(VESSELVOYAGE_PORT)
                .build()
        }

        return containers
    }
}
