mongodb:
  host: localhost
  port: 27017
  authDb: admin
  username:
  password:
  db: reventsengine

kubernetes:
  namespace: revents-jobs
  max-jobs-count: 4
  kubernetes-version-supports-sidecar-containers: true
  container-images:
    nats: 050356841556.dkr.ecr.eu-west-1.amazonaws.com/docker-hub/library/nats
    mongo: 050356841556.dkr.ecr.eu-west-1.amazonaws.com/docker-hub/library/mongo
    ais-engine-base: 050356841556.dkr.ecr.eu-west-1.amazonaws.com/ais-engine
    vesselvoyage: 050356841556.dkr.ecr.eu-west-1.amazonaws.com/vesselvoyage
  resources:
    revents-engine:
      requests:
        cpu: 2.5
        memory: 10Gi
      limits:
        memory: 16Gi
    nats:
      requests:
        cpu: 2
        memory: 2Gi
    apps:
      diff:
        requests:
          cpu: 0.75
          memory: 2Gi
        limits:
          memory: 2Gi
      stop:
        requests:
          cpu: 0.75
          memory: 2Gi
        limits:
          memory: 2Gi
      anchor:
        requests:
          cpu: 0.1
          memory: 1Gi
        limits:
          memory: 1Gi
      area:
        requests:
          cpu: 1.5
          memory: 2Gi
        limits:
          memory: 2Gi
      berth:
        requests:
          cpu: 0.1
          memory: 1Gi
        limits:
          memory: 1Gi
      encounter:
        requests:
          cpu: 1.5
          memory: 2Gi
        limits:
          memory: 2Gi
    vesselvoyage:
      requests:
        cpu: 0.1
        memory: 2Gi
      limits:
        memory: 2Gi

vesselvoyage:
  url:
  domain:
  realm:
  client-id:
  client-secret:

# Prometheus JVM stats exposing
management:
  metrics:
    enable:
      jvm: true
    tags:
      component: revents-engine-orchestrator
  endpoints:
    web:
      exposure:
        include:
          - health
          - prometheus

csi:
  url: https://csibackend.teqplay.nl
  domain: keycloak.teqplay.nl
  realm: prod
  client-id: revents-engine
  client-secret: TODO-revents
