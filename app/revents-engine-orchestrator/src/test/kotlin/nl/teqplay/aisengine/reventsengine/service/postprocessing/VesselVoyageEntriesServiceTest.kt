package nl.teqplay.aisengine.reventsengine.service.postprocessing

import nl.teqplay.aisengine.reventsengine.common.model.ship.ScenarioInterestVesselVoyage
import nl.teqplay.aisengine.reventsengine.common.service.vesselvoyage.VesselVoyageStorageService
import nl.teqplay.aisengine.reventsengine.model.TimeWindowPair
import nl.teqplay.aisengine.reventsengine.model.scenario.Scenario
import nl.teqplay.aisengine.util.withMargin
import nl.teqplay.skeleton.model.TimeWindow
import nl.teqplay.vesselvoyage.model.Visit
import nl.teqplay.vesselvoyage.model.Voyage
import org.junit.jupiter.api.Test
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.springframework.web.client.RestTemplate
import java.time.Duration
import java.time.Instant

class VesselVoyageEntriesServiceTest {

    private val vesselVoyageStorageService = mock<VesselVoyageStorageService>()
    private val restTemplate = mock<RestTemplate>()
    private val vesselVoyageEntriesService = VesselVoyageEntriesService(
        vesselVoyageStorageService,
        restTemplate,
    )

    private val scenario = "scenario"
    private val imo = 0
    private val windowMargin = Scenario.WindowMargin(2, 2)
    private val window = TimeWindow(Instant.EPOCH, Duration.ofDays(7))
    private val interestPairs = listOf(
        VesselVoyageEntriesService.InterestPair(
            windowPair = TimeWindowPair(
                window = window.withMargin(windowMargin),
                windowNoMargin = window
            ),
            partial = false
        ),
        VesselVoyageEntriesService.InterestPair(
            windowPair = TimeWindowPair(
                window = window.withMargin(windowMargin),
                windowNoMargin = window
            ),
            partial = true
        )
    )
    private val entries = listOf(
        mock<Voyage>(),
        mock<Visit>(),
    )

    @Test
    fun save() {
        vesselVoyageEntriesService.save(scenario, imo, interestPairs, entries)

        val expectedInterests = interestPairs.map {
            ScenarioInterestVesselVoyage(
                imo = imo,
                window = it.windowPair.window,
                windowNoMargin = it.windowPair.windowNoMargin,
                scenario = scenario,
                partial = it.partial
            )
        }

        verify(vesselVoyageStorageService, times(1)).save(eq(scenario), eq(expectedInterests), eq(entries))
    }
}
