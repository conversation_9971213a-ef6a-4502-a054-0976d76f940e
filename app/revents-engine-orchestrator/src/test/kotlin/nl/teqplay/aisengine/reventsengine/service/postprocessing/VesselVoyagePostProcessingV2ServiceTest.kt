package nl.teqplay.aisengine.reventsengine.service.postprocessing

import nl.teqplay.aisengine.reventsengine.common.model.ship.ScenarioInterestRelevantShip
import nl.teqplay.aisengine.reventsengine.model.TimeWindowPair
import nl.teqplay.aisengine.reventsengine.model.scenario.Scenario
import nl.teqplay.aisengine.reventsengine.service.scenario.ScenariosInterestsService
import nl.teqplay.aisengine.util.withMargin
import nl.teqplay.skeleton.model.TimeWindow
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.never
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.stream.Stream

class VesselVoyagePostProcessingV2ServiceTest {

    private val scenariosInterestsService = mock<ScenariosInterestsService>()
    private val vesselVoyageEntriesV2Service = mock<VesselVoyageEntriesV2Service>()
    private val vesselVoyagePostProcessingV2Service = VesselVoyagePostProcessingV2Service(
        scenariosInterestsService,
        vesselVoyageEntriesV2Service,
    )

    private val scenario = "scenario"
    private val window = TimeWindow(Instant.EPOCH, Duration.ofDays(7))

    @Test
    fun `run - does nothing if IMO is not set for an interest`() {
        val interests = listOf(
            ScenarioInterestRelevantShip(mmsi = 0, imo = null, window = window, scenario = scenario),
        )
        whenever(scenariosInterestsService.getUniqueInterestsByScenario(any())).thenReturn(interests)

        vesselVoyagePostProcessingV2Service.run(scenario, scenario)

        verify(scenariosInterestsService, times(1)).getUniqueInterestsByScenario(eq(scenario))
        verify(vesselVoyageEntriesV2Service, never()).save(any(), any(), any())
    }

    @Test
    fun `run - entries get generated in parallel for interests with an IMO`() {
        val interests = listOf(
            ScenarioInterestRelevantShip(mmsi = 0, imo = 0, window = window, scenario = scenario),
            ScenarioInterestRelevantShip(mmsi = 1, imo = 1, window = window, scenario = scenario),
        )
        whenever(scenariosInterestsService.getUniqueInterestsByScenario(any())).thenReturn(interests)

        vesselVoyagePostProcessingV2Service.run(scenario, scenario)

        verify(scenariosInterestsService, times(1)).getUniqueInterestsByScenario(eq(scenario))

        verify(vesselVoyageEntriesV2Service, times(1)).save(eq(scenario), eq(0), any())
        verify(vesselVoyageEntriesV2Service, times(1)).save(eq(scenario), eq(1), any())
    }

    @Test
    fun `run - entries get generated in parallel for interests with an IMO, and get saved under the parent scenario`() {
        val interests = listOf(
            ScenarioInterestRelevantShip(mmsi = 0, imo = 0, window = window, scenario = scenario),
            ScenarioInterestRelevantShip(mmsi = 1, imo = 1, window = window, scenario = scenario),
        )
        whenever(scenariosInterestsService.getUniqueInterestsByScenario(any())).thenReturn(interests)

        vesselVoyagePostProcessingV2Service.run(scenario, "parent")

        verify(scenariosInterestsService, times(1)).getUniqueInterestsByScenario(eq(scenario))

        verify(vesselVoyageEntriesV2Service, times(1)).save(eq("parent"), eq(0), any())
        verify(vesselVoyageEntriesV2Service, times(1)).save(eq("parent"), eq(1), any())
    }

    @Test
    fun `run - overlapping interests should be merged`() {
        val interests = listOf(
            ScenarioInterestRelevantShip(mmsi = 0, imo = 0, window = window, scenario = scenario),
            ScenarioInterestRelevantShip(mmsi = 1, imo = 0, window = window, scenario = scenario),
        )
        whenever(scenariosInterestsService.getUniqueInterestsByScenario(any())).thenReturn(interests)

        vesselVoyagePostProcessingV2Service.run(scenario, scenario)

        val interestPairs = listOf(
            VesselVoyageEntriesV2Service.InterestPair(
                windowPair = TimeWindowPair(window, window),
                partial = false
            )
        )
        verify(vesselVoyageEntriesV2Service, times(1)).save(eq(scenario), eq(0), eq(interestPairs))
    }

    @ParameterizedTest
    @MethodSource("runOverlappingPartialInterestsSource")
    fun `run - overlapping interests should be merged, including window pairs`(
        partialOverlapping1: Boolean,
        partialOverlapping2: Boolean,
        partialNonOverlapping3: Boolean
    ) {
        val windowMargin = Scenario.WindowMargin(1, 1)
        val overlappingWindow1 = TimeWindow(Instant.EPOCH, Duration.ofDays(7))
        val overlappingWindow2 = TimeWindow(Instant.EPOCH.plus(1, ChronoUnit.DAYS), Duration.ofDays(7))
        val nonOverlappingWindow3 = TimeWindow(Instant.EPOCH.plus(14, ChronoUnit.DAYS), Duration.ofDays(7))
        val interests = listOf(
            ScenarioInterestRelevantShip(
                mmsi = 0,
                imo = 0,
                window = overlappingWindow1.withMargin(windowMargin),
                windowNoMargin = overlappingWindow1,
                scenario = scenario,
                partial = partialOverlapping1
            ),
            ScenarioInterestRelevantShip(
                mmsi = 1,
                imo = 0,
                window = overlappingWindow2.withMargin(windowMargin),
                windowNoMargin = overlappingWindow2,
                scenario = scenario,
                partial = partialOverlapping2
            ),
            ScenarioInterestRelevantShip(
                mmsi = 0,
                imo = 0,
                window = nonOverlappingWindow3.withMargin(windowMargin),
                windowNoMargin = nonOverlappingWindow3,
                scenario = scenario,
                partial = partialNonOverlapping3
            ),
        )
        whenever(scenariosInterestsService.getUniqueInterestsByScenario(any())).thenReturn(interests)

        vesselVoyagePostProcessingV2Service.run(scenario, scenario)

        val interestPairs = listOf(
            VesselVoyageEntriesV2Service.InterestPair(
                windowPair = TimeWindowPair(
                    window = TimeWindow(
                        from = overlappingWindow1.withMargin(windowMargin).from,
                        to = overlappingWindow2.withMargin(windowMargin).to
                    ),
                    windowNoMargin = TimeWindow(
                        from = overlappingWindow1.from,
                        to = overlappingWindow2.to
                    ),
                ),
                partial = partialOverlapping1 || partialOverlapping2
            ),
            VesselVoyageEntriesV2Service.InterestPair(
                windowPair = TimeWindowPair(
                    window = nonOverlappingWindow3.withMargin(windowMargin),
                    windowNoMargin = nonOverlappingWindow3
                ),
                partial = partialNonOverlapping3
            ),
        )
        verify(vesselVoyageEntriesV2Service, times(1)).save(eq(scenario), eq(0), eq(interestPairs))
    }

    @Test
    fun `run - should preserve partial flag when storing interests`() {
        val interests = listOf(
            ScenarioInterestRelevantShip(mmsi = 0, imo = 0, window = window, scenario = scenario, partial = false),
            ScenarioInterestRelevantShip(mmsi = 0, imo = 1, window = window, scenario = scenario, partial = true),
        )
        whenever(scenariosInterestsService.getUniqueInterestsByScenario(any())).thenReturn(interests)

        val data = mutableMapOf<Int, List<VesselVoyageEntriesV2Service.InterestPair>>()
        whenever(vesselVoyageEntriesV2Service.save(any(), any(), any())).thenAnswer {
            data[it.getArgument(1)] = it.getArgument(2)
            return@thenAnswer Unit
        }

        vesselVoyagePostProcessingV2Service.run(scenario, scenario)

        assertEquals(
            mapOf(
                0 to listOf(
                    VesselVoyageEntriesV2Service.InterestPair(
                        windowPair = TimeWindowPair(window, window),
                        partial = false
                    )
                ),
                1 to listOf(
                    VesselVoyageEntriesV2Service.InterestPair(
                        windowPair = TimeWindowPair(window, window),
                        partial = true
                    )
                )
            ),
            data.toSortedMap()
        )
    }

    @Test
    fun `run - should preserve partial flag when overlapping windows, but non-overlapping windowNoMargins`() {
        val window1 = TimeWindow(
            from = Instant.parse("2024-03-26T00:00:00Z"),
            to = Instant.parse("2024-04-05T00:00:00Z")
        )
        val windowNoMargin1 = TimeWindow(
            from = Instant.parse("2024-03-30T00:00:00Z"),
            to = Instant.parse("2024-04-01T00:00:00Z")
        )
        val window2 = TimeWindow(
            from = Instant.parse("2024-03-29T00:00:00Z"),
            to = Instant.parse("2024-04-10T00:00:00Z")
        )
        val windowNoMargin2 = TimeWindow(
            from = Instant.parse("2024-04-02T00:00:00Z"),
            to = Instant.parse("2024-04-06T00:00:00Z")
        )
        val interests = listOf(
            ScenarioInterestRelevantShip(
                mmsi = 205340000,
                imo = 9261487,
                window = window1,
                windowNoMargin = windowNoMargin1,
                scenario = scenario,
                partial = true
            ),
            ScenarioInterestRelevantShip(
                mmsi = 205340000,
                imo = 9261487,
                window = window2,
                windowNoMargin = windowNoMargin2,
                scenario = scenario,
                partial = false
            ),
        )
        whenever(scenariosInterestsService.getUniqueInterestsByScenario(any())).thenReturn(interests)

        val data = mutableMapOf<Int, List<VesselVoyageEntriesV2Service.InterestPair>>()
        whenever(vesselVoyageEntriesV2Service.save(any(), any(), any())).thenAnswer {
            data[it.getArgument(1)] = it.getArgument(2)
            return@thenAnswer Unit
        }

        vesselVoyagePostProcessingV2Service.run(scenario, scenario)

        assertEquals(
            mapOf(
                9261487 to listOf(
                    VesselVoyageEntriesV2Service.InterestPair(
                        windowPair = TimeWindowPair(window1, windowNoMargin1),
                        partial = true
                    ),
                    VesselVoyageEntriesV2Service.InterestPair(
                        windowPair = TimeWindowPair(window2, windowNoMargin2),
                        partial = false
                    )
                )
            ),
            data.toSortedMap()
        )
    }

    companion object {
        /**
         * Partial settings for:
         * 0: overlapping window
         * 1: overlapping window
         * 2: non-overlapping window
         *
         * overlapping windows should be merged, partial=0.partial || 1.partial
         * non-overlapping window preserves partial flag
         */
        @JvmStatic
        private fun runOverlappingPartialInterestsSource() = Stream.of(
            Arguments.of(false, false, false),

            Arguments.of(true, false, false),
            Arguments.of(false, true, false),
            Arguments.of(false, false, true),

            Arguments.of(true, true, true),
            Arguments.of(true, true, false),
            Arguments.of(true, false, true),
            Arguments.of(false, true, true),
        )
    }
}
