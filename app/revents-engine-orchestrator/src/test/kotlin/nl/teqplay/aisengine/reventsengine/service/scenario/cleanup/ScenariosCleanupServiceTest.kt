package nl.teqplay.aisengine.reventsengine.service.scenario.cleanup

import nl.teqplay.aisengine.reventsengine.common.datasource.ScenariosDataSource
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.INITIALIZING_ASSIGN_CONTEXT
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.PROGRESSING
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.PROGRESSING_END
import nl.teqplay.aisengine.reventsengine.common.service.cleanup.ScenariosCleanupTaskService
import nl.teqplay.aisengine.reventsengine.service.scenario.ScenariosQueueService
import nl.teqplay.aisengine.reventsengine.service.scenario.scenarioCreateRequest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.mock
import org.mockito.kotlin.never
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

class ScenariosCleanupServiceTest {

    private val scenariosDataSource = mock<ScenariosDataSource>()
    private val scenariosQueueService = mock<ScenariosQueueService>()
    private val scenariosCleanupTaskService = mock<ScenariosCleanupTaskService>()
    private val scenariosCleanupService = ScenariosCleanupService(
        scenariosQueueService, scenariosCleanupTaskService, scenariosDataSource
    )

    private val scenario1 = ScenarioState(scenarioCreateRequest).copy(phase = PROGRESSING)
    private val scenario2 = ScenarioState(scenarioCreateRequest).copy(phase = PROGRESSING)
    private val scenario3 = ScenarioState(scenarioCreateRequest).copy(phase = INITIALIZING_ASSIGN_CONTEXT)

    @Test
    fun cleanup() {
        whenever(scenariosQueueService.getCurrentScenarios())
            .thenReturn(listOf(scenario1, scenario2))
            .thenReturn(listOf(scenario3))
            .thenReturn(emptyList())
        whenever(scenariosDataSource.save(any())).thenAnswer {
            val scenario = it.arguments[0] as ScenarioState
            assertEquals(scenario.phase, PROGRESSING_END)
            assertTrue(scenario.id.startsWith("TEARDOWN_"))
        }

        scenariosCleanupService.cleanup()

        verify(scenariosQueueService, times(3)).getCurrentScenarios()
        verify(scenariosCleanupTaskService, times(3)).setToCrashedAfterRestart(any())
        verify(scenariosDataSource, times(3)).save(anyOrNull())
    }

    @Test
    fun `cleanup - nothing in queue`() {
        whenever(scenariosQueueService.getCurrentScenarios()).thenReturn(emptyList())

        scenariosCleanupService.cleanup()

        verify(scenariosQueueService, times(1)).getCurrentScenarios()
        verify(scenariosCleanupTaskService, never()).setToCrashedAfterRestart(any())
        verify(scenariosDataSource, never()).save(anyOrNull())
    }
}
