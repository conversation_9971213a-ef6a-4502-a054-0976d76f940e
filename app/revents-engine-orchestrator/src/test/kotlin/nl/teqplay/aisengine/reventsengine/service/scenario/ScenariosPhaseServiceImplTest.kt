package nl.teqplay.aisengine.reventsengine.service.scenario

import nl.teqplay.aisengine.reventsengine.common.model.exception.ScenarioCrashedException
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.CRASHED
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.FINISHED
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.INITIALIZING_ASSIGN_CONTEXT
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.INITIALIZING_END
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.INITIALIZING_JOB_CREATE
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.INITIALIZING_JOB_WAIT_READY
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.INITIALIZING_QUEUED
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.INITIALIZING_RUN_SCENARIO
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.INITIALIZING_STREAMING
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.POST_PROCESSING_INIT
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.POST_PROCESSING_TEARDOWN
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.POST_PROCESSING_VESSEL_VOYAGE
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.POST_PROCESSING_VESSEL_VOYAGE_V2
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.POST_PROCESSING_VESSEL_VOYAGE_V2_WAIT
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.POST_PROCESSING_VESSEL_VOYAGE_WAIT
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.PROGRESSING
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.PROGRESSING_END
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.QUEUED
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.QUEUED_END
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.QUEUED_INITIALIZE_INTERESTS
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.QUEUED_SCENARIO_NOTIFY_EXTERNAL
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.TEARDOWN_JOB
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.TEARDOWN_STREAMING
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.TEARDOWN_UNASSIGN_CONTEXT
import nl.teqplay.aisengine.reventsengine.common.model.ship.ScenarioInterestRelevantShip
import nl.teqplay.aisengine.reventsengine.common.service.scenario.ScenariosMetadataService
import nl.teqplay.aisengine.reventsengine.common.service.scenario.ScenariosPhaseService
import nl.teqplay.aisengine.reventsengine.global.model.InterestsRunProgress
import nl.teqplay.aisengine.reventsengine.global.model.InterestsRunStart
import nl.teqplay.aisengine.reventsengine.model.exception.ScenarioCrashReason
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioEvent
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioPostProcessing.VESSEL_VOYAGE
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioPostProcessing.VESSEL_VOYAGE_V2
import nl.teqplay.aisengine.reventsengine.service.scenario.internal.KubernetesJobService
import nl.teqplay.aisengine.reventsengine.service.scenario.internal.PostProcessingService
import nl.teqplay.aisengine.reventsengine.service.scenario.internal.ReventsEngineContext
import nl.teqplay.aisengine.reventsengine.service.scenario.internal.ReventsEngineContextService
import nl.teqplay.skeleton.model.TimeWindow
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.doNothing
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.never
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.Duration
import java.time.Instant

class ScenariosPhaseServiceImplTest {

    private val kubernetesJobService = mock<KubernetesJobService>()
    private val scenariosInterestsService = mock<ScenariosInterestsService>()
    private val scenariosMetadataService = mock<ScenariosMetadataService>()
    private val reventsEngineContext = mock<ReventsEngineContext>()
    private val reventsEngineContextService = mock<ReventsEngineContextService>().apply {
        whenever(getContext(any())).thenReturn(reventsEngineContext)
        whenever(assignToContext(any())).thenReturn(0)
        doNothing().whenever(this).unassignFromContext(any())
    }
    private val postProcessingService = mock<PostProcessingService>()

    private val scenariosPhaseService = ScenariosPhaseServiceImpl(
        kubernetesJobService,
        scenariosInterestsService,
        scenariosMetadataService,
        reventsEngineContextService,
        postProcessingService,
    )

    private val scenario = ScenarioState(scenarioCreateRequest).copy(
        status = ScenarioState.InternalStatus(Instant.EPOCH),
        events = ScenarioEvent.values().toSet()
    )
    private val defaultReturn = ScenariosPhaseService.RunPhaseResult(scenario.status, true)

    @Test
    fun `runPhase - queued,finished,crashed`() {
        assertEquals(defaultReturn, scenariosPhaseService.runPhase(scenario.copy(phase = QUEUED)))
        assertEquals(defaultReturn, scenariosPhaseService.runPhase(scenario.copy(phase = FINISHED)))
        assertEquals(defaultReturn, scenariosPhaseService.runPhase(scenario.copy(phase = CRASHED)))
    }

    @Test
    fun `runPhase - initializing queued`() {
        assertNotNull(scenariosPhaseService.runPhase(scenario.copy(phase = INITIALIZING_QUEUED)).newStatus?.start)
    }

    @Test
    fun `runPhase - initializing queued - skips to post-processing if inherited`() {
        val result = scenariosPhaseService.runPhase(scenario.copy(phase = INITIALIZING_QUEUED, inherit = "inherit"))
        val start = result.newStatus?.start
        assertNotNull(start)
        assertEquals(start, result.newStatus?.initialized)
        assertEquals(start, result.newStatus?.progressed)
        assertEquals(POST_PROCESSING_INIT, result.skipToPhase)
    }

    @Test
    fun `runPhase - initializing end`() {
        assertNotNull(scenariosPhaseService.runPhase(scenario.copy(phase = INITIALIZING_END)).newStatus?.initialized)
    }

    @Test
    fun `runPhase - progressing end`() {
        assertNotNull(scenariosPhaseService.runPhase(scenario.copy(phase = PROGRESSING_END)).newStatus?.progressed)
    }

    @Test
    fun `runPhase - assign,unassign node`() {
        whenever(reventsEngineContextService.assignToContext(any())).thenReturn(0)

        assertEquals(
            defaultReturn.copy(newStatus = defaultReturn.newStatus!!.copy(contextIndex = 0)),
            scenariosPhaseService.runPhase(scenario.copy(phase = INITIALIZING_ASSIGN_CONTEXT))
        )
        verify(reventsEngineContextService, times(1)).assignToContext(any())

        assertEquals(defaultReturn, scenariosPhaseService.runPhase(scenario.copy(phase = TEARDOWN_UNASSIGN_CONTEXT)))
        verify(reventsEngineContextService, times(1)).unassignFromContext(any())
    }

    @Test
    fun `runPhase - initialize,teardown job`() {
        whenever(kubernetesJobService.initialize(any(), any(), any())).thenReturn(true)
        whenever(kubernetesJobService.teardown(any())).thenReturn(true)

        assertEquals(defaultReturn, scenariosPhaseService.runPhase(scenario.copy(phase = INITIALIZING_JOB_CREATE)))
        verify(kubernetesJobService, times(1)).initialize(any(), any(), any())

        assertEquals(defaultReturn, scenariosPhaseService.runPhase(scenario.copy(phase = TEARDOWN_JOB)))
        verify(kubernetesJobService, times(1)).teardown(any())
    }

    @Test
    fun `runPhase - wait for initializing job`() {
        whenever(kubernetesJobService.waitUntilReady(any())).thenReturn(true)

        assertEquals(defaultReturn, scenariosPhaseService.runPhase(scenario.copy(phase = INITIALIZING_JOB_WAIT_READY)))
        verify(kubernetesJobService, times(1)).waitUntilReady(any())
    }

    @Test
    fun `runPhase - noop`() {
        assertThrows<ScenarioCrashedException> {
            scenariosPhaseService.runPhase(scenario.copy(phase = QUEUED_INITIALIZE_INTERESTS))
        }
        assertThrows<ScenarioCrashedException> {
            scenariosPhaseService.runPhase(scenario.copy(phase = QUEUED_SCENARIO_NOTIFY_EXTERNAL))
        }
        assertThrows<ScenarioCrashedException> {
            scenariosPhaseService.runPhase(scenario.copy(phase = QUEUED_END))
        }
    }

    @Test
    fun `runPhase - run scenario`() {
        val window = TimeWindow(Instant.EPOCH, Duration.ofDays(1))
        val interest = ScenarioInterestRelevantShip(0, null, window, scenario = "scenario")
        whenever(scenariosInterestsService.getUniqueInterestsByScenario(any()))
            .thenReturn(listOf(interest))

        whenever(reventsEngineContext.startScenario(any())).thenReturn(false)
        assertEquals(
            ScenariosPhaseService.RunPhaseResult(scenario.status, false),
            scenariosPhaseService.runPhase(scenario.copy(phase = INITIALIZING_RUN_SCENARIO))
        )

        whenever(reventsEngineContext.startScenario(any())).thenAnswer {
            val interests = it.getArgument<InterestsRunStart>(0)
            assertEquals(
                listOf(InterestsRunStart.RelevantShip(0, window)),
                interests.interests
            )
            return@thenAnswer true
        }
        assertEquals(
            ScenariosPhaseService.RunPhaseResult(scenario.status, true),
            scenariosPhaseService.runPhase(scenario.copy(phase = INITIALIZING_RUN_SCENARIO))
        )
        verify(reventsEngineContext, times(2)).startScenario(any())
    }

    @Test
    fun `runPhase - run scenario - merges overlapping time windows`() {
        val window1 = TimeWindow(Instant.EPOCH, Duration.ofDays(1))
        val window2 = TimeWindow(window1.from, Duration.ofDays(1))
        val window3 = TimeWindow(window2.from, Duration.ofDays(1))
        val interest1 = ScenarioInterestRelevantShip(0, null, window1, scenario = "scenario")
        val interest2 = ScenarioInterestRelevantShip(0, null, window2, scenario = "scenario")
        val interest3 = ScenarioInterestRelevantShip(0, null, window3, scenario = "scenario")
        whenever(scenariosInterestsService.getUniqueInterestsByScenario(any()))
            .thenReturn(listOf(interest1, interest2, interest3))

        whenever(reventsEngineContext.startScenario(any())).thenReturn(false)
        assertEquals(
            ScenariosPhaseService.RunPhaseResult(scenario.status, false),
            scenariosPhaseService.runPhase(scenario.copy(phase = INITIALIZING_RUN_SCENARIO))
        )

        whenever(reventsEngineContext.startScenario(any())).thenAnswer {
            val interests = it.getArgument<InterestsRunStart>(0)
            assertEquals(
                listOf(InterestsRunStart.RelevantShip(0, TimeWindow(window1.from, window3.to))),
                interests.interests
            )
            return@thenAnswer true
        }
        assertEquals(
            ScenariosPhaseService.RunPhaseResult(scenario.status, true),
            scenariosPhaseService.runPhase(scenario.copy(phase = INITIALIZING_RUN_SCENARIO))
        )
        verify(reventsEngineContext, times(2)).startScenario(any())
    }

    @Test
    fun `runPhase - run scenario - metadata from parent scenario`() {
        val parentScenarioId = "parent"
        val childScenario = scenario.copy(parent = parentScenarioId)

        whenever(scenariosInterestsService.getUniqueInterestsByScenario(any())).thenReturn(emptyList())

        whenever(reventsEngineContext.startScenario(any())).thenReturn(true)
        assertEquals(
            ScenariosPhaseService.RunPhaseResult(childScenario.status, true),
            scenariosPhaseService.runPhase(childScenario.copy(phase = INITIALIZING_RUN_SCENARIO))
        )

        verify(reventsEngineContext, times(1)).startScenario(any())
        verify(scenariosMetadataService, times(1)).getMetadataByScenario(eq(parentScenarioId))
    }

    @Test
    fun `runPhase - progressing`() {
        whenever(reventsEngineContext.requestProgress()).thenReturn(null)
        assertEquals(
            ScenariosPhaseService.RunPhaseResult(scenario.status, false),
            scenariosPhaseService.runPhase(scenario.copy(phase = PROGRESSING))
        )

        whenever(reventsEngineContext.requestProgress())
            .thenReturn(InterestsRunProgress("${scenario.id}_different", scenario.window, Instant.EPOCH))
        assertThrows<ScenarioCrashedException> { scenariosPhaseService.runPhase(scenario.copy(phase = PROGRESSING)) }

        val progress = InterestsRunProgress(scenario.id, scenario.window, Instant.EPOCH)
        whenever(reventsEngineContext.requestProgress()).thenReturn(progress)
        assertEquals(
            ScenariosPhaseService.RunPhaseResult(scenario.status?.copy(progress = progress), false),
            scenariosPhaseService.runPhase(scenario.copy(phase = PROGRESSING))
        )

        val progressDone = progress.copy(done = true)
        whenever(reventsEngineContext.requestProgress()).thenReturn(progressDone)
        assertEquals(
            ScenariosPhaseService.RunPhaseResult(scenario.status?.copy(progress = progressDone), true),
            scenariosPhaseService.runPhase(scenario.copy(phase = PROGRESSING))
        )
    }

    @Test
    fun `runPhase - init,teardown streaming`() {
        whenever(reventsEngineContext.startConsuming(any())).thenReturn(true)
        whenever(reventsEngineContext.stopConsuming()).thenReturn(true)

        assertEquals(defaultReturn, scenariosPhaseService.runPhase(scenario.copy(phase = INITIALIZING_STREAMING)))
        verify(reventsEngineContext, times(1)).startConsuming(any())

        assertEquals(defaultReturn, scenariosPhaseService.runPhase(scenario.copy(phase = TEARDOWN_STREAMING)))
        verify(reventsEngineContext, times(1)).stopConsuming()
    }

    @Test
    fun `runPhase - post-processing - no post-processing, skip to end of post-processing`() {
        assertEquals(
            POST_PROCESSING_TEARDOWN,
            scenariosPhaseService.runPhase(scenario.copy(phase = POST_PROCESSING_INIT, postProcessing = emptySet())).skipToPhase
        )
    }

    @Test
    fun `runPhase - post-processing - teardown, skip to end of post-processing`() {
        assertEquals(
            POST_PROCESSING_TEARDOWN,
            scenariosPhaseService.runPhase(scenario.copy(phase = POST_PROCESSING_INIT, postProcessing = setOf(VESSEL_VOYAGE), teardown = true)).skipToPhase
        )
    }

    @Test
    fun `runPhase - post-processing - VesselVoyage crashes`() {
        // waiting waits to be finished
        assertFalse(
            scenariosPhaseService.runPhase(
                scenario.copy(
                    phase = POST_PROCESSING_VESSEL_VOYAGE_WAIT,
                    postProcessing = setOf(VESSEL_VOYAGE),
                )
            ).moveToNextPhase
        )

        // Instead, VesselVoyage crashes and we receive a crashed exception.
        whenever(postProcessingService.isVesselVoyagePostProcessingFinished(any()))
            .thenThrow(ScenarioCrashedException(ScenarioCrashReason.POST_PROCESSING_VESSEL_VOYAGE_FAILED))

        assertThrows<ScenarioCrashedException> {
            scenariosPhaseService.runPhase(
                scenario.copy(
                    phase = POST_PROCESSING_VESSEL_VOYAGE_WAIT,
                    postProcessing = setOf(VESSEL_VOYAGE),
                )
            )
        }
    }

    @Test
    fun `runPhase - post-processing - VesselVoyage`() {
        // skip if no post-processing is set
        assertTrue(
            scenariosPhaseService.runPhase(
                scenario.copy(
                    phase = POST_PROCESSING_VESSEL_VOYAGE,
                    postProcessing = emptySet(),
                    parent = "parent",
                )
            ).moveToNextPhase
        )
        assertTrue(
            scenariosPhaseService.runPhase(
                scenario.copy(
                    phase = POST_PROCESSING_VESSEL_VOYAGE_WAIT,
                    postProcessing = emptySet(),
                    parent = "parent",
                )
            ).moveToNextPhase
        )

        // waiting waits to be finished
        assertFalse(
            scenariosPhaseService.runPhase(
                scenario.copy(
                    phase = POST_PROCESSING_VESSEL_VOYAGE_WAIT,
                    postProcessing = setOf(VESSEL_VOYAGE),
                    parent = "parent",
                )
            ).moveToNextPhase
        )
        whenever(postProcessingService.isVesselVoyagePostProcessingFinished(any())).thenReturn(true)
        assertTrue(
            scenariosPhaseService.runPhase(
                scenario.copy(
                    phase = POST_PROCESSING_VESSEL_VOYAGE_WAIT,
                    postProcessing = setOf(VESSEL_VOYAGE),
                    parent = "parent",
                )
            ).moveToNextPhase
        )

        // start processing
        verify(postProcessingService, never()).startVesselVoyagePostProcessing(any(), any(), anyOrNull())
        assertTrue(
            scenariosPhaseService.runPhase(
                scenario.copy(
                    phase = POST_PROCESSING_VESSEL_VOYAGE,
                    postProcessing = setOf(VESSEL_VOYAGE),
                    parent = "parent",
                )
            ).moveToNextPhase
        )
        verify(postProcessingService, times(1))
            .startVesselVoyagePostProcessing(eq(scenario.id), eq(scenario.id), eq("parent"))
    }

    @Test
    fun `runPhase - post-processing - VesselVoyage - using inherited scenario`() {
        // waiting waits to be finished
        assertFalse(
            scenariosPhaseService.runPhase(
                scenario.copy(
                    phase = POST_PROCESSING_VESSEL_VOYAGE_WAIT,
                    postProcessing = setOf(VESSEL_VOYAGE),
                    inherit = "inherit",
                )
            ).moveToNextPhase
        )
        whenever(postProcessingService.isVesselVoyagePostProcessingFinished(eq(scenario.id))).thenReturn(true)
        assertTrue(
            scenariosPhaseService.runPhase(
                scenario.copy(
                    phase = POST_PROCESSING_VESSEL_VOYAGE_WAIT,
                    postProcessing = setOf(VESSEL_VOYAGE),
                    inherit = "inherit",
                )
            ).moveToNextPhase
        )

        // start processing
        verify(postProcessingService, never()).startVesselVoyagePostProcessing(any(), any(), anyOrNull())
        assertTrue(
            scenariosPhaseService.runPhase(
                scenario.copy(
                    phase = POST_PROCESSING_VESSEL_VOYAGE,
                    postProcessing = setOf(VESSEL_VOYAGE),
                    inherit = "inherit"
                )
            ).moveToNextPhase
        )
        verify(postProcessingService, times(1))
            .startVesselVoyagePostProcessing(eq(scenario.id), eq("inherit"), eq(scenario.id))
    }

    @Test
    fun `runPhase - post-processing - VesselVoyage - using inherited AND forked scenario`() {
        // waiting waits to be finished
        assertFalse(
            scenariosPhaseService.runPhase(
                scenario.copy(
                    phase = POST_PROCESSING_VESSEL_VOYAGE_WAIT,
                    postProcessing = setOf(VESSEL_VOYAGE),
                    parent = "parent",
                    inherit = "inherit",
                )
            ).moveToNextPhase
        )
        whenever(postProcessingService.isVesselVoyagePostProcessingFinished(eq(scenario.id))).thenReturn(true)
        assertTrue(
            scenariosPhaseService.runPhase(
                scenario.copy(
                    phase = POST_PROCESSING_VESSEL_VOYAGE_WAIT,
                    postProcessing = setOf(VESSEL_VOYAGE),
                    parent = "parent",
                    inherit = "inherit",
                )
            ).moveToNextPhase
        )

        // start processing
        verify(postProcessingService, never()).startVesselVoyagePostProcessing(any(), any(), anyOrNull())
        assertTrue(
            scenariosPhaseService.runPhase(
                scenario.copy(
                    phase = POST_PROCESSING_VESSEL_VOYAGE,
                    postProcessing = setOf(VESSEL_VOYAGE),
                    parent = "parent",
                    inherit = "inherit"
                )
            ).moveToNextPhase
        )
        verify(postProcessingService, times(1))
            .startVesselVoyagePostProcessing(eq(scenario.id), eq("inherit"), eq("parent"))
    }

    @Test
    fun `runPhase - post-processing - VesselVoyage V2 crashes`() {
        // waiting waits to be finished
        assertFalse(
            scenariosPhaseService.runPhase(
                scenario.copy(
                    phase = POST_PROCESSING_VESSEL_VOYAGE_V2_WAIT,
                    postProcessing = setOf(VESSEL_VOYAGE_V2),
                )
            ).moveToNextPhase
        )

        // Instead, VesselVoyage crashes and we receive a crashed exception.
        whenever(postProcessingService.isVesselVoyagePostProcessingV2Finished(any()))
            .thenThrow(ScenarioCrashedException(ScenarioCrashReason.POST_PROCESSING_VESSEL_VOYAGE_V2_FAILED))

        assertThrows<ScenarioCrashedException> {
            scenariosPhaseService.runPhase(
                scenario.copy(
                    phase = POST_PROCESSING_VESSEL_VOYAGE_V2_WAIT,
                    postProcessing = setOf(VESSEL_VOYAGE_V2),
                )
            )
        }
    }

    @Test
    fun `runPhase - post-processing - VesselVoyage V2`() {
        // skip if no post-processing is set
        assertTrue(
            scenariosPhaseService.runPhase(
                scenario.copy(
                    phase = POST_PROCESSING_VESSEL_VOYAGE_V2,
                    postProcessing = emptySet(),
                    parent = "parent",
                )
            ).moveToNextPhase
        )
        assertTrue(
            scenariosPhaseService.runPhase(
                scenario.copy(
                    phase = POST_PROCESSING_VESSEL_VOYAGE_V2_WAIT,
                    postProcessing = emptySet(),
                    parent = "parent",
                )
            ).moveToNextPhase
        )

        // waiting waits to be finished
        assertFalse(
            scenariosPhaseService.runPhase(
                scenario.copy(
                    phase = POST_PROCESSING_VESSEL_VOYAGE_V2_WAIT,
                    postProcessing = setOf(VESSEL_VOYAGE_V2),
                    parent = "parent",
                )
            ).moveToNextPhase
        )
        whenever(postProcessingService.isVesselVoyagePostProcessingV2Finished(any())).thenReturn(true)
        assertTrue(
            scenariosPhaseService.runPhase(
                scenario.copy(
                    phase = POST_PROCESSING_VESSEL_VOYAGE_V2_WAIT,
                    postProcessing = setOf(VESSEL_VOYAGE_V2),
                    parent = "parent",
                )
            ).moveToNextPhase
        )

        // start processing
        verify(postProcessingService, never()).startVesselVoyagePostProcessingV2(any(), any(), anyOrNull())
        assertTrue(
            scenariosPhaseService.runPhase(
                scenario.copy(
                    phase = POST_PROCESSING_VESSEL_VOYAGE_V2,
                    postProcessing = setOf(VESSEL_VOYAGE_V2),
                    parent = "parent",
                )
            ).moveToNextPhase
        )
        verify(postProcessingService, times(1))
            .startVesselVoyagePostProcessingV2(eq(scenario.id), eq(scenario.id), eq("parent"))
    }

    @Test
    fun `runPhase - post-processing - VesselVoyage V2 - using inherited scenario`() {
        // waiting waits to be finished
        assertFalse(
            scenariosPhaseService.runPhase(
                scenario.copy(
                    phase = POST_PROCESSING_VESSEL_VOYAGE_V2_WAIT,
                    postProcessing = setOf(VESSEL_VOYAGE_V2),
                    inherit = "inherit",
                )
            ).moveToNextPhase
        )
        whenever(postProcessingService.isVesselVoyagePostProcessingV2Finished(eq(scenario.id))).thenReturn(true)
        assertTrue(
            scenariosPhaseService.runPhase(
                scenario.copy(
                    phase = POST_PROCESSING_VESSEL_VOYAGE_V2_WAIT,
                    postProcessing = setOf(VESSEL_VOYAGE_V2),
                    inherit = "inherit",
                )
            ).moveToNextPhase
        )

        // start processing
        verify(postProcessingService, never()).startVesselVoyagePostProcessingV2(any(), any(), anyOrNull())
        assertTrue(
            scenariosPhaseService.runPhase(
                scenario.copy(
                    phase = POST_PROCESSING_VESSEL_VOYAGE_V2,
                    postProcessing = setOf(VESSEL_VOYAGE_V2),
                    inherit = "inherit"
                )
            ).moveToNextPhase
        )
        verify(postProcessingService, times(1))
            .startVesselVoyagePostProcessingV2(eq(scenario.id), eq("inherit"), eq(scenario.id))
    }

    @Test
    fun `runPhase - post-processing - VesselVoyage V2 - using inherited AND forked scenario`() {
        // waiting waits to be finished
        assertFalse(
            scenariosPhaseService.runPhase(
                scenario.copy(
                    phase = POST_PROCESSING_VESSEL_VOYAGE_V2_WAIT,
                    postProcessing = setOf(VESSEL_VOYAGE_V2),
                    parent = "parent",
                    inherit = "inherit",
                )
            ).moveToNextPhase
        )
        whenever(postProcessingService.isVesselVoyagePostProcessingV2Finished(eq(scenario.id))).thenReturn(true)
        assertTrue(
            scenariosPhaseService.runPhase(
                scenario.copy(
                    phase = POST_PROCESSING_VESSEL_VOYAGE_V2_WAIT,
                    postProcessing = setOf(VESSEL_VOYAGE_V2),
                    parent = "parent",
                    inherit = "inherit",
                )
            ).moveToNextPhase
        )

        // start processing
        verify(postProcessingService, never()).startVesselVoyagePostProcessingV2(any(), any(), anyOrNull())
        assertTrue(
            scenariosPhaseService.runPhase(
                scenario.copy(
                    phase = POST_PROCESSING_VESSEL_VOYAGE_V2,
                    postProcessing = setOf(VESSEL_VOYAGE_V2),
                    parent = "parent",
                    inherit = "inherit"
                )
            ).moveToNextPhase
        )
        verify(postProcessingService, times(1))
            .startVesselVoyagePostProcessingV2(eq(scenario.id), eq("inherit"), eq("parent"))
    }
}
