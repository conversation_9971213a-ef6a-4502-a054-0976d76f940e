package nl.teqplay.aisengine.reventsengine.service.postprocessing

import nl.teqplay.aisengine.reventsengine.common.model.ship.ScenarioInterestVesselVoyage
import nl.teqplay.aisengine.reventsengine.common.service.vesselvoyage.VesselVoyageStorageV2Service
import nl.teqplay.aisengine.reventsengine.model.TimeWindowPair
import nl.teqplay.aisengine.reventsengine.model.scenario.Scenario
import nl.teqplay.aisengine.util.withMargin
import nl.teqplay.skeleton.model.TimeWindow
import org.junit.jupiter.api.Test
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import java.time.Duration
import java.time.Instant

class VesselVoyageEntriesV2ServiceTest {

    private val vesselVoyageStorageV2Service = mock<VesselVoyageStorageV2Service>()
    private val vesselVoyageEntriesV2Service = VesselVoyageEntriesV2Service(vesselVoyageStorageV2Service)

    private val scenario = "scenario"
    private val imo = 0
    private val windowMargin = Scenario.WindowMargin(2, 2)
    private val window = TimeWindow(Instant.EPOCH, Duration.ofDays(7))
    private val interestPairs = listOf(
        VesselVoyageEntriesV2Service.InterestPair(
            windowPair = TimeWindowPair(
                window = window.withMargin(windowMargin),
                windowNoMargin = window
            ),
            partial = false
        ),
        VesselVoyageEntriesV2Service.InterestPair(
            windowPair = TimeWindowPair(
                window = window.withMargin(windowMargin),
                windowNoMargin = window
            ),
            partial = true
        )
    )

    @Test
    fun save() {
        vesselVoyageEntriesV2Service.save(scenario, imo, interestPairs)

        val expectedInterests = interestPairs.map {
            ScenarioInterestVesselVoyage(
                imo = imo,
                window = it.windowPair.window,
                windowNoMargin = it.windowPair.windowNoMargin,
                scenario = scenario,
                partial = it.partial
            )
        }

        verify(vesselVoyageStorageV2Service, times(1)).save(eq(scenario), eq(expectedInterests))
    }
}
