global:
  storageClass: "gp2"
  namespaceOverride: "teqplay-app"

image:
  repository: 050356841556.dkr.ecr.eu-west-1.amazonaws.com/ais-engine/stop-monitor

resources:
  requests:
    cpu: 0.2
    memory: 2Gi
  limits:
    memory: 2Gi

mongodb:
  enabled: false

podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/path: /actuator/prometheus
  prometheus.io/port: "8080"

nodeSelector:
  app.teqplay.nl/nodegroup: ais-engine

tolerations:
  - key: nodegroup
    operator: Equal
    value: ais-engine
    effect: NoSchedule
