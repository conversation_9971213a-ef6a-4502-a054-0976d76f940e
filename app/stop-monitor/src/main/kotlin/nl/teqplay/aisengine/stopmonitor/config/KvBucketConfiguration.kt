package nl.teqplay.aisengine.stopmonitor.config

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.aisengine.nats.stream.KeyValueBucketService
import nl.teqplay.aisengine.nats.stream.properties.EventStreamNatsProperties
import nl.teqplay.aisengine.stopmonitor.model.StopState
import nl.teqplay.aisengine.stopmonitor.model.StopTraceState
import nl.teqplay.skeleton.nats.NatsKeyValueBucket
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class KvBucketConfiguration {
    companion object {
        private const val KV_BUCKET = "stop-monitor"
        private const val KV_TRACE_BUCKET = "stop-monitor-trace"
    }

    @Bean
    fun getKvBucket(
        nats: EventStreamNatsProperties,
        keyValueBucketService: KeyValueBucketService,
        objectMapper: ObjectMapper,
    ): NatsKeyValueBucket<StopState> = keyValueBucketService.keyValueBucket(
        config = nats,
        name = KV_BUCKET,
        serializer = objectMapper::writeValueAsBytes,
        deserializer = objectMapper::readValue,
    )

    @Bean
    fun getKvTraceBucket(
        nats: EventStreamNatsProperties,
        keyValueBucketService: KeyValueBucketService,
        objectMapper: ObjectMapper,
    ): NatsKeyValueBucket<StopTraceState> = keyValueBucketService.keyValueBucket(
        config = nats,
        name = KV_TRACE_BUCKET,
        serializer = objectMapper::writeValueAsBytes,
        deserializer = objectMapper::readValue,
    )
}
