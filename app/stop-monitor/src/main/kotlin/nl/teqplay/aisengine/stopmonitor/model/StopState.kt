package nl.teqplay.aisengine.stopmonitor.model

import nl.teqplay.aisengine.event.model.StopStartEvent
import nl.teqplay.skeleton.model.Location
import java.time.Instant

/**
 * State for a stop.
 */
data class StopState(
    /**
     * [mmsi] of the ship.
     */
    val mmsi: Int,

    /**
     * Identifier of the [StopStartEvent].
     */
    val startEventId: String,

    /**
     * Time when the ship stopped.
     */
    val stoppedTime: Instant = Instant.now(),

    /**
     * Actual stop location, determined by the trace of the ship.
     */
    val stopLocation: Location? = null,

    val totalMovingTracePoints: Int = 0,

    val potentialStopEndTime: Instant? = null,
    val potentialStopEndLocation: Location? = null
)
