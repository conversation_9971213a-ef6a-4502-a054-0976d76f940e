package nl.teqplay.aisengine.reventsengine.service

import io.nats.client.Connection
import io.nats.client.JetStreamManagement
import io.nats.client.api.StreamInfo
import io.nats.client.support.JsonValue
import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.reventsengine.global.model.InterestsRunStart
import nl.teqplay.aisengine.reventsengine.model.data.AisDiffMessageMetadata
import nl.teqplay.skeleton.nats.NatsClientConnection
import nl.teqplay.skeleton.nats.NatsProducerStream
import org.awaitility.kotlin.await
import org.awaitility.kotlin.until
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.Duration
import java.time.Instant
import java.util.concurrent.BlockingQueue
import kotlin.concurrent.thread

class DiffStreamPublishServiceTest {

    private val diffProducerStream = mock<NatsProducerStream<AisDiffMessage>>().apply {
        whenever(getConfig()).thenReturn(mock())
    }
    private val natsClientConnection = mock<NatsClientConnection>().apply {
        val jsm = mock<JetStreamManagement>().apply {
            whenever(getStreamInfo(any())).thenReturn(StreamInfo(JsonValue("")))
        }
        val connection = mock<Connection>().apply {
            whenever(jetStreamManagement()).thenReturn(jsm)
        }
        whenever(getConnection(any())).thenReturn(connection)
    }

    private val diffStreamPublishService = DiffStreamPublishService(
        diffProducerStream,
        natsClientConnection,
    )

    @Test
    fun stats() {
        diffStreamPublishService.stats()
    }

    @Test
    fun run() {
        whenever(diffProducerStream.publishAsync<AisDiffMessageMetadata>(any(), any(), any(), any(), any(), any(), anyOrNull<Duration>())).then {
            val queue = it.getArgument<BlockingQueue<*>>(0)
            queue.clear()
            false
        }

        // start threads
        val interestsRun = mock<InterestsRunStart>()
        whenever(interestsRun.events).thenReturn(emptySet())
        diffStreamPublishService.startStreamPublisher(interestsRun)?.invoke()

        // queue message
        diffStreamPublishService.queue(listOf(mock()), Instant.EPOCH)

        // signalling needs to be done in a separate thread, to ensure await doesn't block indefinitely
        thread { await until { diffStreamPublishService.signalDone() } }
        diffStreamPublishService.await()

        verify(diffProducerStream, times(1)).publishAsync<AisDiffMessageMetadata>(any(), any(), any(), any(), any(), any(), anyOrNull<Duration>())
    }
}
