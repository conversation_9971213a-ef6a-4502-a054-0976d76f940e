package nl.teqplay.aisengine.berthmonitor.service

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.berthmonitor.Settings
import nl.teqplay.aisengine.berthmonitor.model.BerthState
import nl.teqplay.skeleton.nats.NatsKeyValueBucket
import org.springframework.stereotype.Component
import java.time.Instant
import java.util.concurrent.ConcurrentHashMap
import kotlin.system.measureTimeMillis

private val LOG = KotlinLogging.logger {}

/**
 * Component that keeps a local cache and a KV store in NATS of all ships that are anchored.
 */
@Component
class BerthStateService(
    private val settings: Settings,
    private val kvBucket: NatsKeyValueBucket<BerthState>,
) {

    /**
     * Local cache of ongoing anchorages
     * Key = MMSI of vessels
     * Value = Hashmap where the key is the area identifier and the value is [BerthState]
     */
    private val mooredCache = ConcurrentHashMap<Int, ConcurrentHashMap<String, BerthState>>()
    private val confirmedCache = ConcurrentHashMap<Int, ConcurrentHashMap<String, BerthState>>()
    private val uniqueCache = ConcurrentHashMap<Int, String>()

    init {
        LOG.info { "Reading stored berth states..." }
        var currentlyMoored = 0
        val time = measureTimeMillis {
            kvBucket.entries { (_, value) ->
                if (value != null) {
                    store(value)
                    currentlyMoored++
                }
            }
        }
        LOG.info { "Done reading $currentlyMoored stored berth states in $time ms" }
    }

    /**
     * Get current status for a vessel based on MMSI
     */
    fun getAllMooredNonConfirmed(
        currentTime: Instant,
        mmsi: Int?
    ): List<BerthState> {
        val mooredState = mutableListOf<BerthState>()
        // Get all vessels that are eligible to be confirmed
        val cache = getMooredCache(mmsi)
        cache.forEach { shipState ->
            val states = shipState.values
            states.forEach { berthState ->
                if (checkMooredNonConfirmed(berthState, currentTime)) {
                    mooredState.add(berthState)
                }
            }
        }

        return mooredState
    }

    private fun checkMooredNonConfirmed(
        berthState: BerthState,
        currentTime: Instant,
    ): Boolean {
        return !berthState.processed &&
            berthState.end == null &&
            berthState.start.plus(settings.confirmedInterval) <= currentTime &&
            // Filter out all non-updated vessels
            berthState.actualTimeLastSeenInside?.isAfter(berthState.start.plusSeconds(10)) == true
    }

    /**
     * Get all ending berth states that required to be processed
     * @return: a list of [BerthState] that need to be confirmed
     */
    fun getAllEnding(
        currentTime: Instant,
        mmsi: Int?,
    ): List<BerthState> {
        val mooredState = mutableListOf<BerthState>()
        // Get all vessels that are eligible to be ended
        val cache = getMooredCache(mmsi)
        cache.forEach { shipState ->
            val states = shipState.values
            states.forEach { berthState ->
                if (checkEnding(berthState, currentTime)) {
                    mooredState.add(berthState)
                }
            }
        }

        return mooredState
    }

    private fun checkEnding(
        berthState: BerthState,
        currentTime: Instant,
    ): Boolean {
        val endTime = berthState.end
        return !berthState.processed &&
            endTime != null &&
            endTime.plus(settings.confirmedInterval) <= currentTime &&
            berthState.actualTimeLastSeenOutside != null
    }

    private fun getMooredCache(mmsi: Int?): Collection<Map<String, BerthState>> {
        return when {
            mmsi != null -> {
                val cacheForMmsi = mooredCache[mmsi]
                if (cacheForMmsi != null) {
                    listOf(cacheForMmsi.toList().sortedBy { it.second.start }.toMap())
                } else emptyList()
            }
            else -> mooredCache.values
        }
    }

    /**
     * Get current status for a vessel based on [mmsi] and [berthId]
     * @return a berth state if this [mmsi] is inside the given berth, otherwise null
     */
    fun getMoored(mmsi: Int, berthId: String): BerthState? {
        val berthStates = mooredCache[mmsi]?.values ?: return null
        return berthStates.find { it.areaIdentifier.id == berthId }
    }

    /**
     * Get the unique berth identifier for the given [mmsi]
     */
    fun getUniqueBerthId(mmsi: Int): String? {
        return uniqueCache[mmsi]
    }

    /**
     * Utility function that checks if [mmsi] is currently in no other berths
     * and a unique event can be fired
     */
    fun canFireUnique(mmsi: Int): Boolean {
        return !uniqueCache.containsKey(mmsi)
    }

    /**
     * Save the state inside the local cache and persist to KV store
     */
    fun save(state: BerthState) {
        store(state)
        kvBucket.put(state.key(), state)
    }

    /**
     * Remove the berth state from local moored/unique cache and the KV store
     */
    fun clear(areaId: String?, mmsi: Int) {
        areaId?.let { id ->
            // If mmsi is inside moored cache, remove it from cache and kv store
            if (mooredCache.containsKey(mmsi) && mooredCache[mmsi]?.containsKey(id) == true) {
                val state = mooredCache[mmsi]?.get(id)
                if (state != null) {
                    mooredCache[mmsi]?.remove(id)
                    kvBucket.delete(state.key())
                }
            }
            // If mmsi is unique moored cache, remove it from cache and kv store
            if (uniqueCache.containsKey(mmsi) && uniqueCache.getValue(mmsi) == id) {
                uniqueCache.remove(mmsi)
            }
        }
    }

    /**
     * Support function that stores the given anchor state in local cache
     */
    private fun store(state: BerthState) {
        state.areaIdentifier.id?.let {
            mooredCache.getOrPut(state.ship.mmsi, ::ConcurrentHashMap)[it] = state
            // Store berth identifier in unique cache
            if (state.uniqueStartEventId != null) {
                uniqueCache[state.ship.mmsi] = it
            }
        }
    }
}
