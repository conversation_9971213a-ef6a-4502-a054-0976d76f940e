package nl.teqplay.aisengine.berthmonitor.model

import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.event.model.identifier.BerthIdentifier
import nl.teqplay.skeleton.model.Location
import java.time.Instant

/**
 * State of a berth event
 * @param ship this berth event is about
 * @param areaIdentifier information about the area this berth event is about
 * @param start time of the event
 * @param startEventId the identifier of the start of this event, so it can be linked at the end event
 * @param end time of the event
 */
data class BerthState(
    val ship: AisShipIdentifier,
    val areaIdentifier: AreaIdentifier,
    val berthIdentifier: BerthIdentifier,
    val heading: Int?,
    val draught: Float?,
    val location: Location,
    var start: Instant,
    var end: Instant? = null,
    /**
     * If a start event has been generated, the id will be stored here. This is used to refer to the start event when
     * the end event is generated.
     */
    var startEventId: String,
    var confirmedStartEventId: String? = null,
    var confirmedEndEventId: String? = null,
    var uniqueStartEventId: String? = null,

    var actualTimeLastSeenInside: Instant? = null,
    var actualTimeLastSeenOutside: Instant? = null,

    /** Whether this state has been processed to generate confirmed and/or unique berth events */
    var processed: Boolean = false
) {
    /**
     * Create a string key uniquely identifying this berth event, based on the MMSI of the ship and the area identifier
     */
    fun key() = "${ship.mmsi}-${areaIdentifier.id}"
}
