package nl.teqplay.aisengine.berthmonitor.service

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.berthmonitor.Settings
import nl.teqplay.aisengine.berthmonitor.model.BerthState
import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.aisengine.event.interfaces.EndEvent
import nl.teqplay.aisengine.event.interfaces.StartEvent
import nl.teqplay.aisengine.event.model.ConfirmedBerthEndEvent
import nl.teqplay.aisengine.event.model.ConfirmedBerthStartEvent
import nl.teqplay.aisengine.event.model.StateAreaInsideEvent
import nl.teqplay.aisengine.event.model.StateAreaOutsideEvent
import nl.teqplay.aisengine.event.model.UniqueBerthEndEvent
import nl.teqplay.aisengine.event.model.UniqueBerthStartEvent
import org.springframework.stereotype.Component
import java.time.Instant
import java.util.UUID

private val LOG = KotlinLogging.logger {}

/**
 * [BerthHandlerService] processes the start/stop events receives from [EventHandlerService],
 */
@Component
class BerthHandlerService(
    private val settings: Settings,
    private val berthStateService: BerthStateService
) {

    /**
     * Adding area event to the store to monitor
     */
    fun process(event: AreaEvent): List<AreaEvent> {
        val areaId = event.area.id ?: return emptyList()
        val berth = event.berth ?: return emptyList()
        val events = mutableListOf<AreaEvent>()
        when (event) {
            is StartEvent -> {
                // Check for any open events needing to be sent.
                events.addAll(processEvents(event.actualTime, event.ship.mmsi))

                val existingState = berthStateService.getMoored(event.ship.mmsi, areaId)
                if (existingState != null) {
                    // If it has existing state, false exit, resetting values
                    existingState.end = null
                    existingState.actualTimeLastSeenInside = null
                    existingState.actualTimeLastSeenOutside = null
                    // If confirmed start event is set, this is already processed
                    if (existingState.confirmedStartEventId != null) {
                        existingState.processed = true
                    }
                    berthStateService.save(existingState)
                } else {
                    // If no state exists yet, it means it's entering the berth for the first time
                    berthStateService.save(
                        BerthState(
                            ship = event.ship,
                            areaIdentifier = event.area,
                            berthIdentifier = berth,
                            heading = event.heading,
                            draught = event.draught,
                            location = event.location,
                            start = event.actualTime,
                            startEventId = event._id
                        )
                    )
                }
            }
            is StateAreaInsideEvent,
            is StateAreaOutsideEvent -> {
                val state = berthStateService.getMoored(event.ship.mmsi, areaId)
                if (state != null) {
                    when (event) {
                        is StateAreaInsideEvent -> state.actualTimeLastSeenInside = event.actualTime
                        is StateAreaOutsideEvent -> state.actualTimeLastSeenOutside = event.actualTime
                    }
                    berthStateService.save(state)

                    // Check if we need to send any events after updating the state.
                    events.addAll(processEvents(event.actualTime, event.ship.mmsi))
                }
            }
            is EndEvent -> {
                // Check for any open events needing to be sent.
                events.addAll(processEvents(event.actualTime, event.ship.mmsi))

                val state = berthStateService.getMoored(event.ship.mmsi, areaId) ?: return emptyList()
                if (state.start.plus(settings.confirmedInterval) > event.actualTime) {
                    // Remove false positive, make it no longer able to create confirmed/unique berth events
                    berthStateService.clear(state.areaIdentifier.id, state.ship.mmsi)
                } else {
                    // Reset processed flag and set end time on state
                    berthStateService.save(
                        state.copy(
                            end = event.actualTime,
                            processed = false,
                            location = event.location
                        )
                    )
                }
            }
        }
        return events
    }

    fun processEvents(
        currentTime: Instant,
        mmsi: Int? = null,
    ): List<AreaEvent> {
        val establishedEvents = processEstablishedEvents(currentTime, mmsi)
        val endingEvents = processEndingEvents(currentTime, mmsi)
        return establishedEvents + endingEvents
    }

    /**
     * This method is processing the established events.
     * It checks if it's needs to create a confirmed and/or unique event
     * [currentTime]: The time this message is processed. Default should always be the current time.
     *                Only overridden for the unit tests
     */
    private fun processEstablishedEvents(
        currentTime: Instant,
        mmsi: Int?,
    ): List<AreaEvent> {
        val result = mutableListOf<AreaEvent>()
        // Fetching all the non-confirmed
        berthStateService.getAllMooredNonConfirmed(currentTime, mmsi).forEach { state ->
            result.addAll(createConfirmedBerthStartEvent(state))
        }
        return result
    }

    private fun createConfirmedBerthStartEvent(
        state: BerthState,
    ): List<AreaEvent> {
        // For all non-confirmed events, create one
        val confirmedEvent = generateConfirmedBerthStartEvent(state)
        val result = mutableListOf<AreaEvent>(confirmedEvent)
        // Setting state to processed and setting the start event id to referer to
        state.processed = true
        state.confirmedStartEventId = confirmedEvent._id
        if (berthStateService.canFireUnique(state.ship.mmsi)) {
            // If ship is no other berth currently, fire a unique event as well
            val uniqueEvent = generateUniqueBerthStartEvent(state)
            result.add(uniqueEvent)
            // Setting the unique values inside the berth state
            state.uniqueStartEventId = uniqueEvent._id
        }
        berthStateService.save(state)
        return result
    }

    /**
     * Processing all events that are currently ending.
     * It checks if it needs to fire confirmed and/or unique end events
     * [currentTime]: The time this message is processed. Default should always be the current time.
     *                Only overridden for the unit tests
     */
    private fun processEndingEvents(
        currentTime: Instant,
        mmsi: Int?,
    ): List<AreaEvent> {
        val result = mutableListOf<AreaEvent>()
        // Get all berth states that require an end event
        berthStateService.getAllEnding(currentTime, mmsi).forEach { state ->
            result.addAll(createConfirmedBerthEndEvent(state))
        }
        return result
    }

    private fun createConfirmedBerthEndEvent(
        state: BerthState,
    ): List<AreaEvent> {
        // Create confirmed event in all cases
        val confirmedBerthEndEvent = generateConfirmedBerthEndEvent(state) ?: return emptyList()
        val result = mutableListOf<AreaEvent>(confirmedBerthEndEvent)
        state.processed = true
        state.confirmedEndEventId = confirmedBerthEndEvent._id
        if (berthStateService.getUniqueBerthId(state.ship.mmsi) == state.areaIdentifier.id) {
            // If state was previously marked as unique, fire end event as well
            val uniqueBerthEndEvent = generateUniqueBerthEndEvent(state)
            if (uniqueBerthEndEvent != null) result.add(uniqueBerthEndEvent)
        }
        berthStateService.clear(state.areaIdentifier.id, state.ship.mmsi)
        return result
    }

    /**
     * Process an area start event, generating a berth start event.
     * [state]: The state this berth event belongs to
     */
    fun generateConfirmedBerthStartEvent(state: BerthState): ConfirmedBerthStartEvent {
        LOG.debug { "Created berth start event for ${state.ship.mmsi} inside ${state.areaIdentifier.name}" }
        return ConfirmedBerthStartEvent(
            _id = UUID.randomUUID().toString(),
            berthEventId = state.startEventId,
            ship = state.ship,
            area = state.areaIdentifier,
            berth = state.berthIdentifier,
            heading = state.heading,
            draught = state.draught,
            location = state.location,
            actualTime = state.start
        )
    }

    /**
     * Process an area start event, generating a berth start event.
     * [state]: The state this berth event belongs to
     */
    fun generateConfirmedBerthEndEvent(state: BerthState): ConfirmedBerthEndEvent? {
        val endTime = state.end ?: return null
        LOG.debug { "Created berth end event for ${state.ship.mmsi} inside ${state.areaIdentifier.name}" }
        return ConfirmedBerthEndEvent(
            _id = UUID.randomUUID().toString(),
            startEventId = state.confirmedStartEventId,
            berthEventId = state.startEventId,
            ship = state.ship,
            area = state.areaIdentifier,
            berth = state.berthIdentifier,
            heading = state.heading,
            draught = state.draught,
            location = state.location,
            actualTime = endTime
        )
    }

    /**
     * Process an area start event, generating a berth start event.
     * [state]: The state this berth event belongs to
     */
    fun generateUniqueBerthStartEvent(state: BerthState): UniqueBerthStartEvent {
        LOG.debug { "Created berth start event for ${state.ship.mmsi} inside ${state.areaIdentifier.name}" }
        return UniqueBerthStartEvent(
            _id = UUID.randomUUID().toString(),
            berthEventId = state.startEventId,
            berthConfirmedEventId = state.confirmedStartEventId ?: run { state.startEventId },
            ship = state.ship,
            area = state.areaIdentifier,
            berth = state.berthIdentifier,
            heading = state.heading,
            draught = state.draught,
            location = state.location,
            actualTime = state.start
        )
    }

    /**
     * Process an area start event, generating a berth start event.
     * [state]: The state this berth event belongs to
     */
    fun generateUniqueBerthEndEvent(state: BerthState): UniqueBerthEndEvent? {
        val endTime = state.end ?: return null
        LOG.debug { "Created berth end event for ${state.ship.mmsi} inside ${state.areaIdentifier.name}" }
        return UniqueBerthEndEvent(
            _id = UUID.randomUUID().toString(),
            startEventId = state.uniqueStartEventId,
            berthEventId = state.startEventId,
            berthConfirmedEventId = state.confirmedEndEventId ?: run { state.startEventId },
            ship = state.ship,
            area = state.areaIdentifier,
            berth = state.berthIdentifier,
            heading = state.heading,
            draught = state.draught,
            location = state.location,
            actualTime = endTime
        )
    }
}
