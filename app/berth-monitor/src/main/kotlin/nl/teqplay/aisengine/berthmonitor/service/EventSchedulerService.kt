package nl.teqplay.aisengine.berthmonitor.service

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.revents.NotReventsProfile
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.time.Instant

@NotReventsProfile
@Component
class EventSchedulerService(
    private val eventHandlerService: EventHandlerService,
    private val berthHandlerService: BerthHandlerService,
) {

    private val log = KotlinLogging.logger { }

    /**
     * Scheduled task to process the currently moored vessels
     * It checks for updates with all the berth states
     * Runs every 60 seconds, with an initial delay of 10 seconds
     */
    @Scheduled(fixedRate = 60000, initialDelay = 10000)
    fun scheduleTaskWithFixedRate() {
        log.info { "Checking for confirmed, unique and ending berth events" }
        try {
            val now = Instant.now()
            val events = berthHandlerService.processEvents(now)
            eventHandlerService.publish(events)
        } catch (e: Exception) {
            log.error(e) { "Something went wrong while checking for confirmed, unique and ending berth events" }
        }
    }
}
