package nl.teqplay.aisengine.berthmonitor.config

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.aisengine.berthmonitor.model.BerthState
import nl.teqplay.aisengine.nats.stream.KeyValueBucketService
import nl.teqplay.aisengine.nats.stream.properties.EventStreamNatsProperties
import nl.teqplay.skeleton.nats.NatsKeyValueBucket
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class KvBucketConfiguration {
    companion object {
        private const val KV_BUCKET = "berth-monitor"
    }

    /**
     * Bucket in which all states are stored/updated. The states are stored using the mmsis, concatenated in a
     * deterministic order.
     */
    @Bean
    fun getKvBucket(
        nats: EventStreamNatsProperties,
        keyValueBucketService: KeyValueBucketService,
        objectMapper: ObjectMapper,
    ): NatsKeyValueBucket<BerthState> = keyValueBucketService.keyValueBucket(
        config = nats,
        name = KV_BUCKET,
        serializer = objectMapper::writeValueAsBytes,
        deserializer = objectMapper::readValue,
    )
}
