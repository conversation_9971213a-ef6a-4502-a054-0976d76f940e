package nl.teqplay.aisengine.berthmonitor.service

import io.github.oshai.kotlinlogging.KotlinLogging
import io.micrometer.core.instrument.MeterRegistry
import jakarta.annotation.PostConstruct
import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.aisengine.event.interfaces.EndEvent
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.interfaces.StartEvent
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.aisengine.nats.stream.EventStreamService.MessageContext
import nl.teqplay.aisengine.nats.stream.revents.model.ReventsEventStreamOptions
import nl.teqplay.aisengine.revents.ReventsAdditionalSubjectsService
import nl.teqplay.skeleton.metrics.Metric
import nl.teqplay.skeleton.metrics.MetricRegistry
import nl.teqplay.skeleton.nats.NatsConsumerStream
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.time.Duration
import java.time.Instant
import java.util.concurrent.atomic.AtomicLong
import java.util.concurrent.atomic.AtomicReference

@Component
class EventHandlerService(
    meterRegistry: MeterRegistry,
    private val eventConsumerStream: NatsConsumerStream<Event>,
    private val eventStreamService: EventStreamService,
    private val berthHandlerService: BerthHandlerService,
    private val reventsAdditionalSubjectsService: ReventsAdditionalSubjectsService?,
) {

    private val log = KotlinLogging.logger { }

    private val registry = MetricRegistry.of<EventHandlerService>(meterRegistry)

    private val counterTotalMsgIn = registry.createGauge(Metric.MESSAGE_COUNT_INPUT, AtomicLong())
    private val counterTotalMsgOut = registry.createGauge(Metric.MESSAGE_COUNT_OUTPUT, AtomicLong())
    private val counterTotalMsgOutEnding = registry.createGauge(Metric.MESSAGE_COUNT_OUTPUT, AtomicLong())
    private val counterTotalDropped = registry.createGauge(Metric.MESSAGE_COUNT_OUTPUT, AtomicLong())

    private val counterMsgIn = registry.createGauge(Metric.MESSAGE_COUNT_INPUT, AtomicLong())
    private val counterMsgOut = registry.createGauge(Metric.MESSAGE_COUNT_OUTPUT, AtomicLong())
    private val counterMsgOutEnding = registry.createGauge(Metric.MESSAGE_COUNT_OUTPUT, AtomicLong())
    private val counterDropped = registry.createGauge(Metric.MESSAGE_COUNT_OUTPUT, AtomicLong())
    private val lastEventTimestamp = AtomicReference(Instant.MIN)

    companion object {
        const val AREA_EVENT_SUBJECT = "event.area.*.berth.>"
    }

    private fun getNewestMessageAge(): Duration = Duration.between(lastEventTimestamp.get(), Instant.now()).abs()

    @Scheduled(initialDelay = 15000, fixedDelay = 15000)
    fun stats() {
        val msgIn = counterMsgIn.getAndSet(0)
        val msgDropped = counterDropped.getAndSet(0)
        val dropPercentage = msgDropped / msgIn.toFloat() * 100
        val msgOut = counterMsgOut.getAndSet(0)
        val msgOutEnding = counterMsgOutEnding.getAndSet(0)
        val ingestRate = (msgIn - msgDropped) / 15f

        log.info {
            "$msgIn messages received, dropped $msgDropped ($dropPercentage%), " +
                "${ingestRate}m/s, last processed event ${getNewestMessageAge()} ago, " +
                "sent $msgOut confirmed events, sent $msgOutEnding ending events"
        }
    }

    @PostConstruct
    fun receive() {
        log.info { "Start consuming events from the event-stream, having the subject $AREA_EVENT_SUBJECT" }

        // Only consume AreaEvents
        eventStreamService.consume(
            stream = eventConsumerStream,
            subjects = listOf(AREA_EVENT_SUBJECT) +
                (reventsAdditionalSubjectsService?.getAdditionalSubjects() ?: emptyList()),
            enableStateEvents = true,
            revents = ReventsEventStreamOptions(
                onPoisonPill = {
                    // Flush last events.
                    val events = berthHandlerService.processEvents(Instant.now())
                    publish(events)
                }
            )
        ) { event, message ->
            counterMsgIn.incrementAndGet()
            counterTotalMsgIn.incrementAndGet()
            process(event, message)
        }
    }

    /**
     * Method to process an incoming [event]
     * After processing acknowledging the [message]
     */
    private fun process(event: Event, message: MessageContext) {
        val parsedEvent = event as? AreaEvent

        // Not the right event, should drop it.
        if (parsedEvent == null) {
            message.term()
            return
        }

        // Check if it's the type we are interested in
        if (parsedEvent.area.type == AreaIdentifier.AreaType.BERTH) {
            if (parsedEvent.createdTime > lastEventTimestamp.get()) {
                lastEventTimestamp.set(parsedEvent.createdTime)
            }
            val events = berthHandlerService.process(parsedEvent)
            publish(events)
        } else {
            counterDropped.incrementAndGet()
            counterTotalDropped.incrementAndGet()
        }
        message.ack()
    }

    fun publish(events: List<AreaEvent>) {
        updateStats(events)
        events.forEach {
            eventStreamService.publish(it)
        }
    }

    private fun updateStats(events: List<AreaEvent>) {
        if (events.isEmpty()) {
            return
        }

        val establishedEventsCount = events.count { it is StartEvent }.toLong()
        val endingEventsCount = events.count { it is EndEvent }.toLong()
        counterTotalMsgOut.addAndGet(establishedEventsCount)
        counterMsgOut.addAndGet(establishedEventsCount)
        counterTotalMsgOutEnding.addAndGet(endingEventsCount)
        counterMsgOutEnding.addAndGet(endingEventsCount)
    }
}
