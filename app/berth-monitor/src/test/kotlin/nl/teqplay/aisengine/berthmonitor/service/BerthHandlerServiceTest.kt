package nl.teqplay.aisengine.berthmonitor.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.aisengine.berthmonitor.Settings
import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.aisengine.event.model.AreaEndEvent
import nl.teqplay.aisengine.event.model.AreaStartEvent
import nl.teqplay.aisengine.event.model.ConfirmedBerthEndEvent
import nl.teqplay.aisengine.event.model.ConfirmedBerthStartEvent
import nl.teqplay.aisengine.event.model.StateAreaInsideEvent
import nl.teqplay.aisengine.event.model.StateAreaOutsideEvent
import nl.teqplay.aisengine.event.model.UniqueBerthEndEvent
import nl.teqplay.aisengine.event.model.UniqueBerthStartEvent
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.event.model.identifier.BerthIdentifier
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.skeleton.common.BaseTest
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.nats.NatsClientMock
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.springframework.boot.autoconfigure.EnableAutoConfiguration
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.TestConstructor
import org.springframework.test.context.bean.override.mockito.MockitoBean
import java.time.Duration
import java.time.Instant
import java.util.UUID

@ContextConfiguration
@EnableAutoConfiguration
@MockitoBean(
    types = [EventStreamService::class]
)
@TestConstructor(autowireMode = TestConstructor.AutowireMode.ALL)
class BerthHandlerServiceTest(
    private val berthHandlerService: BerthHandlerService,
    private val berthStateService: BerthStateService,
) : BaseTest() {

    @TestConfiguration
    class Config {

        private val settings: Settings = Settings()

        @Bean
        fun berthStateService(
            natsClientMock: NatsClientMock,
            objectMapper: ObjectMapper,
        ) = BerthStateService(
            settings,
            natsClientMock.keyValueBucket(
                "berth-monitor-test",
                serializer = objectMapper::writeValueAsBytes,
                deserializer = objectMapper::readValue, maxAge = null, replicas = null, storeOnDisk = false
            ),
        )

        @Bean
        fun berthHandlerService(berthStateService: BerthStateService) = BerthHandlerService(settings, berthStateService)

        @Bean
        fun natsClientMock() = NatsClientMock()
    }

    private val startEventId = "event-123"
    private val startAltEventId = "event-alt-123"
    private val area = AreaIdentifier("berth-123", AreaIdentifier.AreaType.BERTH, "Berth 123", "NLRTM")
    private val area2 = AreaIdentifier("berth-234", AreaIdentifier.AreaType.BERTH, "Berth 234", "NLRTM")
    private val berth = BerthIdentifier(null, null)
    private val eventLocation = Location(1.0, 1.0)
    private val endEventLocation = Location(2.0, 2.0)
    private val ship = AisShipIdentifier(123, null)

    private fun createStartEvent(offsetFromNow: Duration): AreaStartEvent {
        val time = Instant.EPOCH + offsetFromNow
        return AreaStartEvent(
            _id = startEventId,
            ship = ship,
            area = area,
            berth = berth,
            heading = null,
            draught = null,
            speedOverGround = null,
            location = eventLocation,
            actualTime = time,
            createdTime = time
        )
    }

    private fun createAlternateStartEvent(offsetFromNow: Duration): AreaStartEvent {
        val time = Instant.EPOCH + offsetFromNow
        return AreaStartEvent(
            _id = startAltEventId,
            ship = ship,
            area = area2,
            berth = berth,
            heading = null,
            draught = null,
            speedOverGround = null,
            location = eventLocation,
            actualTime = time,
            createdTime = time
        )
    }

    private fun createStateAreaInsideEvent(offsetFromNow: Duration): StateAreaInsideEvent {
        val time = Instant.EPOCH + offsetFromNow
        return StateAreaInsideEvent(
            _id = startEventId,
            ship = ship,
            area = area,
            berth = berth,
            heading = null,
            draught = null,
            location = eventLocation,
            speedOverGround = 0f,
            actualTime = time,
            createdTime = time
        )
    }

    private fun createAlternateStateAreaInsideEvent(offsetFromNow: Duration): StateAreaInsideEvent {
        val time = Instant.EPOCH + offsetFromNow
        return StateAreaInsideEvent(
            _id = startAltEventId,
            ship = ship,
            area = area2,
            berth = berth,
            heading = null,
            draught = null,
            location = eventLocation,
            speedOverGround = 0f,
            actualTime = time,
            createdTime = time
        )
    }

    private fun createStateAreaOutsideEvent(offsetFromNow: Duration): StateAreaOutsideEvent {
        val time = Instant.EPOCH + offsetFromNow
        return StateAreaOutsideEvent(
            _id = startEventId,
            ship = ship,
            area = area,
            berth = berth,
            heading = null,
            draught = null,
            location = endEventLocation,
            speedOverGround = 0f,
            actualTime = time,
            createdTime = time
        )
    }

    private fun createAlternateStateAreaOutsideEvent(offsetFromNow: Duration): StateAreaOutsideEvent {
        val time = Instant.EPOCH + offsetFromNow
        return StateAreaOutsideEvent(
            _id = startAltEventId,
            ship = ship,
            area = area2,
            berth = berth,
            heading = null,
            draught = null,
            location = endEventLocation,
            speedOverGround = 0f,
            actualTime = time,
            createdTime = time
        )
    }

    private fun createEndEvent(offsetFromNow: Duration): AreaEndEvent {
        val time = Instant.EPOCH + offsetFromNow
        return AreaEndEvent(
            _id = UUID.randomUUID().toString(),
            startEventId = startEventId,
            ship = ship,
            area = area,
            berth = berth,
            heading = null,
            draught = null,
            speedOverGround = null,
            location = endEventLocation,
            actualTime = time,
            createdTime = time
        )
    }

    private fun createAlternateEndEvent(offsetFromNow: Duration): AreaEndEvent {
        val time = Instant.EPOCH + offsetFromNow
        return AreaEndEvent(
            _id = UUID.randomUUID().toString(),
            startEventId = startAltEventId,
            ship = ship,
            area = area2,
            berth = berth,
            heading = null,
            draught = null,
            speedOverGround = null,
            location = endEventLocation,
            actualTime = time,
            createdTime = time
        )
    }

    private fun processEvents(offsetFromNow: Duration): List<AreaEvent> {
        return berthHandlerService.processEvents(Instant.EPOCH + offsetFromNow)
    }

    @AfterEach
    fun clear() {
        berthStateService.clear(area.id, ship.mmsi)
        berthStateService.clear(area2.id, ship.mmsi)
    }

    @Test
    fun `Test AreaStart and AreaEndEvent Flow`() {
        // Initial creation
        berthHandlerService.process(createStartEvent(Duration.ofMinutes(0)))
        berthHandlerService.process(createStateAreaInsideEvent(Duration.ofMinutes(0)))
        assert(berthStateService.getMoored(ship.mmsi, area.id!!) != null)
        var events = processEvents(Duration.ZERO)
        assert(events.isEmpty())

        berthHandlerService.process(createStateAreaInsideEvent(Duration.ofMinutes(13)))

        // Second run, after 14 minutes
        events = processEvents(Duration.ofMinutes(14))
        assert(events.isEmpty())

        // Third run, after 15,5 minutes
        events = processEvents(Duration.ofMinutes(16))
        assert(events.isNotEmpty())
        assert(events.size == 2)
        assert(events[0].getEventType() == "berth-confirmed")
        assert(events[1].getEventType() == "berth-unique")

        val uniqueBerthStartEvent = events[1] as UniqueBerthStartEvent
        assert(uniqueBerthStartEvent.berthConfirmedEventId == events[0]._id)

        berthHandlerService.process(createStateAreaInsideEvent(Duration.ofMinutes(16)))

        // Fourth run, shouldn't be any duplicates created
        events = processEvents(Duration.ofMinutes(17))
        assert(events.isEmpty())

        berthHandlerService.process(createStateAreaInsideEvent(Duration.ofMinutes(17)))

        // Fifth run, vessel is leaving berth, but no events fired yet
        berthHandlerService.process(createEndEvent(Duration.ofMinutes(17)))
        assert(berthStateService.getMoored(ship.mmsi, area.id!!) != null)
        assert(berthStateService.getMoored(ship.mmsi, area.id!!)?.processed == false)
        assert(berthStateService.getMoored(ship.mmsi, area.id!!)?.end != null)
        events = processEvents(Duration.ofMinutes(18))
        assert(events.isEmpty())

        berthHandlerService.process(createStateAreaOutsideEvent(Duration.ofMinutes(30)))

        // Sixth run, confirming the end event
        events = processEvents(Duration.ofMinutes(33))
        assert(events.size == 2)
        assert(events[0].getEventType() == "berth-confirmed")
        assert(events[1].getEventType() == "berth-unique")

        val uniqueBerthEndEvent = events[1] as UniqueBerthEndEvent
        assert(uniqueBerthEndEvent.berthConfirmedEventId == events[0]._id)
        assertEquals(endEventLocation, uniqueBerthEndEvent.location)

        val confirmedBerthEndEvent = events[0] as ConfirmedBerthEndEvent
        assertEquals(endEventLocation, confirmedBerthEndEvent.location)

        assert(berthStateService.getMoored(ship.mmsi, area.id!!) == null)
    }

    @Test
    fun `Test AreaStart but exit within 15 minutes window and re-entering`() {
        // Initial creation
        berthHandlerService.process(createStartEvent(Duration.ofMinutes(0)))
        assert(berthStateService.getMoored(ship.mmsi, area.id!!) != null)
        var events = processEvents(Duration.ZERO)
        assert(events.isEmpty())

        berthHandlerService.process(createStateAreaInsideEvent(Duration.ofMinutes(13)))

        // Second run, after 14 minutes
        berthHandlerService.process(createEndEvent(Duration.ofMinutes(14)))
        events = processEvents(Duration.ofMinutes(14))
        assert(events.isEmpty())

        berthHandlerService.process(createStateAreaOutsideEvent(Duration.ofMinutes(15)))

        // Third run
        berthHandlerService.process(createStartEvent(Duration.ofMinutes(16)))
        assert(berthStateService.getMoored(ship.mmsi, area.id!!) != null)
        events = processEvents(Duration.ofMinutes(17))
        assert(events.isEmpty())

        berthHandlerService.process(createStateAreaInsideEvent(Duration.ofMinutes(17)))

        // Fourth run, just regular run, should not be fired yet
        events = processEvents(Duration.ofMinutes(18))
        assert(events.isEmpty())

        berthHandlerService.process(createStateAreaInsideEvent(Duration.ofMinutes(30)))

        // Fifth run, after 15 minutes
        events = processEvents(Duration.ofMinutes(32))
        assert(events.isNotEmpty())
        assert(events.size == 2)
        assert(events[0].getEventType() == "berth-confirmed")
        assert(events[1].getEventType() == "berth-unique")
    }

    @Test
    fun `Test AreaEndEvent, false exit`() {
        // Initial creation
        berthHandlerService.process(createStartEvent(Duration.ofMinutes(0)))
        berthHandlerService.process(createStateAreaInsideEvent(Duration.ofSeconds(180)))
        assert(berthStateService.getMoored(ship.mmsi, area.id!!) != null)
        var events = processEvents(Duration.ZERO)
        assert(events.isEmpty())

        // Second run
        assert(berthStateService.getMoored(ship.mmsi, area.id!!) != null)
        events = processEvents(Duration.ofMinutes(17))
        assert(events.isNotEmpty())
        assert(events.size == 2)
        assert(events[0].getEventType() == "berth-confirmed")
        assert(events[1].getEventType() == "berth-unique")

        berthHandlerService.process(createStateAreaInsideEvent(Duration.ofMinutes(29)))

        // Third run, exiting
        berthHandlerService.process(
            createEndEvent(Duration.ofMinutes(30))
        )
        assert(berthStateService.getMoored(ship.mmsi, area.id!!) != null)
        assert(berthStateService.getMoored(ship.mmsi, area.id!!)?.processed == false)
        assert(berthStateService.getMoored(ship.mmsi, area.id!!)?.end != null)
        events = processEvents(Duration.ofMinutes(30))
        assert(events.isEmpty())

        berthHandlerService.process(createStateAreaInsideEvent(Duration.ofMinutes(35)))

        // Fourth run, false exit
        berthHandlerService.process(
            createStartEvent(Duration.ofMinutes(40))
        )
        assert(berthStateService.getMoored(ship.mmsi, area.id!!) != null)
        assert(berthStateService.getMoored(ship.mmsi, area.id!!)?.processed == true)
        assert(berthStateService.getMoored(ship.mmsi, area.id!!)?.end == null)
        events = processEvents(Duration.ofMinutes(40))
        assert(events.isEmpty())
    }

    @Test
    fun `Test ship being in multiple berths at the same time`() {
        // Initial creation
        berthHandlerService.process(createStartEvent(Duration.ofMinutes(0)))
        assert(berthStateService.getMoored(ship.mmsi, area.id!!) != null)
        var events = processEvents(Duration.ofMinutes(1))
        assert(events.isEmpty())

        berthHandlerService.process(createStateAreaInsideEvent(Duration.ofMinutes(1)))

        // Going into another berth two minutes later
        berthHandlerService.process(createAlternateStartEvent(Duration.ofMinutes(2)))
        assert(berthStateService.getMoored(ship.mmsi, area.id!!) != null)
        events = processEvents(Duration.ofMinutes(3))
        assert(events.isEmpty())

        berthHandlerService.process(createStateAreaInsideEvent(Duration.ofMinutes(10)))
        berthHandlerService.process(createAlternateStateAreaInsideEvent(Duration.ofMinutes(10)))

        // Processing the first confirmed/unique after 15 minutes
        events = processEvents(Duration.ofMinutes(15))
        assert(events.isNotEmpty())
        assert(events.size == 2)
        assert(events[0].getEventType() == "berth-confirmed")
        assert(events[1].getEventType() == "berth-unique")

        // Processing the second berth entry, should only be confirmed
        events = processEvents(Duration.ofMinutes(20))
        assert(events.isNotEmpty())
        assert(events.size == 1)
        assert(events[0].getEventType() == "berth-confirmed")

        berthHandlerService.process(createStateAreaInsideEvent(Duration.ofMinutes(25)))

        // Exit event for second berth, shouldn't fire anything yet
        berthHandlerService.process(createAlternateEndEvent(Duration.ofMinutes(30)))
        assert(berthStateService.getMoored(ship.mmsi, area.id!!) != null)
        events = processEvents(Duration.ofMinutes(30))
        assert(events.isEmpty())

        berthHandlerService.process(createAlternateStateAreaOutsideEvent(Duration.ofMinutes(40)))
        berthHandlerService.process(createStateAreaInsideEvent(Duration.ofMinutes(40)))

        // 15 minutes later, should fire confirmed only
        events = processEvents(Duration.ofMinutes(45))
        assert(events.isNotEmpty())
        assert(events.size == 1)
        assert(events[0].getEventType() == "berth-confirmed")

        berthHandlerService.process(createStateAreaInsideEvent(Duration.ofMinutes(70)))

        // 30 minutes later, create end event
        berthHandlerService.process(createEndEvent(Duration.ofMinutes(75)))
        assert(berthStateService.getMoored(ship.mmsi, area.id!!) != null)
        events = processEvents(Duration.ofMinutes(75))
        assert(events.isEmpty())

        berthHandlerService.process(createStateAreaOutsideEvent(Duration.ofMinutes(85)))

        // 15 minutes later, should fire confirmed and unique for first berth
        events = processEvents(Duration.ofMinutes(90))
        assert(events.isNotEmpty())
        assert(events.size == 2)
        assert(events[0].getEventType() == "berth-confirmed")
        assert(events[1].getEventType() == "berth-unique")
    }

    @Test
    fun `Test unable to confirm start because vessel didn't give an recent update`() {
        // Initial creation
        berthHandlerService.process(createStartEvent(Duration.ofMinutes(0)))
        assert(berthStateService.getMoored(ship.mmsi, area.id!!) != null)
        var events = processEvents(Duration.ZERO)
        assert(events.isEmpty())

        // Second run
        assert(berthStateService.getMoored(ship.mmsi, area.id!!) != null)
        events = processEvents(Duration.ofMinutes(17))
        assert(events.isEmpty())

        // Third run, able to confirm now
        assert(berthStateService.getMoored(ship.mmsi, area.id!!) != null)
        events = berthHandlerService.process(createStateAreaInsideEvent(Duration.ofMinutes(19)))
        assert(events.isNotEmpty())
        assert(events.size == 2)
        assert(events[0].getEventType() == "berth-confirmed")
        assert(events[1].getEventType() == "berth-unique")
    }

    @Test
    fun `Test unable to confirm exit because vessel didn't give an recent update`() {
        // Initial creation
        berthHandlerService.process(createStartEvent(Duration.ofMinutes(0)))
        assert(berthStateService.getMoored(ship.mmsi, area.id!!) != null)
        var events = processEvents(Duration.ZERO)
        assert(events.isEmpty())

        berthHandlerService.process(createStateAreaInsideEvent(Duration.ofMinutes(1)))

        // Second run
        assert(berthStateService.getMoored(ship.mmsi, area.id!!) != null)
        events = processEvents(Duration.ofMinutes(17))
        assert(events.isNotEmpty())
        assert(events.size == 2)
        assert(events[0].getEventType() == "berth-confirmed")
        assert(events[1].getEventType() == "berth-unique")

        berthHandlerService.process(createStateAreaInsideEvent(Duration.ofMinutes(29)))

        // Third run, exiting
        berthHandlerService.process(createEndEvent(Duration.ofMinutes(30)))
        assert(berthStateService.getMoored(ship.mmsi, area.id!!) != null)
        assert(berthStateService.getMoored(ship.mmsi, area.id!!)?.processed == false)
        assert(berthStateService.getMoored(ship.mmsi, area.id!!)?.end != null)
        events = processEvents(Duration.ofMinutes(30))
        assert(events.isEmpty())

        // Fourth run, 16 minutes later, but vessel did not give an update yet
        events = processEvents(Duration.ofMinutes(46))
        assert(events.isEmpty())

        // Fifth run, 18 minutes later, vessel gave an update now
        assert(berthStateService.getMoored(ship.mmsi, area.id!!) != null)
        events = berthHandlerService.process(createStateAreaOutsideEvent(Duration.ofMinutes(48)))
        assert(events.isNotEmpty())
        assert(events.size == 2)
        assert(events[0].getEventType() == "berth-confirmed")
        assert(events[1].getEventType() == "berth-unique")
    }

    private fun processEvents(vararg inputEvents: AreaEvent): List<AreaEvent> {
        val events = mutableListOf<AreaEvent>()
        inputEvents.forEach { inputEvent ->
            events.addAll(berthHandlerService.process(inputEvent))
        }
        return events
    }

    @Test
    fun `event-based flow should generate confirmed,unique start,end events without relying on the timer`() {
        val events = processEvents(
            createStartEvent(Duration.ofMinutes(0)),
            createStateAreaInsideEvent(Duration.ofMinutes(1)),
            createStateAreaInsideEvent(Duration.ofMinutes(29)),
            createEndEvent(Duration.ofMinutes(30)),
            createStateAreaOutsideEvent(Duration.ofMinutes(47))
        )

        val firstEvent = events[0]
        assertTrue(firstEvent is ConfirmedBerthStartEvent)
        assertEquals(Instant.EPOCH, firstEvent.actualTime)

        val secondEvent = events[1]
        assertTrue(secondEvent is UniqueBerthStartEvent)
        assertEquals(Instant.EPOCH, secondEvent.actualTime)

        val thirdEvent = events[2]
        assertTrue(thirdEvent is ConfirmedBerthEndEvent)
        assertEquals(Instant.EPOCH.plus(Duration.ofMinutes(30)), thirdEvent.actualTime)

        val fourthEvent = events[3]
        assertTrue(fourthEvent is UniqueBerthEndEvent)
        assertEquals(Instant.EPOCH.plus(Duration.ofMinutes(30)), fourthEvent.actualTime)

        assertEquals(4, events.size)
    }

    @Test
    fun `event-based flow false-exit`() {
        val events = processEvents(
            createStartEvent(Duration.ofMinutes(0)),
            createStateAreaInsideEvent(Duration.ofMinutes(1)),
            createStateAreaInsideEvent(Duration.ofMinutes(29)),
            createEndEvent(Duration.ofMinutes(30)),

            createStartEvent(Duration.ofMinutes(31)),
            createStateAreaInsideEvent(Duration.ofMinutes(32)),
            createStateAreaInsideEvent(Duration.ofMinutes(62)),
            createEndEvent(Duration.ofMinutes(70)),
            createStateAreaOutsideEvent(Duration.ofMinutes(85))
        )

        val firstEvent = events[0]
        assertTrue(firstEvent is ConfirmedBerthStartEvent)
        assertEquals(Instant.EPOCH, firstEvent.actualTime)

        val secondEvent = events[1]
        assertTrue(secondEvent is UniqueBerthStartEvent)
        assertEquals(Instant.EPOCH, secondEvent.actualTime)

        val thirdEvent = events[2]
        assertTrue(thirdEvent is ConfirmedBerthEndEvent)
        assertEquals(Instant.EPOCH.plus(Duration.ofMinutes(70)), thirdEvent.actualTime)

        val fourthEvent = events[3]
        assertTrue(fourthEvent is UniqueBerthEndEvent)
        assertEquals(Instant.EPOCH.plus(Duration.ofMinutes(70)), fourthEvent.actualTime)

        assertEquals(4, events.size)
    }

    @Test
    fun `event-based two berth entries and exits`() {
        val events = processEvents(
            createStartEvent(Duration.ofMinutes(0)),
            createStateAreaInsideEvent(Duration.ofMinutes(1)),
            createStateAreaInsideEvent(Duration.ofMinutes(29)),
            createEndEvent(Duration.ofMinutes(30)),
            createStateAreaOutsideEvent(Duration.ofMinutes(31)),

            createStartEvent(Duration.ofMinutes(60)),
            createStateAreaInsideEvent(Duration.ofMinutes(61)),
            createStateAreaInsideEvent(Duration.ofMinutes(89)),
            createEndEvent(Duration.ofMinutes(90)),
            createStateAreaOutsideEvent(Duration.ofMinutes(105)),
        )

        val firstEvent = events[0]
        assertTrue(firstEvent is ConfirmedBerthStartEvent)
        assertEquals(Instant.EPOCH, firstEvent.actualTime)

        val secondEvent = events[1]
        assertTrue(secondEvent is UniqueBerthStartEvent)
        assertEquals(Instant.EPOCH, secondEvent.actualTime)

        val thirdEvent = events[2]
        assertTrue(thirdEvent is ConfirmedBerthEndEvent)
        assertEquals(Instant.EPOCH.plus(Duration.ofMinutes(30)), thirdEvent.actualTime)

        val fourthEvent = events[3]
        assertTrue(fourthEvent is UniqueBerthEndEvent)
        assertEquals(Instant.EPOCH.plus(Duration.ofMinutes(30)), fourthEvent.actualTime)

        val fifthEvent = events[4]
        assertTrue(fifthEvent is ConfirmedBerthStartEvent)
        assertEquals(Instant.EPOCH.plus(Duration.ofMinutes(60)), fifthEvent.actualTime)

        val sixthEvent = events[5]
        assertTrue(sixthEvent is UniqueBerthStartEvent)
        assertEquals(Instant.EPOCH.plus(Duration.ofMinutes(60)), sixthEvent.actualTime)

        val seventhEvent = events[6]
        assertTrue(seventhEvent is ConfirmedBerthEndEvent)
        assertEquals(Instant.EPOCH.plus(Duration.ofMinutes(90)), seventhEvent.actualTime)

        val eighthEvent = events[7]
        assertTrue(eighthEvent is UniqueBerthEndEvent)
        assertEquals(Instant.EPOCH.plus(Duration.ofMinutes(90)), eighthEvent.actualTime)

        assertEquals(8, events.size)
    }

    @Test
    fun `event-based flow with multiple berths (alternate last)`() {
        val events = processEvents(
            createStartEvent(Duration.ofMinutes(0)),
            createStateAreaInsideEvent(Duration.ofMinutes(1)),

            createAlternateStartEvent(Duration.ofMinutes(2)),
            createStateAreaInsideEvent(Duration.ofMinutes(3)),

            // state updates, even though out-of-order, should still result in the first berth to be unique
            createAlternateStateAreaInsideEvent(Duration.ofMinutes(29)),
            createStateAreaInsideEvent(Duration.ofMinutes(29)),

            createEndEvent(Duration.ofMinutes(30)),
            createAlternateEndEvent(Duration.ofMinutes(31)),
            createStateAreaOutsideEvent(Duration.ofMinutes(47)),
            createAlternateStateAreaOutsideEvent(Duration.ofMinutes(48))
        )

        val firstEvent = events[0]
        assertTrue(firstEvent is ConfirmedBerthStartEvent)
        assertEquals(area.id, firstEvent.area.id)
        assertEquals(Instant.EPOCH, firstEvent.actualTime)

        val secondEvent = events[1]
        assertTrue(secondEvent is UniqueBerthStartEvent)
        assertEquals(area.id, secondEvent.area.id)
        assertEquals(Instant.EPOCH, secondEvent.actualTime)

        val thirdEvent = events[2]
        assertTrue(thirdEvent is ConfirmedBerthStartEvent)
        assertEquals(area2.id, thirdEvent.area.id)
        assertEquals(Instant.EPOCH.plus(Duration.ofMinutes(2)), thirdEvent.actualTime)

        val fourthEvent = events[3]
        assertTrue(fourthEvent is ConfirmedBerthEndEvent)
        assertEquals(area.id, fourthEvent.area.id)
        assertEquals(Instant.EPOCH.plus(Duration.ofMinutes(30)), fourthEvent.actualTime)

        val fifthEvent = events[4]
        assertTrue(fifthEvent is UniqueBerthEndEvent)
        assertEquals(area.id, fifthEvent.area.id)
        assertEquals(Instant.EPOCH.plus(Duration.ofMinutes(30)), fifthEvent.actualTime)

        val sixthEvent = events[5]
        assertTrue(sixthEvent is ConfirmedBerthEndEvent)
        assertEquals(area2.id, sixthEvent.area.id)
        assertEquals(Instant.EPOCH.plus(Duration.ofMinutes(31)), sixthEvent.actualTime)

        assertEquals(6, events.size)
    }

    @Test
    fun `event-based flow with multiple berths (alternate first)`() {
        val events = processEvents(
            createAlternateStartEvent(Duration.ofMinutes(0)),
            createAlternateStateAreaInsideEvent(Duration.ofMinutes(1)),

            createStartEvent(Duration.ofMinutes(2)),
            createAlternateStateAreaInsideEvent(Duration.ofMinutes(3)),

            // state updates, even though out-of-order, should still result in the first berth to be unique
            createStateAreaInsideEvent(Duration.ofMinutes(29)),
            createAlternateStateAreaInsideEvent(Duration.ofMinutes(29)),

            createAlternateEndEvent(Duration.ofMinutes(30)),
            createEndEvent(Duration.ofMinutes(31)),
            createAlternateStateAreaOutsideEvent(Duration.ofMinutes(47)),
            createStateAreaOutsideEvent(Duration.ofMinutes(48))
        )

        val firstEvent = events[0]
        assertTrue(firstEvent is ConfirmedBerthStartEvent)
        assertEquals(area2.id, firstEvent.area.id)
        assertEquals(Instant.EPOCH, firstEvent.actualTime)

        val secondEvent = events[1]
        assertTrue(secondEvent is UniqueBerthStartEvent)
        assertEquals(area2.id, secondEvent.area.id)
        assertEquals(Instant.EPOCH, secondEvent.actualTime)

        val thirdEvent = events[2]
        assertTrue(thirdEvent is ConfirmedBerthStartEvent)
        assertEquals(area.id, thirdEvent.area.id)
        assertEquals(Instant.EPOCH.plus(Duration.ofMinutes(2)), thirdEvent.actualTime)

        val fourthEvent = events[3]
        assertTrue(fourthEvent is ConfirmedBerthEndEvent)
        assertEquals(area2.id, fourthEvent.area.id)
        assertEquals(Instant.EPOCH.plus(Duration.ofMinutes(30)), fourthEvent.actualTime)

        val fifthEvent = events[4]
        assertTrue(fifthEvent is UniqueBerthEndEvent)
        assertEquals(area2.id, fifthEvent.area.id)
        assertEquals(Instant.EPOCH.plus(Duration.ofMinutes(30)), fifthEvent.actualTime)

        val sixthEvent = events[5]
        assertTrue(sixthEvent is ConfirmedBerthEndEvent)
        assertEquals(area.id, sixthEvent.area.id)
        assertEquals(Instant.EPOCH.plus(Duration.ofMinutes(31)), sixthEvent.actualTime)

        assertEquals(6, events.size)
    }
}
