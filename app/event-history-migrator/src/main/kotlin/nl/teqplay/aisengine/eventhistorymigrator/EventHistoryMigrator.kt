package nl.teqplay.aisengine.eventhistorymigrator

import nl.teqplay.skeleton.datasource.DataSourceAutoConfiguration
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication
import org.springframework.scheduling.annotation.EnableScheduling

@SpringBootApplication(exclude = [DataSourceAutoConfiguration::class, MongoAutoConfiguration::class])
@ConfigurationPropertiesScan
@EnableScheduling
class EventHistoryMigrator

fun main(args: Array<String>) {
    runApplication<EventHistoryMigrator>(*args)
}
