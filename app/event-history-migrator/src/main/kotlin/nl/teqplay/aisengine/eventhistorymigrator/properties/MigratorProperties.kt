package nl.teqplay.aisengine.eventhistorymigrator.properties

import nl.teqplay.skeleton.common.config.MongoDbConfiguration
import org.springframework.boot.context.properties.ConfigurationProperties
import java.time.YearMonth

@ConfigurationProperties(prefix = "migrator")
data class MigratorProperties(
    val start: Year<PERSON><PERSON>h,
    val end: Year<PERSON><PERSON><PERSON>,

    val prontoMongo: PlatformMongo,
    val globalMongo: PlatformMongo
) {
    data class PlatformMongo(
        override val host: String,
        override val port: Int,
        override val db: String,
        override val authDb: String,
        override val username: String,
        override val password: String,

        // platform isn't a replicated database, so we can ignore the following
        override val hosts: List<String> = emptyList(),
        override val replicaSetName: String = ""
    ) : MongoDbConfiguration
}
