package nl.teqplay.aisengine.eventhistorymigrator.service

import io.github.oshai.kotlinlogging.KotlinLogging
import kotlinx.coroutines.CoroutineScope
import nl.teqplay.aisengine.eventhistorymigrator.properties.MigratorProperties
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component
import kotlin.concurrent.thread

@Component
class SchedulerService(
    private val properties: MigratorProperties,
    private val coroutineScope: CoroutineScope,
    private val runOneCycleService: RunOneCycleService
) {
    private val LOG = KotlinLogging.logger {}

    /**
     * Runs through all cycles.
     */
    @EventListener(ApplicationReadyEvent::class)
    fun run() {
        // run in a thread to not block application startup
        thread(name = "Migrator") {
            LOG.info { "Starting processing from ${properties.start} until ${properties.end}" }
            do {
                val next = runOneCycleService.runOneCycle().also {
                    LOG.info { "Cycle finished, progressing to the next" }
                }
            } while (next)
            LOG.info { "Finished all cycles" }
        }
    }
}
