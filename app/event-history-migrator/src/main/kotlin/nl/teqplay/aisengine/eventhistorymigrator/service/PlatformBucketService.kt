package nl.teqplay.aisengine.eventhistorymigrator.service

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.eventhistorymigrator.datasource.PlatformDataSource
import nl.teqplay.aisengine.eventhistorymigrator.model.PlatformBucket
import nl.teqplay.aisengine.eventhistorymigrator.timed
import nl.teqplay.platform.model.event.TeqplayEvent
import org.springframework.stereotype.Service
import java.time.YearMonth
import java.util.TreeSet

@Service
class PlatformBucketService(private val platformDataSource: PlatformDataSource) {
    private val LOG = KotlinLogging.logger {}

    /**
     * Event [Comparator] used for removing duplicate events
     */
    private val eventComparator = compareBy<TeqplayEvent>(
        { it.type },
        { it.eventTime ?: it.datetime },
        { it.shipMmsi },
        { it.otherMmsi },
        { it.shipImo },
        { it.otherShipImo }
    )

    fun getBuckets(yearMonth: YearMonth): List<PlatformBucket> {
        LOG.timed("Getting all buckets") {
            return platformDataSource.getAllBuckets(yearMonth)
        }
    }

    fun mergeBuckets(buckets: List<PlatformBucket>): List<PlatformBucket> {
        LOG.timed("Merging and removing duplicates from buckets") {
            return buckets.groupBy { bucket ->
                // Group by date and entity/area, ignore the event category
                bucket._id.removeEventCategory()
            }.map(::mergeGroupedBuckets)
        }
    }

    /**
     * Merge grouped buckets into a single [PlatformBucket].
     */
    private fun mergeGroupedBuckets(entry: Map.Entry<String, List<PlatformBucket>>): PlatformBucket {
        val (newBucketId, groupedBuckets) = entry

        return if (groupedBuckets.size > 1) {
            // We throw all events on one single list as it is possible we have more than 2 event buckets of the same day or category
            val allEvents = groupedBuckets.map { it.data }
                .flatten()

            // We have to do a remove duplicate as pronto and global could've generated the same event with a different datetime
            mergeAndRemoveDuplicateEvents(newBucketId, allEvents)
        } else {
            // No need to remove any duplicates when only one of the two servers had this bucket
            val singleBucket = groupedBuckets.first()
            singleBucket.copy(_id = newBucketId)
        }
    }

    /**
     * Remove duplicate events from a single [PlatformBucket].
     */
    private fun mergeAndRemoveDuplicateEvents(bucketId: String, allEvents: List<TeqplayEvent>): PlatformBucket {
        val eventTreeSet = TreeSet(eventComparator)
        eventTreeSet.addAll(allEvents)

        return PlatformBucket(_id = bucketId, data = eventTreeSet.toList())
    }

    /**
     * Remove the event category part from a bucket id
     */
    private fun String.removeEventCategory(): String {
        return this.removeSuffix(",BASIC")
            .removeSuffix(",CONTEXT")
            .removeSuffix(",ENCOUNTER")
            .removeSuffix(",UNKNOWN")
    }
}
