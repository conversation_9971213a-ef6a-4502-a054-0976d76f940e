buildscript {
    project.extra.set("baseVersion", "2.1.0")
}

plugins {
    id("ais-engine.kotlin-app-webmvc-conventions")
}

dependencies {
    implementation(project(":lib:bucketing-event"))

    implementation("nl.teqplay.skeleton:datasource2:$skeletonVersion")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:$kotlinCoroutinesVersion")

    testImplementation(project(":lib:testing-event"))
    testImplementation(project(":lib:testing-platform"))
}
