package nl.teqplay.aisengine.encountermonitor.util

import nl.teqplay.aisengine.aisstream.model.TransponderPosition
import nl.teqplay.skeleton.model.Location
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import kotlin.math.roundToInt
import kotlin.math.sqrt

class EcefEnuTest {
    @Test
    fun `ECEF sample conversion`() {
        val location = Location(12.34, 56.78)
        val ecef = location.toECEF()

        assertEquals(3414088, ecef.x.roundToInt())
        assertEquals(5213301, ecef.y.roundToInt())
        assertEquals(1354169, ecef.z.roundToInt())
    }

    @Test
    fun `distance to bow is correct when rotating`() {
        val location = Location(12.34, 56.78)
        val ecef = location.toECEF()
        val bow = bowPosition(
            TransponderPosition(100, 20, 10, 10),
            location,
            ecef,
            45.0
        )
        assertEquals(100.0, ecef.distanceTo(bow), 1e-9)
    }

    @Test
    fun `ENU rotation rotates the correct way around`() {
        val result = ENU(0.0, 1.0, 0.0)
            .rotate(90.0)

        assertEquals(1.0, result.e, 1e-10)
        assertEquals(0.0, result.n, 1e-10)
    }

    @Test
    fun `ship length between bow and stern correct`() {
        val location = Location(-1.23, -5.6)
        val transponderPosition = TransponderPosition(140, 60, 12, 8)
        val heading = 178.9
        val ecefLocation = location.toECEF()

        val bow = bowPosition(transponderPosition, location, ecefLocation, heading)
        val stern = sternPosition(transponderPosition, location, ecefLocation, heading)

        assertEquals(
            (transponderPosition.distanceToBow + transponderPosition.distanceToStern).toDouble(),
            bow.distanceTo(stern),
            1e-9
        )
    }

    @Test
    fun `ECEF distance check`() {
        val location1 = Location(51.969775, 4.031434)
        val location2 = Location(51.974686, 4.034614)
        val ecef1 = location1.toECEF()
        val ecef2 = location2.toECEF()

        val distance = ecef1.distanceTo(ecef2)

        assertEquals(588.5, distance, 1e-2)
    }

    @Test
    fun `ENU distance check`() {
        val location1 = Location(51.969775, 4.031434)
        val location2 = Location(51.974686, 4.034614)
        val ecef1 = location1.toECEF()
        val ecef2 = location2.toECEF()

        val m = enuMatrix(location1)
        val ecefVector = ECEF(
            ecef2.x - ecef1.x,
            ecef2.y - ecef1.y,
            ecef2.z - ecef1.z
        )
        val enuVector = ecefVector.toENU(m)

        val ecefDistance = ecef1.distanceTo(ecef2)
        val enuDistance = sqrt(enuVector.e * enuVector.e + enuVector.n * enuVector.n + enuVector.u * enuVector.u)

        assertEquals(ecefDistance, enuDistance, 1e-9)
    }
}
