package nl.teqplay.aisengine.encountermonitor.service

import nl.teqplay.aisengine.aisstream.model.AisMessage
import nl.teqplay.aisengine.encountermonitor.EncounterMonitorBaseTest
import nl.teqplay.aisengine.encountermonitor.datasource.ShipInfoDatasource
import nl.teqplay.aisengine.encountermonitor.model.EncounterState
import nl.teqplay.aisengine.encountermonitor.model.EncounterState.Order.REVERSE
import nl.teqplay.aisengine.encountermonitor.model.ShipState
import nl.teqplay.aisengine.encountermonitor.service.poma.PomaService
import nl.teqplay.aisengine.encountermonitor.service.poma.ShipToShipArea
import nl.teqplay.aisengine.encountermonitor.util.toECEF
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.csi.model.ship.info.component.ShipRole
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.Polygon
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.bean.override.mockito.MockitoBean
import java.time.Instant

@ContextConfiguration
@MockitoBean(
    types = [ShipInfoDatasource::class, PomaService::class]
)
class DetectionServiceTest(
    private val shipInfoDatasource: ShipInfoDatasource,
    private val detectionService: DetectionService,
    private val shipStateService: ShipStateService,
    private val pomaService: PomaService
) : EncounterMonitorBaseTest() {

    companion object {
        private val SHIP_TO_SHIP_AREA = ShipToShipArea(
            _id = null,
            polygon = Polygon(
                listOf(
                    Location(0.0, 0.0),
                    Location(1.0, 0.0),
                    Location(1.0, 1.0),
                    Location(0.0, 1.0),
                    Location(0.0, 0.0),
                )
            )
        )
    }

    @TestConfiguration
    class Config {
        @Bean
        fun detectionService(shipInfoDatasource: ShipInfoDatasource, pomaService: PomaService) = DetectionService(shipInfoDatasource, pomaService)

        @Bean
        fun shipStateService(shipInfoDatasource: ShipInfoDatasource) = ShipStateService(shipInfoDatasource)
    }

    @BeforeEach
    fun beforeEach() {
        whenever(shipInfoDatasource.getRoleForShip(any())).thenReturn(ShipRole.NONE)
        whenever(shipInfoDatasource.getIsSeaVessel(any())).thenReturn(false)
        whenever(shipInfoDatasource.getShipType(any())).thenReturn(AisMessage.ShipType.UNDEFINED)
        whenever(pomaService.getShipToShipAreas()).thenReturn(listOf(SHIP_TO_SHIP_AREA))
    }

    @Test
    fun `moving tug encounter is detected`() {
        setRole(1, ShipRole.TUG)
        seavessel(2)

        assertEquals(
            REVERSE,
            detectionService.isMovingTugEncounter(ship(1, speed = 4.0) to ship(2, speed = 4.5))
        )
    }

    @Test
    fun `moving tug encounter not detected when speed difference too high`() {
        setRole(2, ShipRole.TUG)
        seavessel(1)

        assertNull(detectionService.isMovingTugEncounter(ship(1, speed = 6.5) to ship(2, speed = 4.0)))
    }

    @Test
    fun `moving tug encounter not detected when ships are not moving`() {
        setRole(1, nl.teqplay.csi.model.ship.info.component.ShipRole.TUG)
        seavessel(2)

        assertNull(detectionService.isMovingTugEncounter(ship(1, speed = 0.2) to ship(2, speed = 0.5)))
    }

    @Test
    fun `moving tug encounter not detected when it's not a tug`() {
        setRole(1, ShipRole.BOATMAN)
        seavessel(2)

        assertNull(detectionService.isMovingTugEncounter(ship(1, speed = 4.0) to ship(2, speed = 4.5)))
    }

    @Test
    fun `Singapore pilot encounter is detected`() {
        setRole(1, ShipRole.PILOT)
        seavessel(2)

        assertEquals(
            REVERSE,
            detectionService.isSingaporePilotEncounter(
                ship(1, Location(1.261874, 103.685738), speed = 2.7) to
                    ship(2, Location(1.261874, 103.685738), speed = 0.8)
            )
        )
    }

    @Test
    fun `Singapore pilot encounter not detected outside of relevant area`() {
        setRole(2, ShipRole.PILOT)
        seavessel(1)

        assertNull(
            detectionService.isSingaporePilotEncounter(
                ship(1, Location(0.870581, 103.685738), speed = 0.0) to
                    ship(2, Location(0.870581, 103.685738), speed = 1.3)
            )
        )
    }

    @Test
    fun `Singapore pilot encounter is not detected because of too high speed`() {
        setRole(1, ShipRole.PILOT)
        seavessel(2)

        assertNull(
            detectionService.isSingaporePilotEncounter(
                ship(1, Location(1.261874, 103.685738), speed = 0.7) to
                    ship(2, Location(1.261874, 103.685738), speed = 2.8)
            )
        )
    }

    @Test
    fun `Singapore pilot encounter is not detected because of incorrect role`() {
        setRole(1, ShipRole.PILOT)
        seavessel(2)
        setRole(2, ShipRole.TUG)

        assertNull(
            detectionService.isSingaporePilotEncounter(
                ship(1, Location(1.261874, 103.685738), speed = 0.7) to
                    ship(2, Location(1.261874, 103.685738), speed = 0.0)
            )
        )
    }

    @Test
    fun `pilot encounter is detected`() {
        setRole(1, ShipRole.PILOT)
        seavessel(2)

        assertEquals(
            REVERSE,
            detectionService.isPilotEncounter(ship(1, speed = 0.7) to ship(2, speed = 0.8))
        )
    }

    @Test
    fun `pilot encounter is not detected because of incorrect role`() {
        setRole(2, ShipRole.BOATMAN)
        seavessel(1)

        assertNull(detectionService.isPilotEncounter(ship(1, speed = 0.7) to ship(2, speed = 0.8)))
    }

    @Test
    fun `pilot encounter is not detected when pilot vessel is not moving`() {
        setRole(1, ShipRole.PILOT)
        seavessel(2)

        assertNull(detectionService.isPilotEncounter(ship(1, speed = 0.1) to ship(2, speed = 0.8)))
    }

    @Test
    fun `pilot encounter is detected when long-range pilot vessel is not moving`() {
        setRole(244820146, ShipRole.PILOT)
        seavessel(2)

        assertEquals(
            REVERSE,
            detectionService.isLongrangePilotEncounter(ship(244820146, speed = 0.1) to ship(2, speed = 0.8))
        )
    }

    @Test
    fun `water boat encounter is detected`() {
        setRole(1, ShipRole.WATER)
        seavessel(2)

        assertEquals(
            REVERSE,
            detectionService.isWaterBoatEncounter(ship(1, speed = 0.7) to ship(2, speed = 0.2))
        )
    }

    @Test
    fun `water boat encounter is not detected because of incorrect role`() {
        setRole(2, ShipRole.WATER)
        setRole(1, ShipRole.BUNKER)
        seavessel(1)

        assertNull(detectionService.isWaterBoatEncounter(ship(1, speed = 0.2) to ship(2, speed = 0.7)))
    }

    @Test
    fun `water boat encounter is not detected because other ship is moving`() {
        setRole(1, ShipRole.WATER)
        seavessel(2)

        assertNull(detectionService.isWaterBoatEncounter(ship(1, speed = 0.7) to ship(2, speed = 1.2)))
    }

    @Test
    fun `boatman encounter is detected`() {
        setRole(1, ShipRole.BOATMAN)
        seavessel(2)

        assertEquals(
            REVERSE,
            detectionService.isBoatmanEncounter(ship(1, speed = 0.2) to ship(2, speed = 0.5))
        )
    }

    @Test
    fun `boatman encounter is not detected because other ship is moving`() {
        setRole(2, ShipRole.BOATMAN)
        seavessel(1)

        assertNull(detectionService.isBoatmanEncounter(ship(1, speed = 1.2) to ship(2, speed = 0.2)))
    }

    @Test
    fun `boatman encounter is not detected because boatman is moving`() {
        setRole(1, ShipRole.BOATMAN)
        seavessel(2)

        assertNull(detectionService.isBoatmanEncounter(ship(1, speed = 1.2) to ship(2, speed = 0.2)))
    }

    @Test
    fun `bunker encounter is detected`() {
        setRole(1, ShipRole.BUNKER)
        seavessel(2)

        assertEquals(
            REVERSE,
            detectionService.isBunkerEncounter(ship(1, speed = 0.1) to ship(2, speed = 0.1))
        )
    }

    @Test
    fun `bunker encounter is not detected because bunker is moving`() {
        setRole(2, ShipRole.BUNKER)
        seavessel(1)

        assertNull(detectionService.isBunkerEncounter(ship(1, speed = 2.1) to ship(2, speed = 0.1)))
    }

    @Test
    fun `bunker encounter is not detected because of restricted location`() {
        setRole(1, ShipRole.BUNKER)
        seavessel(2)

        assertNull(
            detectionService.isBunkerEncounter(
                ship(
                    1,
                    location = Location(51.92676161835748, 4.197422213940667),
                    speed = 0.1
                ) to ship(2, speed = 0.1)
            )
        )
    }

    @Test
    fun `authorities encounter is detected`() {
        setRole(1, ShipRole.AUTHORITIES)
        seavessel(2)

        assertEquals(
            REVERSE,
            detectionService.isAuthoritiesEncounter(ship(1, speed = 0.1) to ship(2, speed = 0.1))
        )
    }

    @Test
    fun `authorities encounter is not detected because of incorrect role`() {
        setRole(1, ShipRole.AUTHORITIES)
        setRole(2, ShipRole.AUTHORITIES)
        seavessel(2)

        assertNull(detectionService.isAuthoritiesEncounter(ship(1, speed = 0.1) to ship(2, speed = 0.1)))
    }

    @Test
    fun `authorities encounter is not detected because authorities is moving`() {
        setRole(2, ShipRole.AUTHORITIES)
        seavessel(1)

        assertNull(detectionService.isAuthoritiesEncounter(ship(1, speed = 0.0) to ship(2, speed = 3.1)))
    }

    @Test
    fun `lubes encounter is detected`() {
        setRole(1, ShipRole.LUBES)
        seavessel(2)

        assertEquals(
            REVERSE,
            detectionService.isLubesEncounter(ship(1, speed = 0.1) to ship(2, speed = 0.1))
        )
    }

    @Test
    fun `lubes encounter is not detected because lubes is moving`() {
        setRole(2, ShipRole.LUBES)
        seavessel(1)

        assertNull(detectionService.isLubesEncounter(ship(1, speed = 0.1) to ship(2, speed = 2.0)))
    }

    @Test
    fun `lubes encounter is not detected because of restricted location`() {
        setRole(1, ShipRole.LUBES)
        seavessel(2)

        assertNull(
            detectionService.isLubesEncounter(
                ship(1, location = Location(51.92676161835748, 4.197422213940667), speed = 0.1) to
                    ship(2, speed = 0.1)
            )
        )
    }

    @Test
    fun `water boat encounter with barge is detected`() {
        setRole(1, ShipRole.WATER)
        setRole(2, ShipRole.CARGOBARGE)

        assertEquals(
            REVERSE,
            detectionService.isBargeWaterEncounter(ship(1, speed = 0.7) to ship(2, speed = 0.2))
        )
    }

    @Test
    fun `water boat encounter with barge is not detected because the barge is moving`() {
        setRole(2, ShipRole.WATER)
        setRole(1, ShipRole.CARGOBARGE)

        assertNull(detectionService.isBargeWaterEncounter(ship(1, speed = 2.7) to ship(2, speed = 0.2)))
    }

    @Test
    fun `water boat encounter with barge is not detected because of incorrect role`() {
        setRole(1, ShipRole.WATER)
        setRole(2, ShipRole.WATER)

        assertNull(detectionService.isBargeWaterEncounter(ship(1, speed = 0.7) to ship(2, speed = 0.2)))
    }

    @Test
    fun `bunker encounter with barge is detected`() {
        setRole(1, ShipRole.BUNKER)
        setRole(2, ShipRole.CARGOBARGE)

        assertEquals(
            REVERSE,
            detectionService.isBargeBunkerEncounter(ship(1, speed = 0.7) to ship(2, speed = 0.2))
        )
    }

    @Test
    fun `bunker encounter with barge is not detected because the barge is moving`() {
        setRole(2, ShipRole.BUNKER)
        setRole(1, ShipRole.CARGOBARGE)

        assertNull(detectionService.isBargeBunkerEncounter(ship(1, speed = 2.7) to ship(2, speed = 0.2)))
    }

    @Test
    fun `bunker encounter with barge is not detected because of incorrect role`() {
        setRole(1, ShipRole.BUNKER)
        setRole(2, ShipRole.BUNKER)

        assertNull(detectionService.isBargeBunkerEncounter(ship(1, speed = 0.7) to ship(2, speed = 0.2)))
    }

    @Test
    fun `departure tug encounter is detected`() {
        setRole(1, ShipRole.TUG)
        seavessel(2)

        assertEquals(
            REVERSE,
            detectionService.isTugWaitingDepartureEncounter(ship(1, speed = 0.1) to ship(2, speed = 0.1))
        )
    }

    @Test
    fun `departure tug encounter is not detected because of incorrect role`() {
        setRole(1, ShipRole.TUG)
        seavessel(2)
        setRole(2, ShipRole.TUG)

        assertNull(detectionService.isTugWaitingDepartureEncounter(ship(1, speed = 0.1) to ship(2, speed = 0.1)))
    }

    @Test
    fun `departure tug encounter is not detected because ship is moving`() {
        setRole(2, ShipRole.TUG)
        seavessel(1)

        assertNull(detectionService.isTugWaitingDepartureEncounter(ship(1, speed = 2.1) to ship(2, speed = 0.1)))
    }

    @Test
    fun `generic encounter is detected for correct role`() {
        setRole(1, ShipRole.SWOG)
        seavessel(2)

        assertEquals(
            REVERSE,
            detectionService.isEncounterOfRole(ship(1, speed = 0.1) to ship(2, speed = 0.2), ShipRole.SWOG)
        )
    }

    @Test
    fun `generic encounter is not detected when sea vessel is moving`() {
        setRole(2, ShipRole.SWOG)
        seavessel(1)

        assertNull(detectionService.isEncounterOfRole(ship(1, speed = 3.1) to ship(2, speed = 0.2), ShipRole.SWOG))
    }

    @Test
    fun `generic encounter is not detected for incorrect role`() {
        setRole(1, ShipRole.BUNKER)
        seavessel(2)

        assertNull(detectionService.isEncounterOfRole(ship(1, speed = 0.1) to ship(2, speed = 0.2), ShipRole.SWOG))
    }

    @Test
    fun `generic board-board encounter is detected for correct role`() {
        setRole(1, ShipRole.SWOG)
        seavessel(2)

        assertEquals(
            REVERSE,
            detectionService.isBoardBoardEncounterOfRole(
                ship(1, speed = 0.1) to ship(2, speed = 0.2),
                ShipRole.SWOG,
                false
            )
        )
    }

    @Test
    fun `generic board-board encounter is not detected when sea vessel is moving`() {
        setRole(2, ShipRole.SWOG)
        seavessel(1)

        assertNull(
            detectionService.isBoardBoardEncounterOfRole(
                ship(1, speed = 3.1) to ship(2, speed = 0.2),
                ShipRole.SWOG,
                false
            )
        )
    }

    @Test
    fun `generic board-board encounter is not detected for incorrect role`() {
        setRole(1, ShipRole.BUNKER)
        seavessel(2)

        assertNull(
            detectionService.isBoardBoardEncounterOfRole(
                ship(1, speed = 0.1) to
                    ship(2, speed = 0.2),
                ShipRole.SWOG,
                false
            )
        )
    }

    @Test
    fun `should return null when one ship is bunker vessel`() {
        setType(1, AisMessage.ShipType.CARGO)
        setType(2, AisMessage.ShipType.CARGO)
        setRole(1, ShipRole.BUNKER)
        seavessel(1)
        seavessel(2)

        val loc = Location(0.1, 0.1)

        assertNull(
            detectionService.isShipToShipEncounter(
                ship(1, speed = 0.1, location = loc) to ship(2, speed = 0.1, location = loc)
            )
        )
    }

    @Test
    fun `ship to ship encounter should not detect when ships are not alongside`() {
        setType(1, AisMessage.ShipType.CARGO)
        setType(2, AisMessage.ShipType.CARGO)
        seavessel(1)
        seavessel(2)

        val loc1 = Location(0.5, 0.5)
        val loc2 = Location(0.6, 0.6)

        assertNull(
            detectionService.isShipToShipEncounter(
                ship(1, speed = 0.1, location = loc1) to ship(2, speed = 0.1, location = loc2)
            )
        )
    }

    @Test
    fun `ship to ship encounter should not detect when no sea vessels are involved`() {
        setType(1, AisMessage.ShipType.TANKER)
        setType(2, AisMessage.ShipType.TANKER)

        val loc = Location(0.1, 0.1)

        assertNull(
            detectionService.isShipToShipEncounter(
                ship(1, speed = 0.1, location = loc) to ship(2, speed = 0.1, location = loc)
            )
        )
    }

    @Test
    fun `ship to ship encounter should not detect when ships are moving`() {
        setType(1, AisMessage.ShipType.TANKER)
        setType(2, AisMessage.ShipType.TANKER)
        seavessel(1)
        seavessel(2)

        val loc = Location(0.1, 0.1)

        assertNull(
            detectionService.isShipToShipEncounter(
                ship(1, speed = 3.5, location = loc) to ship(2, speed = 0.1, location = loc)
            )
        )
    }

    @Test
    fun `ship to ship encounter should be detected`() {
        setType(1, AisMessage.ShipType.CARGO)
        setType(2, AisMessage.ShipType.CARGO)
        seavessel(1)
        seavessel(2)

        val loc = Location(0.1, 0.1)

        assertEquals(
            EncounterState.Order.NORMAL,
            detectionService.isShipToShipEncounter(
                ship(1, speed = 0.1, location = loc) to ship(2, speed = 0.1, location = loc)
            )
        )
    }

    private fun setType(mmsi: Int, type: AisMessage.ShipType) {
        whenever(shipInfoDatasource.getShipType(mmsi)).thenReturn(type)
    }

    private fun setRole(mmsi: Int, role: ShipRole) {
        whenever(shipInfoDatasource.getRoleForShip(mmsi)).thenReturn(role)
    }

    private fun seavessel(mmsi: Int) {
        whenever(shipInfoDatasource.getIsSeaVessel(mmsi)).thenReturn(true)
    }

    private fun ship(
        mmsi: Int,
        location: Location = Location(0.0, 0.0),
        speed: Double = 0.0
    ): ShipState = shipStateService.getState(
        oldState = null,
        ship = AisShipIdentifier(mmsi),
        messageTime = Instant.EPOCH,
        to = location,
        toECEF = location.toECEF(),
        speed = speed.toFloat(),
        courseOverGround = null,
        heading = null
    )
}
