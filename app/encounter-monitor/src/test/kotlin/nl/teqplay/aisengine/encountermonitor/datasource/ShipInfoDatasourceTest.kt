package nl.teqplay.aisengine.encountermonitor.datasource

import nl.teqplay.aisengine.aisstream.model.AisMessage.ShipType
import nl.teqplay.aisengine.aisstream.model.TransponderPosition
import nl.teqplay.aisengine.encountermonitor.EncounterMonitorBaseTest
import nl.teqplay.csi.model.ship.info.ShipRegisterInfo
import nl.teqplay.csi.model.ship.info.component.ShipAdministration
import nl.teqplay.csi.model.ship.info.component.ShipCategories
import nl.teqplay.csi.model.ship.info.component.ShipCommunication
import nl.teqplay.csi.model.ship.info.component.ShipDimensions
import nl.teqplay.csi.model.ship.info.component.ShipIdentifiers
import nl.teqplay.csi.model.ship.info.component.ShipRole
import nl.teqplay.csi.model.ship.info.component.ShipScores
import nl.teqplay.csi.model.ship.info.component.ShipSpecification
import nl.teqplay.csi.model.ship.info.component.ShipTypes
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.mockito.ArgumentMatchers.eq
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.bean.override.mockito.MockitoBean

@ContextConfiguration
@MockitoBean(
    types = [RoleDatasource::class]
)
class ShipInfoDatasourceTest(
    private val shipInfoDatasource: ShipInfoDatasource,
    private val roleDatasource: RoleDatasource,
) : EncounterMonitorBaseTest() {
    @TestConfiguration
    class Config {
        @Bean
        fun shipInfoDatasource(roleDatasource: RoleDatasource) = ShipInfoDatasource(roleDatasource)
    }

    @Test
    fun `role is returned`() {
        val mmsi = 123456789

        shipInfoDatasource.updateRoles()
        verify(roleDatasource, times(1)).updateRoles()

        whenever(roleDatasource.getRoleForShip(eq(mmsi))).thenReturn(ShipRole.PILOT)
        assertEquals(ShipRole.PILOT, shipInfoDatasource.getRoleForShip(mmsi), "Expected role PILOT for $mmsi")
    }

    @Test
    fun `NONE role is returned if CSI returns no role`() {
        val mmsi = 123456788
        assertEquals(ShipRole.NONE, shipInfoDatasource.getRoleForShip(mmsi), "Expected role NONE for $mmsi")
    }

    @Test
    fun `ship dimensions are returned`() {
        shipInfoDatasource.update(
            mmsi = 123123123,
            transponderPosition = TransponderPosition(1, 2, 3, 4)
        )

        val dimensions = shipInfoDatasource.getTransponderPosition(123123123)

        assertNotNull(dimensions)
        assertEquals(1, dimensions!!.distanceToBow)
        assertEquals(2, dimensions.distanceToStern)
        assertEquals(3, dimensions.distanceToPort)
        assertEquals(4, dimensions.distanceToStarboard)
    }

    @Test
    fun `sea vessel state is returned correctly`() {
        shipInfoDatasource.update(100000000, false)
        shipInfoDatasource.update(200000000, true)

        assertEquals(false, shipInfoDatasource.getIsSeaVessel(100000000), "ship updated to be not a sea vessel should not be a sea vessel")
        assertEquals(true, shipInfoDatasource.getIsSeaVessel(200000000), "ship updated to be a sea vessel should be a sea vessel")
        assertEquals(false, shipInfoDatasource.getIsSeaVessel(300000000), "unknown ship should not be a sea vessel")
    }

    @Test
    fun `ship type is returned correctly`() {
        shipInfoDatasource.update(mmsi = 100000000, shipType = ShipType.MILITARY_OPS)

        assertEquals(ShipType.MILITARY_OPS, shipInfoDatasource.getShipType(100000000), "ship type returned is not as set")
        assertEquals(ShipType.UNDEFINED, shipInfoDatasource.getShipType(200000000), "unknown ship should have no ship type")
    }

    private fun getShipRegisterInfo(
        mmsi: Int,
        role: ShipRole?
    ) = ShipRegisterInfo(
        identifiers = ShipIdentifiers(mmsi.toString(), null, null, null, null),
        types = ShipTypes(null, role),
        categories = ShipCategories(),
        dimensions = ShipDimensions(),
        administration = ShipAdministration(null, null, null, null, null, null, null, null, null),
        specification = ShipSpecification(),
        communication = ShipCommunication(null),
        scores = ShipScores(null, null)
    )
}
