package nl.teqplay.aisengine.encountermonitor.service

import nl.teqplay.aisengine.aisstream.model.AisMessage
import nl.teqplay.aisengine.encountermonitor.EncounterMonitorBaseTest
import nl.teqplay.aisengine.encountermonitor.datasource.ShipInfoDatasource
import nl.teqplay.aisengine.encountermonitor.util.toECEF
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.csi.model.ship.info.component.ShipRole
import nl.teqplay.skeleton.model.Location
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.bean.override.mockito.MockitoBean
import java.time.Instant
import java.time.temporal.ChronoUnit

@MockitoBean(
    types = [ShipInfoDatasource::class]
)
@ContextConfiguration
class ShipStateServiceTest(
    private val shipStateService: ShipStateService,
    private val shipInfoDatasource: ShipInfoDatasource
) : EncounterMonitorBaseTest() {
    @TestConfiguration
    class Config {
        @Bean
        fun shipStateService(shipInfoDatasource: ShipInfoDatasource) = ShipStateService(shipInfoDatasource)
    }

    @Test
    fun `new ShipState is constructed correctly`() {
        whenever(shipInfoDatasource.getRoleForShip(any())).thenReturn(ShipRole.PILOT)
        whenever(shipInfoDatasource.getIsSeaVessel(any())).thenReturn(false)
        whenever(shipInfoDatasource.getShipType(any())).thenReturn(AisMessage.ShipType.TANKER)
        val location = Location(12.34, 56.78)
        val state = shipStateService.getState(
            null,
            AisShipIdentifier(123456789, null),
            Instant.parse("2023-05-01T12:00:00Z"),
            location,
            location.toECEF(),
            12.3f,
            56.78f,
            90.12f
        )

        assertEquals(123456789, state.ship.mmsi)
        assertEquals(location, state.location)
        assertEquals(location.toECEF(), state.ecefLocation)
        assertEquals(12.3f, state.speed)
        assertEquals(56.78f, state.courseOverGround)
        assertEquals(90.12f, state.heading)
        assertEquals(ShipRole.PILOT, state.role)
        assertEquals(AisMessage.ShipType.TANKER, state.type)
        assertFalse(state.isSeaVessel)
        assertEquals(0.0, state.averageSpeed)
    }

    @Test
    fun `average speed converges to the current value`() {
        whenever(shipInfoDatasource.getRoleForShip(any())).thenReturn(ShipRole.PILOT)
        whenever(shipInfoDatasource.getIsSeaVessel(any())).thenReturn(false)
        whenever(shipInfoDatasource.getShipType(any())).thenReturn(AisMessage.ShipType.TANKER)
        var instant = Instant.parse("2023-05-01T12:00:00Z")
        val location = Location(12.34, 56.78)
        var state = shipStateService.getState(
            null,
            AisShipIdentifier(123456789, null),
            instant,
            location,
            location.toECEF(),
            12.3f,
            56.78f,
            90.12f
        )
        repeat(200) {
            instant = instant.plus(5, ChronoUnit.MINUTES)
            state = shipStateService.getState(
                state,
                AisShipIdentifier(123456789, null),
                instant,
                location,
                location.toECEF(),
                12.3f,
                56.78f,
                90.12f
            )
        }
        assertEquals(state.speed?.toDouble() ?: 0.0, state.averageSpeed.toDouble(), 1e-6)
    }

    @Test
    fun `average speed converges to the current value with varying update rate`() {
        whenever(shipInfoDatasource.getRoleForShip(any())).thenReturn(ShipRole.PILOT)
        whenever(shipInfoDatasource.getIsSeaVessel(any())).thenReturn(false)
        whenever(shipInfoDatasource.getShipType(any())).thenReturn(AisMessage.ShipType.TANKER)
        var instant = Instant.parse("2023-05-01T12:00:00Z")
        val location = Location(12.34, 56.78)
        var state = shipStateService.getState(
            null,
            AisShipIdentifier(123456789, null),
            instant,
            location,
            location.toECEF(),
            12.3f,
            56.78f,
            90.12f
        )
        repeat(10) {
            instant = instant.plus(5, ChronoUnit.MINUTES)
            state = shipStateService.getState(
                state,
                AisShipIdentifier(123456789, null),
                instant,
                location,
                location.toECEF(),
                12.3f,
                56.78f,
                90.12f
            )
        }
        repeat(10) {
            instant = instant.plus(12, ChronoUnit.MINUTES)
            state = shipStateService.getState(
                state,
                AisShipIdentifier(123456789, null),
                instant,
                location,
                location.toECEF(),
                12.3f,
                56.78f,
                90.12f
            )
        }
        repeat(10) {
            instant = instant.plus(20, ChronoUnit.MINUTES)
            state = shipStateService.getState(
                state,
                AisShipIdentifier(123456789, null),
                instant,
                location,
                location.toECEF(),
                12.3f,
                56.78f,
                90.12f
            )
        }
        assertEquals(state.speed?.toDouble() ?: 0.0, state.averageSpeed.toDouble(), 1e-3)
    }
}
