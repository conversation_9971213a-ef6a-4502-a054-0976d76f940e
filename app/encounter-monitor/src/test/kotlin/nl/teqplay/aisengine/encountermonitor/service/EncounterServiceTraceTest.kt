package nl.teqplay.aisengine.encountermonitor.service

import com.fasterxml.jackson.databind.ObjectMapper
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import nl.teqplay.aisengine.ObjectMapperConfiguration
import nl.teqplay.aisengine.aisstream.model.AisMessage
import nl.teqplay.aisengine.aisstream.model.TransponderPosition
import nl.teqplay.aisengine.encountermonitor.EncounterMonitorBaseTest
import nl.teqplay.aisengine.encountermonitor.datasource.ShipInfoDatasource
import nl.teqplay.aisengine.encountermonitor.longrangePilotVessels
import nl.teqplay.aisengine.encountermonitor.model.EncounterState
import nl.teqplay.aisengine.encountermonitor.properties.EncounterMonitorProperties
import nl.teqplay.aisengine.encountermonitor.properties.EncounterMonitorProperties.CacheTtl
import nl.teqplay.aisengine.encountermonitor.service.poma.PomaService
import nl.teqplay.aisengine.encountermonitor.service.poma.ShipToShipArea
import nl.teqplay.aisengine.encountermonitor.util.TestTracePoint
import nl.teqplay.aisengine.encountermonitor.util.TraceUtil
import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.model.EncounterEndEvent
import nl.teqplay.aisengine.event.model.EncounterStartEvent
import nl.teqplay.aisengine.event.model.encountermetadata.BoatmanEncounterMetadata
import nl.teqplay.aisengine.nats.stream.EventStreamServiceImpl
import nl.teqplay.csi.model.ship.info.component.ShipRole
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.Polygon
import nl.teqplay.skeleton.nats.NatsClientMock
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Assertions.fail
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import org.springframework.test.annotation.DirtiesContext
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.bean.override.mockito.MockitoBean
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.concurrent.ConcurrentHashMap
import java.util.stream.Stream

@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@ContextConfiguration(classes = [ObjectMapperConfiguration::class])
class EncounterServiceTraceTest(private val objectMapper: ObjectMapper) : EncounterMonitorBaseTest() {
    private lateinit var encounterService: EncounterService
    private lateinit var traceUtil: TraceUtil
    private lateinit var eventStream: NatsClientMock.NatsProducerStreamMock<Event>

    @MockitoBean
    private lateinit var shipInfoDatasource: ShipInfoDatasource
    private lateinit var pomaService: PomaService

    companion object {
        private val USCRP = Polygon(
            listOf(
                Location(27.951078544971693, -97.60183741599744),
                Location(27.52303280644702, -97.67354111009136),
                Location(25.993084557736978, -96.57116769177601),
                Location(27.470735878976996, -95.08782069271713),
                Location(28.860536131631065, -96.09416602299497),
                Location(27.951078544971693, -97.60183741599744)
            )
        )

        private val NLAMS = Polygon(
            listOf(
                Location(52.48485946266811, 4.510507064551746),
                Location(52.435122367388914, 4.510507064551746),
                Location(52.435122367388914, 4.693374674667552),
                Location(52.48485946266811, 4.693374674667552),
                Location(52.48485946266811, 4.510507064551746)
            )
        )

        private val stsAreas = listOf(
            ShipToShipArea(_id = "USCRP", polygon = USCRP), ShipToShipArea(_id = "NLAMS", polygon = NLAMS)
        )
    }

    @BeforeEach
    fun setUp() {
        shipInfoDatasource = mock<ShipInfoDatasource>()
        pomaService = mock<PomaService>()

        val encounterStateService = MockEncounterStateService()
        val testMeterRegistry = SimpleMeterRegistry()
        val shipStateService = ShipStateService(shipInfoDatasource)
        val natsClientMock = NatsClientMock()

        traceUtil = TraceUtil(objectMapper)
        encounterService = EncounterService(
            DetectionService(shipInfoDatasource, pomaService),
            encounterStateService,
            EventStreamServiceImpl(
                natsClientMock.producerStream(
                    stream = "event-stream",
                    subjects = listOf("event.>"),
                    serializer = objectMapper::writeValueAsBytes
                )
            ),
            testMeterRegistry,
            shipStateService,
            encounterMonitorProperties = EncounterMonitorProperties(
                cacheTtl = CacheTtl(
                    shipRole = Duration.ZERO
                )
            ),
            pomaService = pomaService
        )
        eventStream = natsClientMock.producerStreams["event-stream"] as NatsClientMock.NatsProducerStreamMock<Event>

        whenever(pomaService.getShipToShipAreas()).thenReturn(stsAreas)
    }

    @Test
    fun `SGSIN pilot encounter with one of three vessels`() {
        val mainVessel = 355048000
        val pilot = 563021070
        mockSeavessel(mainVessel)
        mockSeavessel(232004453)
        mockSeavessel(564110000)
        mockPilot(pilot)

        processTraces(
            "SGSIN/ship_traces.json",
            "SGSIN/pilot_trace.json",
            filterHistoric = false
        )

        val (start, _) = assertEncounter(mainVessel, pilot, EncounterType.PILOT)!!
        assertEquals(false, start.mainMoving)
        assertEquals(true, start.serviceMoving)
    }

    @Test
    fun `SGSIN pilot encounter with TORM`() {
        val mainVessel = 564458000
        val pilot = 563021040
        mockSeavessel(mainVessel)
        mockPilot(pilot)

        processTraces(
            "SGSIN/ship_trace_torm.json",
            "SGSIN/pilot_trace_torm.json",
            filterHistoric = false
        )

        val (start, _) = assertEncounter(mainVessel, pilot, EncounterType.PILOT)!!
        assertEquals(false, start.mainMoving)
        assertEquals(true, start.serviceMoving)
    }

    @Test
    fun `SGSIN pilot encounter with GRACE ZEPHYR`() {
        val mainVessel = 374643000
        val pilot = 563021040

        val mainVessels = listOf(
            mainVessel,
            574001320,
            563062100,
            371489000,
        )
        mainVessels.forEach(::mockSeavessel)
        mockPilot(pilot)

        processTraces(*((mainVessels + pilot).map { "SGSIN/zephyr-$it.json" }.toTypedArray()))

        val (start, _) = assertEncounter(mainVessel, pilot, EncounterType.PILOT)!!
        assertEquals(false, start.mainMoving)
        assertEquals(true, start.serviceMoving)
    }

    @Test
    fun `SGSIN no pilot encounter with MCP LINZ`() {
        val mainVessel = 212939000
        val pilot = 563021040

        mockSeavessel(mainVessel)
        mockPilot(pilot)

        processTraces(*(listOf(mainVessel, pilot).map { "SGSIN/linz-$it.json" }.toTypedArray()))

        assertEncounter(mainVessel, pilot, EncounterType.PILOT, count = 0)
    }

    @Test
    fun `SGSIN pilot encounter with MCP LINZ - simulating it was close enough - timestamps should be in order`() {
        val mainVessel = 212939000
        val pilot = 563021040

        mockSeavessel(mainVessel)
        mockPilot(pilot)

        // The MCP LINZ is too far away from the pilot to trigger an encounter,
        // force an encounter by moving the mainVessel closer.
        // Need to do it after a certain time, to ensure the service is in the right state.
        val pilotPassedByTime = Instant.parse("2022-09-08T15:10:00Z")
        val nearbyPilotTurnLocation = Location(1.2748361965729202, 103.74493348480388)

        val tracePoints = getTracePoints(*(listOf(mainVessel, pilot).map { "SGSIN/linz-$it.json" }.toTypedArray()))
            .map {
                if (it.ship.mmsi == mainVessel && it.messageTime.isAfter(pilotPassedByTime)) {
                    it.copy(from = nearbyPilotTurnLocation, to = nearbyPilotTurnLocation)
                } else it
            }

        processTraces(tracePoints)

        val events = getEventsForMmsis(setOf(mainVessel, pilot), EncounterType.PILOT)

        assertEquals(2, events.size, "only one encounter event pair")

        val startEvent = events.first() as? EncounterStartEvent
            ?: fail("expected an EncounterStartEvent as first event")
        val endEvent = events.last() as? EncounterEndEvent
            ?: fail("expected an EncounterEndEvent as second event")

        assertEquals(Instant.parse("2022-09-08T16:25:37Z"), startEvent.actualTime)
        assertEquals(Instant.parse("2022-09-08T16:30:57Z"), endEvent.actualTime)
    }

    @Test
    fun `no encounter with GRAVITAS`() {
        val mainVessel = 563774000
        val pilot = 564654000
        mockSeavessel(mainVessel)
        mockPilot(pilot)

        processTraces(
            "SGSIN/pilot_trace_noservice.json",
            "SGSIN/ship_trace_noservice.json"
        )

        assertEncounter(mainVessel, pilot, EncounterType.PILOT, count = 0)
    }

    @Test
    fun `bunker encounter between TURQUOISE and KRISTIN SCHEPERS`() {
        val bunker = 244010042
        val mainVessel = 255806436
        mockBunker(bunker)
        mockSeavessel(
            mainVessel,
            TransponderPosition(134, 7, 11, 11)
        )

        processTraces(
            "NLRTM/bunker-encounter-1-turquoise.json",
            "NLRTM/bunker-encounter-1-kristin_schepers.json"
        )

        assertEncounter(mainVessel, bunker, EncounterType.BUNKER)
    }

    @Test
    fun `double bunker encounter between VIRAGE and STI SAN ANTONIO`() {
        val bunker = 244690994
        val mainVessel = 538005413
        mockBunker(bunker)
        mockSeavessel(
            mainVessel,
            TransponderPosition(147, 36, 8, 24)
        )

        processTraces(
            "NLRTM/bunker-encounter-2-virage.json",
            "NLRTM/bunker-encounter-2-sti_san_antonio.json"
        )

        assertEncounter(mainVessel, bunker, EncounterType.BUNKER, count = 2)
    }

    @Test
    fun `NLRTM long-range pilot encounter`() {
        val pilotMmsi = 244790810
        assertTrue(pilotMmsi in longrangePilotVessels)

        mockSeavessel(211315100)
        mockSeavessel(636019842)
        mockPilot(pilotMmsi)

        processTraces(
            "NLRTM/long-range/pollux-211315100.json",
            "NLRTM/long-range/pollux-636019842.json",
            "NLRTM/long-range/pollux-$pilotMmsi.json"
        )

        val results = assertEncounters(listOf(211315100, 636019842), pilotMmsi, EncounterType.PILOT)
        results.forEach { (start, _) ->
            assertEquals(true, start.mainMoving)
            assertEquals(true, start.serviceMoving)
        }
    }

    @Test
    fun `JASMINE KNUTSEN arrives at NLRTM`() {
        val mainVessel = 257324000
        val krve1 = 244136452
        val krve25 = 244137351
        val tug1 = 244791000
        val tug2 = 245931000
        mockSeavessel(
            mainVessel,
            TransponderPosition(232, 45, 28, 18)
        )
        mockService(krve1, ShipRole.BOATMAN)
        mockService(krve25, ShipRole.BOATMAN)
        mockService(tug1, ShipRole.TUG)
        mockService(tug2, ShipRole.TUG)

        processTraces(
            "NLRTM/arrival-jasmine_knutsen.json",
            "NLRTM/arrival-jasmine_knutsen-krve_1.json",
            "NLRTM/arrival-jasmine_knutsen-krve_25.json",
            "NLRTM/arrival-jasmine_knutsen-rt_rob.json",
            "NLRTM/arrival-jasmine_knutsen-vb_brent.json",
            filterHistoric = false // Bad quality trace, load all data
        )

        assertEncounter(mainVessel, tug1, EncounterType.TUG)
        assertEncounter(mainVessel, tug2, EncounterType.TUG)
        assertEncounter(mainVessel, krve1, EncounterType.BOATMAN)
        assertEncounter(mainVessel, krve25, EncounterType.BOATMAN)
    }

    @Test
    fun `BEANR tugs encounter`() {
        val mainVesselMmsi = 352619000
        val tug1mmsi = 205483000
        val tug2mmsi = 205484000

        mockSeavessel(
            mainVesselMmsi,
            TransponderPosition(226, 74, 20, 28)
        )
        mockService(
            tug1mmsi,
            ShipRole.TUG,
            TransponderPosition(14, 18, 5, 7)
        )
        mockService(
            tug2mmsi,
            ShipRole.TUG,
            TransponderPosition(10, 22, 7, 5)
        )

        processTraces(
            "BEANR/tugs-1-msc_adelaide.json",
            "BEANR/tugs-1-union_grizzly.json",
            "BEANR/tugs-1-vb_kodiak.json",
        )

        assertEncounter(mainVesselMmsi, tug1mmsi, EncounterType.TUG)
        assertEncounter(mainVesselMmsi, tug2mmsi, EncounterType.TUG)
    }

    @Test
    fun `NLRTM tug departure encounters`() {
        val deRuyter = 244896000
        val vaga = 219170000
        val tug = 244900124

        mockSeavessel(
            deRuyter,
            TransponderPosition(82, 62, 9, 9)
        )
        mockSeavessel(
            vaga,
            TransponderPosition(161, 39, 18, 18)
        )
        mockService(tug, ShipRole.TUG)

        processTraces(
            "NLRTM/departure-hnlms_de_ruyter.json",
            "NLRTM/departure-hnlms_de_ruyter-vaga_maersk.json",
            "NLRTM/departure-hnlms_de_ruyter-vb_mars.json",
        )

        assertEncounter(deRuyter, tug, EncounterType.TUG_WAITING_DEPARTURE)
        assertEncounter(vaga, tug, EncounterType.TUG_WAITING_DEPARTURE)

        val endEvents = eventStream.values
            .map { it.second }
            .filterIsInstance<EncounterEndEvent>()
            .filter {
                it.encounterType == EncounterType.TUG_WAITING_DEPARTURE
            }
            .toMutableList()

        val vagaEndEvent = endEvents.first { it.ship.mmsi == vaga }
        val deRuyterEndEvent = endEvents.first { it.ship.mmsi == deRuyter }

        assertFalse(vagaEndEvent.valid, "Departure event should not be valid for VAGA MAERSK")
        assertTrue(deRuyterEndEvent.valid, "Departure event should be valid for HNLMS DE RUYTER")
    }

    @Test
    fun `NLRTM boatmen encounter with GIANCARLO D`() {
        val giancarlo = 249287000
        val krve5 = 244790318
        val krve9 = 244790319
        val tug = 224091000

        mockSeavessel(
            giancarlo,
            TransponderPosition(137, 18, 13, 13)
        )
        mockService(krve5, ShipRole.BOATMAN)
        mockService(krve9, ShipRole.BOATMAN)
        mockService(tug, ShipRole.TUG)

        processTraces(
            "NLRTM/boatmen-1-giancarlo_d.json",
            "NLRTM/boatmen-1-krve_5.json",
            "NLRTM/boatmen-1-krve_9.json",
            "NLRTM/boatmen-1-fairplay_bandama.json",
        )

        assertEncounter(giancarlo, krve5, EncounterType.BOATMAN)
        assertEncounter(giancarlo, krve9, EncounterType.BOATMAN)

        val events = getEventsForMmsis(setOf(giancarlo, krve5, krve9), EncounterType.BOATMAN)
            .filterIsInstance(EncounterEvent::class.java)

        assertEquals(
            4,
            events.filter { (it.metadata as? BoatmanEncounterMetadata)?.arrival == true }.size,
            "all 4 encounters should have metadata.arrival set to true"
        )

        assertEquals(
            4,
            events.filter { (it.metadata as? BoatmanEncounterMetadata)?.hasSimultaneousTugEncounter == true }.size,
            "all 4 encounters should have metadata.hasSimultaneousTugEncounter set to true"
        )

        assertEquals(
            2,
            events.filter { (it.metadata as? BoatmanEncounterMetadata)?.nrOfBoatmen == 2 }.size,
            "2 of 4 encounters should have the nr of boatmen set to 2"
        )

        assertEquals(
            2,
            events.filter { (it.metadata as? BoatmanEncounterMetadata)?.nrOfBoatmen == 1 }.size,
            "2 of 4 encounters should have the nr of boatmen set to 1"
        )
    }

    @Test
    fun `SVITZER TETOUAN tugs MICHEL A at MAPTM`() {
        val mainVessel = 215417000
        val tug = 242782300
        mockSeavessel(
            mainVessel,
            TransponderPosition(160, 24, 12, 12)
        )
        mockSeavessel(
            tug,
            TransponderPosition(19, 10, 11, 2)
        )
        mockService(tug, ShipRole.TUG)

        processTraces(
            "MAPTM/visit-michel-a.json",
            "MAPTM/visit-michel-a-svitzer-tetouan.json",
        )

        assertEncounter(mainVessel, tug, EncounterType.TUG)
        assertEncounter(mainVessel, tug, EncounterType.TUG_WAITING_DEPARTURE)
    }

    @Test
    fun `slow moving main vessel gets pilot at SEGOT`() {
        val mainVessel = 209467000
        val pilot = 265036400
        mockSeavessel(
            mainVessel,
            TransponderPosition(160, 24, 12, 12)
        )
        mockService(pilot, ShipRole.PILOT)

        processTraces(
            "SEGOT/pilot-X-PRESS-AGILITY.json",
            "SEGOT/pilot-PILOT-220-SE.json",
        )

        val (start, _) = assertEncounter(mainVessel, pilot, EncounterType.PILOT)!!
        assertEquals(true, start.mainMoving)
        assertEquals(false, start.serviceMoving)
    }

    /**
     * Test where the user can input the mainVesselTrace and serviceVesselTrace to test the expected encounter
     */
    @ParameterizedTest
    @MethodSource("processTestData")
    fun process(data: ProcessTestData) {
        val mainVessel = data.main
        val serviceVessel = data.service
        val expected = data.expected
        val type = expected.firstOrNull()?.type

        mockSeavessel(mainVessel.mmsi, mainVessel.transponder, mainVessel.type)

        // In ship-to-ship encounter two main vessels exist
        if (type == EncounterType.SHIP_TO_SHIP) {
            mockSeavessel(serviceVessel.mmsi, serviceVessel.transponder, serviceVessel.type)
        } else {
            mockService(serviceVessel.mmsi, serviceVessel.role, serviceVessel.transponder)
        }

        processTraces(mainVessel.traceFile, serviceVessel.traceFile, filterHistoric = data.filterHistoric)

        val events = assertEncounters(
            mainMmsi = mainVessel.mmsi,
            serviceMmsi = serviceVessel.mmsi,
            type = type,
            count = data.expected.size
        )

        if (type != null) {
            val actual = events.map { (start, end) -> convertToActual(start, end, type) }
            assertEquals(expected, actual)
        } else {
            assertEquals(events.size, 0)
        }
    }

    fun processTestData() = Stream.of(
        ProcessTestData(
            main = MainVessel(
                mmsi = 538005554,
                transponder = TransponderPosition(126, 18, 11, 11),
                traceFile = "USCRP/538005554.json",
                type = AisMessage.ShipType.TANKER
            ),
            service = ServiceVessel(
                mmsi = 563078100,
                transponder = TransponderPosition(288, 52, 27, 33),
                traceFile = "USCRP/563078100.json",
                type = AisMessage.ShipType.TANKER
            ),
            expected = listOf(
                Encounter(
                    from = "2024-01-17T12:22:38Z",
                    to = "2024-01-17T18:39:20Z",
                    startLocation = Location(lat = 27.78867166666667, lon = -96.382265),
                    endLocation = Location(lat = 27.788913333333333, lon = -96.381945),
                    type = EncounterType.SHIP_TO_SHIP,
                    mainMmsi = 538005554,
                    serviceMmsi = 563078100
                ),
                Encounter(
                    from = "2024-01-17T12:22:38Z",
                    to = "2024-01-17T18:39:20Z",
                    startLocation = Location(lat = 27.78867166666667, lon = -96.382265),
                    endLocation = Location(lat = 27.788913333333333, lon = -96.381945),
                    type = EncounterType.SHIP_TO_SHIP,
                    mainMmsi = 563078100,
                    serviceMmsi = 538005554
                ),
            ),
            filterHistoric = true
        ),
        ProcessTestData(
            main = MainVessel(
                mmsi = 538005364,
                transponder = TransponderPosition(199, 26, 12, 21),
                traceFile = "USCRP/538005364.json",
            ),
            service = ServiceVessel(
                mmsi = 368144340,
                transponder = TransponderPosition(7, 16, 7, 4),
                traceFile = "USCRP/368144340.json",
                role = ShipRole.BUNKER
            ),
            expected = listOf(
                Encounter(
                    from = "2024-09-18T20:58:03Z",
                    to = "2024-09-19T06:32:56Z",
                    startLocation = Location(lat = 27.8121, lon = -97.40231666666666),
                    endLocation = Location(lat = 27.8121, lon = -97.40235),
                    type = EncounterType.BUNKER,
                    mainMmsi = 538005364,
                    serviceMmsi = 368144340
                ),
            ),
            filterHistoric = true
        ),
        ProcessTestData(
            main = MainVessel(
                mmsi = 219835000,
                transponder = TransponderPosition(116, 283, 29, 29),
                traceFile = "MAPTM/219835000.json",
            ),
            service = ServiceVessel(
                mmsi = 242903000,
                transponder = TransponderPosition(12, 16, 4, 6),
                traceFile = "MAPTM/242903000.json",
                role = ShipRole.TUG
            ),
            expected = listOf(
                Encounter(
                    from = "2025-03-25T18:29:33Z",
                    to = "2025-03-25T18:36:33Z",
                    startLocation = Location(lat = 35.898316666666666, lon = -5.493833333333333),
                    endLocation = Location(lat = 35.91135, lon = -5.492533333333333),
                    type = EncounterType.TUG,
                    mainMmsi = 219835000,
                    serviceMmsi = 242903000
                ),
            ),
            filterHistoric = true
        ),
        ProcessTestData(
            main = MainVessel(
                mmsi = 477806000,
                transponder = TransponderPosition(199, 79, 27, 13),
                traceFile = "USSEA/477806000.json",
            ),
            service = ServiceVessel(
                mmsi = 366887970,
                transponder = TransponderPosition(20, 21, 6, 6),
                traceFile = "USSEA/366887970.json",
                role = ShipRole.TUG
            ),
            expected = listOf(
                Encounter(
                    from = "2025-01-28T07:14:27Z",
                    to = "2025-01-28T07:47:27Z",
                    startLocation = Location(lat = 47.57887166666667, lon = -122.36),
                    endLocation = Location(lat = 47.60039, lon = -122.36512333333333),
                    type = EncounterType.TUG,
                    mainMmsi = 477806000,
                    serviceMmsi = 366887970
                ),
            ),
            filterHistoric = true
        ),
        ProcessTestData(
            main = MainVessel(
                mmsi = 431614000,
                transponder = TransponderPosition(254, 38, 28, 17),
                traceFile = "NLAMS/431614000.json",
                type = AisMessage.ShipType.CARGO
            ),
            service = ServiceVessel(
                mmsi = 244740579,
                transponder = TransponderPosition(172, 0, 12, 0),
                traceFile = "NLAMS/244740579.json",
                type = AisMessage.ShipType.CARGO
            ),
            expected = listOf(
                Encounter(
                    from = "2024-12-11T02:51:59Z",
                    to = "2024-12-11T06:33:38Z",
                    startLocation = Location(lat = 52.469995000000004, lon = 4.58233),
                    endLocation = Location(lat = 52.469969999999996, lon = 4.587695),
                    type = EncounterType.SHIP_TO_SHIP,
                    mainMmsi = 244740579,
                    serviceMmsi = 431614000
                ),
                Encounter(
                    from = "2024-12-11T02:51:59Z",
                    to = "2024-12-11T06:33:38Z",
                    startLocation = Location(lat = 52.469995000000004, lon = 4.58233),
                    endLocation = Location(lat = 52.469969999999996, lon = 4.587695),
                    type = EncounterType.SHIP_TO_SHIP,
                    mainMmsi = 431614000,
                    serviceMmsi = 244740579
                ),
            ),
            filterHistoric = true
        ),
        ProcessTestData(
            main = MainVessel(
                mmsi = 431614000,
                transponder = TransponderPosition(254, 38, 28, 17),
                traceFile = "NLAMS/431614000.json",
                type = AisMessage.ShipType.CARGO
            ),
            service = ServiceVessel(
                mmsi = 244890469,
                transponder = TransponderPosition(172, 0, 6, 6),
                traceFile = "NLAMS/244890469.json",
                type = AisMessage.ShipType.CARGO
            ),
            expected = listOf(
                Encounter(
                    from = "2024-12-11T16:47:52Z",
                    to = "2024-12-11T20:05:37Z",
                    startLocation = Location(lat = 52.46876333333333, lon = 4.5792399999999995),
                    endLocation = Location(lat = 52.46877666666667, lon = 4.57921),
                    type = EncounterType.SHIP_TO_SHIP,
                    mainMmsi = 431614000,
                    serviceMmsi = 244890469
                ),
                Encounter(
                    from = "2024-12-11T16:47:52Z",
                    to = "2024-12-11T20:05:37Z",
                    startLocation = Location(lat = 52.46876333333333, lon = 4.5792399999999995),
                    endLocation = Location(lat = 52.46877666666667, lon = 4.57921),
                    type = EncounterType.SHIP_TO_SHIP,
                    mainMmsi = 244890469,
                    serviceMmsi = 431614000
                ),
            ),
            filterHistoric = true
        ),
        ProcessTestData(
            main = MainVessel(
                mmsi = 566453000,
                transponder = TransponderPosition(227, 47, 28, 21),
                traceFile = "USCRP/566453000-2.json",
                type = AisMessage.ShipType.TANKER
            ),
            service = ServiceVessel(
                mmsi = 477692200,
                transponder = TransponderPosition(276, 57, 38, 22),
                traceFile = "USCRP/477692200.json",
                type = AisMessage.ShipType.TANKER
            ),
            expected = listOf(
                Encounter(
                    from = "2024-11-02T08:13:09Z",
                    to = "2024-11-02T09:09:49Z",
                    startLocation = Location(lat = 27.669505, lon = -96.23995166666667),
                    endLocation = Location(lat = 27.664673333333333, lon = -96.26427166666667),
                    type = EncounterType.SHIP_TO_SHIP,
                    mainMmsi = 566453000,
                    serviceMmsi = 477692200
                ),
                Encounter(
                    from = "2024-11-02T08:13:09Z",
                    to = "2024-11-02T09:09:49Z",
                    startLocation = Location(lat = 27.669505, lon = -96.23995166666667),
                    endLocation = Location(lat = 27.664673333333333, lon = -96.26427166666667),
                    type = EncounterType.SHIP_TO_SHIP,
                    mainMmsi = 477692200,
                    serviceMmsi = 566453000
                ),
                Encounter(
                    from = "2024-11-02T09:24:18Z",
                    to = "2024-11-02T13:38:59Z",
                    startLocation = Location(lat = 27.665253333333332, lon = -96.267535),
                    endLocation = Location(lat = 27.680043333333334, lon = -96.33902333333334),
                    type = EncounterType.SHIP_TO_SHIP,
                    mainMmsi = 566453000,
                    serviceMmsi = 477692200
                ),
                Encounter(
                    from = "2024-11-02T09:24:18Z",
                    to = "2024-11-02T13:38:59Z",
                    startLocation = Location(lat = 27.665253333333332, lon = -96.267535),
                    endLocation = Location(lat = 27.680043333333334, lon = -96.33902333333334),
                    type = EncounterType.SHIP_TO_SHIP,
                    mainMmsi = 477692200,
                    serviceMmsi = 566453000
                ),
                Encounter(
                    from = "2024-11-02T22:17:23Z",
                    to = "2024-11-03T07:31:10Z",
                    startLocation = Location(lat = 27.485711666666667, lon = -95.89259),
                    endLocation = Location(lat = 27.488913333333336, lon = -95.93047833333334),
                    type = EncounterType.SHIP_TO_SHIP,
                    mainMmsi = 477692200,
                    serviceMmsi = 566453000
                ),
                Encounter(
                    from = "2024-11-02T22:17:23Z",
                    to = "2024-11-03T07:31:10Z",
                    startLocation = Location(lat = 27.485711666666667, lon = -95.89259),
                    endLocation = Location(lat = 27.488913333333336, lon = -95.93047833333334),
                    type = EncounterType.SHIP_TO_SHIP,
                    mainMmsi = 566453000,
                    serviceMmsi = 477692200
                ),
            ),
            filterHistoric = true
        ),
        ProcessTestData(
            main = MainVessel(
                mmsi = 636021066,
                transponder = TransponderPosition(227, 47, 28, 21),
                traceFile = "USCRP/636021066.json",
                type = AisMessage.ShipType.CARGO
            ),
            service = ServiceVessel(
                mmsi = 566453000,
                transponder = TransponderPosition(287, 52, 36, 24),
                traceFile = "USCRP/566453000.json",
                type = AisMessage.ShipType.CARGO
            ),
            expected = listOf(
                Encounter(
                    from = "2024-10-25T23:39:33Z",
                    to = "2024-10-26T21:37:33Z",
                    startLocation = Location(lat = 27.78638833333333, lon = -96.517595),
                    endLocation = Location(lat = 27.782506666666666, lon = -96.50539),
                    type = EncounterType.SHIP_TO_SHIP,
                    mainMmsi = 566453000,
                    serviceMmsi = 636021066
                ),
                Encounter(
                    from = "2024-10-25T23:39:33Z",
                    to = "2024-10-26T21:37:33Z",
                    startLocation = Location(lat = 27.78638833333333, lon = -96.517595),
                    endLocation = Location(lat = 27.782506666666666, lon = -96.50539),
                    type = EncounterType.SHIP_TO_SHIP,
                    mainMmsi = 636021066,
                    serviceMmsi = 566453000
                ),
            ),
            filterHistoric = true
        ),
        ProcessTestData(
            main = MainVessel(
                mmsi = 416070000,
                transponder = TransponderPosition(132, 268, 17, 45),
                traceFile = "NLRTM/main-416070000.json"
            ),
            service = ServiceVessel(
                mmsi = 244780375,
                transponder = TransponderPosition(17, 6, 3, 3),
                role = ShipRole.PILOT,
                traceFile = "NLRTM/service-244780375.json"
            ),
            expected = listOf(
                Encounter(
                    from = "2024-11-08T04:31:21Z",
                    to = "2024-11-08T04:39:16Z",
                    startLocation = Location(lat = 52.023133333333334, lon = 3.6390533333333335),
                    endLocation = Location(lat = 52.01502333333334, lon = 3.6552716666666667),
                    type = EncounterType.PILOT,
                    mainMmsi = 416070000,
                    serviceMmsi = 244780375
                )
            ),
            filterHistoric = true
        ),
        ProcessTestData(
            main = MainVessel(
                mmsi = 235612000,
                transponder = TransponderPosition(141, 41, 19, 9),
                traceFile = "GRPIR/main-235612000.json"
            ),
            service = ServiceVessel(
                mmsi = 241691000,
                transponder = TransponderPosition(9, 20, 7, 3),
                role = ShipRole.TUG,
                traceFile = "GRPIR/service-241691000.json"
            ),
            expected = listOf(
                Encounter(
                    from = "2023-12-30T05:06:58Z",
                    to = "2023-12-30T05:17:01Z",
                    startLocation = Location(lat = 37.952983333333336, lon = 23.598483333333334),
                    endLocation = Location(lat = 37.94305, lon = 23.599383333333332),
                    type = EncounterType.TUG,
                    mainMmsi = 235612000,
                    serviceMmsi = 241691000
                )
            ),
            filterHistoric = true
        ),
        ProcessTestData(
            main = MainVessel(
                mmsi = 563113500,
                transponder = TransponderPosition(148, 35, 11, 24),
                traceFile = "NLAMS/563113500-main.json"
            ),
            service = ServiceVessel(
                mmsi = 244150566,
                transponder = TransponderPosition(13, 13, 5, 5),
                role = ShipRole.TUG,
                traceFile = "NLAMS/244150566-service.json"
            ),
            expected = listOf(
                Encounter(
                    from = "2024-10-11T18:52:08Z",
                    to = "2024-10-11T21:24:08Z",
                    startLocation = Location(lat = 52.46431833333334, lon = 4.554331666666667),
                    endLocation = Location(lat = 52.41772, lon = 4.748856666666667),
                    type = EncounterType.TUG,
                    mainMmsi = 563113500,
                    serviceMmsi = 244150566
                )
            ),
            filterHistoric = true
        ),
        ProcessTestData(
            main = MainVessel(
                mmsi = 563113500,
                transponder = TransponderPosition(148, 35, 11, 24),
                traceFile = "NLAMS/563113500-main.json"
            ),
            service = ServiceVessel(
                mmsi = 244013126,
                transponder = TransponderPosition(13, 15, 5, 5),
                role = ShipRole.TUG,
                traceFile = "NLAMS/244013126-service.json"
            ),
            expected = listOf(
                Encounter(
                    from = "2024-10-11T20:41:34Z",
                    to = "2024-10-11T21:48:01Z",
                    startLocation = Location(lat = 52.43222166666667, lon = 4.732008333333334),
                    endLocation = Location(lat = 52.417705, lon = 4.74887),
                    type = EncounterType.TUG,
                    mainMmsi = 563113500,
                    serviceMmsi = 244013126
                )
            ),
            filterHistoric = true
        ),
        ProcessTestData(
            main = MainVessel(
                mmsi = 563113500,
                transponder = TransponderPosition(148, 35, 11, 24),
                traceFile = "NLAMS/563113500-main.json"
            ),
            service = ServiceVessel(
                mmsi = 244469000,
                transponder = TransponderPosition(7, 26, 7, 5),
                role = ShipRole.TUG,
                traceFile = "NLAMS/244469000-service.json"
            ),
            expected = listOf(
                Encounter(
                    from = "2024-10-11T18:58:30Z",
                    to = "2024-10-11T19:46:29Z",
                    startLocation = Location(lat = 52.46698833333333, lon = 4.573761666666667),
                    endLocation = Location(lat = 52.46763833333333, lon = 4.608071666666667),
                    type = EncounterType.TUG,
                    mainMmsi = 563113500,
                    serviceMmsi = 244469000
                )
            ),
            filterHistoric = true
        ),
        ProcessTestData(
            main = MainVessel(
                mmsi = 255792000,
                transponder = TransponderPosition(221, 73, 17, 15),
                traceFile = "CAMTR/main-255792000.json"
            ),
            service = ServiceVessel(
                mmsi = 316018399,
                transponder = TransponderPosition(12, 11, 5, 5),
                role = ShipRole.TUG,
                traceFile = "CAMTR/service-316018399.json"
            ),
            expected = listOf(),
            filterHistoric = true
        ),
        ProcessTestData(
            main = MainVessel(
                mmsi = 255792000,
                transponder = TransponderPosition(221, 73, 17, 15),
                traceFile = "CAMTR/main-255792000.json"
            ),
            service = ServiceVessel(
                mmsi = 316003325,
                transponder = TransponderPosition(10, 14, 5, 5),
                role = ShipRole.TUG,
                traceFile = "CAMTR/service-316003325.json"
            ),
            expected = listOf(
                Encounter(
                    from = "2023-05-29T09:37:31Z",
                    to = "2023-05-29T09:42:44Z",
                    startLocation = Location(lat = 45.57508333333333, lon = -73.50587),
                    endLocation = Location(lat = 45.58251833333334, lon = -73.50186333333333),
                    type = EncounterType.TUG,
                    mainMmsi = 255792000,
                    serviceMmsi = 316003325
                )
            ),
            filterHistoric = true
        ),
        ProcessTestData(
            main = MainVessel(
                mmsi = 219133000,
                transponder = TransponderPosition(161, 39, 18, 18),
                traceFile = "NLRTM/main-219133000.json"
            ),
            service = ServiceVessel(
                mmsi = 245425000,
                transponder = TransponderPosition(12, 16, 5, 6),
                role = ShipRole.TUG,
                traceFile = "NLRTM/service-245425000.json"
            ),
            expected = listOf(
                Encounter(
                    from = "2024-10-04T16:37:02Z",
                    to = "2024-10-04T17:55:23Z",
                    startLocation = Location(lat = 51.98373333333333, lon = 4.0623),
                    endLocation = Location(lat = 51.95783333333333, lon = 4.041683333333333),
                    type = EncounterType.TUG,
                    mainMmsi = 219133000,
                    serviceMmsi = 245425000
                )
            ),
            filterHistoric = true
        ),
        ProcessTestData(
            main = MainVessel(
                mmsi = 255806451,
                transponder = TransponderPosition(134, 24, 3, 21),
                traceFile = "NLRTM/main-255806451.json"
            ),
            service = ServiceVessel(
                mmsi = 244924000,
                transponder = TransponderPosition(12, 16, 5, 5),
                role = ShipRole.TUG,
                traceFile = "NLRTM/service-244924000.json"
            ),
            expected = listOf(
                Encounter(
                    from = "2024-09-24T17:13:48Z",
                    to = "2024-09-24T17:29:07Z",
                    startLocation = Location(lat = 51.946875, lon = 4.052793333333333),
                    endLocation = Location(lat = 51.96281666666666, lon = 4.087196666666666),
                    type = EncounterType.TUG,
                    mainMmsi = 255806451,
                    serviceMmsi = 244924000
                )
            ),
            filterHistoric = true
        ),
        ProcessTestData(
            main = MainVessel(
                mmsi = 219836000,
                transponder = TransponderPosition(116, 283, 29, 29),
                traceFile = "NLRTM/main-219836000.json"
            ),
            service = ServiceVessel(
                mmsi = 245427000,
                transponder = TransponderPosition(20, 12, 6, 6),
                role = ShipRole.TUG,
                traceFile = "NLRTM/service-245427000.json"
            ),
            expected = listOf(
                Encounter(
                    from = "2024-09-22T14:58:00Z",
                    to = "2024-09-22T17:33:06Z",
                    startLocation = Location(lat = 51.9856, lon = 4.058533333333333),
                    endLocation = Location(lat = 51.95675000000001, lon = 4.005016666666666),
                    type = EncounterType.TUG,
                    mainMmsi = 219836000,
                    serviceMmsi = 245427000
                )
            ),
            filterHistoric = true
        )
    )

    private fun convertToActual(start: EncounterStartEvent, end: EncounterEndEvent, type: EncounterType) =
        Encounter(
            startLocation = start.location,
            endLocation = end.location,
            from = start.actualTime.truncatedTo(ChronoUnit.SECONDS).toString(),
            to = end.actualTime.truncatedTo(ChronoUnit.SECONDS).toString(),
            type = type,
            mainMmsi = start.ship.mmsi,
            serviceMmsi = start.otherShip.mmsi
        ).also { it.printExpectedEncounter() }

    /**
     * Function to allow the user to easily copy the expected result.
     */
    private fun Encounter.printExpectedEncounter() {
        println(
            """
                Encounter(
                    from = "${this.from}",
                    to = "${this.to}",
                    startLocation = Location(lat = ${this.startLocation?.lat}, lon = ${this.startLocation?.lon}),
                    endLocation = ${this.endLocation?.let { "Location(lat = ${this.endLocation.lat}, lon = ${this.endLocation.lon})" } ?: "null"},
                    type = EncounterType.${this.type},
                    mainMmsi = ${this.mainMmsi},
                    serviceMmsi = ${this.serviceMmsi}
                ),
            """.trimIndent()
        )
    }

    private fun mockBunker(mmsi: Int) {
        mockService(mmsi, ShipRole.BUNKER)
    }

    private fun mockPilot(mmsi: Int) {
        mockService(mmsi, ShipRole.PILOT)
    }

    private fun mockService(mmsi: Int, role: ShipRole, transponder: TransponderPosition? = null) {
        whenever(shipInfoDatasource.getRoleForShip(mmsi)).thenReturn(role)
        whenever(shipInfoDatasource.getTransponderPosition(mmsi)).thenReturn(transponder)
        whenever(shipInfoDatasource.getShipType(mmsi)).thenReturn(AisMessage.ShipType.UNDEFINED)
    }

    private fun mockSeavessel(
        mmsi: Int,
        transponder: TransponderPosition? = null,
        type: AisMessage.ShipType = AisMessage.ShipType.UNDEFINED
    ) {
        whenever(shipInfoDatasource.getRoleForShip(mmsi)).thenReturn(ShipRole.NONE)
        whenever(shipInfoDatasource.getIsSeaVessel(mmsi)).thenReturn(true)
        whenever(shipInfoDatasource.getTransponderPosition(mmsi)).thenReturn(transponder)
        whenever(shipInfoDatasource.getShipType(mmsi)).thenReturn(type)
    }

    private fun processTraces(vararg resources: String, filterHistoric: Boolean = true) {
        val tracePoints = getTracePoints(*resources, filterHistoric = filterHistoric)
        processTraces(tracePoints)
    }

    private fun getTracePoints(vararg resources: String, filterHistoric: Boolean = true): Sequence<TestTracePoint> {
        return traceUtil.combine(*resources.map { traceUtil.loadTrace(it, filterHistoric) }.toTypedArray())
    }

    private fun processTraces(tracePoints: Sequence<TestTracePoint>) {
        tracePoints.forEach {
            encounterService.processMovement(it.messageTime, it.ship, it.from, it.to, it.speed, it.course, it.heading)
        }
    }

    private fun assertEncounter(mainMmsi: Int, serviceMmsi: Int, type: EncounterType, count: Int = 1): Pair<EncounterStartEvent, EncounterEndEvent>? {
        val mainMmsis = mutableListOf<Int>()
        // mainMmsis requires at least one entry to be able to request events during the assertion
        repeat(count.coerceAtLeast(1)) { mainMmsis.add(mainMmsi) }
        return assertEncounters(mainMmsis, serviceMmsi, type, count).firstOrNull()
    }

    private fun assertEncounters(mainMmsis: List<Int>, serviceMmsi: Int, type: EncounterType, count: Int = mainMmsis.size): List<Pair<EncounterStartEvent, EncounterEndEvent>> {
        val relevantMmsis = (mainMmsis + serviceMmsi).toSet()
        val events = getEventsForMmsis(relevantMmsis, type)
            .toMutableList()
        assertEquals(2 * count, events.size, "expected ${2 * count} $type encounters")

        // don't check if we expect no encounters
        if (count == 0) return emptyList()
        return mainMmsis.map { mainMmsi ->
            val event1 = events.removeFirst() as? EncounterStartEvent
                ?: fail("expected an EncounterStartEvent as first event")
            val event2 = events.removeFirst() as? EncounterEndEvent
                ?: fail("expected an EncounterEndEvent as second event")

            assertEquals(mainMmsi, event1.ship.mmsi, "wrong main vessel in encounter start")
            assertEquals(serviceMmsi, event1.otherShip.mmsi, "wrong service vessel in encounter start")
            assertEquals(mainMmsi, event2.ship.mmsi, "wrong main vessel in encounter end")
            assertEquals(serviceMmsi, event2.otherShip.mmsi, "wrong service vessel in encounter end")

            assertEquals(type, event1.encounterType, "wrong type in encounter start")
            assertEquals(type, event2.encounterType, "wrong type in encounter end")

            event1 to event2
        }
    }

    private fun assertEncounters(mainMmsi: Int, serviceMmsi: Int, type: EncounterType?, count: Int): List<Pair<EncounterStartEvent, EncounterEndEvent>> {
        val relevantMmsis = setOf(mainMmsi, serviceMmsi)
        val events = getEventsForMmsis(relevantMmsis, type)
            .toMutableList()

        // An event contains two events (start and end)
        assertEquals(count * 2, events.size, "expected ${2 * count} $type encounters")

        // don't check if we expect no encounters
        if (count == 0) return emptyList()

        val encounters = mutableListOf<Pair<EncounterStartEvent, EncounterEndEvent>>()

        while (events.isNotEmpty()) {
            val start = events.removeFirst() as? EncounterStartEvent
                ?: fail("expected an EncounterStartEvent as first event")

            val end = events.firstOrNull { (it as? EncounterEndEvent)?.startEventId == start._id }?.also {
                events.remove(it)
            } as? EncounterEndEvent

            if (end != null) {
                encounters.add(start to end)
            }
        }

        return encounters
    }

    private fun getEventsForMmsis(relevantMmsis: Set<Int>, type: EncounterType?): List<Event> {
        return eventStream.values
            .map { it.second }
            .filter {
                it.ship.mmsi in relevantMmsis &&
                    (it as? EncounterEvent)?.otherShip?.mmsi in relevantMmsis &&
                    (it as? EncounterEvent)?.encounterType == type
            }
    }

    /**
     * Test data which holds the data needed for testing between a sea vessel (main) and a tug/pilot/bunker vessel (service)
     */
    data class ProcessTestData(
        val main: MainVessel,
        val service: ServiceVessel,
        val expected: List<Encounter>,
        val filterHistoric: Boolean
    )

    data class MainVessel(
        val mmsi: Int,
        val transponder: TransponderPosition,
        val traceFile: String,
        val type: AisMessage.ShipType = AisMessage.ShipType.UNDEFINED
    )

    data class ServiceVessel(
        val mmsi: Int,
        val transponder: TransponderPosition? = null,
        val role: ShipRole = ShipRole.NONE,
        val traceFile: String,
        val type: AisMessage.ShipType = AisMessage.ShipType.UNDEFINED
    )

    data class Encounter(
        val startLocation: Location? = null,
        val endLocation: Location? = null,
        val from: String? = null,
        val to: String? = null,
        val type: EncounterType,
        val mainMmsi: Int,
        val serviceMmsi: Int
    )

    class MockEncounterStateService : EncounterStateService {
        private val ongoing = ConcurrentHashMap<Int, ConcurrentHashMap<Int, EncounterState>>()

        override fun get(mmsi: Int): Map<Int, EncounterState> = ongoing[mmsi]?.toMap() ?: emptyMap()

        override fun persist(encounter: EncounterState) {
            ongoing.getOrPut(encounter.mmsi1, ::ConcurrentHashMap)[encounter.mmsi2] = encounter
            ongoing.getOrPut(encounter.mmsi2, ::ConcurrentHashMap)[encounter.mmsi1] = encounter
        }

        override fun remove(encounter: EncounterState) {
            ongoing[encounter.mmsi1]?.remove(encounter.mmsi2)
            ongoing[encounter.mmsi2]?.remove(encounter.mmsi1)
        }

        override fun ongoingCount(): Int {
            return ongoing.values.sumOf { it.filter { it.value.startEventId != null }.size } / 2
        }
    }
}
