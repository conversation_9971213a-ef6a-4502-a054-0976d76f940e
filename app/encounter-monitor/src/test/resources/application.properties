nats.enabled=true

nats.ais-stream.enabled=true
nats.ais-stream.url=nats://localhost:4222
nats.ais-stream.username=ais-stream-consume-encounter-monitor
nats.ais-stream.password=

nats.event-stream.enabled=true
nats.event-stream.url=nats://localhost:4222
nats.event-stream.username=event-publish-encounter-monitor
nats.event-stream.password=

# Prometheus JVM stats exposing
management.metrics.enable.jvm=true
management.metrics.tags.component=encounter-monitor
management.endpoints.web.exposure.include=health,prometheus

csi.url=https://csibackend.dev.teqplay.com
csi.domain=keycloak.teqplay.nl
csi.realm=dev
csi.clientId=encounter-monitor
csi.clientSecret=

encounter.cache-ttl.ship-role=PT4H

poma.url=https://backendpoma.dev.teqplay.com
poma.domain=keycloakdev.teqplay.nl
poma.realm=dev
poma.client-id=encounter-monitor
poma.client-secret=
