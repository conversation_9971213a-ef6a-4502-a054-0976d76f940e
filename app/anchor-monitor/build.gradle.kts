buildscript {
    project.extra.set("baseVersion", "2.1.0")
}

plugins {
    id("ais-engine.kotlin-app-webmvc-conventions")
    id("org.cyclonedx.bom") version cyclonedxBomVersion
}

dependencies {
    implementation(project(":api:nats-stream-event"))

    implementation("nl.teqplay.skeleton:auth-credentials-keycloak-s2s-common:$skeletonVersion")
    implementation("nl.teqplay.skeleton:auth-credentials-keycloak-s2s-client:$skeletonVersion")
    implementation("nl.teqplay.skeleton:nats:$skeletonVersion")

    implementation("nl.teqplay.poma:api:$pomaVersion")

    testImplementation(project(":lib:testing-event"))
}

tasks.cyclonedxBom {
    setIncludeConfigs(listOf("runtimeClasspath"))
    setOutputFormat("json")
    setOutputName("bom")
}
