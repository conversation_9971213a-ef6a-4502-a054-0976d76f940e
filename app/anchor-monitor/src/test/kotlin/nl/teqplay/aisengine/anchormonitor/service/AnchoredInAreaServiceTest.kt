package nl.teqplay.aisengine.anchormonitor.service

import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.aisengine.anchormonitor.model.AnchorState
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier.AreaType
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.aisengine.testing.event.createAnchoredEndEvent
import nl.teqplay.aisengine.testing.event.createAnchoredStartEvent
import nl.teqplay.aisengine.testing.event.createAreaEndEvent
import nl.teqplay.aisengine.testing.event.createAreaStartEvent
import nl.teqplay.aisengine.testing.event.createShipMovingStartEvent
import nl.teqplay.aisengine.testing.event.createStateAreaInsideEvent
import nl.teqplay.aisengine.testing.event.defaultAisShipIdentifier
import nl.teqplay.skeleton.nats.NatsKeyValueBucket
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.time.Duration
import java.time.Instant
import java.util.concurrent.atomic.AtomicLong
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class AnchoredInAreaServiceTest {

    private val eventStreamService = mock<EventStreamService>()
    private val kvBucket = mock<NatsKeyValueBucket<AnchorState>>()
    private val anchoredInAreaStateService = AnchoredInAreaStateService(
        kvBucket
    )
    private val meterRegistry = mock<MeterRegistry>().apply {
        whenever(gauge(any(), any(), any<AtomicLong>())).thenReturn(mock())
    }
    private val anchoredInAreaService = AnchoredInAreaService(
        eventStreamService,
        anchoredInAreaStateService,
        meterRegistry
    )

    private val anchor1 = AreaIdentifier(id = "anchor1", type = AreaType.ANCHOR)
    private val anchor2 = AreaIdentifier(id = "anchor2", type = AreaType.ANCHOR)
    private val anchor3 = AreaIdentifier(id = "anchor3", type = AreaType.ANCHOR)

    companion object {
        const val LAYING_STILL = 0f
        const val MOVING_SPEED = AnchoredInAreaService.ANCHORED_MAX_SPEED + .1f
    }

    data class DetectAnchoredEventsTestData(
        val message: String,
        val input: List<Event>,
        val output: List<Event>,
    )

    @Test
    fun `detectAnchoredEvents - backward compatible, end anchored events when ship starts moving`() {
        val anchoredInAreaStateService = AnchoredInAreaStateService(
            kvBucket
        )
        val anchoredInAreaService = AnchoredInAreaService(
            eventStreamService,
            anchoredInAreaStateService,
            meterRegistry
        )

        val ship = defaultAisShipIdentifier

        // Two events were generated based on movement events, these should trigger anchored end events.
        anchoredInAreaStateService.save(AnchorState(ship = ship, areaIdentifier = anchor1)) // Defaults to version=1.
        anchoredInAreaStateService.save(AnchorState(ship = ship, areaIdentifier = anchor2, version = 1))

        // One event was based on area events, so these should not trigger an end event based on movement.
        anchoredInAreaStateService.save(AnchorState(ship = ship, areaIdentifier = anchor3, version = 2))

        val movingStartEvent = createShipMovingStartEvent()
        val actual = anchoredInAreaService.detectAnchoredEvents(movingStartEvent)

        val expected = listOf(
            createAnchoredEndEvent(area = anchor1),
            createAnchoredEndEvent(area = anchor2),
        )
        assertThat(actual)
            .usingRecursiveComparison().ignoringFields("_id", "startEventId", "createdTime", "deleted")
            .isEqualTo(expected)
    }

    @ParameterizedTest
    @MethodSource("detectAnchoredEventsTestData")
    fun detectAnchoredEvents(data: DetectAnchoredEventsTestData) {
        val actual = data.input.flatMap { anchoredInAreaService.detectAnchoredEvents(it) }

        assertThat(actual)
            .usingRecursiveComparison().ignoringFields("_id", "startEventId", "createdTime", "deleted")
            .isEqualTo(data.output)
    }

    private fun detectAnchoredEventsTestData() = Stream.of(
        DetectAnchoredEventsTestData(
            message = "no events",
            input = emptyList(),
            output = emptyList()
        ),
        DetectAnchoredEventsTestData(
            message = "simple area start/end, anchor events equal",
            input = listOf(
                createAreaStartEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH
                ),
                createAreaEndEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                )
            ),
            output = listOf(
                createAnchoredStartEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH
                ),
                createAnchoredEndEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                )
            )
        ),
        DetectAnchoredEventsTestData(
            message = "simple area start/end, anchor events equal, even if the end event doesn't have speedOverGround",
            input = listOf(
                createAreaStartEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH
                ),
                createAreaEndEvent(
                    area = anchor1,
                    speedOverGround = null,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                )
            ),
            output = listOf(
                createAnchoredStartEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH
                ),
                createAnchoredEndEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                )
            )
        ),
        DetectAnchoredEventsTestData(
            message = "exiting the area with a low speed should not start an anchor event",
            input = listOf(
                createAreaEndEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                )
            ),
            output = emptyList()
        ),
        DetectAnchoredEventsTestData(
            message = "missing speed doesn't cause flip-flop",
            input = listOf(
                createAreaStartEvent(
                    area = anchor1,
                    speedOverGround = null,
                    actualTime = Instant.EPOCH
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(1))
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = null,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(2))
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(3))
                ),
                createAreaEndEvent(
                    area = anchor1,
                    speedOverGround = null,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                )
            ),
            output = listOf(
                createAnchoredStartEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(1))
                ),
                createAnchoredEndEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(3))
                )
            )
        ),
        DetectAnchoredEventsTestData(
            message = "multiple area start/end, anchor events equal",
            input = listOf(
                createAreaStartEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH
                ),
                createAreaEndEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                ),
                createAreaStartEvent(
                    area = anchor2,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(2))
                ),
                createAreaEndEvent(
                    area = anchor2,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(3))
                )
            ),
            output = listOf(
                createAnchoredStartEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH
                ),
                createAnchoredEndEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                ),
                createAnchoredStartEvent(
                    area = anchor2,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(2))
                ),
                createAnchoredEndEvent(
                    area = anchor2,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(3))
                ),
            )
        ),
        DetectAnchoredEventsTestData(
            message = "interleaved area start/end, anchor events equal",
            input = listOf(
                createAreaStartEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH
                ),
                createAreaStartEvent(
                    area = anchor2,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                ),
                createAreaEndEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(2))
                ),
                createAreaEndEvent(
                    area = anchor2,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(3))
                )
            ),
            output = listOf(
                createAnchoredStartEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH
                ),
                createAnchoredStartEvent(
                    area = anchor2,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                ),
                createAnchoredEndEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(2))
                ),
                createAnchoredEndEvent(
                    area = anchor2,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(3))
                ),
            )
        ),
        DetectAnchoredEventsTestData(
            message = "anchored after area start, area end always ends",
            input = listOf(
                createAreaStartEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(1))
                ),
                createAreaEndEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                )
            ),
            output = listOf(
                createAnchoredStartEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(1))
                ),
                createAnchoredEndEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                )
            )
        ),
        DetectAnchoredEventsTestData(
            message = "anchored during area start, starts moving before area end",
            input = listOf(
                createAreaStartEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(1))
                ),
                createAreaEndEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                )
            ),
            output = listOf(
                createAnchoredStartEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH
                ),
                createAnchoredEndEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(1))
                )
            )
        ),
        DetectAnchoredEventsTestData(
            message = "anchored inside area start/end",
            input = listOf(
                createAreaStartEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(1))
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(2))
                ),
                createAreaEndEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                )
            ),
            output = listOf(
                createAnchoredStartEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(1))
                ),
                createAnchoredEndEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(2))
                )
            )
        ),
        DetectAnchoredEventsTestData(
            message = "anchored inside area start/end multiple times",
            input = listOf(
                createAreaStartEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(1))
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(2))
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(8))
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(9))
                ),
                createAreaEndEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                )
            ),
            output = listOf(
                createAnchoredStartEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(1))
                ),
                createAnchoredEndEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(2))
                ),
                createAnchoredStartEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(8))
                ),
                createAnchoredEndEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(9))
                )
            )
        ),
        DetectAnchoredEventsTestData(
            message = "anchored inside area, drifting should result in only one anchor event pair",
            input = listOf(
                createAreaStartEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = 1.5f,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(2))
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(4))
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = 1.1f,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(6))
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = 1.5f,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(7))
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(8))
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(9))
                ),
                createAreaEndEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                )
            ),
            output = listOf(
                createAnchoredStartEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(4))
                ),
                createAnchoredEndEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(9))
                ),
            )
        ),
        DetectAnchoredEventsTestData(
            message = "not anchored, drifting with ANCHORED_MAX_SPEED",
            input = listOf(
                createAreaStartEvent(
                    area = anchor1,
                    speedOverGround = 4.4f,
                    actualTime = Instant.EPOCH
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = 4.3f,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(2))
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = 1.1f,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(6))
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = 4.5f,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(7))
                ),
                createAreaEndEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                )
            ),
            output = emptyList()
        ),
        DetectAnchoredEventsTestData(
            message = "anchored inside area, lay still then drifting with more than ANCHORED_MAX_SPEED result to anchor events",
            input = listOf(
                createAreaStartEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = 4.3f,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(2))
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = LAYING_STILL,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(6))
                ),
                createStateAreaInsideEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(7))
                ),
                createAreaEndEvent(
                    area = anchor1,
                    speedOverGround = MOVING_SPEED,
                    actualTime = Instant.EPOCH.plus(Duration.ofDays(1))
                )
            ),
            output = listOf(
                createAnchoredStartEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(6))
                ),
                createAnchoredEndEvent(
                    area = anchor1,
                    actualTime = Instant.EPOCH.plus(Duration.ofHours(7))
                ),
            )
        ),
    )
}
