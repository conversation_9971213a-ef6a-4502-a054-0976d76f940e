package nl.teqplay.aisengine.anchormonitor.service

import io.github.oshai.kotlinlogging.KotlinLogging
import jakarta.annotation.PostConstruct
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.model.ShipMovingEndEvent
import nl.teqplay.aisengine.event.model.ShipMovingStartEvent
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.aisengine.nats.stream.EventStreamService.MessageContext
import nl.teqplay.aisengine.revents.ReventsAdditionalSubjectsService
import nl.teqplay.skeleton.nats.NatsConsumerStream
import org.springframework.stereotype.Component

/**
 * [EventHandlerService] reading events from the event-stream
 */
@Component
class EventHandlerService(
    private val anchoredInAreaService: AnchoredInAreaService,
    private val eventStreamService: EventStreamService,
    private val eventConsumerStream: NatsConsumerStream<Event>,
    private val reventsAdditionalSubjectsService: ReventsAdditionalSubjectsService?,
) {
    private val LOG = KotlinLogging.logger {}

    companion object {
        const val EVENT_SUBJECT = "event.area.*.anchor.>"
    }

    @PostConstruct
    fun receive() {
        LOG.info { "Start consuming events from the event-stream, having the subject $EVENT_SUBJECT" }

        eventStreamService.consume(
            stream = eventConsumerStream,
            subjects = listOf(EVENT_SUBJECT) +
                (reventsAdditionalSubjectsService?.getAdditionalSubjects() ?: emptyList()),
            enableStateEvents = true
        ) { event, message ->
            process(event, message)
        }
    }

    /**
     * This method processes all [ShipMovingStartEvent] and [ShipMovingEndEvent] events received from stream
     */
    private fun process(event: Event, message: MessageContext) {
        anchoredInAreaService.process(event)
        message.ack()
    }
}
