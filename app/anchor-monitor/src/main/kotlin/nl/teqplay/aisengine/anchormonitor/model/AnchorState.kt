package nl.teqplay.aisengine.anchormonitor.model

import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier

/**
 * State of an anchorage
 * [ship]: the ship this anchor event is about
 * [areaIdentifier]: information about the area this anchor event is about
 * [startEventId]: the identifier of the start of this event, so it can be linked at the end event
 */
data class AnchorState(
    val ship: AisShipIdentifier,
    val areaIdentifier: AreaIdentifier,
    /**
     * If a start event has been generated, the id will be stored here. This is used to refer to the start event when
     * the end event is generated.
     */
    var startEventId: String? = null,

    /**
     * Which version of anchor detection is used.
     * - 1 = movement events (default, since version didn't exist before)
     * - 2 = area events
     */
    val version: Int = 1,
) {
    /**
     * Create a string key uniquely identifying this encounter, based on the MMSI of the ship and the area identifier
     */
    fun key() = "${ship.mmsi}-${areaIdentifier.id}"
}
