package nl.teqplay.aisengine.anchormonitor

import nl.teqplay.skeleton.common.logging.EnableIncomingRequestLogging
import nl.teqplay.skeleton.common.logging.EnableOutgoingRequestLogging
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication
import org.springframework.scheduling.annotation.EnableScheduling

@SpringBootApplication
@EnableScheduling
@EnableIncomingRequestLogging
@EnableOutgoingRequestLogging
@ConfigurationPropertiesScan
class AnchorMonitor

fun main(args: Array<String>) {
    runApplication<AnchorMonitor>(*args)
}
