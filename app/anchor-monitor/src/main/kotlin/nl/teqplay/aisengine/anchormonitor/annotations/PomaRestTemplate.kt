package nl.teqplay.aisengine.anchormonitor.annotations

import org.springframework.beans.factory.annotation.Qualifier
import java.lang.annotation.Inherited

/** Standard name for the template */
const val POMA_REST_TEMPLATE = "pomaRestTemplate"

/** Allow the template to be used as an annotation */
@Target(AnnotationTarget.VALUE_PARAMETER, AnnotationTarget.FIELD)
@Retention(AnnotationRetention.RUNTIME)
@MustBeDocumented
@Inherited
@Qualifier(POMA_REST_TEMPLATE)
annotation class PomaRestTemplate
