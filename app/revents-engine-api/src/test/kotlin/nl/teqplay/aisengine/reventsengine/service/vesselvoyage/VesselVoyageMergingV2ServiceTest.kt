package nl.teqplay.aisengine.reventsengine.service.vesselvoyage

import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.reventsengine.common.datasource.ScenariosDataSource
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState
import nl.teqplay.aisengine.reventsengine.common.model.ship.ScenarioInterestVesselVoyage
import nl.teqplay.aisengine.reventsengine.common.service.vesselvoyage.VesselVoyageStorageV2Service
import nl.teqplay.aisengine.reventsengine.model.interest.scenario.InterestArea
import nl.teqplay.aisengine.reventsengine.model.scenario.Scenario
import nl.teqplay.aisengine.reventsengine.model.vesselvoyage.MergeEntryV2
import nl.teqplay.aisengine.reventsengine.service.scenario.scenarioCreateRequest
import nl.teqplay.aisengine.util.withMargin
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.TimeWindow
import nl.teqplay.vesselvoyage.model.v2.AreaActivity
import nl.teqplay.vesselvoyage.model.v2.EntryESoFWrapper
import nl.teqplay.vesselvoyage.model.v2.LocationTime
import nl.teqplay.vesselvoyage.model.v2.NewEntry
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import nl.teqplay.vesselvoyage.model.v2.NewVoyage
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.kotlin.any
import org.mockito.kotlin.clearInvocations
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class VesselVoyageMergingV2ServiceTest {

    private val vesselVoyageStorageV2Service = mock<VesselVoyageStorageV2Service>()
    private val scenariosDataSource = mock<ScenariosDataSource>()
    private val vesselVoyageMergingV2Service = VesselVoyageMergingV2Service(
        vesselVoyageStorageV2Service,
        scenariosDataSource
    )

    companion object {
        private const val IMO = 0
        private val LOC = Location(0.0, 0.0)
        private val window1 = TimeWindow(Instant.EPOCH, Duration.ofDays(7))
        private val window2 = TimeWindow(Instant.EPOCH.plus(10, ChronoUnit.DAYS), Duration.ofDays(7))
        private val window3 = TimeWindow(Instant.EPOCH.plus(20, ChronoUnit.DAYS), Duration.ofDays(7))
    }

    @BeforeEach
    fun beforeEach() {
        clearInvocations(
            vesselVoyageStorageV2Service,
            scenariosDataSource
        )
    }

    @Test
    fun `fetchEntriesForMerging - multiple interests should result into multiple merge entries`() {
        whenever(vesselVoyageStorageV2Service.getInterestsByScenarioAndImo(any(), any())).thenReturn(
            listOf(
                ScenarioInterestVesselVoyage(0, window1, window1, false, "scenario"),
                ScenarioInterestVesselVoyage(0, window2, window2, false, "scenario"),
                ScenarioInterestVesselVoyage(0, window3, window3, true, "scenario"),
            )
        )
        whenever(vesselVoyageStorageV2Service.fetchEntries(any(), any(), any())).thenReturn(emptyList())

        val actual = vesselVoyageMergingV2Service.fetchEntriesForMerging("scenario", IMO)

        val expected = listOf(
            MergeEntryV2(window = window1, entries = emptyList()),
            MergeEntryV2(window = window2, entries = emptyList())
        )
        assertEquals(expected, actual)

        verify(vesselVoyageStorageV2Service, times(1)).getInterestsByScenarioAndImo(eq("scenario"), eq(0))
    }

    @Test
    fun `fetchEntriesForMerging - filter visits if port interests are specified`() {
        whenever(vesselVoyageStorageV2Service.getInterestsByScenarioAndImo(any(), any())).thenReturn(
            listOf(ScenarioInterestVesselVoyage(0, window1, window1, false, "scenario"))
        )
        whenever(scenariosDataSource.get(any())).thenReturn(
            ScenarioState(
                scenarioCreateRequest.copy(
                    interests = listOf(InterestArea(InterestArea.Area(AreaIdentifier.AreaType.PORT, "NLRTM_AREA_ID")))
                )
            )
        )

        var expectedVisit: EntryESoFWrapper<NewVisit>? = null
        whenever(vesselVoyageStorageV2Service.fetchEntries(any(), any(), any())).thenReturn(
            build {
                createVoyage(Duration.ofDays(1))
                expectedVisit = createVisit(Duration.ofDays(1), areaId = "NLRTM_AREA_ID")
                createVoyage(Duration.ofDays(1))
                createVisit(Duration.ofDays(1), areaId = "USHOU_AREA_ID")
            }
        )

        val actual = vesselVoyageMergingV2Service.fetchEntriesForMerging("scenario", IMO)

        val expected = listOf(
            MergeEntryV2(window = window1, entries = listOf(expectedVisit!!))
        )
        assertEquals(expected, actual)

        verify(scenariosDataSource, times(1)).get(eq("scenario"))
        verify(vesselVoyageStorageV2Service, times(1)).getInterestsByScenarioAndImo(eq("scenario"), eq(0))
    }

    data class FetchEntriesForMergingTestData(
        val message: String,
        val windowMargin: Scenario.WindowMargin,
        val windowNoMargin: TimeWindow,
        val entries: List<EntryESoFWrapper<out NewEntry>>,
        val expected: List<EntryESoFWrapper<out NewEntry>>,
    )

    @ParameterizedTest
    @MethodSource("fetchEntriesForMergingTestData")
    fun `fetchEntriesForMerging - no metadata`(data: FetchEntriesForMergingTestData) {
        val window = data.windowNoMargin.withMargin(data.windowMargin)

        whenever(vesselVoyageStorageV2Service.getInterestsByScenarioAndImo(any(), any()))
            .thenReturn(listOf(ScenarioInterestVesselVoyage(0, window, data.windowNoMargin, false, "scenario")))
        whenever(vesselVoyageStorageV2Service.fetchEntries(any(), any(), any()))
            .thenReturn(data.entries)
        whenever(scenariosDataSource.get(any())).thenReturn(null)

        val actual = vesselVoyageMergingV2Service.fetchEntriesForMerging("scenario", IMO).first()
        val expected = MergeEntryV2(window = window, entries = data.expected)
        assertThat(actual)
            .usingRecursiveComparison()
            .ignoringFields("entries.entry.updatedAt")
            .isEqualTo(expected)
    }

    @ParameterizedTest
    @MethodSource("fetchEntriesForMergingTestData")
    fun `fetchEntriesForMerging - metadata with empty interests`(data: FetchEntriesForMergingTestData) {
        val window = data.windowNoMargin.withMargin(data.windowMargin)

        whenever(vesselVoyageStorageV2Service.getInterestsByScenarioAndImo(any(), any()))
            .thenReturn(listOf(ScenarioInterestVesselVoyage(0, window, data.windowNoMargin, false, "scenario")))
        whenever(vesselVoyageStorageV2Service.fetchEntries(any(), any(), any()))
            .thenReturn(data.entries)
        whenever(scenariosDataSource.get(any())).thenReturn(ScenarioState(scenarioCreateRequest))

        val actual = vesselVoyageMergingV2Service.fetchEntriesForMerging("scenario", IMO).first()
        val expected = MergeEntryV2(window = window, entries = data.expected)
        assertThat(actual)
            .usingRecursiveComparison()
            .ignoringFields("entries.entry.updatedAt")
            .isEqualTo(expected)
    }

    private fun fetchEntriesForMergingTestData() = Stream.of(
        FetchEntriesForMergingTestData(
            message = "empty",
            windowMargin = Scenario.WindowMargin(),
            windowNoMargin = TimeWindow(Instant.EPOCH, Duration.ofDays(1)),
            entries = emptyList(),
            expected = emptyList(),
        ),
        FetchEntriesForMergingTestData(
            message = "visit within window stays the same",
            windowMargin = Scenario.WindowMargin(),
            windowNoMargin = TimeWindow(Instant.EPOCH, Duration.ofDays(1)),
            entries = build {
                createVisit(Duration.ofDays(1))
            },
            expected = build {
                createVisit(Duration.ofDays(1))
            },
        ),
        FetchEntriesForMergingTestData(
            message = "unfinished visit within window is not used",
            windowMargin = Scenario.WindowMargin(),
            windowNoMargin = TimeWindow(Instant.EPOCH, Duration.ofDays(1)),
            entries = build {
                createVisit(Duration.ofDays(1), finished = false)
            },
            expected = emptyList(),
        ),
        FetchEntriesForMergingTestData(
            message = "unfinished entries are not used",
            windowMargin = Scenario.WindowMargin(),
            windowNoMargin = TimeWindow(Instant.EPOCH, Duration.ofDays(3)),
            entries = build {
                createVoyage(Duration.ofDays(1), finished = false)
                createVisit(Duration.ofDays(1), finished = false)
                createVoyage(Duration.ofDays(1), finished = false)
            },
            expected = emptyList(),
        ),
        FetchEntriesForMergingTestData(
            message = "voyages at both ends should be removed",
            windowMargin = Scenario.WindowMargin(),
            windowNoMargin = TimeWindow(Instant.EPOCH, Duration.ofDays(3)),
            entries = build {
                createVoyage(Duration.ofDays(1))
                createVisit(Duration.ofDays(1))
                createVoyage(Duration.ofDays(1))
            },
            expected = build(offset = Duration.ofDays(1)) {
                createVisit(Duration.ofDays(1))
            },
        ),
        FetchEntriesForMergingTestData(
            // First/last visits might not have enough data, therefore we need to remove them.
            message = "margin - remove first/last visits at the edges outside window",
            windowMargin = Scenario.WindowMargin(2, 2),
            windowNoMargin = TimeWindow(Instant.EPOCH, Duration.ofDays(3)),
            entries = build(offset = Duration.ofDays(-2)) {
                createVoyage(Duration.ofDays(1))
                createVisit(Duration.ofDays(1))
                createVoyage(Duration.ofDays(1))
                createVisit(Duration.ofDays(1)) // <-- only this should be kept
                createVoyage(Duration.ofDays(1))
                createVisit(Duration.ofDays(1))
                createVoyage(Duration.ofDays(1))
            },
            expected = build(offset = Duration.ofDays(1)) {
                createVisit(Duration.ofDays(1))
            },
        ),
        FetchEntriesForMergingTestData(
            // First/last visits might not have enough data, therefore we need to remove them.
            message = "margin - remove first/last visits at the edges outside window (more data)",
            windowMargin = Scenario.WindowMargin(4, 4),
            windowNoMargin = TimeWindow(Instant.EPOCH, Duration.ofDays(3)),
            entries = build(offset = Duration.ofDays(-4)) {
                createVoyage(Duration.ofDays(1))
                createVisit(Duration.ofDays(1))
                createVoyage(Duration.ofDays(1))
                createVisit(Duration.ofDays(1)) // <--
                createVoyage(Duration.ofDays(1)) //
                createVisit(Duration.ofDays(1)) // Within this range should be kept.
                createVoyage(Duration.ofDays(1)) //
                createVisit(Duration.ofDays(1)) // <--
                createVoyage(Duration.ofDays(1))
                createVisit(Duration.ofDays(1))
                createVoyage(Duration.ofDays(1))
            },
            expected = build(offset = Duration.ofDays(-1)) {
                createVisit(Duration.ofDays(1))
                createVoyage(Duration.ofDays(1))
                createVisit(Duration.ofDays(1))
                createVoyage(Duration.ofDays(1))
                createVisit(Duration.ofDays(1))
            },
        ),
    )

    private fun build(
        offset: Duration = Duration.ZERO,
        action: Builder.() -> Unit,
    ): List<EntryESoFWrapper<out NewEntry>> {
        val builder = Builder(offset)
        action(builder)
        return builder.entries
    }

    private class Builder(
        private var duration: Duration,
    ) {
        val entries: MutableList<EntryESoFWrapper<out NewEntry>> = mutableListOf()

        private fun time(): Instant = Instant.EPOCH.plus(duration)

        private fun time(duration: Duration): Instant {
            val time = time()
            this.duration += duration
            return time
        }

        fun createVisit(
            duration: Duration,
            finished: Boolean = true,
            areaId: String = "areaId"
        ): EntryESoFWrapper<NewVisit> {
            val start = LocationTime(LOC, time())
            val end = if (finished) LocationTime(LOC, time(duration)) else null
            val areaActivity = AreaActivity("", start, end, areaId)
            val visit = EntryESoFWrapper(
                entry = NewVisit(
                    _id = "",
                    imo = IMO,
                    start = start,
                    end = end,
                    stops = emptyList(),
                    previous = null,
                    next = null,
                    eospAreaActivity = areaActivity,
                    portAreaActivities = listOf(areaActivity),
                    destination = null
                ),
                esof = null
            )
            entries.add(visit)
            return visit
        }

        fun createVoyage(
            duration: Duration,
            finished: Boolean = true
        ): EntryESoFWrapper<NewVoyage> {
            val start = LocationTime(LOC, time())
            val end = if (finished) LocationTime(LOC, time(duration)) else null
            val voyage = EntryESoFWrapper(
                entry = NewVoyage(
                    _id = "",
                    imo = IMO,
                    start = start,
                    end = end,
                    stops = emptyList(),
                    previous = null,
                    next = null,
                    destination = null,
                    destinationPort = null,
                    originPort = null,
                    actualStart = null
                ),
                esof = null
            )
            entries.add(voyage)
            return voyage
        }
    }
}
