mongodb:
  host: localhost
  port: 27017
  authDb: admin
  username:
  password:
  db: reventsengine

auth-credentials-keycloak-s2s:
  domain: keycloakdev.teqplay.nl
  realm: dev
  audience: revents-engine-api

csi:
  url:
  domain:
  realm:
  client-id:
  client-secret:

poma:
  url:
  domain:
  realm:
  client-id:
  client-secret:

vesselvoyage:
  url:
  domain:
  realm:
  client-id:
  client-secret:

bucket:
  archive:
    credentials:
      access-key-id:
      secret-key:
    ship:
      area-index:
        enabled: true
        name: ship-history

scenarios:
  interests:
    distribution:
      max-interests-per-scenario: 4_000
      max-fork-count: 4
  prune:
    cron: '0 0 9 * * *'
    age: P60D
  notification:
    send-http: true

# Prometheus JVM stats exposing
management:
  metrics:
    enable:
      jvm: true
    tags:
      component: revents-engine-api
  endpoints:
    web:
      exposure:
        include:
          - health
          - prometheus
