package nl.teqplay.aisengine.reventsengine.service.scenario.resolver

import nl.teqplay.aisengine.reventsengine.common.model.exception.ScenarioCrashedException
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState
import nl.teqplay.aisengine.reventsengine.common.service.external.CsiService
import nl.teqplay.aisengine.reventsengine.global.model.InterestRelevantResolvedInternal
import nl.teqplay.aisengine.reventsengine.global.model.InterestRelevantShipInternal
import nl.teqplay.aisengine.reventsengine.global.model.InterestShipInternal
import nl.teqplay.aisengine.reventsengine.model.exception.ScenarioCrashReason.SCENARIO_INTERESTS_RESOLVE_INVALID_COMBINATION
import nl.teqplay.aisengine.reventsengine.model.exception.ScenarioCrashReason.SCENARIO_INTERESTS_RESOLVE_NONE_SPECIFIED
import nl.teqplay.aisengine.reventsengine.model.exception.ScenarioCrashReason.SCENARIO_INTERESTS_RESOLVE_SHIP_NOT_SUPPORTED
import nl.teqplay.aisengine.reventsengine.model.interest.scenario.InterestArea
import nl.teqplay.aisengine.reventsengine.model.interest.scenario.InterestPolygon
import nl.teqplay.aisengine.reventsengine.model.interest.scenario.InterestShip
import nl.teqplay.aisengine.reventsengine.model.scenario.Scenario
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioEvent
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioPostProcessing
import nl.teqplay.aisengine.util.withMargin
import nl.teqplay.skeleton.model.TimeWindow
import org.springframework.stereotype.Component

@Component
class ScenariosResolverService(
    private val scenariosAreaResolverService: ScenariosAreaResolverService,
    private val csiService: CsiService,
) {

    /**
     * Resolve the [events] of a scenario.
     *
     * Returning all events if it's empty, otherwise resolving the specified events.
     * Also including which events are required based on the [postProcessing].
     */
    fun resolveEvents(
        events: Set<ScenarioEvent>,
        postProcessing: Set<ScenarioPostProcessing>,
    ): Set<ScenarioEvent> {
        if (events.isEmpty() && postProcessing.isEmpty()) {
            return ScenarioEvent.values().toSet()
        }
        // resolve both the provided events as the ones required for post-processing
        val inputEvents = events +
            postProcessing.flatMap { resolveEvents(it.events, emptySet()) }
        return inputEvents.flatMap { it.resolveDependencies() + it }.toSet()
    }

    /**
     * Resolve the interests of the [scenario] into concrete
     * [InterestRelevantResolvedInternal.ships] and [InterestRelevantResolvedInternal.areas].
     */
    fun resolveInterests(scenario: ScenarioState): InterestRelevantResolvedInternal {
        if (scenario.interests.isEmpty()) {
            throw ScenarioCrashedException(SCENARIO_INTERESTS_RESOLVE_NONE_SPECIFIED)
        }

        val polygonInterestsRaw = scenario.interests.filterIsInstance<InterestPolygon>()
        val areaInterestsRaw = scenario.interests.filterIsInstance<InterestArea>()
        val shipInterestsRaw = scenario.interests.filterIsInstance<InterestShip>()

        if (shipInterestsRaw.isNotEmpty() && (areaInterestsRaw.isNotEmpty() || polygonInterestsRaw.isNotEmpty())) {
            throw ScenarioCrashedException(SCENARIO_INTERESTS_RESOLVE_INVALID_COMBINATION)
        }

        val settings = scenario.settings ?: Scenario.Settings(scenario)
        val areaInterests = scenariosAreaResolverService.resolveInterests(
            window = scenario.window,
            windowMargin = scenario.windowMargin,
            guarantees = scenario.guarantees,
            settings = settings,
            areaInterests = areaInterestsRaw,
            polygonInterests = polygonInterestsRaw
        )
        val shipInterests = shipInterestsRaw.resolve(scenario.window, scenario.windowMargin)

        val areas = areaInterests.polygons
        val (shipsByImo, shipsByMmsi) = (areaInterests.ships + shipInterests).gatherShipInterests()
        val ships = (shipsByImo + shipsByMmsi.optionallyResolveForImo(settings))
        return InterestRelevantResolvedInternal(
            ships = ships,
            unlocodes = areaInterests.unlocodes.toSet(),
            areas = areas
        )
    }

    private fun List<InterestShipInternal>.gatherShipInterests(): Pair<MutableList<InterestRelevantShipInternal>, MutableList<InterestRelevantShipInternal>> {
        val byImo = mutableListOf<InterestRelevantShipInternal>()
        val byMmsi = mutableListOf<InterestRelevantShipInternal>()
        forEach {
            val mmsi = it.ship.mmsi
            val imo = it.ship.imo
            val window = it.window
            val globalWindowNoMargin = it.windowNoMargin
            when {
                // only one of the two must be set
                mmsi != null && imo != null ->
                    throw ScenarioCrashedException(SCENARIO_INTERESTS_RESOLVE_SHIP_NOT_SUPPORTED)

                mmsi != null -> byMmsi.add(
                    InterestRelevantShipInternal(
                        mmsi = mmsi,
                        // we could look up the IMO here, but the user indicated they want the MMSI specifically
                        // if they'd like the IMO/MMSI mapping to be used, they should set the IMO, or
                        // enable resolving IMOs based on MMSIs in the settings
                        imo = null,
                        window = window,
                        windowNoMargin = globalWindowNoMargin,
                        partial = it.partial
                    )
                )

                imo != null -> byImo.addAll(
                    csiService
                        .getInterestsByImo(imo, window, globalWindowNoMargin)
                        .map { interest -> interest.copy(partial = it.partial) }
                )

                // at least one must be set
                else -> throw ScenarioCrashedException(SCENARIO_INTERESTS_RESOLVE_SHIP_NOT_SUPPORTED)
            }
        }

        return byImo to byMmsi
    }

    private fun List<InterestRelevantShipInternal>.optionallyResolveForImo(
        settings: Scenario.Settings,
    ): List<InterestRelevantShipInternal> {
        if (!settings.resolveShipInterestsForImo) {
            return this
        }
        return csiService.resolveInterestsByMmsi(this)
    }

    private fun List<InterestShip>.resolve(
        window: TimeWindow,
        windowMargin: Scenario.WindowMargin,
    ): List<InterestShipInternal> = map { interest ->
        InterestShipInternal(
            ship = interest.ship,
            window = window.withMargin(windowMargin),
            windowNoMargin = window,
        )
    }
}
