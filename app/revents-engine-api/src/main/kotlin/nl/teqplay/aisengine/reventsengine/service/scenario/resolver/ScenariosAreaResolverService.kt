package nl.teqplay.aisengine.reventsengine.service.scenario.resolver

import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier.AreaType.PORT
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier.AreaType.TERMINAL
import nl.teqplay.aisengine.reventsengine.common.model.exception.ScenarioCrashedException
import nl.teqplay.aisengine.reventsengine.common.service.external.CsiService
import nl.teqplay.aisengine.reventsengine.global.model.InterestResolvedInternal
import nl.teqplay.aisengine.reventsengine.global.model.InterestShipInternal
import nl.teqplay.aisengine.reventsengine.model.exception.ScenarioCrashReason
import nl.teqplay.aisengine.reventsengine.model.interest.scenario.InterestArea
import nl.teqplay.aisengine.reventsengine.model.interest.scenario.InterestPolygon
import nl.teqplay.aisengine.reventsengine.model.interest.scenario.InterestShip
import nl.teqplay.aisengine.reventsengine.model.scenario.Scenario
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioGuarantee
import nl.teqplay.aisengine.reventsengine.service.external.PomaService
import nl.teqplay.aisengine.util.ceilToLocalDate
import nl.teqplay.aisengine.util.coerceTimeWindowWithinBoundary
import nl.teqplay.aisengine.util.floorToLocalDate
import nl.teqplay.aisengine.util.isOverlapping
import nl.teqplay.aisengine.util.withMargin
import nl.teqplay.poma.api.v1.Anchorage
import nl.teqplay.poma.api.v1.PilotBoardingPlace
import nl.teqplay.poma.api.v1.Port
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.Polygon
import nl.teqplay.skeleton.model.TimeWindow
import org.springframework.stereotype.Component
import java.time.Duration
import java.time.ZoneOffset
import nl.teqplay.poma.api.v1.Location as PomaLocation

@Component
class ScenariosAreaResolverService(
    private val csiService: CsiService,
    private val pomaService: PomaService,
    private val scenariosPolygonResolverService: ScenariosPolygonResolverService,
) {

    /**
     * Resolves [areaInterests] and [polygonInterests] by:
     * - requesting terminals and ports
     * - fetching ships within the polygons (either from VesselVoyage or from the area buckets)
     * - returning all relevant areas as polygons
     */
    fun resolveInterests(
        window: TimeWindow,
        windowMargin: Scenario.WindowMargin,
        guarantees: Set<ScenarioGuarantee>,
        settings: Scenario.Settings,
        areaInterests: List<InterestArea>,
        polygonInterests: List<InterestPolygon>,
    ): InterestResolvedInternal {
        if (areaInterests.isEmpty() && polygonInterests.isEmpty()) {
            return InterestResolvedInternal.empty()
        }

        val resolvedAreaInterests = resolveAreaInterests(guarantees, areaInterests)
        val polygons = resolvedAreaInterests.polygons + polygonInterests.map { it.polygon }
        val ships = resolvePolygonInterests(window, windowMargin, settings, polygons)
        return InterestResolvedInternal(
            ships = ships,
            polygons = polygons,
            unlocodes = resolvedAreaInterests.unlocodes
        )
    }

    private fun resolveAreaInterests(
        guarantees: Set<ScenarioGuarantee>,
        areaInterests: List<InterestArea>,
    ): InterestResolvedInternal {
        if (areaInterests.isEmpty()) {
            return InterestResolvedInternal.empty()
        }

        if (areaInterests.any { it.area.type != TERMINAL && it.area.type != PORT }) {
            throw ScenarioCrashedException(ScenarioCrashReason.SCENARIO_INTERESTS_RESOLVE_AREA_NOT_SUPPORTED)
        }

        val terminalUnlocodes = pomaService.requestUnlocodesForTerminals(areaInterests)
        val terminalPorts = pomaService.requestPortsByUnlocodes(terminalUnlocodes)

        val interestsPorts = pomaService.requestPortsByInterests(areaInterests)
        val unlocodes = (terminalPorts + interestsPorts).mapNotNull { it.unlocode }

        val ports = terminalPorts + interestsPorts
        val anchorages = pomaService.requestAnchorages(unlocodes)
        val pilotBoardingPlaces = pomaService.requestPilotBoardingPlaces(unlocodes)

        // VesselVoyage merging only allows main ports, otherwise VesselVoyage will get an incomplete picture.
        if (guarantees.any { it.requireOnlyMainPortInterests } && ports.any { it.mainPort != null }) {
            throw ScenarioCrashedException(ScenarioCrashReason.SCENARIO_INTERESTS_RESOLVE_AREA_REQUIRES_ONLY_MAIN_PORTS)
        }

        val pomaAreas = resolvePomaAreasForEntities(ports, anchorages, pilotBoardingPlaces)
        val polygons = pomaAreas.map { areas ->
            val convertedLocationsList = areas.map { location -> location.toAisEngineLocation() }
            Polygon(convertedLocationsList)
        }.toSet()

        return InterestResolvedInternal(
            ships = emptyList(),
            polygons = polygons.toList(),
            unlocodes = unlocodes
        )
    }

    private fun PomaLocation.toAisEngineLocation(): Location {
        return Location(lat = this.latitude, lon = this.longitude)
    }

    private fun resolvePomaAreasForEntities(ports: List<Port>, anchorages: List<Anchorage>, pilotBoardingPlaces: List<PilotBoardingPlace>): List<List<PomaLocation>> {
        return ports.flatMap { port -> resolveAreasOfPort(port) } +
            anchorages.mapNotNull { it.area.takeAreaIfNotEmpty() } +
            pilotBoardingPlaces.mapNotNull { it.area.takeAreaIfNotEmpty() }
    }

    private fun resolveAreasOfPort(port: Port): List<List<PomaLocation>> {
        return listOfNotNull(
            port.eosArea.takeAreaIfNotEmpty(),
            port.outerArea.takeAreaIfNotEmpty(),
            port.area
        )
    }

    private fun List<PomaLocation>?.takeAreaIfNotEmpty(): List<PomaLocation>? {
        return this?.takeIf { it.isNotEmpty() }
    }

    /**
     * Resolves the [polygons] by requesting the nearby ships. Afterward applying the [windowMargin].
     */
    private fun resolvePolygonInterests(
        window: TimeWindow,
        windowMargin: Scenario.WindowMargin,
        settings: Scenario.Settings,
        polygons: List<Polygon>,
    ): List<InterestShipInternal> {
        if (polygons.isEmpty()) {
            return emptyList()
        }

        // Add padding to the window, to check if the ship was not already in the polygons at the start or end.
        val paddedWindow = window.withMargin(Duration.ofDays(1))
        val paddedStart = paddedWindow.from.floorToLocalDate()
        val paddedEnd = paddedWindow.to.ceilToLocalDate()

        val serviceVesselMmsis = csiService.getServiceVessels()
        val mmsisPerDate = scenariosPolygonResolverService.findShipsNearPolygons(paddedWindow, polygons)

        val groupedInterests = mutableMapOf<Int, MutableList<InterestShipInternal>>()
        mmsisPerDate.forEach { (date, nearVessels) ->
            nearVessels.forEach inner@{ mmsi ->
                // we want to ignore service vessels, they'll be automatically included upon area lookups for encounters
                if (mmsi in serviceVesselMmsis) {
                    return@inner
                }

                val initialWindow = TimeWindow(
                    from = date.atStartOfDay().toInstant(ZoneOffset.UTC),
                    to = date.atStartOfDay().plusDays(1).toInstant(ZoneOffset.UTC)
                )

                val interests = groupedInterests[mmsi] ?: mutableListOf()
                val (enlargedWindow, _) = enlargeTimeWindowIfOverlapping(interests, initialWindow)
                interests.add(
                    InterestShipInternal(
                        ship = InterestShip.Identifier(mmsi),
                        window = enlargedWindow,
                        windowNoMargin = enlargedWindow
                    )
                )
                groupedInterests[mmsi] = interests
            }
        }

        return groupedInterests.values.flatMap { interestsByMmsi ->
            interestsByMmsi.mapNotNull { interest ->
                // Use the window without margin and then remove the interest if it was not exclusively within the original window.
                val from = interest.windowNoMargin.from.floorToLocalDate()
                val to = interest.windowNoMargin.to.ceilToLocalDate()
                val partial = from == paddedStart || to == paddedEnd
                if (!settings.allowPartialInterests && partial) {
                    return@mapNotNull null
                }

                // Remove if no time is spent.
                val boundedWindow = coerceTimeWindowWithinBoundary(window = interest.windowNoMargin, boundary = window)
                if (boundedWindow.from >= boundedWindow.to) {
                    return@mapNotNull null
                }
                interest.copy(
                    window = boundedWindow.withMargin(windowMargin),
                    windowNoMargin = boundedWindow,
                    partial = partial
                )
            }
        }
    }

    /**
     * Enlarges the [initialWindow] depending on if there are overlapping [interests].
     *
     * @return the enlarged [TimeWindow]
     */
    private fun enlargeTimeWindowIfOverlapping(
        interests: MutableList<InterestShipInternal>,
        initialWindow: TimeWindow
    ): Pair<TimeWindow, Boolean> {
        var currentWindow = initialWindow
        val overlappingInterests = interests.filter { currentWindow.isOverlapping(it.window) }
        if (overlappingInterests.isNotEmpty()) {
            interests.removeAll(
                overlappingInterests.onEach {
                    currentWindow = TimeWindow(
                        from = minOf(currentWindow.from, it.window.from),
                        to = maxOf(currentWindow.to, it.window.to),
                    )
                }
            )
        }
        return currentWindow to overlappingInterests.any { it.partial }
    }
}
