package nl.teqplay.aisengine.reventsengine.controller

import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioMetadata
import nl.teqplay.aisengine.reventsengine.model.interest.InterestRelevantShip
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioCreateRequest
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioEvent
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioPostProcessing
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioQueryResponse
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioResponse
import nl.teqplay.aisengine.reventsengine.service.scenario.ScenariosService
import nl.teqplay.skeleton.common.exception.NotFoundException
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/scenario")
class ScenariosController(
    private val scenariosService: ScenariosService
) {

    // TODO: can be removed once PTO supports the STOP value
    private fun ScenarioResponse.applyBackwardCompatibility(): ScenarioResponse = this.copy(
        events = this.events.filterNot { it == ScenarioEvent.STOP }.toSet()
    )

    private fun ScenarioQueryResponse.applyBackwardCompatibility(): ScenarioQueryResponse = this.copy(
        matches = this.matches.map { it.applyBackwardCompatibility() },
        overlaps = this.overlaps.map { it.applyBackwardCompatibility() }
    )

    @PostMapping
    fun create(
        @RequestBody scenario: ScenarioCreateRequest
    ): ScenarioResponse = scenariosService.create(scenario).applyBackwardCompatibility()

    @PostMapping("/{id}/post-processing")
    fun createForAfterwardsPostProcessing(
        @PathVariable id: String,
        @RequestBody postProcessing: Set<ScenarioPostProcessing>
    ): ScenarioResponse = scenariosService.createForAfterwardsPostProcessing(id, postProcessing)
        .applyBackwardCompatibility()

    @GetMapping("/{id}")
    fun get(
        @PathVariable id: String
    ): ScenarioResponse = scenariosService.get(id)
        ?.applyBackwardCompatibility()
        ?: throw NotFoundException("Scenario not found")

    @DeleteMapping("/{id}/prune")
    fun prune(
        @PathVariable id: String
    ): ScenarioResponse = scenariosService.prune(id)
        ?.applyBackwardCompatibility()
        ?: throw NotFoundException("Scenario not found")

    @PutMapping("/{id}/cancel")
    fun cancel(
        @PathVariable id: String
    ): ScenarioResponse = scenariosService.cancel(id)
        ?.applyBackwardCompatibility()
        ?: throw NotFoundException("Scenario not found")

    @PostMapping("/query")
    fun query(
        @RequestBody scenario: ScenarioCreateRequest
    ): ScenarioQueryResponse = scenariosService.query(scenario).applyBackwardCompatibility()

    @GetMapping("/{id}/interests")
    fun getInterests(
        @PathVariable id: String
    ): List<InterestRelevantShip> = scenariosService.getInterests(id)
        ?: throw NotFoundException("Scenario interests not found")

    @GetMapping("/{id}/metadata")
    fun getMetadata(
        @PathVariable id: String
    ): ScenarioMetadata = scenariosService.getMetadata(id)
        ?: throw NotFoundException("Scenario metadata not found")
}
