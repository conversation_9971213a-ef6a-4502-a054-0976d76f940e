package nl.teqplay.aisengine.reventsengine.service.external

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.reventsengine.common.model.exception.ScenarioCrashedException
import nl.teqplay.aisengine.reventsengine.common.util.retryHttp
import nl.teqplay.aisengine.reventsengine.model.exception.ScenarioCrashReason.SCENARIO_INTERESTS_RESOLVE_AREA_REQUEST_FAILED
import nl.teqplay.aisengine.reventsengine.model.exception.ScenarioCrashReason.SCENARIO_INTERESTS_RESOLVE_AREA_RESPONSE_INCOMPLETE
import nl.teqplay.aisengine.reventsengine.model.interest.scenario.InterestArea
import nl.teqplay.poma.api.v1.Anchorage
import nl.teqplay.poma.api.v1.PilotBoardingPlace
import nl.teqplay.poma.api.v1.Port
import nl.teqplay.poma.api.v1.Terminal
import nl.teqplay.skeleton.poma.client.PomaInfrastructureClient
import nl.teqplay.skeleton.poma.client.PomaRestTemplate
import org.springframework.stereotype.Component
import org.springframework.web.client.RestTemplate
import org.springframework.web.client.postForObject

private val LOG = KotlinLogging.logger { }

@Component
class PomaService(
    @PomaRestTemplate private val restTemplate: RestTemplate,
    private val pomaInfrastructureClient: PomaInfrastructureClient
) {

    fun requestUnlocodesForTerminals(
        interests: List<InterestArea>
    ): List<String> {
        val terminalIds = interests
            .filter { it.area.type == AreaIdentifier.AreaType.TERMINAL }
            .map { it.area.id }
            .toSet()

        if (terminalIds.isEmpty()) {
            return emptyList()
        }

        val terminals = runCatchingHttp { restTemplate.postForObject<Array<Terminal>>("/v1/terminal/bulk", terminalIds) }
            .onFailure { LOG.error(it) { "Something went wrong while requesting terminals in bulk" } }
            .getOrNull()
            ?: throw ScenarioCrashedException(SCENARIO_INTERESTS_RESOLVE_AREA_REQUEST_FAILED)

        if (terminals.size != terminalIds.size) {
            throw ScenarioCrashedException(SCENARIO_INTERESTS_RESOLVE_AREA_RESPONSE_INCOMPLETE)
        }

        return terminals.flatMap { it.ports }
    }

    fun requestPortsByInterests(
        interests: List<InterestArea>
    ): List<Port> {
        val portIds = interests
            .filter { it.area.type == AreaIdentifier.AreaType.PORT }
            .map { it.area.id }
            .toSet()

        if (portIds.isEmpty()) {
            return emptyList()
        }

        val ports = runCatchingHttp { restTemplate.postForObject<Array<Port>>("/v1/port/bulk", portIds) }
            .onFailure { LOG.error(it) { "Something went wrong while requesting ports in bulk" } }
            .getOrNull()
            ?: throw ScenarioCrashedException(SCENARIO_INTERESTS_RESOLVE_AREA_REQUEST_FAILED)

        if (ports.size != portIds.size) {
            throw ScenarioCrashedException(SCENARIO_INTERESTS_RESOLVE_AREA_RESPONSE_INCOMPLETE)
        }

        return ports.toList()
    }

    fun requestPortsByUnlocodes(
        unlocodes: List<String>
    ): List<Port> {
        if (unlocodes.isEmpty()) {
            return emptyList()
        }

        val ports = runCatchingHttp { pomaInfrastructureClient.getPortsByUnlocode(unlocodes.toList()) }
            .onFailure { LOG.error(it) { "Something went wrong while requesting ports by unlocode" } }
            .getOrNull()
            ?: throw ScenarioCrashedException(SCENARIO_INTERESTS_RESOLVE_AREA_REQUEST_FAILED)

        if (ports.size != unlocodes.size) {
            throw ScenarioCrashedException(SCENARIO_INTERESTS_RESOLVE_AREA_RESPONSE_INCOMPLETE)
        }

        return ports.toList()
    }

    fun requestAnchorages(
        unlocodes: List<String>
    ): List<Anchorage> {
        if (unlocodes.isEmpty()) {
            return emptyList()
        }

        return unlocodes.flatMap { unlocode ->
            runCatchingHttp { pomaInfrastructureClient.getAnchorages(port = unlocode) }
                .onFailure { LOG.error(it) { "Something went wrong while requesting anchorages" } }
                .getOrNull()
                ?.toList()
                ?: throw ScenarioCrashedException(SCENARIO_INTERESTS_RESOLVE_AREA_REQUEST_FAILED)
        }.distinct()
    }

    fun requestPilotBoardingPlaces(
        unlocodes: List<String>
    ): List<PilotBoardingPlace> {
        if (unlocodes.isEmpty()) {
            return emptyList()
        }

        return unlocodes.flatMap { unlocode ->
            runCatchingHttp { pomaInfrastructureClient.getPilotBoardingPlaces(port = unlocode) }
                .onFailure { LOG.error(it) { "Something went wrong while requesting pilot boarding places" } }
                .getOrNull()
                ?.toList()
                ?: throw ScenarioCrashedException(SCENARIO_INTERESTS_RESOLVE_AREA_REQUEST_FAILED)
        }.distinct()
    }

    private inline fun <reified R> runCatchingHttp(block: () -> R): Result<R> {
        return runCatching { retryHttp { block() } }
    }
}
