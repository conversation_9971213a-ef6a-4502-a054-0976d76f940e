package nl.teqplay.aisengine.reventsengine.controller

import nl.teqplay.aisengine.event.interfaces.ActualEvent
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioEventRequests
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioRequestEvent
import nl.teqplay.aisengine.reventsengine.service.event.ActualEventsService
import nl.teqplay.aisengine.reventsengine.service.scenario.ScenariosService
import nl.teqplay.skeleton.common.exception.BadRequestException
import nl.teqplay.skeleton.model.TimeWindow
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/events")
class ActualEventsController(
    private val actualEventsService: ActualEventsService,
    private val scenariosService: ScenariosService
) {

    private val errorMessageOnlyMmsiOrImo = "Either 'mmsi' or 'imo' needs to be provided."

    @PostMapping
    fun fetchEvents(
        @RequestParam scenario: String,
        @RequestParam(required = false) mmsi: Int?,
        @RequestParam(required = false) imo: Int?,
        @RequestParam types: Set<String>,
        @RequestBody window: TimeWindow
    ): List<ActualEvent> = when {
        mmsi != null && imo == null -> actualEventsService.fetchEventsByMmsi(scenario, mmsi, window, types)
        imo != null && mmsi == null -> actualEventsService.fetchEventsByImo(scenario, imo, window, types)
        else -> throw BadRequestException(errorMessageOnlyMmsiOrImo)
    }

    @PostMapping("/scenario/requests")
    fun fetchScenarioRequests(
        @RequestParam scenario: String,
        @RequestParam(required = false) mmsi: Int?,
        @RequestParam(required = false) imo: Int?,
        @RequestBody window: TimeWindow
    ): ScenarioEventRequests {
        val requests = when {
            mmsi != null && imo == null -> actualEventsService.fetchScenarioRequestsByMmsi(scenario, mmsi, window)
            imo != null && mmsi == null -> actualEventsService.fetchScenarioRequestsByImo(scenario, imo, window)
            else -> throw BadRequestException(errorMessageOnlyMmsiOrImo)
        }
        return resolveScenarioRequests(requests)
    }

    private fun resolveScenarioRequests(
        requests: List<ScenarioRequestEvent>
    ): ScenarioEventRequests {
        val scenarioIds = requests.map { it.scenario }.toSet()
        val scenarios = scenariosService.getByIds(scenarioIds)
        return ScenarioEventRequests(requests, scenarios)
    }
}
