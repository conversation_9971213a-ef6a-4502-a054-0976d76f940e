package nl.teqplay.aisengine.reventsengine

import nl.teqplay.skeleton.common.logging.EnableIncomingRequestLogging
import nl.teqplay.skeleton.common.logging.EnableOutgoingRequestLogging
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer
import org.springframework.scheduling.annotation.EnableAsync
import org.springframework.scheduling.annotation.EnableScheduling

@SpringBootApplication(exclude = [MongoAutoConfiguration::class])
@EnableIncomingRequestLogging
@EnableOutgoingRequestLogging
@EnableAsync
@EnableScheduling
@ConfigurationPropertiesScan
class ReventsEngineApi : SpringBootServletInitializer()

fun main(args: Array<String>) {
    runApplication<ReventsEngineApi>(*args)
}
