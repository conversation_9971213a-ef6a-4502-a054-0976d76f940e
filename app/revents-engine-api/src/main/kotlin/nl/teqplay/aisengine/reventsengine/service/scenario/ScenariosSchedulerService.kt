package nl.teqplay.aisengine.reventsengine.service.scenario

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState
import nl.teqplay.aisengine.reventsengine.common.service.cleanup.ScenariosCleanupTaskService
import nl.teqplay.aisengine.reventsengine.common.service.scenario.ScenariosBaseSchedulerService
import nl.teqplay.aisengine.reventsengine.common.service.scenario.ScenariosBaseSchedulerService.SchedulerState.CONTINUE
import nl.teqplay.aisengine.reventsengine.common.service.scenario.ScenariosBaseSchedulerService.SchedulerState.CRASHED
import nl.teqplay.aisengine.reventsengine.common.service.scenario.ScenariosBaseSchedulerService.SchedulerState.FINISHED
import nl.teqplay.aisengine.reventsengine.common.service.scenario.ScenariosBaseSchedulerService.SchedulerState.RETRY
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean

private val LOG = KotlinLogging.logger { }

@Component
class ScenariosSchedulerService(
    private val scenariosQueueService: ScenariosQueueService,
    private val scenariosCleanupTaskService: ScenariosCleanupTaskService,
    private val scenariosBaseSchedulerService: ScenariosBaseSchedulerService,
) {

    private val running = AtomicBoolean()

    /**
     * Loop over scenarios asynchronously. Optionally starting off with an [initialScenario].
     */
    @Async
    fun loopOverScenarios(initialScenario: ScenarioState? = null) {
        if (!running.compareAndSet(false, true)) {
            LOG.warn { "A scenario is already running, skipping duplicate call" }
            return
        }

        var initial = initialScenario
        do {
            val scenario = initial ?: scenariosQueueService.getCurrentScenario() ?: break
            initial = null

            LOG.info { "[${scenario.id}] Starting scenario" }
            loopOverScenario(scenario)
            LOG.info { "[${scenario.id}] Ended scenario" }
        } while (true)

        running.set(false)
    }

    /**
     * Loop over the phases of a specific [scenario], and handles teardown if the [scenario] crashes.
     */
    private fun loopOverScenario(scenario: ScenarioState, previousCrashed: Boolean = false) {
        var s = scenario
        var crashed = false
        while (true) {
            val (newScenario, state) = scenariosBaseSchedulerService.runPhase(s)
            s = newScenario

            when (state) {
                // continue immediately
                CONTINUE -> {}

                // retry after a delay
                RETRY -> {
                    TimeUnit.SECONDS.sleep(15)
                }

                // immediately stop processing
                FINISHED -> break

                CRASHED -> {
                    crashed = true
                    break
                }
            }
        }

        // if crashed, perform a teardown for this scenario before exiting this method
        // as well as ensure we don't infinitely recurse if the teardown task crashes as well
        if (crashed && !previousCrashed) {
            val teardownScenario = scenariosCleanupTaskService.createAndSaveTeardownScenario(s)
            loopOverScenario(teardownScenario, previousCrashed = true)
        }
    }
}
