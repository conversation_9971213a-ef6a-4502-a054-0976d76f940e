package nl.teqplay.aisengine.reventsengine.controller

import nl.teqplay.aisengine.reventsengine.common.service.vesselvoyage.VesselVoyageStorageV2Service
import nl.teqplay.aisengine.reventsengine.model.interest.InterestVesselVoyage
import nl.teqplay.aisengine.reventsengine.model.vesselvoyage.MergeEntryV2
import nl.teqplay.aisengine.reventsengine.model.vesselvoyage.ReventsVesselVoyageVisitV2Query
import nl.teqplay.aisengine.reventsengine.service.vesselvoyage.VesselVoyageMergingV2Service
import nl.teqplay.skeleton.common.exception.NotFoundException
import nl.teqplay.skeleton.model.TimeWindow
import nl.teqplay.vesselvoyage.model.v2.EntryESoFWrapper
import nl.teqplay.vesselvoyage.model.v2.NewEntry
import nl.teqplay.vesselvoyage.model.v2.NewVisit
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/vesselvoyage/v2")
class VesselVoyageV2Controller(
    private val vesselVoyageStorageV2Service: VesselVoyageStorageV2Service,
    private val vesselVoyageMergingV2Service: VesselVoyageMergingV2Service,
) {

    @GetMapping("/{id}/interests")
    fun getInterests(
        @PathVariable id: String,
    ): List<InterestVesselVoyage> = vesselVoyageStorageV2Service.getInterestsByScenario(id)
        .map { InterestVesselVoyage(it.imo, it.window, it.windowNoMargin, it.partial) }

    @PostMapping("/{id}/entries/imo/{imo}")
    fun fetchEntries(
        @PathVariable id: String,
        @PathVariable imo: Int,
        @RequestBody window: TimeWindow,
    ): List<EntryESoFWrapper<out NewEntry>> = vesselVoyageStorageV2Service.fetchEntries(id, imo, window)

    @GetMapping("/{id}/entries/{entryId}")
    fun getEntryById(
        @PathVariable id: String,
        @PathVariable entryId: String,
    ): EntryESoFWrapper<out NewEntry> = vesselVoyageStorageV2Service.getEntryById(id, entryId)
        ?: throw NotFoundException("Entry not found")

    @PostMapping("/{id}/entries")
    fun getEntriesById(
        @PathVariable id: String,
        @RequestBody entryIds: List<String>,
    ): List<EntryESoFWrapper<out NewEntry>> = vesselVoyageStorageV2Service.getEntriesById(id, entryIds)

    @PostMapping("/{id}/visits/query")
    fun queryVisits(
        @PathVariable id: String,
        @RequestBody query: ReventsVesselVoyageVisitV2Query,
    ): List<EntryESoFWrapper<NewVisit>> = vesselVoyageStorageV2Service.queryVisits(id, query)

    @GetMapping("/{id}/merging/interests")
    fun getInterestsForMerging(
        @PathVariable id: String,
    ): List<Int> = vesselVoyageStorageV2Service.getInterestsForMergingByScenario(id)

    @GetMapping("/{id}/merging/entries/imo/{imo}")
    fun fetchEntriesForMerging(
        @PathVariable id: String,
        @PathVariable imo: Int,
    ): List<MergeEntryV2> = vesselVoyageMergingV2Service.fetchEntriesForMerging(id, imo)
}
