package nl.teqplay.aisengine.reventsengine.controller

import nl.teqplay.aisengine.reventsengine.common.service.vesselvoyage.VesselVoyageStorageService
import nl.teqplay.aisengine.reventsengine.model.interest.InterestVesselVoyage
import nl.teqplay.aisengine.reventsengine.model.vesselvoyage.MergeEntry
import nl.teqplay.aisengine.reventsengine.model.vesselvoyage.ReventsVesselVoyageVisitQuery
import nl.teqplay.aisengine.reventsengine.service.vesselvoyage.VesselVoyageMergingService
import nl.teqplay.skeleton.common.exception.NotFoundException
import nl.teqplay.skeleton.model.TimeWindow
import nl.teqplay.vesselvoyage.model.Entry
import nl.teqplay.vesselvoyage.model.Visit
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/vesselvoyage")
class VesselVoyageController(
    private val vesselVoyageStorageService: VesselVoyageStorageService,
    private val vesselVoyageMergingService: VesselVoyageMergingService,
) {

    @GetMapping("/{id}/interests")
    fun getInterests(
        @PathVariable id: String,
    ): List<InterestVesselVoyage> = vesselVoyageStorageService.getInterestsByScenario(id)
        .map { InterestVesselVoyage(it.imo, it.window, it.windowNoMargin, it.partial) }

    @PostMapping("/{id}/entries/imo/{imo}")
    fun fetchEntries(
        @PathVariable id: String,
        @PathVariable imo: String,
        @RequestBody window: TimeWindow,
    ): List<Entry> = vesselVoyageStorageService.fetchEntries(id, imo, window)

    @GetMapping("/{id}/entries/{entryId}")
    fun getEntryById(
        @PathVariable id: String,
        @PathVariable entryId: String,
    ): Entry = vesselVoyageStorageService.getEntryById(id, entryId)
        ?: throw NotFoundException("Entry not found")

    @PostMapping("/{id}/entries")
    fun getEntriesById(
        @PathVariable id: String,
        @RequestBody entryIds: List<String>,
    ): List<Entry> = vesselVoyageStorageService.getEntriesById(id, entryIds)

    @PostMapping("/{id}/visits/query")
    fun queryVisits(
        @PathVariable id: String,
        @RequestBody query: ReventsVesselVoyageVisitQuery,
    ): List<Visit> = vesselVoyageStorageService.queryVisits(id, query)

    @GetMapping("/{id}/merging/interests")
    fun getInterestsForMerging(
        @PathVariable id: String,
    ): List<String> = vesselVoyageStorageService.getInterestsForMergingByScenario(id)

    @GetMapping("/{id}/merging/entries/imo/{imo}")
    fun fetchEntriesForMerging(
        @PathVariable id: String,
        @PathVariable imo: String,
    ): List<MergeEntry> = vesselVoyageMergingService.fetchEntriesForMerging(id, imo)
}
