package nl.teqplay.aisengine.reventsengine.service.vesselvoyage

import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioMetadata
import nl.teqplay.aisengine.reventsengine.common.model.ship.ScenarioInterestVesselVoyage
import nl.teqplay.aisengine.reventsengine.common.service.scenario.ScenariosMetadataService
import nl.teqplay.aisengine.reventsengine.common.service.vesselvoyage.VesselVoyageStorageService
import nl.teqplay.aisengine.reventsengine.model.vesselvoyage.MergeEntry
import nl.teqplay.skeleton.common.exception.BadRequestException
import nl.teqplay.vesselvoyage.model.Entry
import nl.teqplay.vesselvoyage.model.Visit
import nl.teqplay.vesselvoyage.model.Voyage
import org.springframework.stereotype.Service
import java.time.ZoneOffset

@Service
class VesselVoyageMergingService(
    private val vesselVoyageStorageService: VesselVoyageStorageService,
    private val scenariosMetadataService: ScenariosMetadataService
) {

    /**
     * Fetches the entries for merging back into VesselVoyage, specific to the scenario [id] and [imoString].
     */
    fun fetchEntriesForMerging(
        id: String,
        imoString: String,
    ): List<MergeEntry> {
        val imo = imoString.toIntOrNull() ?: throw BadRequestException("IMO must be of type Int.")
        val interests = vesselVoyageStorageService.getInterestsByScenarioAndImo(id, imo).filter { !it.partial }
        val metadata = scenariosMetadataService.getMetadataByScenario(id)
        return interests.map { interest ->
            val entries = vesselVoyageStorageService
                .fetchEntries(id, imoString, interest.window)
                .toMutableList()
            pruneEntries(interest, entries, metadata)
            MergeEntry(
                window = interest.window,
                entries = entries
            )
        }
    }

    /**
     * Prunes [entries] by removing any first/last visits outside the [ScenarioInterestVesselVoyage.windowNoMargin].
     * These first/last visits can't be trusted, only the visits after are trustable.
     * Any visits within the window are always to be trusted.
     */
    private fun pruneEntries(
        interest: ScenarioInterestVesselVoyage,
        entries: MutableList<Entry>,
        metadata: ScenarioMetadata?,
    ) {
        val windowStart = interest.windowNoMargin.from.atZone(ZoneOffset.UTC)
        val windowEnd = interest.windowNoMargin.to.atZone(ZoneOffset.UTC)

        // If unlocodes are specified we've resolved based on area interests,
        // we can only trust visits about the mentioned ports.
        val filterUnlocodes = metadata?.unlocodes
        if (!filterUnlocodes.isNullOrEmpty()) {
            entries.removeIf { entry ->
                when (entry) {
                    is Visit -> entry.portAreas.none { it.portId in filterUnlocodes }
                    // Always remove voyages if we're filtering on unlocodes, only visits can be used.
                    is Voyage -> true
                }
            }
        }

        // Remove entries that aren't finished.
        while (entries.isNotEmpty() && !entries.last().finished) entries.removeLast()

        var removedFirstVisit = false
        while (entries.isNotEmpty() && !removedFirstVisit) {
            val first = entries.first()
            val endTime = first.endTime
            if (endTime != null && endTime >= windowStart) break
            if (first is Visit) removedFirstVisit = true
            entries.removeFirst()
        }

        var removedLastVisit = false
        while (entries.isNotEmpty() && !removedLastVisit) {
            val last = entries.last()
            if (last.startTime < windowEnd) break
            if (last is Visit) removedLastVisit = true
            entries.removeLast()
        }

        // Trim voyages at both ends.
        while (entries.isNotEmpty() && entries.first() is Voyage) entries.removeFirst()
        while (entries.isNotEmpty() && entries.last() is Voyage) entries.removeLast()
    }
}
