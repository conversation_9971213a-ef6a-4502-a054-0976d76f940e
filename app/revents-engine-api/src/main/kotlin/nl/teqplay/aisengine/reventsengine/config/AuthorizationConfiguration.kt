package nl.teqplay.aisengine.reventsengine.config

import nl.teqplay.skeleton.auth.credentials.AuthorizationConfigurer
import nl.teqplay.skeleton.auth.credentials.SWAGGER_PATHS
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class AuthorizationConfiguration {
    @Bean
    fun authorizationConfigurer(): AuthorizationConfigurer =
        AuthorizationConfigurer(
            SWAGGER_PATHS + listOf("/actuator/health", "/actuator/prometheus")
        )
}
