package nl.teqplay.aisengine.reventsengine.service.scenario

import jakarta.annotation.PostConstruct
import org.springframework.stereotype.Component

@Component
class ScenariosSchedulerInitService(
    private val scenariosSchedulerService: ScenariosSchedulerService,
) {

    /**
     * On startup loop over the scenarios, since we might already have some scenarios ready-to-go.
     */
    @PostConstruct
    fun init() {
        scenariosSchedulerService.loopOverScenarios()
    }
}
