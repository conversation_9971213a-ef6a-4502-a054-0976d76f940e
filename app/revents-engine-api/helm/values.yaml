global:
  storageClass: "gp3"
  namespaceOverride: "teqplay-app"

image:
  repository: 050356841556.dkr.ecr.eu-west-1.amazonaws.com/ais-engine/revents-engine-api

resources:
  requests:
    cpu: 0.1
    memory: 4Gi
  limits:
    memory: 4Gi

mongodb:
  enabled: true
  persistence:
    size: 150Gi
    storageClass: gp3-retained
  auth:
    database: reventsengine
    username: reventsengine
  resources:
    requests:
      cpu: 0.1
      memory: 2Gi
    limits:
      memory: 2Gi

terminationGracePeriodSeconds: 90

podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/path: /actuator/prometheus
  prometheus.io/port: "8080"

logs:
  - name: request-log
    file: /var/log/requests.log

env:
  - name: JAVA_TOOL_OPTIONS
    value: -XX:+UseParallelGC
