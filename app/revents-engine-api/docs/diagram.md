# revents-engine-api

[<< back](../README.md)

## Architectural diagram

---

![](../assets/revents-engine-api.drawio.png)

**Components:**

- `revents-engine-api` manages the scenarios and interests, and fetches events.
- `revents-engine-orchestrator` manages the process, instructs the `revents-engine` to start a run and watches the
  progress.
- `revents-engine` fetches history, converts them into diff events and sends them.
- various monitors process these diff events and generate ship events.
- `vesselvoyage` might run as part of (r)events when running V2, or outside of it when running V1.
- `revents-engine-orchestrator` collects these ship events and stores them in the database.

**NOTE:** there is only one instance of `revents-engine-api` and `revents-engine-orchestrator`, while there may be
multiple instances running in parallel for the `revents-engine` and the other components.

**Apart from the shown connections, there are some others:**

- `POMA` contains various areas
    - `revents-engine-api` uses this to resolve areas originating from POMA
    - `area-monitor` requests all areas
    - `anchor-monitor` requests all anchorages
- `CSI` contains ship roles
    - `revents-engine` uses this to determine nearby service vessels
    - `encounter-monitor` uses this to determine service vessels for encounter detection

**Rundown of running a scenario from start to finish:**

- A user creates a scenario and schedules it using the `revents-engine-api` endpoint to create a scenario.
- Interests are resolved by the `revents-engine-api`. Based on the interests, `revents-engine-api` might fork the
  current scenario into multiple processes to run the original scenario in parallel.
- The scenario is then handed over to `revents-engine-orchestrator`.
- The `revents-engine-orchestrator` then waits for capacity to come available.
- Once capacity is available it will spin up an AWS instance using AWS Fargate, and it will then deploy a Kubernetes Job
  containing all required components.
- Once all applications are ready for revents to be run the `revents-engine-orchestrator` instructs the `revents-engine`
  to start.
- The `revents-engine` then manages the process until the end, while `revents-engine-orchestrator` is monitoring the
  progress and collects all generated events and saving them in the database.
- When the `revents-engine` is finished it indicates this by sending a 'poison pill' over to the other applications.
- Those applications continue processing the data. When they receive the 'poison pill' from the `revents-engine` they
  will send out a 'poison pill' of their own.
- These 'poison pills' get picked up by the `revents-engine-orchestrator`, which in turn signals back to
  the `revents-engine` if it has received all 'poison pills' from the processing applications.
- The `revents-engine-orchestrator` then waits for `revents-engine` to indicate the process is finished. After this
  the `revents-engine-orchestrator` stops consuming events and tears down the Kubernetes Job and subsequent Fargate
  instance.
- If further post-processing is required, the `revents-engine-orchestrator` could perform this by making requests
  to `revents-vesselvoyage`, for example.
- After all is torn down, the `revents-engine-orchestrator` sets the scenario to `FINISHED`. Events can then be
  requested on the `revents-engine-api`.
