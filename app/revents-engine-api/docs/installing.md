# revents-engine-api

[<< back](../README.md)

## Installing the revents API

> **Note:** below the manual steps to install the (r)events API are described. However, CircleCI can be used to easily build
> and deploy the apps. You'll only need to set up the Fargate profile and initialize the ConfigMaps with the proper values.

---

1. Build the project and push the images (from the root of the project).
    ```shell
    ./app/revents-engine-api/scripts/buildImages.sh
    ./app/revents-engine-api/scripts/pushImages.sh
    ```

2. Set up the node groups to run revents on.
    - [Go to the `develop` cluster](https://eu-west-1.console.aws.amazon.com/eks/home?region=eu-west-1#/clusters/develop)
    - Go to the `Compute` tab, and press `Add Fargate profile`.
    - Set the `Name` to `namespace-revents-jobs`, set `Pod execution role` to `AmazonEKSFargatePodExecutionRole` and
      the `Subnets` to the subnets you want the servers to be spawned in. (This will most likely be
      just `eks-private-1c`.) Press `Next`.
    - Set the `Namespace` to `revents-jobs`, and press `Next`.
    - Press `Create` and wait for the Fargate profile to become active.

3. Navigate to `app/revents-engine-api`
    ```shell
    cd app/revents-engine-api
    ```

4. Inspect and run the `install.sh` script. (Ensure you have the `develop` cluster context set with `kubectl`)
    ```shell
    ./scripts/install.sh
    ```

5. Wait for the `install.sh` to initialize the applications.

6. In namespace `revents-core` update the `revents-engine-api` and `revents-engine-orchestrator` ConfigMaps.

7. In namespace `revents-jobs` set the `ais-engine-version` ConfigMap to contain a value for `tag` that is a container
   image tag. And update the values for ConfigMap `revents-engine-app-configmap`.

8. The `revents-engine-api` and all its required components are now ready to be used!