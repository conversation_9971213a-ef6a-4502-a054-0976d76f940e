# revents-engine-api

[<< back](../README.md)

## event-history-like API for (r)events

---

The `revents-engine-api` is designed to create scenarios, request their status and resulting data. Scenarios are
isolated and meant to be pruned after some time. This ensures (r)events can be continuously regenerated without
unnecessarily filling up disk space.

However, with the advent of merging visits and voyages back into VesselVoyage there needs to be a way to get the events
that were generated as part of a scenario for debuggability purposes.

The exact way in which the `revents-engine-api` is going to facilitate this is undecided for now. Apart from:

- `revents-engine-api` must preserve events that have been used to merge back into VesselVoyage.
- `revents-engine-api` must offer an event-history-like API that can be used to display (r)events within a tool like
  Timeline. Being able to show purely event-history's events, purely (r)events' events or both combined.
