package nl.teqplay.aisengine.portreportermonitor.service.conversion.converter.tug

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.aisengine.event.model.EncounterEndEvent
import nl.teqplay.aisengine.event.model.EncounterStartEvent
import nl.teqplay.aisengine.portreportermonitor.client.csi.CachedCsiClient
import nl.teqplay.aisengine.portreportermonitor.model.EnrichEvent
import nl.teqplay.aisengine.portreportermonitor.model.constant.EventType
import nl.teqplay.aisengine.portreportermonitor.service.conversion.converter.EncounterConverter
import nl.teqplay.aisengine.portreportermonitor.utils.buildKvKey
import nl.teqplay.skeleton.nats.NatsKeyValueBucket
import org.springframework.stereotype.Component

/**
 * The [DefaultTugConverter] handles the converting of [EncounterEvent] of type Tug and should result in "tug-standby" events.
 * - EncounterStart: Should result in a [EventType.TUGSTANDBY] if we didn't have a EncounterEnd event of type TugWaiting in the last 3 hours.
 * - EncounterEnd: Should result in a [EventType.TUGSNOMORESTANDBY_AT]
 */
@Component
class DefaultTugConverter(
    private val tugStandbyKV: NatsKeyValueBucket<String>,
    cachedCsiClient: CachedCsiClient
) : EncounterConverter<EncounterEvent>(cachedCsiClient, EncounterEvent.EncounterType.TUG) {
    private val LOG = KotlinLogging.logger {}

    override fun shouldConvert(event: EnrichEvent<EncounterEvent>): Boolean {
        if (event.originalEvent is EncounterStartEvent) {
            // We should only convert the tug event if the tug is not standby for this vessel
            return !hasAlreadyTugStandby(event)
        }

        // EncounterEnd events should always be converted as it will result in a "no-more-standby" event
        // Ignore any unsupported encounter types
        return event.originalEvent is EncounterEndEvent
    }

    /**
     * Check if we've already encountered a standby tug for the combination ship-tug.
     *
     * @return true when the tug is already standby at the ship, false if not or when we got an exception from NATS
     */
    private fun hasAlreadyTugStandby(event: EnrichEvent<EncounterEvent>): Boolean {
        return try {
            val tugStandbyKVEntry = tugStandbyKV.get(event.buildKvKey())

            tugStandbyKVEntry?.value != null
        } catch (_: Throwable) {
            LOG.debug { "Couldn't get standby tug from NATS, assuming we never had one" }
            false
        }
    }

    override fun EnrichEvent<EncounterEvent>.getEventType(): String? {
        return when (this.originalEvent) {
            is EncounterStartEvent -> EventType.TUGSTANDBY
            is EncounterEndEvent -> EventType.TUGSNOMORESTANDBY_AT
            else -> {
                LOG.warn { "TugEventConverter: Unknown EncounterEvent type: ${this.originalEvent::class.simpleName}" }
                null
            }
        }
    }
}
