package nl.teqplay.aisengine.portreportermonitor.configuration

import org.springframework.boot.context.properties.ConfigurationProperties
import java.time.Duration

@ConfigurationProperties(prefix = "portreporter-monitor")
data class PortreporterMonitorProperties(
    val natsSubject: String,
    val ignoredSubjects: List<String> = listOf(),
    val ignoredEventTypes: List<String> = emptyList(),

    /**
     * A toggle which indicates if there is support for HAMIS in PortcallPlus.
     *
     * TODO this should be set to true once HAMIS is included in PortcallPlus and the generated events are of type PortcallPlusEvent
     */
    val portcallPlusHamisSupport: Boolean = false
)

@ConfigurationProperties(prefix = "rabbitmq")
data class RabbitMqProperties(
    val uri: String,
    val exchange: String,
)

@ConfigurationProperties(prefix = "nats.settings")
data class NatsSettingsProperties(
    val requestMetadataTimeout: Duration = Duration.ofSeconds(2)
)
