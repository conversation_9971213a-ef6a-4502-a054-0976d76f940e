package nl.teqplay.aisengine.portreportermonitor.service.conversion.converter.portcallplus

import nl.teqplay.aisengine.event.interfaces.PortcallPlusEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusPortcallVisit
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusVisitsUpdateEvent
import nl.teqplay.aisengine.portreportermonitor.model.EnrichEvent
import nl.teqplay.aisengine.portreportermonitor.model.PortReporterEvent
import nl.teqplay.aisengine.portreportermonitor.model.PortcallEventContext
import nl.teqplay.aisengine.portreportermonitor.model.PortcallShipDetails
import nl.teqplay.aisengine.portreportermonitor.model.PortcallVisit
import nl.teqplay.aisengine.portreportermonitor.model.constant.EventType
import nl.teqplay.aisengine.shiphistory.client.ShipHistoricClient
import nl.teqplay.csi.model.ship.info.ShipRegisterInfo
import org.springframework.stereotype.Component

@Component
class VisitsUpdatePortcallPlusConverter(
    shipHistoricClient: ShipHistoricClient
) : BasePortcallPlusConverter(shipHistoricClient) {
    override fun EnrichEvent<PortcallPlusEvent>.getEventType(): String? {
        if (this.originalEvent is PortcallPlusVisitsUpdateEvent) {
            return EventType.BERTHVISITS_UPDATE_AGENT
        }

        // This converter should only be used by PortcallPlusVisitsUpdateEvent
        return null
    }

    override fun PortReporterEvent.afterGenerateEvent(event: EnrichEvent<PortcallPlusEvent>): PortReporterEvent {
        val originalEvent = event.originalEvent

        if (originalEvent is PortcallPlusVisitsUpdateEvent) {
            return this.copy(
                visits = originalEvent.visits.map { visit ->
                    visit.toPortReporterVisit()
                }
            )
        }

        return this
    }

    override fun PortcallEventContext.afterGenerateContext(
        event: EnrichEvent<PortcallPlusEvent>,
        ship: ShipRegisterInfo,
        staticShipInfo: PortcallShipDetails
    ): PortcallEventContext {
        val originalEvent = event.originalEvent

        if (originalEvent is PortcallPlusVisitsUpdateEvent) {
            return this.copy(
                agent = originalEvent.vesselAgent
            )
        }

        return this
    }

    /**
     * Convert a [PortcallPlusPortcallVisit] to a PortReporter compatible model
     */
    private fun PortcallPlusPortcallVisit.toPortReporterVisit(): PortcallVisit {
        return PortcallVisit(
            terminal = this.terminal,
            berthName = this.berthName,
            berthOwnerId = null,
            startTime = this.berthAta?.toEpochMilli() ?: this.berthEta?.toEpochMilli(),
            endTime = this.berthEtd?.toEpochMilli() ?: this.berthAtd?.toEpochMilli(),
            arrivalMovementId = this.arrivalMovementId,
            departureMovementId = this.departureMovementId
        )
    }
}
