package nl.teqplay.aisengine.portreportermonitor.service.conversion.converter.eta

import nl.teqplay.aisengine.event.model.PortcallPilotBoardingEtaEvent
import nl.teqplay.aisengine.portreportermonitor.model.EnrichEvent
import nl.teqplay.aisengine.portreportermonitor.model.constant.EventType
import nl.teqplay.aisengine.portreportermonitor.service.conversion.converter.Converter
import org.springframework.stereotype.Component

@Component
class DefaultPortcallPilotBoardingEtaConverter : Converter<PortcallPilotBoardingEtaEvent>() {
    override fun isLocationRequired(): Boolean = false

    companion object {
        private const val SOURCE_MARINE_TRAFFIC = "Teqplay based on MarineTraffic"
        private const val SOURCE_TEQPLAY_SLOW = "Teqplay Slow Steaming Predictor"
        private const val SOURCE_TEQPLAY_CANCEL = "Teqplay Cancel Predictor"
    }

    override fun getSource(event: EnrichEvent<PortcallPilotBoardingEtaEvent>): String {
        return when (event.originalEvent.source) {
            "MARINETRAFFIC" -> SOURCE_MARINE_TRAFFIC
            "TEQPLAY_LOW_SPEED" -> SOURCE_TEQPLAY_SLOW
            "TEQPLAY_CANCEL" -> SOURCE_TEQPLAY_CANCEL
            else -> super.getSource(event)
        }
    }

    override fun EnrichEvent<PortcallPilotBoardingEtaEvent>.getEventType(): String {
        return EventType.PILOTBOARDINGPLACE_ETA
    }
}
