package nl.teqplay.aisengine.portreportermonitor.service.conversion

import nl.teqplay.aisengine.event.model.PortcallPilotBoardingEtaEvent
import nl.teqplay.aisengine.portreportermonitor.service.conversion.converter.eta.DefaultPortcallPilotBoardingEtaConverter
import org.springframework.stereotype.Component

@Component
class PortcallPilotBoardingEtaEventConversionPicker(
    converter: DefaultPortcallPilotBoardingEtaConverter
) : PortConversionPicker<PortcallPilotBoardingEtaEvent>(defaultConverter = converter)
