package nl.teqplay.aisengine.portreportermonitor.service.conversion

import nl.teqplay.aisengine.event.interfaces.LockEvent
import nl.teqplay.aisengine.portreportermonitor.service.conversion.converter.lockprediction.DefaultLockPredictionConverter
import org.springframework.stereotype.Component

@Component
class LockPredictionEventConversionPicker(
    defaultConverter: DefaultLockPredictionConverter
) : PortConversionPicker<LockEvent>(defaultConverter)
