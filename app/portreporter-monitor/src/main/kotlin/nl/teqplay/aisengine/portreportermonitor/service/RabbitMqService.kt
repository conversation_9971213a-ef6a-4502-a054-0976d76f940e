package nl.teqplay.aisengine.portreportermonitor.service

import com.fasterxml.jackson.databind.ObjectMapper
import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.portreportermonitor.configuration.RabbitMqProperties
import nl.teqplay.aisengine.portreportermonitor.model.PortReporterEvent
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Service

private val LOG = KotlinLogging.logger {}
@Service
class RabbitMqService(
    private val rabbitMqEventSender: RabbitMqEventSender,
    @Qualifier("rmqMapper")
    private val rmqMapper: ObjectMapper,
    private val config: RabbitMqProperties,
) {
    /*
     * Function to send an event to rabbitMQ
     * Will keep retrying and create an infinite loop, blocking the thread until send
     */
    fun send(event: PortReporterEvent) {
        runCatching { sendToRabbitMq(event) }
            .onFailure { sleepAndRun { send(event) } }
    }

    /*
     * Function to send a list of events to rabbitMQ
     * Will keep retrying and create an infinite loop, blocking the thread until send
     */
    fun send(events: List<PortReporterEvent>) {
        runCatching { sendToRabbitMq(events) }
            .onFailure { sleepAndRun { send(events) } }
    }

    private fun sleepAndRun(function: () -> Unit) {
        LOG.warn { "Could not send message to RabbitMQ, trying again" }
        Thread.sleep(1000)
        function()
    }

    private fun sendToRabbitMq(event: Any) = rabbitMqEventSender.send(config.exchange, rmqMapper.writeValueAsBytes(event))
}
