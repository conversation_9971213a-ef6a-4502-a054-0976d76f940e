package nl.teqplay.aisengine.portreportermonitor.service.conversion

import nl.teqplay.aisengine.event.interfaces.AnchoredEvent
import nl.teqplay.aisengine.portreportermonitor.service.conversion.converter.anchored.DefaultAnchoredConverter
import org.springframework.stereotype.Component

@Component
class AnchoredEventConversionPicker(
    private val defaultConverter: DefaultAnchoredConverter,
) : PortConversionPicker<AnchoredEvent>(defaultConverter)
