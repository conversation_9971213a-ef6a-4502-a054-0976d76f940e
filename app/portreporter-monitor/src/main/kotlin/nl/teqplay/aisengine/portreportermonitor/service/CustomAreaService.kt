package nl.teqplay.aisengine.portreportermonitor.service

import nl.teqplay.aisengine.portreportermonitor.client.poma.CachedPomaClient
import nl.teqplay.poma.api.v1.CustomArea
import org.springframework.stereotype.Service

@Service
class CustomAreaService(
    private val cachedPomaClient: CachedPomaClient
) {

    fun getSeaEntranceForPort(unlocode: String): CustomArea? {
        return cachedPomaClient.allSeaEntrancesById[unlocode]
    }
}
