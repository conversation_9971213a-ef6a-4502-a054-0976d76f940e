package nl.teqplay.aisengine.portreportermonitor.configuration

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.databind.module.SimpleModule
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class RabbitMqConfiguration {
    @Bean("rmqMapper")
    fun rmqMapper(): ObjectMapper {
        val dateModule = SimpleModule("dateModule")
        return jacksonObjectMapper()
            .registerKotlinModule()
            .registerModule(dateModule)
            .registerModule(JavaTimeModule())
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
            .setSerializationInclusion(JsonInclude.Include.NON_NULL)
    }

    @Bean
    fun rabbitMqEventSender(config: RabbitMqProperties) = RabbitMqEventSender(config.uri)
        .also(RabbitMqEventSender::ensureChannelOpened)
}
