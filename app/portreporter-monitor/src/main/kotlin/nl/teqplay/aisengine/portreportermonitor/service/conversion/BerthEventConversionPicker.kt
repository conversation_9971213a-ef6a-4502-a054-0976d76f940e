package nl.teqplay.aisengine.portreportermonitor.service.conversion

import nl.teqplay.aisengine.event.interfaces.UniqueBerthEvent
import nl.teqplay.aisengine.portreportermonitor.service.conversion.converter.berth.BerthEventConverter
import org.springframework.stereotype.Component

@Component
class BerthEventConversionPicker(
    private val defaultConverter: BerthEventConverter,
) : PortConversionPicker<UniqueBerthEvent>(defaultConverter)
