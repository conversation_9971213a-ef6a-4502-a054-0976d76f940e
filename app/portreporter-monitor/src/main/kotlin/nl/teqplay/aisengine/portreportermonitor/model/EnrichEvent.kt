package nl.teqplay.aisengine.portreportermonitor.model

import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.csi.model.ship.info.ShipRegisterInfo
import nl.teqplay.poma.api.v1.Port

typealias GetEventMetadata = (Event, Port) -> EventMetadata
data class EnrichEvent<T : Event>(
    val originalEvent: T,
    val portReporterEvents: List<PortReporterEvent>,
    val shipRegisterInfo: ShipRegisterInfo?,
    val port: Port,
    private val getEventMetadata: GetEventMetadata,
) {
    val eventMetadata: EventMetadata by lazy { getEventMetadata(originalEvent, port) }
}
