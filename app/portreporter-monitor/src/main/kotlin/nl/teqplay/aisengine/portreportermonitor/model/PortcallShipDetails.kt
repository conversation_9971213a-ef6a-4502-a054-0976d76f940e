package nl.teqplay.aisengine.portreportermonitor.model

/**
 * Copied from platform
 */
data class PortcallShipDetails(
    val mmsi: String? = null,
    /** The IMO number of the sea-vessel  */
    val imo: String? = null,
    /** The ENI number of the barge  */
    val eni: String? = null,
    /** The name of the vessel  */
    val name: String? = null,
    /** The location of the vessel  */
    val location: Location?,
    /** The length of the vessel  */
    val length: Double? = null,
    /** The width of the vessel  */
    val width: Double? = null,
)
