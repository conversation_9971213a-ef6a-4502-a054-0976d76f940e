package nl.teqplay.aisengine.portreportermonitor.service.conversion.converter.nauticalmile

import nl.teqplay.aisengine.event.model.AreaStartEvent
import nl.teqplay.aisengine.portreportermonitor.model.EnrichEvent
import nl.teqplay.aisengine.portreportermonitor.model.constant.EventType._120NM
import nl.teqplay.aisengine.portreportermonitor.model.constant.EventType._12NM
import nl.teqplay.aisengine.portreportermonitor.model.constant.EventType._240NM
import nl.teqplay.aisengine.portreportermonitor.model.constant.EventType._60NM
import nl.teqplay.aisengine.portreportermonitor.model.constant.EventType._80NM
import nl.teqplay.aisengine.portreportermonitor.service.conversion.converter.Converter
import org.springframework.stereotype.Component

@Component
class DefaultNauticalMileConverter : Converter<AreaStartEvent>() {
    override fun EnrichEvent<AreaStartEvent>.getEventType() = when {
        originalEvent.area.id?.endsWith(".12nm") == true -> _12NM
        originalEvent.area.id?.endsWith(".60nm") == true -> _60NM
        originalEvent.area.id?.endsWith(".80nm") == true -> _80NM
        originalEvent.area.id?.endsWith(".120nm") == true -> _120NM
        originalEvent.area.id?.endsWith(".240nm") == true -> _240NM
        else -> null
    }
}
