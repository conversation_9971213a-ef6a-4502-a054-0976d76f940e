package nl.teqplay.aisengine.portreportermonitor.service.conversion.converter.portcallplus

import nl.teqplay.aisengine.event.interfaces.PortcallPlusEvent
import nl.teqplay.aisengine.event.interfaces.PortcallPredictionEvent
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier.AreaType
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusEtaEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusEtdEvent
import nl.teqplay.aisengine.portreportermonitor.model.EnrichEvent
import nl.teqplay.aisengine.portreportermonitor.model.NamedLocation
import nl.teqplay.aisengine.portreportermonitor.model.PortcallEventContext
import nl.teqplay.aisengine.portreportermonitor.model.PortcallShipDetails
import nl.teqplay.aisengine.portreportermonitor.model.constant.EventType
import nl.teqplay.aisengine.portreportermonitor.service.BerthService
import nl.teqplay.aisengine.portreportermonitor.utils.toNamedLocationType
import nl.teqplay.aisengine.shiphistory.client.ShipHistoricClient
import nl.teqplay.csi.model.ship.info.ShipRegisterInfo
import org.springframework.stereotype.Component

@Component
class PredictionPortcallPlusConverter(
    private val berthService: BerthService,
    shipHistoricClient: ShipHistoricClient
) : BasePortcallPlusConverter(shipHistoricClient) {

    override fun generateNamedLocation(event: EnrichEvent<PortcallPlusEvent>): NamedLocation? {
        if (event.originalEvent !is PortcallPredictionEvent) {
            return super.generateNamedLocation(event)
        }

        val area = event.originalEvent.area
        if (area.type == AreaType.BERTH) {
            // Search the berth via the provided id so we can find the name currently known in poma
            val berth = area.id?.let { berthService.getBerthById(it) }
                // We can't resolve the berth if we don't have anything
                // Fall back to the normal method of using the area name in the event
                ?: return super.generateNamedLocation(event)

            val berthName = berth.nameLong ?: berth.name

            return NamedLocation(
                name = berthName,
                type = area.type.toNamedLocationType(),
                id = area.id
            )
        }

        // Everything that isn't a berth should be handled like we do currently
        return super.generateNamedLocation(event)
    }

    override fun EnrichEvent<PortcallPlusEvent>.getEventType(): String? {
        return when (this.originalEvent) {
            is PortcallPlusEtaEvent -> getPortcallEtaEventType(this.originalEvent)
            is PortcallPlusEtdEvent -> getPortcallEtdEventType(this.originalEvent)
            else -> null
        }
    }

    override fun getSource(event: EnrichEvent<PortcallPlusEvent>): String {
        val originalEvent = event.originalEvent

        // We take the source from the prediction if we have it
        // Otherwise fallback to getting it via the port instead
        if (originalEvent is PortcallPredictionEvent) {
            return originalEvent.source ?: super.getSource(event)
        }

        return super.getSource(event)
    }

    /**
     * Get the event type corresponding to the provided PortcallPlus [event]
     *
     * The following options are possible:
     * - NOMINATION = nomination.eta.agent
     * - LOCK = lock.eta.vessel
     * - PORT = port.eta.agent
     * - PBP = pilotBoardingPlace.eta.agent
     * - BERTH = berth.eta.agent
     *
     * @param event The ETA events we want to get the type for
     * @return The event type supported by PortReporter
     */
    private fun getPortcallEtaEventType(event: PortcallPlusEtaEvent): String {
        if (event.nomination == true) {
            return EventType.NOMINATION_ETA_AGENT
        }

        return when (event.area.type) {
            AreaType.LOCK -> EventType.LOCK_ETA
            AreaType.PORT -> EventType.PORT_ETA_AGENT
            AreaType.PILOT_BOARDING_PLACE -> EventType.PILOTBOARDINGPLACE_ETA_AGENT
            AreaType.BERTH -> EventType.BERTH_ETA_AGENT
            else -> EventType.PORT_ETA_AGENT
        }
    }

    /**
     * Get the event type corresponding to the provided PortcallPlus [event]
     *
     * The following options are possible:
     * - NOMINATION = nomination.etd.agent
     * - LOCK = lock.etd.vessel
     * - PORT = port.etd.agent
     * - BERTH = berth.etd.agent
     *
     * @param event The ETD events we want to get the type for
     * @return The event type supported by PortReporter
     */
    private fun getPortcallEtdEventType(event: PortcallPlusEtdEvent): String {
        if (event.nomination == true) {
            return EventType.NOMINATION_ETD_AGENT
        }

        return when (event.area.type) {
            AreaType.LOCK -> EventType.LOCK_ETD
            AreaType.BERTH -> EventType.BERTH_ETD_AGENT
            else -> EventType.PORT_ETD_AGENT
        }
    }

    override fun PortcallEventContext.afterGenerateContext(
        event: EnrichEvent<PortcallPlusEvent>,
        ship: ShipRegisterInfo,
        staticShipInfo: PortcallShipDetails
    ): PortcallEventContext {
        val originalPortcallPredictionEvent = (event.originalEvent as? PortcallPredictionEvent)
        val pomaBerth = originalPortcallPredictionEvent?.berthId?.let(berthService::getBerthById)

        return this.copy(
            terminalName = pomaBerth?.terminalName,
            terminalId = pomaBerth?.terminalId,
            agent = originalPortcallPredictionEvent?.vesselAgent
        )
    }
}
