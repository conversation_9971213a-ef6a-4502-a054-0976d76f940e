package nl.teqplay.aisengine.portreportermonitor.configuration

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.aisengine.nats.stream.properties.EventStreamNatsProperties
import nl.teqplay.aisengine.portreportermonitor.service.PilotStateService
import nl.teqplay.skeleton.nats.NatsClientBuilder
import nl.teqplay.skeleton.nats.NatsKeyValueBucket
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.time.Duration
import java.util.Date

@Configuration
class KvBucketConfiguration {
    companion object {
        private const val KV_BUCKET_TUG_WAITING = "portreporter-monitor-tug-waiting"
        private const val KV_BUCKET_TUG_STANDBY = "portreporter-monitor-tug-standby"
        private const val KV_BUCKET_PILOT_ONBOARD = "portreporter-monitor-pilot-onboard"
        private const val KV_BUCKET_ENCOUNTER_CONCURRENCY = "portreporter-monitor-encounter-concurrency"
    }

    /**
     * Creates a KV Bucket to keep state of the actual time for Tug Waiting Start Events.
     * Key should be `MMSI-
     */
    @Bean
    fun kvBucketTugWaitingStartActualTime(
        nats: EventStreamNatsProperties,
        natsClientBuilder: NatsClientBuilder,
        objectMapper: ObjectMapper,
    ): NatsKeyValueBucket<Date> =
        natsClientBuilder.keyValueBucket(
            config = nats,
            name = KV_BUCKET_TUG_WAITING,
            serializer = objectMapper::writeValueAsBytes,
            deserializer = objectMapper::readValue,
            storeOnDisk = true,
            maxAge = Duration.ofHours(3),
        ) ?: throw Exception("Error creating KV bucket")

    @Bean
    fun kvBucketTugStandBy(
        nats: EventStreamNatsProperties,
        natsClientBuilder: NatsClientBuilder,
        objectMapper: ObjectMapper,
    ): NatsKeyValueBucket<String> =
        natsClientBuilder.keyValueBucket(
            config = nats,
            name = KV_BUCKET_TUG_STANDBY,
            serializer = objectMapper::writeValueAsBytes,
            deserializer = objectMapper::readValue,
            storeOnDisk = true,
            maxAge = Duration.ofHours(3),
        ) ?: throw Exception("Error creating KV bucket")

    @Bean
    fun kvBucketPilotOnboard(
        nats: EventStreamNatsProperties,
        natsClientBuilder: NatsClientBuilder,
        objectMapper: ObjectMapper,
    ): NatsKeyValueBucket<PilotStateService.PilotState> =
        natsClientBuilder.keyValueBucket(
            config = nats,
            name = KV_BUCKET_PILOT_ONBOARD,
            serializer = { value: PilotStateService.PilotState -> value.toString().encodeToByteArray() },
            deserializer = { value -> PilotStateService.PilotState.valueOf(value.decodeToString()) },
            storeOnDisk = true,
            maxAge = Duration.ofHours(3)
        ) ?: throw Exception("Error creating KV bucket")

    /**
     * KV bucket storing the number of ongoing encounters for a sea vessel of a specific type. Keyed as mmsi-type.
     */
    @Bean
    fun kvBucketEncounterConcurrency(
        nats: EventStreamNatsProperties,
        natsClientBuilder: NatsClientBuilder,
        objectMapper: ObjectMapper,
    ): NatsKeyValueBucket<Set<Int>> =
        natsClientBuilder.keyValueBucket(
            config = nats,
            name = KV_BUCKET_ENCOUNTER_CONCURRENCY,
            serializer = objectMapper::writeValueAsBytes,
            deserializer = objectMapper::readValue,
            storeOnDisk = true,
            maxAge = Duration.ofHours(3),
        ) ?: throw Exception("Error creating KV bucket")
}
