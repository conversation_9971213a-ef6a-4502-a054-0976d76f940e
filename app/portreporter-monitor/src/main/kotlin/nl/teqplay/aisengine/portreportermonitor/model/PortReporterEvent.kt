package nl.teqplay.aisengine.portreportermonitor.model

import com.fasterxml.jackson.annotation.JsonFormat
import java.util.Date
import java.util.UUID

/**
 * Copied from platform
 */
data class PortReporterEvent(
    /** UUID (version 4) for the event generate by the original event source  */
    val uuid: String = UUID.randomUUID().toString(),
    /** EventType defining which event this is  */
    val eventType: String,
    /** The source of this event  */
    val source: String? = null,
    /** Port call ID if known. UCRN for Port of Rotterdam  */
    val portcallId: String? = null,
    /** The port this event is related to, as a 5 letter UNLOCODE  */
    val port: String? = null,
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ssXXX")
    val recordTime: Date,
    /**
     * Timestamp related to the subject of the event, e.g. ETA, AT, ATA. using
     * ISO 8601 datetime with date, time and timezone
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ssXXX")
    val eventTime: Date,
    /** The ship concerning the event. */
    val ship: PortcallShipDetails,
    /** The location where the event happens */
    val location: NamedLocation?,

    /**
     * All possible visits of the portcall
     */
    val visits: List<PortcallVisit>? = null,

    /** Specific context information for this event, specific per event */
    val context: PortcallEventContext? = null,
)
