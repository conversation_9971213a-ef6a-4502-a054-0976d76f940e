package nl.teqplay.aisengine.portreportermonitor.model.constant // ktlint-disable filename

object EventPrefix {
    const val BASIN = "portBasin"
    const val FAIRWAY = "fairway"
    const val PBP = "pilotBoardingPlace"
    const val LOCK = "lock"
    const val VTS = "vtsArea"
    const val ANCHOR = "anchorArea"
    const val APPROACH_AREA = "approachArea"
    const val SEA_PASSAGE = "seaPassage"
    const val TUGAREA = "tugArea"
    const val BERTH = "berth"
    const val PORT = "port"

    const val NOMINATION = "nomination"
    const val NM = "nm"

    // local services (encounters) event prefixes
    const val POTABLE_WATER = "bunkerPW"
    const val SUPPLY_BARGE = "supplyBarge"
    const val LUBES = "lubesService"
    const val BUNKERS = "bunkerService"
    const val SLOPS = "slops"
    const val TENDER = "tender"
    const val WASTE = "waste"
    const val PUSH_BARGE = "pushBargeService"
}

object EventSuffix {

    const val AUTHORITY = ".portAuthority"
    const val CANCEL = ".cancel"
    const val VESSEL = ".vessel"
    const val ATA = ".ata$VESSEL"
    const val ATD = ".atd$VESSEL"
    const val ATA_AUTHORITY = ".ata$AUTHORITY"
    const val ATD_AUTHORITY = ".atd$AUTHORITY"
    const val ATAE = ".atae$VESSEL" // exit on inbound

    const val ATDE = ".atde$VESSEL" // exit on outbound

    const val ATS = ".ats$VESSEL"
    const val ATC = ".atc$VESSEL"
    const val AT = ".at$VESSEL"

    const val ETA = ".eta$VESSEL"
    const val ETD = ".etd$VESSEL"
    const val AGENT = ".agent"
    const val ETA_AGENT = ".eta$AGENT"
    const val ETD_AGENT = ".etd$AGENT"
}

object EventType {
    // extra
    const val BERTHVISIT_ADD_AGENT = "berthvisit.add.agent"
    const val BERTHVISIT_UPDATE_AGENT = "berthvisit.update.agent"
    const val BERTHVISIT_CANCEL_AGENT = "berthvisit.cancel.agent"

    // This event type is one with multiple visits events
    const val BERTHVISITS_UPDATE_AGENT = "berthvisits.update.agent"
    const val ORDER_AT_AGENT = "order.at.agent"
    const val VISIT_DECLARE = "agent.visitdeclare"
    const val NOMINATION_SHIPCHANGED_AGENT = "${EventPrefix.NOMINATION}.shipchanged.agent"

    // local Area monitor related events
    const val _120NM = "120${EventPrefix.NM}${EventSuffix.ATA}"
    const val _12NM = "12${EventPrefix.NM}${EventSuffix.ATA}"
    const val _240NM = "240${EventPrefix.NM}${EventSuffix.ATA}"
    const val _60NM = "60${EventPrefix.NM}${EventSuffix.ATA}"
    const val _80NM = "80${EventPrefix.NM}${EventSuffix.ATA}"

    const val APPROACH_ATA = EventPrefix.APPROACH_AREA + EventSuffix.ATA
    const val APPROACH_ATD = EventPrefix.APPROACH_AREA + EventSuffix.ATD
    const val VTSAREA_ATA = EventPrefix.VTS + EventSuffix.ATA
    const val VTSAREA_ATD = EventPrefix.VTS + EventSuffix.ATD
    const val ANCHOR_ATA = EventPrefix.ANCHOR + EventSuffix.ATA
    const val ANCHOR_ATD = EventPrefix.ANCHOR + EventSuffix.ATD
    const val LOCK_ETA = EventPrefix.LOCK + EventSuffix.ETA
    const val LOCK_ETD = EventPrefix.LOCK + EventSuffix.ETD
    const val LOCK_ATA = EventPrefix.LOCK + EventSuffix.ATA
    const val LOCK_ATD = EventPrefix.LOCK + EventSuffix.ATD
    const val PILOTBOARDINGPLACE_ATA = EventPrefix.PBP + EventSuffix.ATA
    const val PILOTBOARDINGPLACE_ATD = EventPrefix.PBP + EventSuffix.ATD
    const val PILOTBOARDINGPLACE_ATAE = EventPrefix.PBP + EventSuffix.ATAE
    const val PILOTBOARDINGPLACE_ATDE = EventPrefix.PBP + EventSuffix.ATDE
    const val BASIN_ATA = EventPrefix.BASIN + EventSuffix.ATA
    const val FAIRWAY_ATA = EventPrefix.FAIRWAY + EventSuffix.ATA
    const val TUGAREA_ATA = EventPrefix.TUGAREA + EventSuffix.ATA
    const val TUGAREA_ATD = EventPrefix.TUGAREA + EventSuffix.ATD

    // Remote PORT monitor events
    const val PORT_ETA = EventPrefix.PORT + EventSuffix.ETA
    const val PORT_ETA_AGENT = EventPrefix.PORT + EventSuffix.ETA_AGENT
    const val PORT_ETA_AGENT_CANCEL = EventPrefix.PORT + EventSuffix.ETA_AGENT + EventSuffix.CANCEL
    const val PORT_ETD_AGENT = EventPrefix.PORT + EventSuffix.ETD_AGENT
    const val PORT_ATA = EventPrefix.PORT + EventSuffix.ATA
    const val PORT_ATD = EventPrefix.PORT + EventSuffix.ATD

    // Sea-passage events. EOSP: End of Sea passage. SOSP: Start of Sea passage.
    const val PORT_EOSP = EventPrefix.SEA_PASSAGE + EventSuffix.ATA
    const val PORT_SOSP = EventPrefix.SEA_PASSAGE + EventSuffix.ATD

    const val PORT_1_ATA = "${EventPrefix.PORT}-1${EventSuffix.ATA}"
    const val PORT_1_ATD = "${EventPrefix.PORT}-1${EventSuffix.ATD}"
    const val PORT_2_ATA = "${EventPrefix.PORT}-2${EventSuffix.ATA}"
    const val PORT_2_ATD = "${EventPrefix.PORT}-2${EventSuffix.ATD}"

    // local berth monitor related events
    const val BERTH_ATA = EventPrefix.BERTH + EventSuffix.ATA
    const val BERTH_ATD = EventPrefix.BERTH + EventSuffix.ATD
    const val BERTH_ATA_AUTHORITY = EventPrefix.BERTH + EventSuffix.ATA_AUTHORITY
    const val BERTH_ATD_AUTHORITY = EventPrefix.BERTH + EventSuffix.ATD_AUTHORITY

    // local nautical services (encounter) events
    const val FIRSTLINE_RELEASED_AT = "firstLineReleased${EventSuffix.AT}"
    const val FIRSTLINE_SECURED_AT = "firstLineSecured${EventSuffix.AT}"
    const val LASTLINE_RELEASED_AT = "lastLineReleased${EventSuffix.AT}"
    const val LASTLINE_SECURED_AT = "lastLineSecured${EventSuffix.AT}"
    const val PILOT_DISEMBARKED_AT = "pilotDisembarked${EventSuffix.AT}"
    const val PILOT_ONBOARD_AT = "pilotOnBoard${EventSuffix.AT}"
    const val TUGSNOMORESTANDBY_AT = "tugsNoMoreStandby${EventSuffix.AT}"
    const val TUGSTANDBY = "tugsStandby${EventSuffix.AT}"

    // local services (encounters) events
    const val POTABLE_WATER_ATC = "${EventPrefix.POTABLE_WATER}${EventSuffix.ATC}"
    const val POTABLE_WATER_ATS = "${EventPrefix.POTABLE_WATER}${EventSuffix.ATS}"
    const val SUPPLY_BARGE_ATC = "${EventPrefix.SUPPLY_BARGE}${EventSuffix.ATC}"
    const val SUPPLY_BARGE_ATS = "${EventPrefix.SUPPLY_BARGE}${EventSuffix.ATS}"
    const val LUBES_ATC = "${EventPrefix.LUBES}${EventSuffix.ATC}"
    const val LUBES_ATS = "${EventPrefix.LUBES}${EventSuffix.ATS}"
    const val BUNKERS_ATC = "${EventPrefix.BUNKERS}${EventSuffix.ATC}"
    const val BUNKERS_ATS = "${EventPrefix.BUNKERS}${EventSuffix.ATS}"
    const val CUSTOMS_ATC = "customs${EventSuffix.ATC}"
    const val CUSTOMS_ATS = "customs${EventSuffix.ATS}"
    const val CRANE_ATC = "floatingCrane${EventSuffix.ATC}"
    const val CRANE_ATS = "floatingCrane${EventSuffix.ATS}"
    const val IMMIGRATION_ATC = "immigration${EventSuffix.ATC}"
    const val IMMIGRATION_ATS = "immigration${EventSuffix.ATS}"
    const val PORTAUTHORITY_ATC = "portAuthority${EventSuffix.ATC}"
    const val PORTAUTHORITY_ATS = "portAuthority${EventSuffix.ATS}"
    const val PROVISION_ATC = "provision${EventSuffix.ATC}"
    const val PROVISION_ATS = "provision${EventSuffix.ATS}"
    const val SLOPS_ATC = "${EventPrefix.SLOPS}${EventSuffix.ATC}"
    const val SLOPS_ATS = "${EventPrefix.SLOPS}${EventSuffix.ATS}"
    const val TENDER_ATC = "${EventPrefix.TENDER}${EventSuffix.ATC}"
    const val TENDER_ATS = "${EventPrefix.TENDER}${EventSuffix.ATS}"
    const val WASTE_ATC = "${EventPrefix.WASTE}${EventSuffix.ATC}"
    const val WASTE_ATS = "${EventPrefix.WASTE}${EventSuffix.ATS}"
    const val PUSH_BARGE_ATC = "${EventPrefix.PUSH_BARGE}${EventSuffix.ATC}"
    const val PUSH_BARGE_ATS = "${EventPrefix.PUSH_BARGE}${EventSuffix.ATS}"

    // ETA events
    const val PILOTBOARDINGPLACE_ETA = EventPrefix.PBP + EventSuffix.ETA
    const val PILOTBOARDINGPLACE_ETA_AGENT = EventPrefix.PBP + EventSuffix.ETA_AGENT
    const val BERTH_ETA = "berth${EventSuffix.ETA}"
    const val BERTH_ETA_AGENT = EventPrefix.BERTH + EventSuffix.ETA_AGENT
    const val BERTH_ETD_AGENT = EventPrefix.BERTH + EventSuffix.ETD_AGENT
    const val NOMINATION_ETA_AGENT = EventPrefix.NOMINATION + EventSuffix.ETA_AGENT
    const val NOMINATION_ETD_AGENT = EventPrefix.NOMINATION + EventSuffix.ETD_AGENT
    const val SAILING_ARRANGED = "sailing.arranged"
    const val PBP_ETD_AGENT = EventPrefix.PBP + EventSuffix.ETD_AGENT
    const val PORTCALL_FINISHED = "portcall.finished"
    const val PORTCALL_AGENT_CHANGED = "portcall.agentchanged"
}
