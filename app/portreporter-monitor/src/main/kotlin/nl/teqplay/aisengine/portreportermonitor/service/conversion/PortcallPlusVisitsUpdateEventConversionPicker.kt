package nl.teqplay.aisengine.portreportermonitor.service.conversion

import nl.teqplay.aisengine.event.interfaces.PortcallPlusEvent
import nl.teqplay.aisengine.portreportermonitor.service.conversion.converter.portcallplus.VisitsUpdatePortcallPlusConverter
import org.springframework.stereotype.Component

@Component
class PortcallPlusVisitsUpdateEventConversionPicker(
    converter: VisitsUpdatePortcallPlusConverter
) : PortConversionPicker<PortcallPlusEvent>(converter)
