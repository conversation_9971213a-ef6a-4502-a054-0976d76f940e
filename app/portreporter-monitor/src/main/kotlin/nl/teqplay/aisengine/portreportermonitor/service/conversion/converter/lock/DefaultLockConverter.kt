package nl.teqplay.aisengine.portreportermonitor.service.conversion.converter.lock

import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.aisengine.event.model.AreaEndEvent
import nl.teqplay.aisengine.event.model.AreaStartEvent
import nl.teqplay.aisengine.portreportermonitor.model.EnrichEvent
import nl.teqplay.aisengine.portreportermonitor.model.constant.EventType
import nl.teqplay.aisengine.portreportermonitor.service.conversion.converter.Converter
import org.springframework.stereotype.Component

@Component
class DefaultLockConverter : Converter<AreaEvent>() {
    override fun EnrichEvent<AreaEvent>.getEventType(): String? {
        return when (this.originalEvent) {
            is AreaStartEvent -> EventType.LOCK_ATA
            is AreaEndEvent -> EventType.LOCK_ATD
            else -> null
        }
    }
}
