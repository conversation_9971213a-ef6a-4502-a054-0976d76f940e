package nl.teqplay.aisengine.portreportermonitor.service.conversion

import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.aisengine.portreportermonitor.service.conversion.converter.tug.DefaultTugConverter
import org.springframework.stereotype.Component

@Component
class TugEventConversionPicker(
    private val defaultConverter: DefaultTugConverter,
) : PortConversionPicker<EncounterEvent>(defaultConverter)
