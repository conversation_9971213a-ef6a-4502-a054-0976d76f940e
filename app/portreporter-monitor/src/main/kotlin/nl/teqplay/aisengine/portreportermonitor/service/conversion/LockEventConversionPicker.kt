package nl.teqplay.aisengine.portreportermonitor.service.conversion

import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.aisengine.portreportermonitor.service.conversion.converter.lock.DefaultLockConverter
import org.springframework.stereotype.Component

@Component
class LockEventConversionPicker(
    defaultConverter: DefaultLockConverter,
) : PortConversionPicker<AreaEvent>(defaultConverter)
