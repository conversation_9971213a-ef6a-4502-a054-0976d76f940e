package nl.teqplay.aisengine.portreportermonitor.service

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.event.interfaces.AnchoredEvent
import nl.teqplay.aisengine.event.interfaces.AreaBasedEvent
import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.interfaces.LockEvent
import nl.teqplay.aisengine.event.interfaces.PortcallFinishEvent
import nl.teqplay.aisengine.event.interfaces.PortcallPlusEvent
import nl.teqplay.aisengine.event.interfaces.PortcallPredictionEvent
import nl.teqplay.aisengine.event.interfaces.UniqueBerthEvent
import nl.teqplay.aisengine.event.model.AreaEndEvent
import nl.teqplay.aisengine.event.model.AreaStartEvent
import nl.teqplay.aisengine.event.model.EncounterStartEvent
import nl.teqplay.aisengine.event.model.PortcallPilotBoardingEtaEvent
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier.AreaType
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusAgentChangedEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusVisitsUpdateEvent
import nl.teqplay.aisengine.portreportermonitor.service.conversion.AlongsideEventConversionPicker
import nl.teqplay.aisengine.portreportermonitor.service.conversion.AnchoredEventConversionPicker
import nl.teqplay.aisengine.portreportermonitor.service.conversion.ApproachEventConversionPicker
import nl.teqplay.aisengine.portreportermonitor.service.conversion.BerthEventConversionPicker
import nl.teqplay.aisengine.portreportermonitor.service.conversion.BoatmanEventConversionPicker
import nl.teqplay.aisengine.portreportermonitor.service.conversion.LockEventConversionPicker
import nl.teqplay.aisengine.portreportermonitor.service.conversion.LockPredictionEventConversionPicker
import nl.teqplay.aisengine.portreportermonitor.service.conversion.NauticalMileEventConversionPicker
import nl.teqplay.aisengine.portreportermonitor.service.conversion.PilotEventConversionPicker
import nl.teqplay.aisengine.portreportermonitor.service.conversion.PilotFallbackEventConversionPicker
import nl.teqplay.aisengine.portreportermonitor.service.conversion.PortConversionPicker
import nl.teqplay.aisengine.portreportermonitor.service.conversion.PortEventConversionPicker
import nl.teqplay.aisengine.portreportermonitor.service.conversion.PortcallPilotBoardingEtaEventConversionPicker
import nl.teqplay.aisengine.portreportermonitor.service.conversion.PortcallPlusAgentChangedEventConversionPicker
import nl.teqplay.aisengine.portreportermonitor.service.conversion.PortcallPlusDefaultEventConversionPicker
import nl.teqplay.aisengine.portreportermonitor.service.conversion.PortcallPlusPredictionEventConversionPicker
import nl.teqplay.aisengine.portreportermonitor.service.conversion.PortcallPlusVisitsUpdateEventConversionPicker
import nl.teqplay.aisengine.portreportermonitor.service.conversion.PortcallplusFinishedEventConversionPicker
import nl.teqplay.aisengine.portreportermonitor.service.conversion.TugEventConversionPicker
import nl.teqplay.aisengine.portreportermonitor.service.conversion.TugWaitingDepartureEventConversionPicker
import org.springframework.stereotype.Service

private val LOG = KotlinLogging.logger {}

@Service
class EventConverterService(
    private val alongsideEventConversionPicker: AlongsideEventConversionPicker,
    private val lockPredictionEventConversionPicker: LockPredictionEventConversionPicker,
    private val berthEventConversionPicker: BerthEventConversionPicker,
    private val boatmanEventConversionPicker: BoatmanEventConversionPicker,
    private val portEventConversionPicker: PortEventConversionPicker,
    private val anchoredEventConversionPicker: AnchoredEventConversionPicker,
    private val tugEventConversionPicker: TugEventConversionPicker,
    private val tugWaitingDepartureEventConversionPicker: TugWaitingDepartureEventConversionPicker,
    private val nauticalMileEventConversionPicker: NauticalMileEventConversionPicker,
    private val pilotEventConversionPicker: PilotEventConversionPicker,
    private val approachEventConversionPicker: ApproachEventConversionPicker,
    private val pilotFallbackEventConversionPicker: PilotFallbackEventConversionPicker,
    private val lockEventConversionPicker: LockEventConversionPicker,
    private val portcallPlusDefaultEventConversionPicker: PortcallPlusDefaultEventConversionPicker,
    private val portcallPlusPredictionEventConversionPicker: PortcallPlusPredictionEventConversionPicker,
    private val portcallPlusAgentChangedEventConversionPicker: PortcallPlusAgentChangedEventConversionPicker,
    private val portcallPlusVisitsUpdateEventConversionPicker: PortcallPlusVisitsUpdateEventConversionPicker,
    private val portcallPilotBoardingEtaEventConversionPicker: PortcallPilotBoardingEtaEventConversionPicker,
    private val portcallplusFinishedEventConversionPicker: PortcallplusFinishedEventConversionPicker
) {
    /**
     * @return Get a type safe [PortConversionPicker] based on the event type and additional checks on the event if needed.
     */
    fun <T : Event, C : PortConversionPicker<T>> getEventConversionPicker(event: T): C? {
        return when {
            event.isPortcallPlusEvent() -> getPortcallPlusEventConversionPicker(event as PortcallPlusEvent)
            event.isPortcallPilotBoardingEtaEvent() -> portcallPilotBoardingEtaEventConversionPicker
            event.isAlongsideEvent() -> alongsideEventConversionPicker
            event.isLockPredictionEvent() -> lockPredictionEventConversionPicker
            event.isBerthEvent() -> berthEventConversionPicker
            event.isBoatmanEvent() -> boatmanEventConversionPicker
            event.isPortEvent() -> portEventConversionPicker
            event.isAnchoredEvent() -> anchoredEventConversionPicker
            event.isTugEvent() -> tugEventConversionPicker
            event.isTugWaitingDepartureEvent() -> tugWaitingDepartureEventConversionPicker
            event.isNauticalMileEvent() -> nauticalMileEventConversionPicker
            event.isPilotEvent() -> pilotEventConversionPicker
            event.isApproachEvent() -> approachEventConversionPicker
            event.isPilotFallbackEvent() -> pilotFallbackEventConversionPicker
            event.isLockEvent() -> lockEventConversionPicker
            else -> {
                LOG.debug { "Event type ${event.getEventType()} not supported class: ${event::class}" }
                null
            }
        } as? C
    }

    /**
     * Select the PortcallPlus event conversion picker based on the specific event type or otherwise fallback to the default picker
     */
    private fun getPortcallPlusEventConversionPicker(event: PortcallPlusEvent): PortConversionPicker<PortcallPlusEvent> {
        return when (event) {
            is PortcallPredictionEvent -> portcallPlusPredictionEventConversionPicker
            is PortcallPlusAgentChangedEvent -> portcallPlusAgentChangedEventConversionPicker
            is PortcallPlusVisitsUpdateEvent -> portcallPlusVisitsUpdateEventConversionPicker
            is PortcallFinishEvent -> portcallplusFinishedEventConversionPicker
            else -> portcallPlusDefaultEventConversionPicker
        }
    }

    private fun Event.isPortcallPlusEvent(): Boolean =
        this is PortcallPlusEvent

    private fun Event.isPortcallPilotBoardingEtaEvent(): Boolean =
        this is PortcallPilotBoardingEtaEvent

    private fun Event.isAlongsideEvent(): Boolean {
        val relevantEncounterTypes = setOf(
            EncounterType.BUNKER,
            EncounterType.SWOG,
            EncounterType.TENDER,
            EncounterType.WATER,
            EncounterType.WASTE,
            EncounterType.LUBES,
            EncounterType.BARGE_SUPPLY,
            EncounterType.BARGE_PUSH,
        )

        return this is EncounterEvent && this.encounterType in relevantEncounterTypes
    }

    private fun Event.isApproachEvent(): Boolean = this is AreaEvent && this.area.type === AreaType.APPROACH_POINT

    private fun Event.isLockPredictionEvent(): Boolean =
        this is LockEvent && this.area.type == AreaType.LOCK

    private fun Event.isBerthEvent(): Boolean = this is UniqueBerthEvent

    private fun Event.isBoatmanEvent(): Boolean = this is EncounterEvent && this.encounterType == EncounterType.BOATMAN

    private fun Event.isAnchoredEvent(): Boolean = this is AnchoredEvent

    private fun Event.isPortEvent(): Boolean =
        this is AreaBasedEvent && this.area.type == AreaType.PORT

    private fun Event.isTugEvent(): Boolean =
        this is EncounterEvent && this.encounterType == EncounterType.TUG

    private fun Event.isTugWaitingDepartureEvent(): Boolean =
        this is EncounterEvent && this.encounterType == EncounterType.TUG_WAITING_DEPARTURE

    private fun Event.isNauticalMileEvent(): Boolean =
        this is AreaStartEvent && area.type == AreaType.NAUTICAL_MILE

    private fun Event.isPilotEvent(): Boolean =
        this is EncounterStartEvent && this.encounterType == EncounterType.PILOT

    private fun Event.isPilotFallbackEvent(): Boolean =
        this is AreaEndEvent && area.type == AreaType.PILOT_BOARDING_PLACE

    private fun Event.isLockEvent(): Boolean =
        this is AreaEvent && area.type === AreaType.LOCK
}
