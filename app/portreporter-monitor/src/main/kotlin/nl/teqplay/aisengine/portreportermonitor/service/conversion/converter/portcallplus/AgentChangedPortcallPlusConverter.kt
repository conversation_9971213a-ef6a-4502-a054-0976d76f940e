package nl.teqplay.aisengine.portreportermonitor.service.conversion.converter.portcallplus

import nl.teqplay.aisengine.event.interfaces.PortcallPlusEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusAgentChangedEvent
import nl.teqplay.aisengine.portreportermonitor.model.EnrichEvent
import nl.teqplay.aisengine.portreportermonitor.model.PortcallEventContext
import nl.teqplay.aisengine.portreportermonitor.model.PortcallShipDetails
import nl.teqplay.aisengine.portreportermonitor.model.constant.EventType
import nl.teqplay.aisengine.shiphistory.client.ShipHistoricClient
import nl.teqplay.csi.model.ship.info.ShipRegisterInfo
import org.springframework.stereotype.Component

@Component
class AgentChangedPortcallPlusConverter(
    shipHistoricClient: ShipHistoricClient
) : BasePortcallPlusConverter(shipHistoricClient) {
    override fun EnrichEvent<PortcallPlusEvent>.getEventType(): String? {
        if (this.originalEvent is PortcallPlusAgentChangedEvent) {
            return EventType.PORTCALL_AGENT_CHANGED
        }

        // This converter should only be used by AgentChangedEvents
        return null
    }

    override fun getSource(event: EnrichEvent<PortcallPlusEvent>): String {
        val originalEvent = event.originalEvent

        // We take the source from the event directly when available
        // Otherwise fallback to getting it via the port instead
        if (originalEvent is PortcallPlusAgentChangedEvent) {
            originalEvent.source ?: super.getSource(event)
        }

        return super.getSource(event)
    }

    override fun PortcallEventContext.afterGenerateContext(
        event: EnrichEvent<PortcallPlusEvent>,
        ship: ShipRegisterInfo,
        staticShipInfo: PortcallShipDetails
    ): PortcallEventContext {
        val originalEvent = event.originalEvent

        if (originalEvent is PortcallPlusAgentChangedEvent) {
            return this.copy(
                agent = originalEvent.vesselAgent
            )
        }

        return this
    }
}
