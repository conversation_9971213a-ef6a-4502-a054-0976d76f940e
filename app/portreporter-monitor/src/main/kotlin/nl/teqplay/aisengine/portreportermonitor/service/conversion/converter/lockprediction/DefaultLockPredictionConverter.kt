package nl.teqplay.aisengine.portreportermonitor.service.conversion.converter.lockprediction

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.event.interfaces.LockEvent
import nl.teqplay.aisengine.event.model.LockEtaEvent
import nl.teqplay.aisengine.event.model.LockEtdEvent
import nl.teqplay.aisengine.portreportermonitor.model.EnrichEvent
import nl.teqplay.aisengine.portreportermonitor.model.LocationType
import nl.teqplay.aisengine.portreportermonitor.model.NamedLocation
import nl.teqplay.aisengine.portreportermonitor.model.constant.EventType
import nl.teqplay.aisengine.portreportermonitor.service.conversion.converter.Converter
import nl.teqplay.aisengine.portreportermonitor.utils.toNamedLocationType
import org.springframework.stereotype.Component

private val LOG = KotlinLogging.logger {}

@Component
class DefaultLockPredictionConverter : Converter<LockEvent>() {
    companion object {
        const val DEFAULT_LOCK_SOURCE = "LOCK_SCHEDULE"
    }

    override fun shouldConvert(event: EnrichEvent<LockEvent>): Boolean {
        val area = event.originalEvent.area

        return area.name != null && area.type.toNamedLocationType() != null
    }

    override fun EnrichEvent<LockEvent>.getEventType(): String? {
        return when (this.originalEvent) {
            is LockEtaEvent -> EventType.LOCK_ETA
            is LockEtdEvent -> EventType.LOCK_ETD
            else -> null
        }
    }

    override fun getSource(event: EnrichEvent<LockEvent>): String {
        val portUnlocode = event.port.unlocode?.uppercase()

        // Prefix the source with an unlocode if its known
        if (portUnlocode != null) {
            return "${portUnlocode}_$DEFAULT_LOCK_SOURCE"
        }

        return DEFAULT_LOCK_SOURCE
    }

    override fun generateNamedLocation(event: EnrichEvent<LockEvent>): NamedLocation? {
        val lockEvent = event.originalEvent

        // The lock id isn't the poma id but the ISRS code we get from the event
        val lockId = lockEvent.isrsId
        val lockName = lockEvent.area.name

        if (lockName != null) {
            return NamedLocation(
                name = lockName,
                type = LocationType.LOCK,
                id = lockId
            )
        }

        LOG.warn { "Could not create NamedLocation from lock area ${lockEvent.area}" }
        return null
    }
}
