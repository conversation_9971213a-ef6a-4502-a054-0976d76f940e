package nl.teqplay.aisengine.portreportermonitor.service.conversion

import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.aisengine.portreportermonitor.service.conversion.converter.tugwaiting.DefaultTugWaitingConverter
import org.springframework.stereotype.Component

@Component
class TugWaitingDepartureEventConversionPicker(
    private val defaultConverter: DefaultTugWaitingConverter,
) : PortConversionPicker<EncounterEvent>(defaultConverter)
