package nl.teqplay.aisengine.portreportermonitor.model

/**
 * Copied from platform
 */
data class PortcallMooring(
    val bollardFore: Float? = null,
    /** Bollard the aft of the ship is moored at, integer or integer + 0.5  */
    val bollardAft: Float? = null,
    /** orientation of the vessel at the berth, either 'port' or 'starboard'  */
    val orientation: String? = null,
    /**
     * If the ship is moored side-by-side to another ship (which is closer to
     * the berth). Defaults to false
     */
    val doubleBanked: Boolean? = null,
)
