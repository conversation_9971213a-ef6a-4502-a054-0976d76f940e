package nl.teqplay.aisengine.portreportermonitor.service.conversion.converter.berth

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.event.interfaces.UniqueBerthEvent
import nl.teqplay.aisengine.event.model.UniqueBerthEndEvent
import nl.teqplay.aisengine.event.model.UniqueBerthStartEvent
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.portreportermonitor.model.EnrichEvent
import nl.teqplay.aisengine.portreportermonitor.model.NamedLocation
import nl.teqplay.aisengine.portreportermonitor.model.PortcallEventContext
import nl.teqplay.aisengine.portreportermonitor.model.PortcallShipDetails
import nl.teqplay.aisengine.portreportermonitor.model.constant.EventType
import nl.teqplay.aisengine.portreportermonitor.service.BerthService
import nl.teqplay.aisengine.portreportermonitor.service.conversion.converter.Converter
import nl.teqplay.aisengine.portreportermonitor.utils.toNamedLocationType
import nl.teqplay.csi.model.ship.info.ShipRegisterInfo
import nl.teqplay.skeleton.model.Location
import org.springframework.stereotype.Component

val LOG = KotlinLogging.logger {}

@Component
class BerthEventConverter(
    private val berthService: BerthService,
) : Converter<UniqueBerthEvent>() {

    override fun EnrichEvent<UniqueBerthEvent>.getEventType(): String? {
        return when (this.originalEvent) {
            is UniqueBerthStartEvent -> EventType.BERTH_ATA
            is UniqueBerthEndEvent -> EventType.BERTH_ATD
            else -> {
                LOG.warn { "Unknown UniqueBerthEvent type: ${this.originalEvent::class.simpleName}" }
                return null
            }
        }
    }

    override fun PortcallEventContext.afterGenerateContext(
        event: EnrichEvent<UniqueBerthEvent>,
        ship: ShipRegisterInfo,
        staticShipInfo: PortcallShipDetails,
    ): PortcallEventContext {
        return this.copy(
            draught = event.originalEvent.draught?.meterToCentimeter(),
            adjacentBerths = event.originalEvent.getAdjacentBerthNames(),
            terminalName = event.originalEvent.berth?.terminalName,
        )
    }

    private fun UniqueBerthEvent.getAdjacentBerthNames(): Set<String>? {
        val berth = this.area.id?.let { berthService.getBerthById(it) } ?: return null
        return berthService.getAdjacentBerths(berth).map { it.name }.toSet()
    }

    /**
     * Override the way a named location is generated since the original events contains the short name.
     */
    override fun generateNamedLocation(event: EnrichEvent<UniqueBerthEvent>): NamedLocation? {
        val areaId = event.originalEvent.area.getValueOrLogIfNull(AreaIdentifier::id) ?: return null
        val locationType = event.originalEvent.area.type.toNamedLocationType() ?: return null
        val berth = berthService.getBerthById(areaId) ?: return null

        return NamedLocation(berth.nameLong ?: berth.name, locationType, areaId)
    }

    override fun resolveLocation(event: EnrichEvent<UniqueBerthEvent>): Location = event.originalEvent.location
}
