<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <springProperty scope="context" name="separate-request-logging" source="common.separate-request-logging"/>

    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">

        <encoder>
            <pattern>%d{ISO8601} %highlight(%-5level) [%cyan(%thread)] %yellow(%C{5}): %msg%n%throwable</pattern>
        </encoder>

    </appender>

    <!-- LOG everything at INFO level -->
    <root level="info">

        <appender-ref ref="console"/>

    </root>

    <if condition='isDefined("separate-request-logging") &amp;&amp; property("separate-request-logging").equals("true")'>
        <then>
            <!-- Log all the request logging stuff to a separate file -->
            <include resource="requestlogging.xml"/>
        </then>
    </if>

    <appender name="eventlog" class="ch.qos.logback.core.rolling.RollingFileAppender">

        <file>/var/log/event.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>event.%i.log.zip</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>2</maxIndex>
        </rollingPolicy>

        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>10MB</maxFileSize>
        </triggeringPolicy>

        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>%d{ISO8601} %-5level [%t] %C{1}: %msg%n%throwable</pattern>
        </layout>

    </appender>

    <logger name="portreportermonitor-eventlog" additivity="false">
        <appender-ref ref="eventlog"/>
    </logger>

    <!-- Suppress output of logback when starting the application -->
    <statusListener class="ch.qos.logback.core.status.NopStatusListener" />

</configuration>
