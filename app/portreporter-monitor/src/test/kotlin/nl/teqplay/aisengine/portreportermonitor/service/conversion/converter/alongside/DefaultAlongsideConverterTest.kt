package nl.teqplay.aisengine.portreportermonitor.service.conversion.converter.alongside

import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType
import nl.teqplay.aisengine.portreportermonitor.client.csi.CachedCsiClient
import nl.teqplay.aisengine.portreportermonitor.createEncounterPortReporterEvent
import nl.teqplay.aisengine.portreportermonitor.createEnrichEncounterEndEvent
import nl.teqplay.aisengine.portreportermonitor.createEnrichEncounterStartEvent
import nl.teqplay.aisengine.portreportermonitor.createShipIdentifiers
import nl.teqplay.aisengine.portreportermonitor.createShipRegisterInfo
import nl.teqplay.aisengine.portreportermonitor.model.EventMetadata
import nl.teqplay.aisengine.portreportermonitor.service.EventMetadataService
import nl.teqplay.aisengine.portreportermonitor.utils.updateEventsUuid
import nl.teqplay.aisengine.testing.event.defaultOtherAisShipIdentifier
import nl.teqplay.aisengine.testing.event.defaultOtherImo
import nl.teqplay.aisengine.testing.event.defaultOtherMmsi
import nl.teqplay.skeleton.common.BaseTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import org.springframework.test.context.TestConstructor
import org.springframework.test.context.bean.override.mockito.MockitoBean
import java.util.stream.Stream

@MockitoBean(types = [CachedCsiClient::class])
@TestConstructor(autowireMode = TestConstructor.AutowireMode.ALL)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class DefaultAlongsideConverterTest(
    private val cachedCsiClient: CachedCsiClient,
) : BaseTest() {
    private val converter = DefaultAlongsideConverter(cachedCsiClient)

    private fun validStartEncounters(): Stream<Arguments> {
        return Stream.of(
            Arguments.of(EncounterType.BUNKER, "bunkerService.ats.vessel"),
            Arguments.of(EncounterType.SWOG, "slops.ats.vessel"),
            Arguments.of(EncounterType.TENDER, "tender.ats.vessel"),
            Arguments.of(EncounterType.WATER, "bunkerPW.ats.vessel"),
            Arguments.of(EncounterType.WASTE, "waste.ats.vessel"),
            Arguments.of(EncounterType.LUBES, "lubesService.ats.vessel"),
            Arguments.of(EncounterType.BARGE_SUPPLY, "supplyBarge.ats.vessel"),
            Arguments.of(EncounterType.BARGE_PUSH, "pushBargeService.ats.vessel"),
        )
    }

    private fun validEndEncounters(): Stream<Arguments> {
        return Stream.of(
            Arguments.of(EncounterType.BUNKER, "bunkerService.atc.vessel"),
            Arguments.of(EncounterType.SWOG, "slops.atc.vessel"),
            Arguments.of(EncounterType.TENDER, "tender.atc.vessel"),
            Arguments.of(EncounterType.WATER, "bunkerPW.atc.vessel"),
            Arguments.of(EncounterType.WASTE, "waste.atc.vessel"),
            Arguments.of(EncounterType.LUBES, "lubesService.atc.vessel"),
            Arguments.of(EncounterType.BARGE_SUPPLY, "supplyBarge.atc.vessel"),
            Arguments.of(EncounterType.BARGE_PUSH, "pushBargeService.atc.vessel"),
        )
    }

    @ParameterizedTest
    @MethodSource("validStartEncounters")
    fun `should create PortReporterEvent on convert EncounterStartEvent with supported encounter type`(encounterType: EncounterType?, expectedEventType: String) {
        whenever(cachedCsiClient.getShipRegister(eq(defaultOtherAisShipIdentifier)))
            .thenReturn(
                createShipRegisterInfo(
                    createShipIdentifiers(
                        mmsi = defaultOtherMmsi.toString(),
                        imo = defaultOtherImo.toString(),
                        name = "SERVICE_VESSEL_1"
                    )
                )
            )

        val enrichedEvent = createEnrichEncounterStartEvent(encounterType = encounterType, getEventMetadata = mock<EventMetadataService>()::requestMetadata)
        val expectedPortReporterEvent = createEncounterPortReporterEvent(expectedEventType, "SERVICE_VESSEL_1")
        val expected = enrichedEvent.copy(portReporterEvents = listOf(expectedPortReporterEvent))

        val result = converter.convert(enrichedEvent).updateEventsUuid(expectedPortReporterEvent.uuid)

        assertEquals(expected, result)
    }

    @ParameterizedTest
    @MethodSource("validEndEncounters")
    fun `should create PortReporterEvent on convert EncounterEndEvent with supported encounter type`(
        encounterType: EncounterType?,
        expectedEventType: String
    ) {
        whenever(cachedCsiClient.getShipRegister(eq(defaultOtherAisShipIdentifier)))
            .thenReturn(
                createShipRegisterInfo(
                    createShipIdentifiers(
                        mmsi = defaultOtherMmsi.toString(),
                        imo = defaultOtherImo.toString(),
                        name = "SERVICE_VESSEL_2"
                    )
                )
            )

        val enrichedEvent = createEnrichEncounterEndEvent(encounterType = encounterType, getEventMetadata = mock<EventMetadataService>()::requestMetadata)
        val expectedPortReporterEvent = createEncounterPortReporterEvent(expectedEventType, "SERVICE_VESSEL_2")
        val expected = enrichedEvent.copy(portReporterEvents = listOf(expectedPortReporterEvent))

        val result = converter.convert(enrichedEvent).updateEventsUuid(expectedPortReporterEvent.uuid)

        assertEquals(expected, result)
    }

    @ParameterizedTest
    @EnumSource(
        value = EncounterType::class,
        mode = EnumSource.Mode.EXCLUDE,
        names = ["BUNKER", "SWOG", "TENDER", "WATER", "WASTE", "LUBES", "BARGE_SUPPLY", "BARGE_PUSH"],
    )
    fun `should NOT create PortReporterEvent on convert EncounterEvent of unsupported encounter type`(encounterType: EncounterType) {
        mock<EventMetadataService>().apply {
            whenever(requestMetadata(any(), any())).thenReturn(EventMetadata(portcallId = "PORTCALL-123"))
        }
        val enrichedStartEvent = createEnrichEncounterStartEvent(encounterType = encounterType, getEventMetadata = mock<EventMetadataService>()::requestMetadata)
        val enrichedEndEvent = createEnrichEncounterEndEvent(encounterType = encounterType, getEventMetadata = mock<EventMetadataService>()::requestMetadata)

        assertThrows<AssertionError> { converter.convert(enrichedStartEvent) }
        assertThrows<AssertionError> { converter.convert(enrichedEndEvent) }
    }
}
