package nl.teqplay.aisengine.portreportermonitor.service.conversion.converter.tug

import io.nats.client.api.KeyValueEntry
import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.aisengine.portreportermonitor.client.csi.CachedCsiClient
import nl.teqplay.aisengine.portreportermonitor.createPort
import nl.teqplay.aisengine.portreportermonitor.createShipRegisterInfo
import nl.teqplay.aisengine.portreportermonitor.model.EnrichEvent
import nl.teqplay.aisengine.portreportermonitor.service.EventMetadataService
import nl.teqplay.aisengine.testing.event.createEncounterEndEvent
import nl.teqplay.aisengine.testing.event.createEncounterStartEvent
import nl.teqplay.skeleton.common.BaseTest
import nl.teqplay.skeleton.nats.NatsKeyValueBucket
import nl.teqplay.skeleton.nats.NatsKeyValueBucketEntry
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.mockito.ArgumentMatchers.anyString
import org.mockito.Mockito.`when`
import org.mockito.kotlin.mock
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.TestConstructor
import org.springframework.test.context.bean.override.mockito.MockitoBean

@ContextConfiguration
@MockitoBean(
    types = [NatsKeyValueBucket::class, CachedCsiClient::class]
)
@TestConstructor(autowireMode = TestConstructor.AutowireMode.ALL)
class DefaultTugConverterTest(
    private val defaultTugConverter: DefaultTugConverter,
    private val tugStandbyKV: NatsKeyValueBucket<String>
) : BaseTest() {

    @TestConfiguration
    class Config {
        @Bean
        fun tugEventConverter(
            tugStandbyKV: NatsKeyValueBucket<String>,
            cachedCsiClient: CachedCsiClient
        ) = DefaultTugConverter(tugStandbyKV, cachedCsiClient)
    }

    @Test
    fun `Given NOT tugstandBy Event to shouldIgnoreEvent() call ShouldIgnoreTugStandBy()`() {
        val tugEvent = createEncounterEndEvent(
            encounterType = EncounterEvent.EncounterType.TUG,
        )
        val enrichEvent = EnrichEvent<EncounterEvent>(
            originalEvent = tugEvent,
            shipRegisterInfo = createShipRegisterInfo(),
            port = createPort(),
            portReporterEvents = emptyList(),
            getEventMetadata = mock<EventMetadataService>()::requestMetadata
        )
        assertTrue(defaultTugConverter.shouldConvert(enrichEvent))
    }

    @Test
    fun `Given tugStandByEvent to shouldConvert() without history of event in NATS KV return false`() {
        val tugEvent = createEncounterStartEvent(
            encounterType = EncounterEvent.EncounterType.TUG,
        )
        val enrichEvent = EnrichEvent<EncounterEvent>(
            originalEvent = tugEvent,
            shipRegisterInfo = createShipRegisterInfo(_id = "1"),
            port = createPort(),
            portReporterEvents = emptyList(),
            getEventMetadata = mock<EventMetadataService>()::requestMetadata
        )
        `when`(tugStandbyKV.get(anyString())).thenReturn(null)
        assertTrue(defaultTugConverter.shouldConvert(enrichEvent))
    }

    @Test
    fun `Given tugStandByEvent to shouldIgnoreTugStandBy() WITH history of event in NATS KV return true`() {
        val tugEvent = createEncounterStartEvent(
            encounterType = EncounterEvent.EncounterType.TUG,
        )
        val enrichEvent = EnrichEvent<EncounterEvent>(
            originalEvent = tugEvent,
            shipRegisterInfo = createShipRegisterInfo(_id = "1"),
            port = createPort(),
            portReporterEvents = emptyList(),
            getEventMetadata = mock<EventMetadataService>()::requestMetadata
        )
        `when`(tugStandbyKV.get(anyString())).thenReturn(
            NatsKeyValueBucketEntry(
                kvEntry = mock<KeyValueEntry>(),
                value = "",
            ),
        )
        assertFalse(defaultTugConverter.shouldConvert(enrichEvent))
    }
}
