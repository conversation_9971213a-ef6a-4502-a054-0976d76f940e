package nl.teqplay.aisengine.portreportermonitor.utils

import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.aisengine.event.interfaces.LocationBasedEvent
import nl.teqplay.aisengine.event.model.encountermetadata.PilotEncounterMetadata
import nl.teqplay.aisengine.portreportermonitor.createEnrichAreaStartEvent
import nl.teqplay.aisengine.portreportermonitor.createEnrichEncounterStartEvent
import nl.teqplay.aisengine.portreportermonitor.createPort
import nl.teqplay.aisengine.portreportermonitor.model.EnrichEvent
import nl.teqplay.aisengine.portreportermonitor.service.EventMetadataService
import nl.teqplay.skeleton.model.Location
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Assertions.fail
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.kotlin.mock
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class InboundBearingTest {
    private val testEvent = createEnrichAreaStartEvent(
        port = createPort(unlocode = "USHOU"),
        getEventMetadata = mock<EventMetadataService>()::requestMetadata
    )

    private fun validHeadings(): Stream<Arguments> {
        return Stream.of(
            Arguments.of(225..359),
            Arguments.of(0..45)
        )
    }

    private fun invalidHeadings(): Stream<Arguments> {
        return Stream.of(
            Arguments.of(46..224)
        )
    }

    @Test
    fun `should create bearing correctly`() {
        val testBearing = "AABBBBB" between 100 and 50
        val expectedInbound = listOf("AABBBBB") to listOf(
            100 to 359,
            0 to 50
        )

        assertEquals(expectedInbound, testBearing)
    }

    @Test
    fun `should return null when checking bearing when no heading provided`() {
        val result = isInbound(testEvent, null as Int?, null)
        assertNull(result)
    }

    @ParameterizedTest
    @MethodSource("validHeadings")
    fun `heading should be in bearing`(headingRange: IntRange) {
        for (heading in headingRange) {
            val result = isInbound(testEvent, heading, null) == true
            assertTrue(result, "$heading is not between 225 and 45\"")
        }
    }

    @ParameterizedTest
    @MethodSource("invalidHeadings")
    fun `heading should NOT be in bearing`(headingRange: IntRange) {
        for (heading in headingRange) {
            val result = isInbound(testEvent, heading, null) == false
            assertTrue(result, "$heading is not between 225 and 45")
        }
    }

    private fun scheldtTestCases(): Stream<Arguments> {
        return Stream.of(
            Arguments.of(
                // BEGNE inbound lower bound heading
                createEnrichEncounterStartEvent(
                    encounterType = EncounterEvent.EncounterType.PILOT,
                    location = Location(52.0, 3.72),
                    port = createPort(unlocode = "BEGNE"),
                    metadata = PilotEncounterMetadata(45.0f),
                    getEventMetadata = mock<EventMetadataService>()::requestMetadata
                ),
                true
            ),
            Arguments.of(
                // BEGNE inbound upper bound heading
                createEnrichEncounterStartEvent(
                    encounterType = EncounterEvent.EncounterType.PILOT,
                    location = Location(52.0, 3.72),
                    port = createPort(unlocode = "BEGNE"),
                    metadata = PilotEncounterMetadata(225.0f),
                    getEventMetadata = mock<EventMetadataService>()::requestMetadata
                ),
                true
            ),
            Arguments.of(
                // BEGNE outbound lower bound heading
                createEnrichEncounterStartEvent(
                    encounterType = EncounterEvent.EncounterType.PILOT,
                    location = Location(52.0, 3.72),
                    port = createPort(unlocode = "BEGNE"),
                    metadata = PilotEncounterMetadata(44.0f),
                    getEventMetadata = mock<EventMetadataService>()::requestMetadata
                ),
                false
            ),
            Arguments.of(
                // BEGNE outbound upper bound heading
                createEnrichEncounterStartEvent(
                    encounterType = EncounterEvent.EncounterType.PILOT,
                    location = Location(52.0, 3.72),
                    port = createPort(unlocode = "BEGNE"),
                    metadata = PilotEncounterMetadata(226.0f),
                    getEventMetadata = mock<EventMetadataService>()::requestMetadata
                ),
                false
            ),
            Arguments.of(
                // Steenbank inbound lower bound heading
                createEnrichEncounterStartEvent(
                    encounterType = EncounterEvent.EncounterType.PILOT,
                    location = Location(52.0, 4.0),
                    port = createPort(unlocode = "BEANR"),
                    metadata = PilotEncounterMetadata(90.0f),
                    getEventMetadata = mock<EventMetadataService>()::requestMetadata
                ),
                true
            ),
            Arguments.of(
                // Steenbank inbound upper bound heading
                createEnrichEncounterStartEvent(
                    encounterType = EncounterEvent.EncounterType.PILOT,
                    location = Location(52.0, 4.0),
                    port = createPort(unlocode = "BEANR"),
                    metadata = PilotEncounterMetadata(270.0f),
                    getEventMetadata = mock<EventMetadataService>()::requestMetadata
                ),
                true
            ),
            Arguments.of(
                // Steenbank outbound lower bound heading
                createEnrichEncounterStartEvent(
                    encounterType = EncounterEvent.EncounterType.PILOT,
                    location = Location(52.0, 4.0),
                    port = createPort(unlocode = "BEANR"),
                    metadata = PilotEncounterMetadata(89.0f),
                    getEventMetadata = mock<EventMetadataService>()::requestMetadata
                ),
                false
            ),
            Arguments.of(
                // Steenbank outbound upper bound heading
                createEnrichEncounterStartEvent(
                    encounterType = EncounterEvent.EncounterType.PILOT,
                    location = Location(52.0, 4.0),
                    port = createPort(unlocode = "BEANR"),
                    metadata = PilotEncounterMetadata(89.0f),
                    getEventMetadata = mock<EventMetadataService>()::requestMetadata
                ),
                false
            ),
            Arguments.of(
                // Wandelaar inbound lower bound heading
                createEnrichEncounterStartEvent(
                    encounterType = EncounterEvent.EncounterType.PILOT,
                    location = Location(51.0, 2.5),
                    port = createPort(unlocode = "BEANR"),
                    metadata = PilotEncounterMetadata(60.0f),
                    getEventMetadata = mock<EventMetadataService>()::requestMetadata
                ),
                true
            ),
            Arguments.of(
                // Wandelaar inbound upper bound heading
                createEnrichEncounterStartEvent(
                    encounterType = EncounterEvent.EncounterType.PILOT,
                    location = Location(51.0, 2.5),
                    port = createPort(unlocode = "BEANR"),
                    metadata = PilotEncounterMetadata(180.0f),
                    getEventMetadata = mock<EventMetadataService>()::requestMetadata
                ),
                true
            ),
            Arguments.of(
                // Wandelaar outbound lower bound heading
                createEnrichEncounterStartEvent(
                    encounterType = EncounterEvent.EncounterType.PILOT,
                    location = Location(51.0, 2.5),
                    port = createPort(unlocode = "BEANR"),
                    metadata = PilotEncounterMetadata(59.0f),
                    getEventMetadata = mock<EventMetadataService>()::requestMetadata
                ),
                false
            ),
            Arguments.of(
                // Wandelaar outbound upper bound heading
                createEnrichEncounterStartEvent(
                    encounterType = EncounterEvent.EncounterType.PILOT,
                    location = Location(51.0, 2.5),
                    port = createPort(unlocode = "BEANR"),
                    metadata = PilotEncounterMetadata(181.0f),
                    getEventMetadata = mock<EventMetadataService>()::requestMetadata
                ),
                false
            )
        )
    }

    @ParameterizedTest
    @MethodSource("scheldtTestCases")
    fun `should return expected result on scheldt port event`(event: EnrichEvent<LocationBasedEvent>, expected: Boolean?) {
        val eventHeading = if (event.originalEvent is AreaEvent) {
            (event.originalEvent as AreaEvent).heading?.toFloat()
        } else if (event.originalEvent is EncounterEvent) {
            val castEvent = event.originalEvent as EncounterEvent

            if (castEvent.metadata is PilotEncounterMetadata) {
                (castEvent.metadata as PilotEncounterMetadata).heading
            } else {
                fail("Provided event is an encounter event but not one containing a heading")
            }
        } else {
            fail("Can't test an event without a heading")
        }

        val result = isInbound(event, eventHeading, event.originalEvent.location)

        assertEquals(expected, result)
    }
}
