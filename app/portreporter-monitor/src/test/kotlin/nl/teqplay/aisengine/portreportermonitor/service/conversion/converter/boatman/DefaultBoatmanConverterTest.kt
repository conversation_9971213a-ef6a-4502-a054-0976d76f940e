package nl.teqplay.aisengine.portreportermonitor.service.conversion.converter.boatman

import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType
import nl.teqplay.aisengine.event.model.encountermetadata.BoatmanEncounterMetadata
import nl.teqplay.aisengine.portreportermonitor.client.csi.CachedCsiClient
import nl.teqplay.aisengine.portreportermonitor.createEncounterPortReporterEvent
import nl.teqplay.aisengine.portreportermonitor.createEnrichEncounterEndEvent
import nl.teqplay.aisengine.portreportermonitor.createEnrichEncounterStartEvent
import nl.teqplay.aisengine.portreportermonitor.createShipIdentifiers
import nl.teqplay.aisengine.portreportermonitor.createShipRegisterInfo
import nl.teqplay.aisengine.portreportermonitor.model.constant.EventType
import nl.teqplay.aisengine.portreportermonitor.service.EventMetadataService
import nl.teqplay.aisengine.portreportermonitor.utils.updateEventsUuid
import nl.teqplay.aisengine.testing.event.defaultMmsi
import nl.teqplay.aisengine.testing.event.defaultOtherAisShipIdentifier
import nl.teqplay.aisengine.testing.event.defaultOtherImo
import nl.teqplay.aisengine.testing.event.defaultOtherMmsi
import nl.teqplay.skeleton.common.BaseTest
import nl.teqplay.skeleton.nats.NatsClientMock
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import org.springframework.test.context.TestConstructor
import org.springframework.test.context.bean.override.mockito.MockitoBean
import java.util.stream.Stream

@MockitoBean(types = [CachedCsiClient::class])
@TestConstructor(autowireMode = TestConstructor.AutowireMode.ALL)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class DefaultBoatmanConverterTest(
    private val cachedCsiClient: CachedCsiClient,
) : BaseTest() {
    private val natsClient = NatsClientMock()
    private val kvBucket = natsClient.keyValueBucket(
        name = "portreporter-monitor-encounter-concurrency",
        serializer = { _: Set<Int> -> ByteArray(0) },
        deserializer = { _: ByteArray -> emptySet<Int>() }
    )
    private val converter = DefaultBoatmanConverter(cachedCsiClient, kvBucket)

    private fun invalidStartBoatmanEncounters(): Stream<Arguments> {
        return Stream.of(
            Arguments.of(BoatmanEncounterMetadata(false, 2, true)),
            Arguments.of(BoatmanEncounterMetadata(true, 2, false)),
            Arguments.of(BoatmanEncounterMetadata(false, 2, false))
        )
    }

    @BeforeEach
    fun init() {
        kvBucket.keys().forEach(kvBucket::delete)
    }

    @Test
    fun `should create PortReporterEvent on convert EncounterStartEvent with type Boatman and correct metadata`() {
        whenever(cachedCsiClient.getShipRegister(eq(defaultOtherAisShipIdentifier)))
            .thenReturn(
                createShipRegisterInfo(
                    createShipIdentifiers(
                        mmsi = defaultOtherMmsi.toString(),
                        imo = defaultOtherImo.toString(),
                        name = "BOATMAN_1"
                    )
                )
            )

        val metadata = BoatmanEncounterMetadata(true, 2, true)
        val enrichedEvent = createEnrichEncounterStartEvent(encounterType = EncounterType.BOATMAN, metadata = metadata, getEventMetadata = mock<EventMetadataService>()::requestMetadata)
        val expectedPortReporterEvent = createEncounterPortReporterEvent("firstLineSecured.at.vessel", "BOATMAN_1")
        val expected = enrichedEvent.copy(portReporterEvents = listOf(expectedPortReporterEvent))

        val result = converter.convert(enrichedEvent).updateEventsUuid(expectedPortReporterEvent.uuid)

        assertEquals(expected, result)
    }

    @Test
    fun `should not create PortReporterEvent on convert EncounterEndEvent with type Boatman and correct metadata when start event has not been fired`() {
        whenever(cachedCsiClient.getShipRegister(eq(defaultOtherAisShipIdentifier)))
            .thenReturn(
                createShipRegisterInfo(
                    createShipIdentifiers(
                        mmsi = defaultOtherMmsi.toString(),
                        imo = defaultOtherImo.toString(),
                        name = "BOATMAN_1"
                    )
                )
            )

        val metadata = BoatmanEncounterMetadata(true, 2, true)
        val enrichedEvent = createEnrichEncounterEndEvent(encounterType = EncounterType.BOATMAN, metadata = metadata, getEventMetadata = mock<EventMetadataService>()::requestMetadata)
        val expectedPortReporterEvent = createEncounterPortReporterEvent(EventType.LASTLINE_SECURED_AT, "BOATMAN_1")

        val result = converter.convert(enrichedEvent).updateEventsUuid(expectedPortReporterEvent.uuid)

        assertEquals(enrichedEvent, result)
    }

    @Test
    fun `should create PortReporterEvent on convert EncounterEndEvent with type Boatman and correct metadata when start event has been fired`() {
        whenever(cachedCsiClient.getShipRegister(eq(defaultOtherAisShipIdentifier)))
            .thenReturn(
                createShipRegisterInfo(
                    createShipIdentifiers(
                        mmsi = defaultOtherMmsi.toString(),
                        imo = defaultOtherImo.toString(),
                        name = "BOATMAN_1"
                    )
                )
            )

        kvBucket.put("$defaultMmsi-boatmen", setOf(defaultOtherMmsi))

        val metadata = BoatmanEncounterMetadata(true, 2, true)
        val enrichedEvent = createEnrichEncounterEndEvent(encounterType = EncounterType.BOATMAN, metadata = metadata, getEventMetadata = mock<EventMetadataService>()::requestMetadata)
        val expectedPortReporterEvent = createEncounterPortReporterEvent(EventType.LASTLINE_SECURED_AT, "BOATMAN_1")
        val expected = enrichedEvent.copy(portReporterEvents = listOf(expectedPortReporterEvent))

        val result = converter.convert(enrichedEvent).updateEventsUuid(expectedPortReporterEvent.uuid)

        assertEquals(expected, result)
    }

    @ParameterizedTest
    @MethodSource("invalidStartBoatmanEncounters")
    fun `should NOT create PortReporterEvent on convert EncounterStartEvent with type Boatman and incorrect metadata`(metadata: BoatmanEncounterMetadata) {
        val enrichedEvent = createEnrichEncounterStartEvent(encounterType = EncounterType.BOATMAN, metadata = metadata, getEventMetadata = mock<EventMetadataService>()::requestMetadata)

        assertEquals(enrichedEvent, converter.convert(enrichedEvent))
    }
}
