package nl.teqplay.aisengine.portreportermonitor.service

import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.interfaces.UniqueBerthEvent
import nl.teqplay.aisengine.event.model.AisLostEvent
import nl.teqplay.aisengine.event.model.AreaStartEvent
import nl.teqplay.aisengine.event.model.UniqueBerthStartEvent
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.portreportermonitor.client.CachedMap
import nl.teqplay.aisengine.portreportermonitor.client.csi.CachedCsiClient
import nl.teqplay.aisengine.portreportermonitor.client.poma.CachedPomaClient
import nl.teqplay.aisengine.portreportermonitor.configuration.PortreporterMonitorProperties
import nl.teqplay.aisengine.portreportermonitor.createLock
import nl.teqplay.aisengine.portreportermonitor.createPort
import nl.teqplay.aisengine.portreportermonitor.createPortReporterEvent
import nl.teqplay.aisengine.portreportermonitor.createShipIdentifiers
import nl.teqplay.aisengine.portreportermonitor.createShipRegisterInfo
import nl.teqplay.aisengine.portreportermonitor.model.EnrichEvent
import nl.teqplay.aisengine.portreportermonitor.model.EventMetadata
import nl.teqplay.aisengine.portreportermonitor.service.conversion.BerthEventConversionPicker
import nl.teqplay.aisengine.portreportermonitor.service.conversion.LockEventConversionPicker
import nl.teqplay.aisengine.portreportermonitor.service.conversion.converter.berth.BerthEventConverter
import nl.teqplay.aisengine.portreportermonitor.service.conversion.converter.lock.DefaultLockConverter
import nl.teqplay.aisengine.testing.event.createAisLostEvent
import nl.teqplay.aisengine.testing.event.createAreaStartEvent
import nl.teqplay.aisengine.testing.event.createUniqueBerthStartEvent
import nl.teqplay.aisengine.testing.event.defaultBerthAreaIdentifier
import nl.teqplay.csi.model.ship.info.ShipRegisterInfo
import nl.teqplay.poma.api.v1.Berth
import nl.teqplay.poma.api.v1.Location
import nl.teqplay.poma.api.v1.Lock
import nl.teqplay.poma.api.v1.Port
import nl.teqplay.skeleton.common.BaseTest
import nl.teqplay.smartfleet.model.event.FleetMinimal
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.Mockito.anyString
import org.mockito.Mockito.`when`
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.never
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.TestConstructor
import org.springframework.test.context.bean.override.mockito.MockitoBean

@ContextConfiguration
@MockitoBean(
    types = [
        CachedCsiClient::class,
        PortService::class,
        EventConverterService::class,
        BerthEventConversionPicker::class,
        BerthEventConverter::class,
        CachedPomaClient::class,
        LockEventConversionPicker::class,
        DefaultLockConverter::class,
        TrueDestinationService::class,
    ]
)
@TestConstructor(autowireMode = TestConstructor.AutowireMode.ALL)
class EventEnricherServiceTest(
    private val eventEnricherService: EventEnricherService,

    private val cachedCsiClient: CachedCsiClient,
    private val portService: PortService,
    private val eventConverterService: EventConverterService,
    private val lockEventConversionPicker: LockEventConversionPicker,
    private val berthEventConversionPicker: BerthEventConversionPicker,
    private val defaultBerthConverter: BerthEventConverter,
    private val defaultLockConverter: DefaultLockConverter,
    private val eventMetadataService: EventMetadataService,
    private val cachedPomaClient: CachedPomaClient
) : BaseTest() {

    companion object {
        private val config = PortreporterMonitorProperties(
            natsSubject = "",
            ignoredSubjects = listOf("event.ais.lost"),
            ignoredEventTypes = listOf(),
        )
    }

    @TestConfiguration
    class Config {

        @Bean
        fun eventEnricherService(
            eventConverterService: EventConverterService,
            eventMetadataService: EventMetadataService,
            cachedCsiClient: CachedCsiClient,
            cachedPomaClient: CachedPomaClient,
            portService: PortService,
            trueDestinationService: TrueDestinationService,
        ) = EventEnricherService(eventConverterService, eventMetadataService, config, cachedCsiClient, cachedPomaClient, portService, trueDestinationService)

        @Bean
        fun eventMetadataService() = mock<EventMetadataService>().apply {
            whenever(requestMetadata(any(), any())).thenReturn(EventMetadata("portcallId", null))
        }
    }

    @Test
    fun `Given an event which is ignored to the enrichEvent() expect enriches to do callback with null`() {
        val startEvent = createAisLostEvent()
        val expectedResult = emptyList<EnrichEvent<AisLostEvent>>()

        val outputEnrichEvent = eventEnricherService.enrichEvent(startEvent)
        verify(eventConverterService, never()).getEventConversionPicker(any())
        validator(expectedResult, outputEnrichEvent)
    }

    @Test
    fun `Given an event which NOT ignored with unlocode to the enrichEvent() expect enriches to enrich the event`() {
        val port = createPort()
        val startEvent = createUniqueBerthStartEvent(area = defaultBerthAreaIdentifier.copy(unlocode = port.unlocode))
        val shipRegisterInfo = createShipRegisterInfo(
            identifiers = createShipIdentifiers(
                mmsi = startEvent.ship.mmsi.toString(),
                imo = startEvent.ship.imo?.toString()
            )
        )
        val expectedResult = listOf(
            EnrichEvent(
                originalEvent = startEvent,
                port = port,
                shipRegisterInfo = shipRegisterInfo,
                portReporterEvents = emptyList(),
                getEventMetadata = mock<EventMetadataService>()::requestMetadata,
            ),
        )
        `when`(eventConverterService.getEventConversionPicker(any<UniqueBerthEvent>())).thenReturn(berthEventConversionPicker)
        `when`(berthEventConversionPicker.getPortSpecificConverter(any())).thenReturn(defaultBerthConverter)
        `when`(portService.getByUnlocode(any())).thenAnswer { createPort(unlocode = it.getArgument(0)) }

        `when`(cachedCsiClient.getShipRegister(any())).thenAnswer {
            val shipIdentifier = it.getArgument<AisShipIdentifier?>(0)
            createShipRegisterInfo(
                identifiers = createShipIdentifiers(
                    mmsi = shipIdentifier.mmsi.toString(),
                    imo = shipIdentifier.imo?.toString()
                )
            )
        }

        `when`(defaultBerthConverter.convert(any())).thenAnswer { it.getArgument<EnrichEvent<UniqueBerthStartEvent>>(0) }

        val outputEnrichEvent = eventEnricherService.enrichEvent(startEvent)
        validator(expectedResult, outputEnrichEvent)
    }

    @Test
    fun `Given an event which NOT ignored WITHOUT unlocode to the enrichEvent() expect enriches to enrich the event`() {
        val startEvent = createUniqueBerthStartEvent()
        val port = createPort(
            area = listOf(
                Location(startEvent.location.lat - 1, startEvent.location.lon - 1),
                Location(startEvent.location.lat + 1, startEvent.location.lon - 1),
                Location(startEvent.location.lat + 1, startEvent.location.lon + 1),
                Location(startEvent.location.lat - 1, startEvent.location.lon + 1),
            ),
        )
        val shipRegisterInfo = createShipRegisterInfo(
            identifiers = createShipIdentifiers(
                mmsi = startEvent.ship.mmsi.toString(),
                imo = startEvent.ship.imo?.toString()
            )
        )
        val expectedResult = listOf(
            EnrichEvent(
                originalEvent = startEvent,
                port = port,
                shipRegisterInfo = shipRegisterInfo,
                portReporterEvents = emptyList(),
                getEventMetadata = mock<EventMetadataService>()::requestMetadata,
            ),
        )
        `when`(eventConverterService.getEventConversionPicker(any<UniqueBerthEvent>())).thenReturn(berthEventConversionPicker)
        `when`(berthEventConversionPicker.getPortSpecificConverter(any())).thenReturn(defaultBerthConverter)
        `when`(portService.getPortsByScaledLocation(any(), any())).thenReturn(setOf(port))
        `when`(cachedCsiClient.getShipRegister(any())).thenAnswer {
            val shipIdentifier = it.getArgument<AisShipIdentifier?>(0)
            createShipRegisterInfo(
                identifiers = createShipIdentifiers(
                    mmsi = shipIdentifier.mmsi.toString(),
                    imo = shipIdentifier.imo?.toString()
                )
            )
        }
        `when`(defaultBerthConverter.convert(any())).thenAnswer { it.getArgument<EnrichEvent<UniqueBerthStartEvent>>(0) }

        val outputEnrichEvent = eventEnricherService.enrichEvent(startEvent)
        validator(expectedResult, outputEnrichEvent)
    }

    private val portBEGNE = createPort(
        name = "BEGNE",
        unlocode = "BEGNE",
    )
    private val portNLTNZ = createPort(
        name = "NLTNZ",
        unlocode = "NLTNZ",
    )

    private val pomaLock = createLock(
        _id = "1EF11606A4B8978FC922C129CFCDB2B69673F082",
        uniqueId = "51.32857_3.81693",
        ports = listOf(portBEGNE.unlocode!!, portNLTNZ.unlocode!!),
    )

    @Test
    fun `Given an event which NOT ignored lockEvent, for BEGNE (NLTNZ) to the enrichEvent() expect enriches to enrich two events`() {
        val startEvent = createAreaStartEvent(
            area = AreaIdentifier(
                id = "1EF11606A4B8978FC922C129CFCDB2B69673F082",
                type = AreaIdentifier.AreaType.LOCK,
                name = "WESTSLUIS TERNEUZEN",
                unlocode = "NLTNZ",
            ),
        )

        val shipRegisterInfo = createShipRegisterInfo(
            identifiers = createShipIdentifiers(
                mmsi = startEvent.ship.mmsi.toString(),
                imo = startEvent.ship.imo?.toString()
            )
        )
        val expectedResult = listOf(
            EnrichEvent<AreaStartEvent>(
                originalEvent = startEvent,
                port = portBEGNE,
                shipRegisterInfo = shipRegisterInfo,
                portReporterEvents = emptyList(),
                getEventMetadata = mock<EventMetadataService>()::requestMetadata,
            ),
            EnrichEvent<AreaStartEvent>(
                originalEvent = startEvent,
                port = portNLTNZ,
                shipRegisterInfo = shipRegisterInfo,
                portReporterEvents = emptyList(),
                getEventMetadata = mock<EventMetadataService>()::requestMetadata,
            ),
        )
        `when`(eventConverterService.getEventConversionPicker(any<AreaEvent>())).thenReturn(lockEventConversionPicker)
        `when`(lockEventConversionPicker.getPortSpecificConverter(any())).thenReturn(defaultLockConverter)
        `when`(cachedCsiClient.getShipRegister(any())).thenAnswer { it ->
            val shipIdentifier = it.getArgument<AisShipIdentifier?>(0)
            createShipRegisterInfo(
                identifiers = createShipIdentifiers(
                    mmsi = shipIdentifier.mmsi.toString(),
                    imo = shipIdentifier.imo?.toString()
                )
            )
        }

        val lockMap = CachedMap({ listOf(pomaLock) }) { lock -> lock.filter { it._id != null }.associateBy { it._id!! } }
        `when`(cachedPomaClient.allLocksById).thenReturn(lockMap)
        `when`(defaultLockConverter.convert(any())).thenAnswer { it.getArgument<EnrichEvent<AreaStartEvent>>(0) }

        `when`(portService.getByUnlocode(anyString())).thenAnswer {
            val unlocode = it.getArgument<String>(0)
            createPort(name = unlocode, unlocode = unlocode)
        }

        val outputEnrichEvent = eventEnricherService.enrichEvent(startEvent)
        validator(expectedResult, outputEnrichEvent)
    }

    @Test
    fun `Given an event which NOT ignored lockEvent, for BEGNE (NLTNZ) to the enrichEvent() for ship with trueDestination expect multiple events`() {
        val startEvent = createAreaStartEvent(
            area = AreaIdentifier(
                id = "1EF11606A4B8978FC922C129CFCDB2B69673F082",
                type = AreaIdentifier.AreaType.LOCK,
                name = "WESTSLUIS TERNEUZEN",
                unlocode = "NLTNZ",
            ),
        )

        val shipRegisterInfo = createShipRegisterInfo(
            identifiers = createShipIdentifiers(
                mmsi = startEvent.ship.mmsi.toString(),
                imo = startEvent.ship.imo?.toString()
            )
        )
        val expectedResult = listOf(
            EnrichEvent(
                originalEvent = startEvent,
                port = portBEGNE,
                shipRegisterInfo = shipRegisterInfo,
                portReporterEvents = emptyList(),
                getEventMetadata = mock<EventMetadataService>()::requestMetadata,
            ),
            EnrichEvent(
                originalEvent = startEvent,
                port = portNLTNZ,
                shipRegisterInfo = shipRegisterInfo,
                portReporterEvents = emptyList(),
                getEventMetadata = mock<EventMetadataService>()::requestMetadata,
            )
        )
        `when`(eventConverterService.getEventConversionPicker(any<AreaEvent>())).thenReturn(lockEventConversionPicker)
        `when`(lockEventConversionPicker.getPortSpecificConverter(any())).thenReturn(defaultLockConverter)
        `when`(cachedCsiClient.getShipRegister(any())).thenAnswer {
            val shipIdentifier = it.getArgument<AisShipIdentifier?>(0)
            createShipRegisterInfo(
                identifiers = createShipIdentifiers(
                    mmsi = shipIdentifier.mmsi.toString(),
                    imo = shipIdentifier.imo?.toString()
                )
            )
        }

        val lockMap = CachedMap({ listOf(pomaLock) }) { lock -> lock.filter { it._id != null }.associateBy { it._id!! } }
        `when`(cachedPomaClient.allLocksById).thenReturn(lockMap)
        `when`(defaultLockConverter.convert(any())).thenAnswer { it.getArgument<EnrichEvent<AreaStartEvent>>(0) }

        `when`(portService.getByUnlocode(anyString())).thenAnswer {
            val unlocode = it.getArgument<String>(0)
            createPort(name = unlocode, unlocode = unlocode)
        }

        val outputEnrichEvent = eventEnricherService.enrichEvent(startEvent)
        validator(expectedResult, outputEnrichEvent)
    }

    @Test
    fun `enrichEvent - supply fleetIds from metadata`() {
        val fleetIds = listOf(
            FleetMinimal(
                id = "id",
                domain = "domain",
                name = "name",
                userId = "userId",
                companyId = "companyId",
                type = FleetMinimal.Type.CONTAINS,
            ),
        )

        val port = createPort()
        val startEvent = createUniqueBerthStartEvent(area = defaultBerthAreaIdentifier.copy(unlocode = port.unlocode))
        val shipRegisterInfo = createShipRegisterInfo(
            identifiers = createShipIdentifiers(
                mmsi = startEvent.ship.mmsi.toString(),
                imo = startEvent.ship.imo?.toString(),
            ),
        )

        val portReporterEvent = createPortReporterEvent()
        val expectedResult = listOf(
            EnrichEvent(
                originalEvent = startEvent,
                port = port,
                shipRegisterInfo = shipRegisterInfo,
                portReporterEvents = listOf(portReporterEvent),
                getEventMetadata = eventMetadataService::requestMetadata,
            ),
        )

        whenever(eventConverterService.getEventConversionPicker(any<UniqueBerthEvent>())).thenReturn(berthEventConversionPicker)
        whenever(eventMetadataService.requestMetadata(any(), any())).thenReturn(EventMetadata(null, fleetIds))
        whenever(cachedCsiClient.getShipRegister(any())).thenReturn(shipRegisterInfo)
        whenever(berthEventConversionPicker.getPortSpecificConverter(any())).thenReturn(defaultBerthConverter)
        `when`(defaultBerthConverter.convert(any())).thenAnswer { it.getArgument<EnrichEvent<UniqueBerthStartEvent>>(0).copy(portReporterEvents = listOf(portReporterEvent)) }
        `when`(portService.getByUnlocode(any())).thenAnswer { createPort(unlocode = it.getArgument(0)) }

        val outputEnrichEvent = eventEnricherService.enrichEvent(startEvent)
        validator(expectedResult, outputEnrichEvent)
        assertEquals(fleetIds, outputEnrichEvent.first().portReporterEvents.first().context?.fleetIds)
    }

    @Test
    fun `Given an area event with a null id to enrichEvent() expect enriches to enrich the event for the port in the event only`() {
        val port = createPort(unlocode = "NLRTM")
        val startEvent = createAreaStartEvent(area = AreaIdentifier(id = null, type = AreaIdentifier.AreaType.LOCK, unlocode = "NLRTM"))
        val shipRegisterInfo = createShipRegisterInfo(
            identifiers = createShipIdentifiers(
                mmsi = startEvent.ship.mmsi.toString(),
                imo = startEvent.ship.imo?.toString()
            )
        )
        val expectedResult = listOf(
            EnrichEvent(
                originalEvent = startEvent,
                port = port,
                shipRegisterInfo = shipRegisterInfo,
                portReporterEvents = emptyList(),
                getEventMetadata = mock<EventMetadataService>()::requestMetadata,
            ),
        )
        `when`(eventConverterService.getEventConversionPicker(any<AreaEvent>())).thenReturn(lockEventConversionPicker)
        `when`(lockEventConversionPicker.getPortSpecificConverter(any())).thenReturn(defaultLockConverter)
        `when`(portService.getByUnlocode(any())).thenAnswer { it -> createPort(unlocode = it.getArgument(0)) }

        `when`(cachedCsiClient.getShipRegister(any())).thenAnswer { it ->
            val shipIdentifier = it.getArgument<AisShipIdentifier?>(0)
            createShipRegisterInfo(
                identifiers = createShipIdentifiers(
                    mmsi = shipIdentifier.mmsi.toString(),
                    imo = shipIdentifier.imo?.toString()
                )
            )
        }
        `when`(defaultLockConverter.convert(any())).thenAnswer { it.getArgument<EnrichEvent<AreaStartEvent>>(0) }
        val lockMap: CachedMap<Lock, String, Lock> = mock<CachedMap<Lock, String, Lock>>()
        `when`(cachedPomaClient.allLocksById).thenReturn(lockMap)
        val berthMap: CachedMap<Berth, String, Berth> = mock<CachedMap<Berth, String, Berth>>()
        `when`(cachedPomaClient.allBerthsById).thenReturn(berthMap)

        val outputEnrichEvent = eventEnricherService.enrichEvent(startEvent)
        verify(cachedPomaClient.allLocksById, never()).get(anyString())
        verify(cachedPomaClient.allBerthsById, never()).get(anyString())
        validator(expectedResult, outputEnrichEvent)
    }

    private fun <T : Event> validator(expectedValue: List<EnrichEvent<T>>?, actualValue: List<EnrichEvent<T>>?) {
        assertEquals(expectedValue?.size, actualValue?.size)
        actualValue?.forEachIndexed { index, item ->
            validator(expectedValue?.get(index), item)
        }
    }

    private fun <T : Event> validator(expectedValue: EnrichEvent<T>?, actualValue: EnrichEvent<T>?) {
        if (expectedValue == null) {
            assertEquals(actualValue, expectedValue)
        } else {
            compare(expectedValue.port, actualValue?.port)
            compare(expectedValue.shipRegisterInfo, actualValue?.shipRegisterInfo)
        }
    }

    private fun compare(expected: Port?, actual: Port?) {
        assertEquals(expected?._id, actual?._id)
        assertEquals(expected?.displayName, actual?.displayName)
        assertEquals(expected?.name, actual?.name)
    }

    private fun compare(expected: ShipRegisterInfo?, actual: ShipRegisterInfo?) {
        assertEquals(expected?.identifiers?.name, actual?.identifiers?.name)
        assertEquals(expected?.identifiers?.imo, actual?.identifiers?.imo)
        assertEquals(expected?.identifiers?.mmsi, actual?.identifiers?.mmsi)
    }
}
