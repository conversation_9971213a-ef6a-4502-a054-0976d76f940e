package nl.teqplay.aisengine.portreportermonitor.service.conversion.converter.portcallplus

import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.aisengine.event.interfaces.PortcallPlusEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusVisitsUpdateEvent
import nl.teqplay.aisengine.portreportermonitor.createPort
import nl.teqplay.aisengine.portreportermonitor.service.conversion.TestEventEnricher
import nl.teqplay.aisengine.shiphistory.client.ShipHistoricClient
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.nio.file.Files
import java.nio.file.Paths

class VisitsUpdatePortcallPlusConverterTest : TestEventEnricher() {
    private val objectMapper = jacksonObjectMapper()
        .registerModules(JavaTimeModule())
    private val PORT_USHOU = createPort(_id = "2", unlocode = "USHOU", name = "USHOU")

    private val shipHistoricClient = mock<ShipHistoricClient>().also {
        whenever(it.findHistoricByMmsi(any(), any())).thenReturn(listOf())
    }
    val converter = VisitsUpdatePortcallPlusConverter(shipHistoricClient)

    @Test
    fun `Given an visits update event make sure agent is set`() {
        val eventJsonFile = this.javaClass.classLoader.getResource("events/BEANR93117012405200T-PortcallPlusVisitsUpdateEvent.json")?.toURI()
        val eventJson = Files.readString(Paths.get(eventJsonFile!!))
        val input = objectMapper.readValue<PortcallPlusVisitsUpdateEvent>(eventJson)
            .asEnrichedEvent<PortcallPlusEvent>(PORT_USHOU)

        val result = converter.convert(input)
        val portReporterEvent = result.portReporterEvents.first()

        assertEquals(1, result.portReporterEvents.size)
        assertEquals("Vopak agent", portReporterEvent.context?.agent)
        assertEquals(null, portReporterEvent.context?.terminalName)
        assertEquals(null, portReporterEvent.context?.terminalId)
    }
}
