package nl.teqplay.aisengine.portreportermonitor.utils
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import nl.teqplay.poma.api.v1.Location as PomaLocation

class LocationUtilsTest {

    @Test
    fun `given area with missing points for bucketing to Location addMissignKeysBetweenPoints extend with aditional points`() {
        val area = listOf(
            PomaLocation(1.0, 1.3),
            PomaLocation(1.0, 1.3),
            PomaLocation(1.3, 1.0),
            PomaLocation(1.3, 1.3),
        )

        val expectedResult = listOf(
            PomaLocation(1.0, 1.0),
            PomaLocation(1.1, 1.0),
            PomaLocation(1.2, 1.0),
            PomaLocation(1.3, 1.0),
            PomaLocation(1.0, 1.1),
            PomaLocation(1.1, 1.1),
            PomaLocation(1.2, 1.1),
            PomaLocation(1.3, 1.1),
            PomaLocation(1.0, 1.2),
            PomaLocation(1.1, 1.2),
            PomaLocation(1.2, 1.2),
            PomaLocation(1.3, 1.2),
            PomaLocation(1.0, 1.3),
            PomaLocation(1.1, 1.3),
            PomaLocation(1.2, 1.3),
            PomaLocation(1.3, 1.3),

        )
        val updatedAreaPoints = area.addMissingKeysBetweenPoints(1)
        assertEquals(updatedAreaPoints.size, expectedResult.size)

        expectedResult.forEachIndexed { i, item ->
            assertEquals(item.latitude, updatedAreaPoints.elementAtOrNull(i)?.latitude)
            assertEquals(item.longitude, updatedAreaPoints.elementAtOrNull(i)?.longitude)
        }
    }

    @Test
    fun `given area to squireContainingArea() return locations for min and maximum`() {
        val area = listOf(
            PomaLocation(1.0, 1.0),
            PomaLocation(0.0, 3.0),
            PomaLocation(3.0, 1.0),
        )
        val expectedResult = Pair(
            PomaLocation(0.0, 1.0),
            PomaLocation(3.0, 3.0),
        )

        assertEquals(expectedResult.first.latitude, area.squireContainingArea().first.latitude)
        assertEquals(expectedResult.first.longitude, area.squireContainingArea().first.longitude)
        assertEquals(expectedResult.second.latitude, area.squireContainingArea().second.latitude)
        assertEquals(expectedResult.second.longitude, area.squireContainingArea().second.longitude)
    }

    val approachNLAMS = listOf(
        PomaLocation(52.4617623114352, 4.5549488067627),
        PomaLocation(52.4625467421233, 4.56310272216797),
        PomaLocation(52.4678282115325, 4.56421852111816),
        PomaLocation(52.4678805001408, 4.5567512512207),
        PomaLocation(52.4693445559551, 4.54833984375),
        PomaLocation(52.5032656336422, 4.5208740234375),
        PomaLocation(52.4974136326536, 4.50799942016601),
        PomaLocation(52.4904111079461, 4.4989013671875),
        PomaLocation(52.4791211086397, 4.49581146240234),
        PomaLocation(52.4657366162632, 4.49495315551758),
        PomaLocation(52.4544402843305, 4.50233459472656),
        PomaLocation(52.4636972151833, 4.53228950500489),
        PomaLocation(52.4578922488107, 4.53838348388672),
        PomaLocation(52.4617623114352, 4.5549488067627),
    )

    // Aproach Lagelicht
    val approachNRTM = listOf(
        PomaLocation(51.9759380375377, 4.07407480392199),
        PomaLocation(51.9872403539982, 4.08198395983319),
        PomaLocation(51.9947863047326, 4.04585867706805),
        PomaLocation(51.9843685866977, 4.03832868799396),
        PomaLocation(51.9759380375377, 4.07407480392199),
    )

    // location approach PORT_APPROACH_SEGOT
    val approachSEGOT = listOf(
        PomaLocation(57.6204384497742, 11.6967718506408),
        PomaLocation(57.6331509917662, 11.7027632174535),
        PomaLocation(57.6488309624547, 11.7196351475637),
        PomaLocation(57.6557472352916, 11.7573106138071),
        PomaLocation(57.6581108294687, 11.7737558331204),
        PomaLocation(57.6712715883617, 11.8118720598754),
        PomaLocation(57.678684810607, 11.8113186778527),
        PomaLocation(57.6782105728981, 11.7953225258639),
        PomaLocation(57.673734339303, 11.7962428140253),
        PomaLocation(57.6612636152615, 11.7706493872541),
        PomaLocation(57.6555469767947, 11.7221043740962),
        PomaLocation(57.6495011615446, 11.7049760479029),
        PomaLocation(57.6591306861918, 11.7004972541514),
        PomaLocation(57.6741915102664, 11.7049438669167),
        PomaLocation(57.6743249153803, 11.7398893947757),
        PomaLocation(57.6780328671572, 11.7933351414378),
        PomaLocation(57.6789532696861, 11.8206675319042),
        PomaLocation(57.6826336387824, 11.8196815930846),
        PomaLocation(57.6820331392745, 11.794521986956),
        PomaLocation(57.6786283314777, 11.7394839441167),
        PomaLocation(57.6803581783425, 11.701693734611),
        PomaLocation(57.671169125007, 11.6809626418775),
        PomaLocation(57.6388003568695, 11.6833568635098),
        PomaLocation(57.6214303729063, 11.6641754456152),
        PomaLocation(57.6204384497742, 11.6967718506408),
    )

    // approach VUOSARI APPROACH
    val approachFIHEL = listOf(
        PomaLocation(60.11008204315918, 25.180149078369126),
        PomaLocation(60.101994928773564, 25.20289017475981),
        PomaLocation(60.088179288415574, 25.18581390380855),
        PomaLocation(60.09703173214752, 25.159124595747937),
        PomaLocation(60.11008204315918, 25.180149078369126),
    )

    val locationNLRTM = PomaLocation(51.89821265142771, 4.253226698614309)
    val locationNLAMS = PomaLocation(52.437102875324946, 4.7414358232837035)
    val locationSEGOT = PomaLocation(57.697837957909265, 11.905907489680168)
    val locationFIHEL = PomaLocation(60.1685, 24.9445)

    val possibleHeadingsNLRTM = listOf(80.0, 100.0, 120.0, 140.0, 160.0, 170.0)
    val imposibleHeadingsNLRTM = listOf(0.0, 30.0, 60.0, 200.0, 250.0, 300.0, 360.0)

    val possibleHeadingsNLAMS = listOf(60.0, 100.0, 150.0)
    val imposibleHeadingsNLAMS = listOf(0.0, 30.0, 180.0, 220.0, 300.0, 360.0)

    val possibleHeadingsSEGOT = listOf(30.0, 50.0, 100.0, 110.0)
    val imposibleHeadingsSEGOT = listOf(150.0, 200.0, 250.0, 300.0, 350.0)

    val possibleHeadingsFIHEL = listOf(260.0, 300.0, 350.0)
    val imposibleHeadingsFIHEL = listOf(10.0, 100.0, 150.0, 200.0, 230.0)

    /**
     * Test to go over all point in an approach area, to test every edge case where the event can be triggered.
     */
    @Test
    fun `given ship is sailing away from port to isHeadingTowardsLocation return false`() {
        approachNRTM.forEach { location ->
            imposibleHeadingsNLRTM.forEach { heading ->
                assertEquals(false, isHeadingTowardsLocation(location, heading, locationNLRTM), "NLRTM failed for $heading")
            }
        }
        approachNLAMS.forEach { location ->
            imposibleHeadingsNLAMS.forEach { heading ->
                assertEquals(false, isHeadingTowardsLocation(location, heading, locationNLAMS), "NLAMS failed for $heading")
            }
        }
        approachSEGOT.forEach { location ->
            imposibleHeadingsSEGOT.forEach { heading ->
                assertEquals(false, isHeadingTowardsLocation(location, heading, locationSEGOT), "SEGOT failed for $heading")
            }
        }
        approachFIHEL.forEach { location ->
            imposibleHeadingsFIHEL.forEach { heading ->
                assertEquals(false, isHeadingTowardsLocation(location, heading, locationFIHEL), "FIHEL failed for $heading")
            }
        }
    }

    @Test
    fun `given ship is sailing towards port to isHeadingTowardsLocation return true`() {
        approachNRTM.forEach { location ->
            possibleHeadingsNLRTM.forEach { heading ->
                assertEquals(true, isHeadingTowardsLocation(location, heading, locationNLRTM), "nlrtm, $heading")
            }
        }
        approachNLAMS.forEach { location ->
            possibleHeadingsNLAMS.forEach { heading ->
                assertEquals(true, isHeadingTowardsLocation(location, heading, locationNLAMS), "NLASM, $heading")
            }
        }
        approachSEGOT.forEach { location ->
            possibleHeadingsSEGOT.forEach { heading ->
                assertEquals(true, isHeadingTowardsLocation(location, heading, locationSEGOT), "SEGO, $heading")
            }
        }
        approachFIHEL.forEach { location ->
            possibleHeadingsFIHEL.forEach { heading ->
                assertEquals(true, isHeadingTowardsLocation(location, heading, locationFIHEL), "FIHEL failed for $heading")
            }
        }
    }
}
