package nl.teqplay.aisengine.portreportermonitor.service.conversion.converter.port

import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.portreportermonitor.createCustomArea
import nl.teqplay.aisengine.portreportermonitor.createPort
import nl.teqplay.aisengine.portreportermonitor.model.constant.EventType
import nl.teqplay.aisengine.portreportermonitor.service.CustomAreaService
import nl.teqplay.aisengine.portreportermonitor.service.conversion.TestEventEnricher
import nl.teqplay.aisengine.portreportermonitor.utils.toSkeletonLocation
import nl.teqplay.aisengine.testing.event.createAreaEndEvent
import nl.teqplay.aisengine.testing.event.createAreaStartEvent
import nl.teqplay.poma.api.v1.Location
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever

class DefaultPortConverterTest : TestEventEnricher() {

    private val customAreaService = mock<CustomAreaService>()
    private val converter = DefaultPortConverter(customAreaService)

    @Test
    fun `Given an port event overlapping in two areas make sure the correct event is converted`() {
        val baseEvent = createAreaStartEvent(
            area = AreaIdentifier(id = portGent._id, type = AreaIdentifier.AreaType.PORT, unlocode = portGent.unlocode, name = portGent.name),
            location = Location(51.21051499765748, 3.8020470667455166).toSkeletonLocation(),
        ).asEnrichedEvent<AreaEvent>(portGent)

        val convertedEvent = converter.convert(baseEvent)

        assertEquals(1, convertedEvent.portReporterEvents.size)
        assertEquals(portGent.unlocode, convertedEvent.portReporterEvents[0].port)
        assertEquals(portGent.unlocode, convertedEvent.portReporterEvents[0].location?.name)
        assertEquals(EventType.PORT_ATA, convertedEvent.portReporterEvents[0].eventType)
    }

    @Test
    fun `Given an port end event make sure the correct event is converted`() {
        val baseEvent = createAreaEndEvent(
            area = AreaIdentifier(id = portGent._id, type = AreaIdentifier.AreaType.PORT, unlocode = portGent.unlocode, name = portGent.name),
        ).asEnrichedEvent<AreaEvent>(portGent)

        val convertedEvent = converter.convert(baseEvent)

        assertEquals(1, convertedEvent.portReporterEvents.size)
        assertEquals(portGent.unlocode, convertedEvent.portReporterEvents[0].port)
        assertEquals(portGent.unlocode, convertedEvent.portReporterEvents[0].location?.name)
        assertEquals(EventType.PORT_ATD, convertedEvent.portReporterEvents[0].eventType)
    }

    @Test
    fun `Given an port event overlapping in two areas and no unlocode make sure the correct event is converted`() {
        val baseEvent = createAreaStartEvent(
            area = AreaIdentifier(id = portGent._id, type = AreaIdentifier.AreaType.PORT, name = portGent.name),
            location = Location(51.21051499765748, 3.8020470667455166).toSkeletonLocation(),
        ).asEnrichedEvent<AreaEvent>(portGent)

        val convertedEvent = converter.convert(baseEvent)

        assertEquals(1, convertedEvent.portReporterEvents.size)
        assertEquals(portGent.unlocode, convertedEvent.portReporterEvents[0].port)
        assertEquals(portGent.unlocode, convertedEvent.portReporterEvents[0].location?.name)
        assertEquals(EventType.PORT_ATA, convertedEvent.portReporterEvents[0].eventType)
    }

    @Test
    fun `Given an port event which is also in a from sea area make sure the correct event is converted and from sea is true`() {
        val baseEvent = createAreaStartEvent(
            area = AreaIdentifier(id = portGent._id, type = AreaIdentifier.AreaType.PORT, unlocode = portGent.unlocode, name = portGent.name),
            location = Location(51.21051499765748, 3.8020470667455166).toSkeletonLocation(),
        ).asEnrichedEvent<AreaEvent>(portGent)
        whenever(customAreaService.getSeaEntranceForPort(portGent.unlocode!!)).thenReturn(
            createCustomArea(
                area = listOf(
                    Location(51.11051499765748, 3.7020470667455166),
                    Location(51.11051499765748, 3.9020470667455166),
                    Location(51.31051499765748, 3.8020470667455166),

                ),
            ),
        )

        val convertedEvent = converter.convert(baseEvent)
        assertEquals(1, convertedEvent.portReporterEvents.size)
        assertEquals(portGent.unlocode, convertedEvent.portReporterEvents[0].port)
        assertEquals(portGent.unlocode, convertedEvent.portReporterEvents[0].location?.name)
        assertEquals(EventType.PORT_ATA, convertedEvent.portReporterEvents[0].eventType)
        assertEquals(true, convertedEvent.portReporterEvents[0].context?.fromSea)
    }

    private val portGent = createPort(_id = "PORT_OF_GENT_ID", unlocode = "BEGNE", name = "GENT", location = Location(51.15551834985868, 3.7732887268066406))
    private val portTerneuzen = createPort(_id = "PORT_OF_TERNEUZEN", unlocode = "NLTNZ", name = "TERNEUZEN")
}
