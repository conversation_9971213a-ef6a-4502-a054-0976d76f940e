package nl.teqplay.aisengine.portreportermonitor.service.conversion

import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.portreportermonitor.createShipIdentifiers
import nl.teqplay.aisengine.portreportermonitor.createShipRegisterInfo
import nl.teqplay.aisengine.portreportermonitor.model.EnrichEvent
import nl.teqplay.aisengine.portreportermonitor.model.EventMetadata
import nl.teqplay.csi.model.ship.info.ShipRegisterInfo
import nl.teqplay.poma.api.v1.Port

open class TestEventEnricher {
    fun <T : Event> T.asEnrichedEvent(port: Port): EnrichEvent<T> {
        return EnrichEvent(
            originalEvent = this,
            portReporterEvents = emptyList(),
            shipRegisterInfo = this.asCSIResponse(),
            port = port,
            getEventMetadata = ::getMetadata,
        )
    }

    fun <T : Event> T.asCSIResponse(): ShipRegisterInfo {
        return createShipRegisterInfo(
            identifiers = createShipIdentifiers(
                mmsi = this.ship.mmsi?.toString(),
                imo = this.ship.imo?.toString(),
            ),
        )
    }

    fun getMetadata(event: Event, port: Port) = EventMetadata()
}
