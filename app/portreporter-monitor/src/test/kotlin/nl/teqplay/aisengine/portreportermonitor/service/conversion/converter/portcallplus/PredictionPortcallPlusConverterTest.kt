package nl.teqplay.aisengine.portreportermonitor.service.conversion.converter.portcallplus

import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.aisengine.event.interfaces.PortcallPlusEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusEtaEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusEtdEvent
import nl.teqplay.aisengine.portreportermonitor.createBerth
import nl.teqplay.aisengine.portreportermonitor.createPort
import nl.teqplay.aisengine.portreportermonitor.model.LocationType
import nl.teqplay.aisengine.portreportermonitor.model.NamedLocation
import nl.teqplay.aisengine.portreportermonitor.service.BerthService
import nl.teqplay.aisengine.portreportermonitor.service.conversion.TestEventEnricher
import nl.teqplay.aisengine.shiphistory.client.ShipHistoricClient
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.nio.file.Files
import java.nio.file.Paths

class PredictionPortcallPlusConverterTest : TestEventEnricher() {
    private val objectMapper = jacksonObjectMapper()
        .registerModules(JavaTimeModule())
    private val PORT_BEGNE = createPort(_id = "1", unlocode = "BEGNE", name = "BEGNE")
    private val PORT_USHOU = createPort(_id = "2", unlocode = "USHOU", name = "USHOU")

    private val berthService = mock<BerthService>().also {
        whenever(it.getBerthById(eq("G7120")))
            .thenReturn(createBerth(terminalName = "TEST_TERMINAL", terminalId = "TEST_TERMINAL_ID"))
        whenever(it.getBerthById(eq("VOPAK2-95.09727789971701_29.74599303113119")))
            .thenReturn(createBerth(name = "Vopak 2", terminalName = "Vopak 2", terminalId = "VOPAK2-95.09727789971701_29.74599303113119"))
    }
    private val shipHistoricClient = mock<ShipHistoricClient>().also {
        whenever(it.findHistoricByMmsi(any(), any())).thenReturn(listOf())
    }
    val converter = PredictionPortcallPlusConverter(berthService, shipHistoricClient)

    @Test
    fun `Given an port etd event make sure the event is converted`() {
        val eventJsonFile = this.javaClass.classLoader.getResource("PortPortcallPlusEtdEvent.json")?.toURI()
        val eventJson = Files.readString(Paths.get(eventJsonFile!!))
        val input = objectMapper.readValue<PortcallPlusEtdEvent>(eventJson)
            .asEnrichedEvent<PortcallPlusEvent>(PORT_BEGNE)

        val result = converter.convert(input)
        val portReporterEvent = result.portReporterEvents.first()
        val expectedNamedLocation = NamedLocation(
            name = "BEGNE",
            type = LocationType.PORT,
            id = null
        )

        assertEquals(1, result.portReporterEvents.size)
        assertEquals("port.etd.agent", portReporterEvent.eventType)
        assertEquals("BEGNE", portReporterEvent.port)
        assertEquals("Ghent PCS", portReporterEvent.source)
        assertEquals("Lalemant", portReporterEvent.context?.agent)
        assertEquals("TEST_TERMINAL", portReporterEvent.context?.terminalName)
        assertEquals("TEST_TERMINAL_ID", portReporterEvent.context?.terminalId)
        assertEquals(expectedNamedLocation, portReporterEvent.location)
    }

    @Test
    fun `Given an berth eta event make sure the event is converted`() {
        val eventJsonFile = this.javaClass.classLoader.getResource("BerthPortcallPlusEtaEvent.json")?.toURI()
        val eventJson = Files.readString(Paths.get(eventJsonFile!!))
        val input = objectMapper.readValue<PortcallPlusEtaEvent>(eventJson)
            .asEnrichedEvent<PortcallPlusEvent>(PORT_USHOU)

        val result = converter.convert(input)
        val portReporterEvent = result.portReporterEvents.first()
        val expectedNamedLocation = NamedLocation(
            name = "Vopak 2",
            type = LocationType.BERTH,
            id = "VOPAK2-95.09727789971701_29.74599303113119"
        )

        assertEquals(1, result.portReporterEvents.size)
        assertEquals("nomination.eta.agent", portReporterEvent.eventType)
        assertEquals("USHOU", portReporterEvent.port)
        assertEquals("Vopak Nominations", portReporterEvent.source)
        assertEquals("Vopak agent", portReporterEvent.context?.agent)
        assertEquals("Vopak 2", portReporterEvent.context?.terminalName)
        assertEquals("VOPAK2-95.09727789971701_29.74599303113119", portReporterEvent.context?.terminalId)
        assertEquals(expectedNamedLocation, portReporterEvent.location)
    }

    @Test
    fun `Given an berth eta event without known berth make sure agent is set`() {
        val eventJsonFile = this.javaClass.classLoader.getResource("BerthPortcallPlusEtaEvent.json")?.toURI()
        val eventJson = Files.readString(Paths.get(eventJsonFile!!))
        val input = objectMapper.readValue<PortcallPlusEtaEvent>(eventJson)
            .copy(berthId = "UNKNOWN") // make sure berth doesn't exist
            .asEnrichedEvent<PortcallPlusEvent>(PORT_USHOU)

        val result = converter.convert(input)
        val portReporterEvent = result.portReporterEvents.first()

        assertEquals(1, result.portReporterEvents.size)
        assertEquals("Vopak agent", portReporterEvent.context?.agent)
        assertEquals(null, portReporterEvent.context?.terminalName)
        assertEquals(null, portReporterEvent.context?.terminalId)
    }
}
