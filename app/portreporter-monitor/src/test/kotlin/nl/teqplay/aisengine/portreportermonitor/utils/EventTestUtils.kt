package nl.teqplay.aisengine.portreportermonitor.utils

import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.portreportermonitor.model.EnrichEvent

fun <T : Event> EnrichEvent<T>.updateEventsUuid(expectedUuid: String): EnrichEvent<T> {
    return this.copy(
        portReporterEvents = this.portReporterEvents.map { portReporterEvent ->
            portReporterEvent.copy(uuid = expectedUuid)
        }
    )
}
