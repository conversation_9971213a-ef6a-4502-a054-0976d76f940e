package nl.teqplay.aisengine.portreportermonitor.service

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import nl.teqplay.aisengine.event.interfaces.ActualEvent
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.interfaces.PortcallEvent
import nl.teqplay.aisengine.event.interfaces.PredictedEvent
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.portreportermonitor.configuration.NatsSettingsProperties
import nl.teqplay.aisengine.portreportermonitor.model.EnrichEvent
import nl.teqplay.aisengine.portreportermonitor.model.EventMetadata
import nl.teqplay.aisengine.portreportermonitor.service.EventMetadataService.Companion.PortcallPlusMetadata
import nl.teqplay.aisengine.portreportermonitor.service.EventMetadataService.Companion.RequestMetadataByImoAndPort
import nl.teqplay.aisengine.testing.event.createAreaEndEvent
import nl.teqplay.aisengine.testing.event.createAreaStartEvent
import nl.teqplay.aisengine.testing.event.createUniqueBerthEndEvent
import nl.teqplay.aisengine.testing.event.createUniqueBerthStartEvent
import nl.teqplay.poma.api.v1.Port
import nl.teqplay.skeleton.common.config.NatsProperties
import nl.teqplay.skeleton.metrics.MetricRegistry
import nl.teqplay.skeleton.nats.NatsClientBuilder
import nl.teqplay.skeleton.nats.NatsClientMock
import nl.teqplay.skeleton.nats.NatsClientRequestReply
import nl.teqplay.smartfleet.model.event.FleetMinimal
import nl.teqplay.smartfleet.model.event.RequestMetadataByImo
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.kotlin.any
import org.mockito.kotlin.anyVararg
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.time.Instant
import java.util.concurrent.atomic.AtomicLong
import java.util.stream.Stream
import nl.teqplay.smartfleet.model.event.Metadata as SmartFleetMetadata

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class EventMetadataServiceTest {

    private val objectMapper = jacksonObjectMapper()
        .registerModule(JavaTimeModule())
        .registerKotlinModule()
        .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
    private val natsClientMock = NatsClientMock()
    val metricRegistry = mock<MetricRegistry<NatsClientRequestReply>>().apply {
        whenever(getOrCreateGauge(any(), anyVararg())).thenReturn(AtomicLong())
    }
    private val natsClientBuilder = mock<NatsClientBuilder>().apply {
        whenever(requestReply(any(), any())).thenAnswer {
            val applicationName = it.getArgument<String>(1)
            natsClientMock.requestReply(applicationName, metricRegistry)
        }
    }
    private val natsProperties = mock<NatsProperties>()
    private val natsSettingsProperties = NatsSettingsProperties()
    private val eventMetadataService = EventMetadataService(
        objectMapper,
        natsClientBuilder,
        natsProperties,
        natsSettingsProperties,
        mock<TrueDestinationService>().apply {
            whenever(getDestination(any())).thenReturn(null)
        }
    )

    /**
     * Request model for SmartFleet
     */
    data class RequestMetadataByImoImpl(
        override val imo: String?,
    ) : RequestMetadataByImo

    /**
     * Request model for Portcall+
     */
    data class RequestMetadataByImoAndPortImpl(
        override val imo: String?,
        override val port: String?,
        override val time: Instant?,
        override val from: Instant?,
        override val to: Instant?,
        override val allowFuturePortcalls: Boolean?
    ) : RequestMetadataByImoAndPort

    private val fleetIds = listOf(
        FleetMinimal(
            id = "id",
            domain = "domain",
            name = "name",
            userId = "userId",
            companyId = "companyId",
            type = FleetMinimal.Type.CONTAINS
        )
    )

    private val portcallId = "portcallId"
    private val portcallIdFromEvent = "ID_FROM_PORTCALL_EVENT"

    private fun requestMetadataTestData() = Stream.of(
        Arguments.of(
            mock<ActualEvent>().apply {
                whenever(ship).thenReturn(AisShipIdentifier(0, 1))
                whenever(actualTime).thenReturn(Instant.EPOCH)
            },
            EventMetadata(portcallId, fleetIds)
        ),
        Arguments.of(
            mock<PredictedEvent>().apply {
                whenever(ship).thenReturn(AisShipIdentifier(0, 1))
                whenever(predictedTime).thenReturn(Instant.EPOCH)
            },
            EventMetadata(portcallId, fleetIds)
        ),
        Arguments.of(
            mock<PortcallEvent>().apply {
                whenever(ship).thenReturn(AisShipIdentifier(0, 1))
                whenever(createdTime).thenReturn(Instant.EPOCH)
                whenever(portcallId).thenReturn(portcallIdFromEvent)
            },
            EventMetadata(portcallIdFromEvent, fleetIds)
        )
    )

    @Disabled
    @ParameterizedTest
    @MethodSource("requestMetadataTestData")
    fun requestMetadata(
        event: Event,
        expectedMetadata: EventMetadata,
    ) {
        // simulate SmartFleet as a responder
        natsClientMock.requestReply("smartfleet", metricRegistry).reply<RequestMetadataByImoImpl, SmartFleetMetadata>(
            subject = "portreporter-monitor.request.v1.identifiers",
            deserializer = objectMapper::readValue,
            serializer = objectMapper::writeValueAsBytes,
            handler = { request, _ ->
                val imo = requireNotNull(request.imo)
                return@reply SmartFleetMetadata(
                    imo = imo,
                    fleets = fleetIds
                )
            }
        )

        // simulate Portcall+ as a responder
        natsClientMock.requestReply("portcallplus", metricRegistry).reply<RequestMetadataByImoAndPortImpl, PortcallPlusMetadata>(
            subject = "portreporter-monitor.request.v1.identifiers",
            deserializer = objectMapper::readValue,
            serializer = objectMapper::writeValueAsBytes,
            handler = { _, _ ->
                return@reply PortcallPlusMetadata(
                    portcallId = portcallId
                )
            }
        )

        // simulate fake and invalid responder
        natsClientMock.requestReply("random", metricRegistry).reply<RequestMetadataByImoImpl, String>(
            subject = "portreporter-monitor.request.v1.identifiers",
            deserializer = objectMapper::readValue,
            serializer = objectMapper::writeValueAsBytes,
            handler = { _, _ -> "random" }
        )

        val mockedPort = mock<Port>().apply {
            whenever(unlocode).thenReturn("unlocode")
        }
        val enrichedEvent = mock<EnrichEvent<Event>>().apply {
            whenever(originalEvent).thenReturn(event)
            whenever(port).thenReturn(mockedPort)
        }
        val metadata = eventMetadataService.requestMetadata(enrichedEvent.originalEvent, enrichedEvent.port)

        assertEquals(expectedMetadata, metadata)
    }

    private fun testEvents() = Stream.of(
        Arguments.of(
            createAreaStartEvent(),
            true
        ),
        Arguments.of(
            createAreaEndEvent(),
            false
        ),
        Arguments.of(
            createUniqueBerthStartEvent(),
            true
        ),
        Arguments.of(
            createUniqueBerthEndEvent(),
            false
        )
    )

    @ParameterizedTest
    @MethodSource("testEvents")
    fun `should allow future portcalls`(event: Event, expected: Boolean) {
        val result = eventMetadataService.eventAllowsFuturePortcalls(event)
        assertEquals(expected, result)
    }
}
