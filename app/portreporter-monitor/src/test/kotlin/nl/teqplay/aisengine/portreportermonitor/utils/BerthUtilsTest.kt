package nl.teqplay.aisengine.portreportermonitor.utils

import nl.teqplay.aisengine.portreportermonitor.createBerth
import nl.teqplay.poma.api.v1.Location
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test

class BerthUtilsTest {
    @Test
    fun `Given two adjecent berths to areBerthsAdjacent() expect to return true`() {
        val berth1 = createBerth(
            name = "PLAATS 2",
            length = 295.0,
            width = 60.0,
            area = listOf(
                Location(latitude = 51.93564413430372, longitude = 4.0447280075726),
                Location(latitude = 51.93657673823223, longitude = 4.048743346928976),
                Location(latitude = 51.93607192768426, longitude = 4.049050193532318),
                Location(latitude = 51.935139334325186, longitude = 4.045034894198103),
                Location(latitude = 51.93564413430372, longitude = 4.0447280075726),
            ),
        )
        val berth2 = createBerth(
            name = "PLAATS 1",
            length = 295.0,
            width = 60.0,
            area = listOf(
                Location(latitude = 51.9345729469901, longitude = 4.040100397681023),
                Location(latitude = 51.93564556713012, longitude = 4.044727135804777),
                Location(latitude = 51.93564413430372, longitude = 4.0447280075726),
                Location(latitude = 51.93514063858402, longitude = 4.0450334693077865),
                Location(latitude = 51.934067915223196, longitude = 4.040406278864081),
                Location(latitude = 51.93457278624449, longitude = 4.040099705916468),
                Location(latitude = 51.9345729469901, longitude = 4.040100397681023),
            ),
        )
        Assertions.assertEquals(true, areBerthsAdjacent(berth1, berth2))
    }

    @Test
    fun `Given two NOT adjecent berths to areBerthsAdjacent() expect to return false`() {
        val berth1 = createBerth(
            name = "PLAATS 2",
            length = 295.0,
            width = 60.0,
            area = listOf(
                Location(latitude = 51.93564413430372, longitude = 4.0447280075726),
                Location(latitude = 51.93657673823223, longitude = 4.048743346928976),
                Location(latitude = 51.93607192768426, longitude = 4.049050193532318),
                Location(latitude = 51.935139334325186, longitude = 4.045034894198103),
                Location(latitude = 51.93564413430372, longitude = 4.0447280075726),
            ),
        )
        val berth2 = createBerth(
            name = "PLAATS 1",
            length = 295.0,
            width = 60.0,
            area = listOf(
                Location(latitude = 1.9345729469901, longitude = 4.040100397681023),
                Location(latitude = 1.93564556713012, longitude = 4.044727135804777),
                Location(latitude = 1.93564413430372, longitude = 4.0447280075726),
                Location(latitude = 1.93514063858402, longitude = 4.0450334693077865),
                Location(latitude = 1.934067915223196, longitude = 4.040406278864081),
                Location(latitude = 1.93457278624449, longitude = 4.040099705916468),
                Location(latitude = 1.9345729469901, longitude = 4.040100397681023),
            ),
        )
        Assertions.assertEquals(false, areBerthsAdjacent(berth1, berth2))
    }
}
