package nl.teqplay.aisengine.portreportermonitor

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.nats.client.impl.Headers
import io.nats.client.impl.NatsMessage
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.model.TrueDestinationChangedEvent
import nl.teqplay.aisengine.portreportermonitor.model.PortReporterEvent
import nl.teqplay.aisengine.portreportermonitor.model.PortcallShipDetails
import nl.teqplay.aisengine.portreportermonitor.model.constant.EventType
import nl.teqplay.aisengine.portreportermonitor.service.EventMetadataService
import nl.teqplay.aisengine.portreportermonitor.service.EventMetadataService.Companion.PortcallPlusMetadata
import nl.teqplay.aisengine.portreportermonitor.service.TrueDestinationService
import nl.teqplay.aisengine.shiphistory.client.ShipHistoricClient
import nl.teqplay.csi.model.ship.info.ShipRegisterInfo
import nl.teqplay.poma.api.v1.PilotBoardingPlace
import nl.teqplay.poma.api.v1.Port
import nl.teqplay.skeleton.common.BaseTest
import nl.teqplay.skeleton.csi.client.CsiShipClient
import nl.teqplay.skeleton.nats.NatsClientBuilder
import nl.teqplay.skeleton.nats.NatsClientBuilderMock
import nl.teqplay.skeleton.nats.NatsClientMock
import nl.teqplay.skeleton.poma.client.PomaInfrastructureClient
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import nl.teqplay.smartfleet.model.event.FleetMinimal
import nl.teqplay.smartfleet.model.event.Metadata
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.fail
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.Mockito
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import org.springframework.boot.autoconfigure.EnableAutoConfiguration
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Import
import org.springframework.context.annotation.Primary
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.TestConstructor
import java.time.Duration
import java.time.ZonedDateTime
import java.util.Date
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@SpringBootTest
@ContextConfiguration
@EnableAutoConfiguration
@Import(ScenarioTest.Config::class)
@TestConstructor(autowireMode = TestConstructor.AutowireMode.ALL)
class ScenarioTest(
    private val objectMapper: ObjectMapper,
    private val natsClientBuilderMock: NatsClientBuilderMock,
    private val publishedMessages: MutableList<PortReporterEvent>,
    private val trueDestinationService: TrueDestinationService,
) : BaseTest() {
    @TestConfiguration
    class Config {
        @Bean
        fun natsClientBuilder(): NatsClientBuilder = NatsClientBuilderMock()

        @Bean
        fun publishedMessages() = mutableListOf<PortReporterEvent>()

        @Bean
        fun pomaClient(objectMapper: ObjectMapper) = mock<PomaInfrastructureClient>().apply {
            val ports = objectMapper.readResource<Array<Port>>("ports.json")
            val pbps = objectMapper.readResource<Array<PilotBoardingPlace>>("pbp.json")
            whenever(this.getPorts(anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull()))
                .thenReturn(ports)
            whenever(this.getPilotBoardingPlaces(anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull()))
                .thenReturn(pbps)
        }

        @Bean
        fun csiShipClient(objectMapper: ObjectMapper) = mock<CsiShipClient>().apply {
            val ships = objectMapper.readResource<Array<ShipRegisterInfo>>("ships.json")
            val byImo = ships.associateBy { it.identifiers.imo }
            val byMmsi = ships.associateBy { it.identifiers.mmsi }
            whenever(this.getShipRegisterByIMO(any())).then { byImo[it.arguments[0] as? String] }
            whenever(this.getShipRegisterByMMSI(any())).then { byMmsi[it.arguments[0] as? String] }
        }

        @Primary
        @Bean
        fun shipHistoryClient() = mock<ShipHistoricClient>().apply {
            whenever(this.findHistoricByMmsi(any(), any())).thenReturn(emptyList())
        }

        @Bean
        fun rabbitMqEventSender(
            rmqMapper: ObjectMapper,
            publishedMessages: MutableList<PortReporterEvent>
        ): RabbitMqEventSender = mock<RabbitMqEventSender>().apply {
            whenever(this.send(any(), any(), any())).then { mock ->
                try {
                    (mock.arguments[1] as? ByteArray)
                        ?.let { b ->
                            when (b.decodeToString()[0]) {
                                '{' -> listOf(rmqMapper.readValue<PortReporterEvent>(b))
                                '[' -> rmqMapper.readValue<Array<PortReporterEvent>>(b).toList()
                                else -> emptyList()
                            }
                        }
                        ?.let(publishedMessages::addAll)
                } catch (e: Exception) {
                    // To ensure messages are not dropped without a trace ... say something
                    System.err.println("Error sending messages: $e")
                    e.printStackTrace()
                }
            }
        }

        @Bean
        fun trueDestinationService(): TrueDestinationService = mock()
    }

    @BeforeEach
    fun setup() {
        publishedMessages.clear()
        NatsClientMock.responders.clear()
        Mockito.reset(trueDestinationService)
        // reset the Nats KV store
        natsClientBuilderMock.client.keyValueBuckets.forEach { (_, kvBucket) ->
            val keys = kvBucket.keys()
            keys.forEach { key -> kvBucket.delete(key) }
        }
    }

    /**
     * These are "exact" tests: Teqplay events are loaded from src/test/resources/events/<portcall>.json and the
     * generated PortReporterEvents are compared to src/test/resources/output/<portcall>.json. The output must match
     * exactly, except for the event UUID.
     * If you provide the mmsi and unlocode, a true destination reponse is set up. If you provide the IMO number and
     * the unlocode, a portcall id response will be setup.
     */
    private fun portcalls() = Stream.of(
        Testcase("NLVLI91898972312310T", 311054900, 9189897, "NLVLI"),
        Testcase("USHOU94003942312270T"),
        Testcase("USHOU91432072401020T"),
        Testcase("BEANR95260712401130T", 246733000, 9526071, "BEANR"),
    )

    @ParameterizedTest
    @MethodSource("portcalls")
    fun `test portcall`(testcase: Testcase) {
        processTest(
            testcase.portcallId,
            testcase.mmsi,
            testcase.imo,
            testcase.unlocode
        )
    }

    /**
     * These are "structure" tests. The setup is the same as the "exact" tests above, but the expected result is
     * specified using the "withEvents" function. In here, you can specify an expected subsequence of events that must
     * appear in this order in the output. Note that it's ok if there are other events in between. The events are
     * compared on event type, and on an approximate time match (+/- 2 minutes).
     */
    private fun structureTests() = Stream.of(
        Testcase("USCRP93996122312240T", 636015007, 9399612, "USCRP").withEvents {
            event("2023-12-27T22:49:00Z", EventType.PILOT_ONBOARD_AT)
            event("2023-12-27T23:11:00Z", EventType.PORT_ATA)
            event("2023-12-28T01:21:00Z", EventType.BERTH_ATA)
            event("2023-12-28T08:14:00Z", EventType.BERTH_ATD)
            event("2023-12-28T10:45:00Z", EventType.PILOTBOARDINGPLACE_ATDE)
        },
        Testcase("NLVLI95390802401200T", 249239000, 9539080, "NLVLI").withEvents {
            event("2024-01-20T09:45:00Z", EventType.PORT_ATA)
            event("2024-01-20T10:06:00Z", EventType.BERTH_ATA)
            event("2024-01-22T04:13:00Z", EventType.BERTH_ATD)
            event("2024-01-22T04:45:00Z", EventType.PORT_ATD)
            event("2024-01-22T07:49:00Z", EventType.PILOTBOARDINGPLACE_ATDE)
        }.withoutEvents {
            // No pilot switch event in Vlissingen
            event("2024-01-20T09:32:00Z", EventType.PILOTBOARDINGPLACE_ATAE)
            event("2024-01-22T04:47:00Z", EventType.PILOTBOARDINGPLACE_ATDE)
        },
        Testcase("NLRTM24003448", 477859300, 9919735, "NLRTM").withEvents {
            event("2024-02-18T04:03:00Z", EventType.ANCHOR_ATD)
            event("2024-02-18T05:47:00Z", EventType.PILOT_ONBOARD_AT)
            event("2024-02-18T07:00:00Z", EventType.APPROACH_ATA)
            event("2024-02-18T07:00:00Z", EventType.PORT_ATA)
            event("2024-02-18T08:43:00Z", EventType.TUGSTANDBY)
            event("2024-02-18T08:46:00Z", EventType.TUGSTANDBY)
            event("2024-02-18T09:19:00Z", EventType.FIRSTLINE_SECURED_AT)
            event("2024-02-18T09:29:00Z", EventType.BERTH_ATA)
            event("2024-02-18T10:11:00Z", EventType.LASTLINE_SECURED_AT)
        },
        Testcase("NLRTM24005574", 209391000, 9517458, "NLRTM").withEvents {
            event("2024-03-08T11:41:00Z", EventType.PILOTBOARDINGPLACE_ATAE)
            event("2024-03-08T22:18:00Z", EventType.APPROACH_ATA)
            event("2024-03-08T22:19:00Z", EventType.PORT_ATA)
            event("2024-03-09T00:01:00Z", EventType.BERTH_ATA)
        }.withoutEvents {
            // No tugs involved, so the boatmen encounters should not trigger first/last line events
            event("2024-03-08T23:55:00Z", EventType.FIRSTLINE_SECURED_AT)
            event("2024-03-09T00:26:00Z", EventType.LASTLINE_SECURED_AT)
        },
        // Test inbound/outbound detection and detection outside of pbp for pilot events in sgsin
        Testcase("SGSIN94318982403200T", 477454300, 9431898, "SGSIN").withEvents {
            event("2024-03-20T21:34:00Z", EventType.PILOT_ONBOARD_AT)
            event("2024-03-21T00:18:00Z", EventType.BERTH_ATA)
            event("2024-03-21T21:42:00Z", EventType.BERTH_ATD)
            event("2024-03-21T23:43:00Z", EventType.PILOT_DISEMBARKED_AT)
        },
        // Some other pilot shenanigans in SGSIN, multiple events are triggered here unfortunately, but we want to see
        // at least these two pilot events.
        Testcase("SGSIN99413742403210T", 538009996, 9941374, "SGSIN").withEvents {
            event("2024-03-20T23:17:00Z", EventType.PILOTBOARDINGPLACE_ATAE)
            event("2024-03-21T11:19:00Z", EventType.PILOT_DISEMBARKED_AT)
        },
        // Copy of the above, where we changed the pilot event such that the main vessel is not moving. This should
        // not have the pilot events of the test case above
        Testcase("SGSIN99413742403210T-mod", 538009996, 9941374, "SGSIN").withoutEvents {
            event("2024-03-20T23:17:00Z", EventType.PILOTBOARDINGPLACE_ATAE)
            event("2024-03-21T11:19:00Z", EventType.PILOT_DISEMBARKED_AT)
        },
        // Test 120nm and 80nm events, for some reason this event is not generated in the test but the portcall have it
        /*Testcase("NLRTM24000834", 311062900, 9208629, "NLRTM").withEvents {
            //event("2024-01-10T01:34:53Z", EventType._120NM)
            //event("2024-01-10T01:34:53Z", EventType._80NM)
        },*/
        // Test Supply Barge ATS, Supply Barge ATC, Waste ATS and Waste ATC.
        Testcase("NLRTM24002657", 224989000, 9825568, "NLRTM").withEvents {
            event("2024-03-13T21:22:00Z", EventType.TUGSTANDBY)
            event("2024-03-13T22:21:00Z", EventType.FIRSTLINE_SECURED_AT)
            event("2024-03-13T22:31:00Z", EventType.BERTH_ATA)
            event("2024-03-14T18:39:00Z", EventType.SUPPLY_BARGE_ATS)
            event("2024-03-14T19:04:00Z", EventType.WASTE_ATS)
            event("2024-03-14T19:51:00Z", EventType.WASTE_ATC)
            event("2024-03-14T19:51:00Z", EventType.SUPPLY_BARGE_ATC)
            event("2024-03-14T22:47:00Z", EventType.TUGSTANDBY)
            event("2024-03-14T23:44:00Z", EventType.BERTH_ATD)
        },
        // Check ATA and ATD for Port, Berth and Lock for Antwerp port.
        Testcase("BEANR92324862401140T", 275497000, 9232486, "BEANR").withEvents {
            event("2024-01-17T06:49:44Z", EventType.PORT_ATA)
            event("2024-01-17T07:13:22Z", EventType.BERTH_ATA)
            event("2024-01-17T11:43:53Z", EventType.BERTH_ATD)
            event("2024-01-17T12:27:02Z", EventType.LOCK_ATA)
            event("2024-01-17T14:07:33Z", EventType.LOCK_ATD)
            event("2024-01-17T15:34:12Z", EventType.PORT_ATD)
        },
        // Check anchors and bunkers events.
        Testcase("NLRTM24002025", 305654000, 9328649, "NLRTM").withEvents {
            event("2024-02-10T07:29:17Z", EventType.ANCHOR_ATA)
            event("2024-02-11T17:07:00Z", EventType.ANCHOR_ATD)
            event("2024-02-11T18:11:00Z", EventType.PORT_ATA)
            event("2024-02-11T18:55:10Z", EventType.BERTH_ATA)
            event("2024-02-11T19:40:57Z", EventType.BUNKERS_ATS)
            event("2024-02-11T23:26:00Z", EventType.BUNKERS_ATC)
            event("2024-02-12T10:48:22Z", EventType.BERTH_ATD)
            event("2024-02-12T12:42:00Z", EventType.PORT_ATD)
        },
        // Test Pilot on board and pilot disembarked event. Check Potable water ATS and ATC.
        Testcase("NLRTM24006830", 563049000, 9439864, "NLRTM").withEvents {
            event("2024-03-22T21:33:00Z", EventType.PILOT_ONBOARD_AT)
            event("2024-03-23T12:37:00Z", EventType.WASTE_ATS)
            event("2024-03-23T12:57:00Z", EventType.WASTE_ATC)
            event("2024-03-23T13:34:00Z", EventType.POTABLE_WATER_ATS)
            event("2024-03-23T16:14:00Z", EventType.POTABLE_WATER_ATC)
            event("2024-03-24T08:09:00Z", EventType.BERTH_ATA)
            event("2024-03-26T00:53:00Z", EventType.BERTH_ATD)
            event("2024-03-26T02:59:00Z", EventType.PILOT_DISEMBARKED_AT)
        },
        // Test Approach area as well of some encounter events.
        Testcase("NLRTM24006846", 314426000, 9501710, "NLRTM").withEvents {
            event("2024-03-22T19:21:00Z", EventType.APPROACH_ATA)
            event("2024-03-22T21:45:00Z", EventType.BERTH_ATA)
            event("2024-03-23T09:56:00Z", EventType.TENDER_ATS)
            event("2024-03-23T10:18:00Z", EventType.TENDER_ATC)
            event("2024-03-25T02:54:00Z", EventType.BERTH_ATD)
            event("2024-03-25T03:58:00Z", EventType.BERTH_ATA)
            event("2024-03-25T08:41:00Z", EventType.WASTE_ATS)
            event("2024-03-25T08:49:00Z", EventType.WASTE_ATC)
            event("2024-03-25T13:57:00Z", EventType.BERTH_ATD)
            event("2024-03-25T14:45:00Z", EventType.BERTH_ATA)
            event("2024-03-25T15:21:00Z", EventType.BUNKERS_ATS)
            event("2024-03-25T19:02:00Z", EventType.BUNKERS_ATC)
            event("2024-03-26T17:02:00Z", EventType.BERTH_ATD)
            event("2024-03-27T23:29:00Z", EventType.APPROACH_ATD)
        },
        // Test Slops ATS and ATC
        Testcase("NLRTM24003055", 248604000, 9192375, "NLRTM").withEvents {
            event("2024-02-11T14:40:31Z", EventType.PORT_ATA)
            event("2024-02-11T16:32:00Z", EventType.BERTH_ATA)
            event("2024-02-12T12:26:00Z", EventType.SLOPS_ATS)
            event("2024-02-12T12:37:00Z", EventType.SLOPS_ATC)
            event("2024-02-13T17:04:00Z", EventType.BERTH_ATD)
            event("2024-02-13T19:49:00Z", EventType.PORT_ATD)
        },
        // Test Push Barge ATS in USHOU.
        Testcase("USHOU97911692308200T", 563054800, 9791169, "USHOU").withEvents {
            event("2023-08-29T14:26:00Z", EventType.BERTH_ATA)
            event("2023-08-30T01:22:00Z", EventType.PUSH_BARGE_ATS)
            // event("2023-08-30T04:33:03Z", EventType.PUSH_BARGE_ATC) it's failing but the event is present
            // event("2023-08-30T04:51:25Z", EventType.BERTH_ATD) it's failing but the event is present
        },
    )

    @ParameterizedTest
    @MethodSource("structureTests")
    fun `structured tests`(testcase: Testcase) {
        processStructuredTest(
            testcase.portcallId,
            testcase.mmsi,
            testcase.imo,
            testcase.unlocode,
            testcase.shouldContain,
            testcase.shouldNotContain
        )
    }

    @Disabled
    @Test
    fun `test scenario with two follow up portcalls of the same eos (NLTNZ, BEGNE) with trueDestination for smartfleet`() {
        // setup default mocking

        NatsRequestReplyBuilder(NatsClientMock(), objectMapper)
            .addPortcallId(9609691, "BEANR", "BEANR96096912402070T")
            .addPortcallId(9609691, "NLVLI", "NLVLI96096912401300T")
            .addSmartfleetInfo(9609691, "NLVLI", "smartfleet-id-NLVLI")
            .addSmartfleetInfo(9609691, "BEGNE", "smartfleet-id-BEGNE-dont-use")
            .build()

        // Read all events for ship
        val events = objectMapper.readResource<Array<Event>>("events/BEANR-NLTNZ.json").toList()

        val eventsSplitOnTrueDestinationEvent = events.forEach {
            if (it is TrueDestinationChangedEvent && !it.trueDestination.isNullOrBlank()) {
                // Change true destination mock based on true destination events
                setTrueDestinationResponse(it.ship.mmsi, it.trueDestination!!)
            }
            it.sendEventOnNats()
        }
        // Read expected events from file
        val expected = objectMapper.readResource<Array<PortReporterEvent>>("output/BEANR-NLTNZ.json").toList()
        // Compare Expected with actual to validate result
        assertThat(publishedMessages)
            .usingRecursiveFieldByFieldElementComparatorIgnoringFields("uuid")
            .isEqualTo(expected)
        // validate stuff
    }

    @Disabled
    @Test
    fun `test scenario with two follow up portcalls of the same eos (NLTNZ, BEGNE) without true destination`() {
        // setup default mocking

        NatsRequestReplyBuilder(NatsClientMock(), objectMapper)
            .addPortcallId(9609691, "BEANR", "BEANR96096912402070T")
            .addPortcallId(9609691, "NLVLI", "NLVLI96096912401300T")
            .addSmartfleetInfo(9609691, "NLVLI", "smartfleet-id-NLVLI")
            .addSmartfleetInfo(9609691, "BEGNE", "smartfleet-id-BEGNE")
            .build()

        // Read all events for ship
        val events = objectMapper.readResource<Array<Event>>("events/BEANR-NLTNZ.json")
        // send events
        events.sendEventsOnNats()
        // Read expected events from file
        val expected = objectMapper.readResource<Array<PortReporterEvent>>("output/BEANR-NLTNZ-noTrueDestination.json").toList()
        // Compare Expected with actual to validate result
        assertThat(publishedMessages)
            .usingRecursiveFieldByFieldElementComparatorIgnoringFields("uuid")
            .isEqualTo(expected)
        // validate stuff
    }

    private fun processStructuredTest(
        portcallId: String,
        mmsi: Int? = null,
        imo: Int? = null,
        unlocode: String? = null,
        shouldContain: List<PortReporterEvent>,
        shouldNotContain: List<PortReporterEvent>
    ) {
        if (mmsi != null && unlocode != null) setTrueDestinationResponse(mmsi, unlocode)
        if (imo != null && unlocode != null) {
            NatsRequestReplyBuilder(NatsClientMock(), objectMapper)
                .addPortcallId(imo, unlocode, portcallId)
                .build()
        }

        sendFile("events/$portcallId.json")

        if (shouldContain.isNotEmpty()) {
            assertThat(publishedMessages)
                .usingComparatorForType(
                    compareBy(
                        PortReporterEvent::eventType
                    ).thenComparing({ it.eventTime }, approximateDateComparator(Duration.ofMinutes(2))),
                    PortReporterEvent::class.java
                )
                .containsSubsequence(shouldContain)
        }

        if (shouldNotContain.isNotEmpty()) {
            assertThat(publishedMessages)
                .usingComparatorForType(
                    compareBy(
                        PortReporterEvent::eventType
                    ).thenComparing({ it.eventTime }, approximateDateComparator(Duration.ofMinutes(30))),
                    PortReporterEvent::class.java
                )
                .doesNotContainSequence(shouldNotContain)
        }
    }

    private fun processTest(
        portcallId: String,
        mmsi: Int? = null,
        imo: Int? = null,
        unlocode: String? = null
    ) {
        if (mmsi != null && unlocode != null) setTrueDestinationResponse(mmsi, unlocode)
        if (imo != null && unlocode != null) {
            NatsRequestReplyBuilder(NatsClientMock(), objectMapper)
                .addPortcallId(imo, unlocode, portcallId)
                .build()
        }

        sendFile("events/$portcallId.json")
        val expected = objectMapper.readResource<Array<PortReporterEvent>>("output/$portcallId.json").toList()
        assertThat(publishedMessages)
            .usingRecursiveFieldByFieldElementComparatorIgnoringFields("uuid")
            .isEqualTo(expected)
    }

    private fun sendFile(filename: String) = objectMapper.readResource<Array<Event>>(filename).sendEventsOnNats()

    private fun Array<Event>.sendEventsOnNats() {
        this.forEach {
            it.sendEventOnNats()
        }
    }

    private fun Event.sendEventOnNats() {
        val inputStream = natsClientBuilderMock.client.consumerStreams["event-stream"] as? NatsClientMock.NatsConsumerStreamMock<Event>
            ?: fail("can't find input stream mock")

        val msg = NatsMessage.builder()
            .subject(this.getSubject())
            .build()
        inputStream.handler?.invoke(this, msg)
    }

    private fun setTrueDestinationResponse(mmsi: Int, trueDestination: String) {
        Mockito.reset(trueDestinationService)
        whenever(trueDestinationService.getDestination(mmsi)).thenReturn(trueDestination)
    }

    class NatsRequestReplyBuilder(
        val natsMock: NatsClientMock,
        val objectMapper: ObjectMapper
    ) {

        data class IdentifierObject(
            val imo: Int,
            val unlocode: String,
            val identiefier: String
        )

        private val portcallsMap: MutableList<IdentifierObject> = mutableListOf()
        private val smartfleetMap: MutableList<IdentifierObject> = mutableListOf()
        fun addPortcallId(imo: Int, unlocode: String, portcallId: String): NatsRequestReplyBuilder {
            return portcallsMap.set(imo, unlocode, portcallId)
        }

        fun addSmartfleetInfo(imo: Int, unlocode: String, fleetId: String): NatsRequestReplyBuilder {
            return smartfleetMap.set(imo, unlocode, fleetId)
        }

        private fun MutableList<IdentifierObject>.set(imo: Int, unlocode: String, identifier: String): NatsRequestReplyBuilder {
            if (unlocode.isBlank() || identifier.isBlank()) {
                throw Exception("No valid port or portcallId provided")
            }
            this.add(IdentifierObject(imo, unlocode, identifier))
            return this@NatsRequestReplyBuilder
        }

        /**
         * Building adds the combined function response to the NatsClientMock. Keeps in mind that this will overwrite existing behavior.
         * This builder uses the mutability of the passed NatsClientMock to add the functionality
         */
        fun build() {
            NatsClientMock.responders["portreporter-monitor.request.v1.identifiers"] = mutableListOf(
                Headers().apply { add("application.name", "portcallplus") } to { message, _ ->
                    val msg = objectMapper.readValue<EventMetadataService.RequestMetadata>(message)
                    val matchingPortcall = portcallsMap.find { it.imo.toString() == msg.imo && it.unlocode == msg.port }
                    if (matchingPortcall != null) {
                        objectMapper.writeValueAsBytes(PortcallPlusMetadata(matchingPortcall.identiefier))
                    } else null
                },
                Headers().apply { add("application.name", "smartfleet") } to { message, _ ->
                    val msg = objectMapper.readValue<EventMetadataService.RequestMetadata>(message)
                    val matchingSmartfleet = smartfleetMap.find { it.imo.toString() == msg.imo && it.unlocode == msg.port }
                    if (matchingSmartfleet != null) {
                        val fleetMinimal = FleetMinimal(matchingSmartfleet.identiefier, "", "", "", "", FleetMinimal.Type.ADD)
                        objectMapper.writeValueAsBytes(Metadata(matchingSmartfleet.imo.toString(), listOf(fleetMinimal)))
                    } else null
                }
            )
        }
    }

    data class Testcase(
        val portcallId: String,
        val mmsi: Int? = null,
        val imo: Int? = null,
        val unlocode: String? = null,
    ) {
        val shouldContain: MutableList<PortReporterEvent> = mutableListOf()
        val shouldNotContain: MutableList<PortReporterEvent> = mutableListOf()

        fun withEvents(body: TestcaseBuilder.() -> Unit): Testcase {
            val builder = TestcaseBuilder(shouldContain)
            builder.body()
            return this
        }

        fun withoutEvents(body: TestcaseBuilder.() -> Unit): Testcase {
            val builder = TestcaseBuilder(shouldNotContain)
            builder.body()
            return this
        }

        inner class TestcaseBuilder(
            private val list: MutableList<PortReporterEvent>
        ) {
            fun event(date: String, type: String) {
                val e = PortReporterEvent(
                    eventType = type,
                    portcallId = portcallId,
                    port = unlocode,
                    ship = PortcallShipDetails(
                        mmsi = mmsi?.toString(),
                        imo = imo?.toString(),
                        location = null
                    ),
                    location = null,
                    eventTime = Date.from(ZonedDateTime.parse(date).toInstant()),
                    recordTime = Date.from(ZonedDateTime.parse(date).toInstant())
                )
                list.add(e)
            }
        }
    }

    private fun approximateDateComparator(maxDelta: Duration) = fun(d1: Date?, d2: Date?): Int {
        val d = if (d1 != null && d2 != null) {
            Duration.between(d1.toInstant(), d2.toInstant()).abs()
        } else {
            null
        }

        return if (d != null && d > maxDelta) {
            d1?.compareTo(d2) ?: -1
        } else {
            0
        }
    }
}

inline fun <reified T> ObjectMapper.readResource(filename: String): T {
    val url = javaClass.classLoader.getResource(filename) ?: fail("can't load $filename")
    return readValue<T>(url)
}
