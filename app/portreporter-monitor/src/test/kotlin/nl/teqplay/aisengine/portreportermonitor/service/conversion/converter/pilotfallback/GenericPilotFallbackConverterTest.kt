package nl.teqplay.aisengine.portreportermonitor.service.conversion.converter.pilotfallback

import nl.teqplay.aisengine.event.model.AreaEndEvent
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.portreportermonitor.createPort
import nl.teqplay.aisengine.portreportermonitor.model.EnrichEvent
import nl.teqplay.aisengine.portreportermonitor.model.EventMetadata
import nl.teqplay.aisengine.portreportermonitor.model.GetEventMetadata
import nl.teqplay.aisengine.portreportermonitor.model.constant.EventType
import nl.teqplay.aisengine.portreportermonitor.service.EventMetadataService
import nl.teqplay.aisengine.portreportermonitor.service.PilotStateService
import nl.teqplay.skeleton.common.BaseTest
import nl.teqplay.skeleton.model.Location
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.time.Instant
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class GenericPilotFallbackConverterTest : BaseTest() {
    private val pilotStateService = mock<PilotStateService>()

    private val converter = GenericPilotFallbackConverter(
        pilotStateService,
    )

    private val eventMetadataService = mock<EventMetadataService>().apply {
        whenever(requestMetadata(any(), any())).thenReturn(EventMetadata(portcallId = "PORTCALL-123"))
    }

    private fun eventTypes() = Stream.builder<Arguments>().apply {
        add(Arguments.of(EventType.PILOTBOARDINGPLACE_ATAE, 90))
        add(Arguments.of(EventType.PILOTBOARDINGPLACE_ATDE, 270))
        add(Arguments.of(null, null))
    }.build()

    private fun shouldConvert() = Stream.builder<Arguments>().apply {
        // Inbound or outbound, state matches direction: no event should be converted
        add(Arguments.of(false, 90, PilotStateService.PilotState.ONBOARD))
        add(Arguments.of(false, 270, PilotStateService.PilotState.DISEMBARKED))

        // Inbound, no pilot or in other direction: should convert
        add(Arguments.of(true, 90, null))
        add(Arguments.of(true, 90, PilotStateService.PilotState.DISEMBARKED))

        // Outbound, no pilot or in other direction: should convert
        add(Arguments.of(true, 270, null))
        add(Arguments.of(true, 270, PilotStateService.PilotState.ONBOARD))

        // Heading not known: disregard, we can't determine the direction
        add(Arguments.of(false, null, null))
        add(Arguments.of(false, null, PilotStateService.PilotState.ONBOARD))
        add(Arguments.of(false, null, PilotStateService.PilotState.DISEMBARKED))
    }.build()

    @ParameterizedTest
    @MethodSource("eventTypes")
    fun `converts to the correct event type`(expected: String?, heading: Int?) {
        val type = with(converter) {
            getEvent(heading = heading, eventMetadataService::requestMetadata).getEventType()
        }

        assertEquals(expected, type)
    }

    @ParameterizedTest
    @MethodSource("shouldConvert")
    fun `correctly decides whether the event should be converted`(expected: Boolean, heading: Int?, state: PilotStateService.PilotState?) {
        whenever(pilotStateService.hasPilot(123456789, "NLRTM")).thenReturn(state)

        assertEquals(expected, converter.shouldConvert(getEvent(heading, eventMetadataService::requestMetadata)))
    }

    private fun getEvent(
        heading: Int?,
        getEventMetadata: GetEventMetadata,
    ) = EnrichEvent<AreaEndEvent>(
        originalEvent = AreaEndEvent(
            _id = "test-event",
            startEventId = null,
            ship = AisShipIdentifier(123456789, null),
            area = AreaIdentifier(id = "test-pbp", type = AreaIdentifier.AreaType.PILOT_BOARDING_PLACE),
            heading = heading,
            location = Location(1.2, 3.4),
            actualTime = Instant.now(),
            berth = null,
            draught = null,
            speedOverGround = null,
        ),
        port = createPort(unlocode = "NLRTM"),
        portReporterEvents = emptyList(),
        shipRegisterInfo = null,
        getEventMetadata = getEventMetadata,
    )
}
