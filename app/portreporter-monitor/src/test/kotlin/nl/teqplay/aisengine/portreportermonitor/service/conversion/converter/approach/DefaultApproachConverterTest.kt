package nl.teqplay.aisengine.portreportermonitor.service.conversion.converter.approach

import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.portreportermonitor.createEnrichAreaEndEvent
import nl.teqplay.aisengine.portreportermonitor.createEnrichAreaStartEvent
import nl.teqplay.aisengine.portreportermonitor.createNamedLocation
import nl.teqplay.aisengine.portreportermonitor.createPort
import nl.teqplay.aisengine.portreportermonitor.createPortReporterEvent
import nl.teqplay.aisengine.portreportermonitor.createPortcallShipDetails
import nl.teqplay.aisengine.portreportermonitor.model.Location
import nl.teqplay.aisengine.portreportermonitor.model.LocationType
import nl.teqplay.aisengine.portreportermonitor.model.PortcallEventContext
import nl.teqplay.aisengine.portreportermonitor.service.EventMetadataService
import nl.teqplay.aisengine.portreportermonitor.utils.updateEventsUuid
import nl.teqplay.aisengine.testing.event.defaultImo
import nl.teqplay.aisengine.testing.event.defaultMmsiString
import nl.teqplay.aisengine.testing.event.defaultShipName
import nl.teqplay.aisengine.testing.event.defaultTime
import nl.teqplay.skeleton.common.BaseTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import java.util.Date

class DefaultApproachConverterTest : BaseTest() {
    private val converter = DefaultApproachConverter()
    private val ushouApproachArea = AreaIdentifier(
        id = "E0E8CD2FCFFB2664300390D825B1702794B74A28",
        type = AreaIdentifier.AreaType.APPROACH_POINT,
        name = "Fred Hartman Bridge",
        unlocode = "USHOU"
    )
    private val portReporterLocation = createNamedLocation(
        name = "Fred Hartman Bridge",
        type = LocationType.APPROACH,
        id = "E0E8CD2FCFFB2664300390D825B1702794B74A28"
    )
    private val portReporterShip = createPortcallShipDetails(
        mmsi = defaultMmsiString,
        imo = defaultImo.toString(),
        name = defaultShipName,
        location = Location(0.0, 0.0)
    )

    @Test
    fun `should process approach event when inbound and start area event`() {
        val enrichedEvent = createEnrichAreaStartEvent(
            area = ushouApproachArea,
            heading = 230,
            port = createPort(unlocode = "USHOU"),
            getEventMetadata = mock<EventMetadataService>()::requestMetadata
        )
        val expectedPortReporterEvent = createPortReporterEvent(
            eventType = "approachArea.ata.vessel",
            port = "USHOU",
            source = "Teqplay AIS",
            recordTime = Date.from(defaultTime),
            eventTime = Date.from(defaultTime),
            ship = portReporterShip,
            location = portReporterLocation,
            context = PortcallEventContext()
        )
        val expected = enrichedEvent.copy(portReporterEvents = listOf(expectedPortReporterEvent))

        val convertResult = converter.convert(enrichedEvent)
        val result = convertResult.updateEventsUuid(expectedPortReporterEvent.uuid)

        assertEquals(expected, result)
    }

    @Test
    fun `should process approach event when outbound and end area event`() {
        val enrichedEvent = createEnrichAreaEndEvent(
            area = ushouApproachArea,
            heading = 150,
            port = createPort(unlocode = "USHOU"),
            getEventMetadata = mock<EventMetadataService>()::requestMetadata
        )
        val expectedPortReporterEvent = createPortReporterEvent(
            eventType = "approachArea.atd.vessel",
            port = "USHOU",
            source = "Teqplay AIS",
            recordTime = Date.from(defaultTime),
            eventTime = Date.from(defaultTime),
            ship = portReporterShip,
            location = portReporterLocation,
            context = PortcallEventContext()
        )
        val expected = enrichedEvent.copy(portReporterEvents = listOf(expectedPortReporterEvent))

        val convertResult = converter.convert(enrichedEvent)
        val result = convertResult.updateEventsUuid(expectedPortReporterEvent.uuid)

        assertEquals(expected, result)
    }

    @Test
    fun `should NOT process approach event when inbound and end area event`() {
        val enrichedEvent = createEnrichAreaEndEvent(
            area = ushouApproachArea,
            heading = 230,
            port = createPort(unlocode = "USHOU"),
            getEventMetadata = mock<EventMetadataService>()::requestMetadata
        )
        val convertResult = converter.convert(enrichedEvent)

        assertEquals(enrichedEvent, convertResult)
    }

    @Test
    fun `should NOT process approach event when outbound and start area event`() {
        val enrichedEvent = createEnrichAreaStartEvent(
            area = ushouApproachArea,
            heading = 150,
            port = createPort(unlocode = "USHOU"),
            getEventMetadata = mock<EventMetadataService>()::requestMetadata
        )
        val convertResult = converter.convert(enrichedEvent)

        assertEquals(enrichedEvent, convertResult)
    }
}
