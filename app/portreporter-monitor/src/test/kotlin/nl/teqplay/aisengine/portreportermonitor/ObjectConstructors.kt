package nl.teqplay.aisengine.portreportermonitor

import nl.teqplay.aisengine.aisstream.model.AisMessage
import nl.teqplay.aisengine.aisstream.model.TransponderPosition
import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.aisengine.event.interfaces.EncounterMetadata
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.portreportermonitor.model.EnrichEvent
import nl.teqplay.aisengine.portreportermonitor.model.GetEventMetadata
import nl.teqplay.aisengine.portreportermonitor.model.LocationType
import nl.teqplay.aisengine.portreportermonitor.model.NamedLocation
import nl.teqplay.aisengine.portreportermonitor.model.PortReporterEvent
import nl.teqplay.aisengine.portreportermonitor.model.PortcallEventContext
import nl.teqplay.aisengine.portreportermonitor.model.PortcallShipDetails
import nl.teqplay.aisengine.portreportermonitor.utils.toSkeletonLocation
import nl.teqplay.aisengine.shiphistory.model.AisCurrentMessage
import nl.teqplay.aisengine.shiphistory.model.AisDerivedMessage
import nl.teqplay.aisengine.testing.event.createAreaEndEvent
import nl.teqplay.aisengine.testing.event.createAreaStartEvent
import nl.teqplay.aisengine.testing.event.createEncounterEndEvent
import nl.teqplay.aisengine.testing.event.createEncounterStartEvent
import nl.teqplay.aisengine.testing.event.defaultImo
import nl.teqplay.aisengine.testing.event.defaultLocation
import nl.teqplay.aisengine.testing.event.defaultMmsi
import nl.teqplay.aisengine.testing.event.defaultPortAreaIdentifier
import nl.teqplay.aisengine.testing.event.defaultShipName
import nl.teqplay.aisengine.testing.event.defaultTime
import nl.teqplay.csi.model.ship.info.ShipMetadata
import nl.teqplay.csi.model.ship.info.ShipRegisterInfo
import nl.teqplay.csi.model.ship.info.component.ShipAdministration
import nl.teqplay.csi.model.ship.info.component.ShipCategories
import nl.teqplay.csi.model.ship.info.component.ShipCommunication
import nl.teqplay.csi.model.ship.info.component.ShipCompany
import nl.teqplay.csi.model.ship.info.component.ShipDimensions
import nl.teqplay.csi.model.ship.info.component.ShipIdentifiers
import nl.teqplay.csi.model.ship.info.component.ShipScores
import nl.teqplay.csi.model.ship.info.component.ShipSpecification
import nl.teqplay.csi.model.ship.info.component.ShipTypes
import nl.teqplay.poma.api.v1.Anchorage
import nl.teqplay.poma.api.v1.AvailabilityType
import nl.teqplay.poma.api.v1.Berth
import nl.teqplay.poma.api.v1.CargoCategoryType
import nl.teqplay.poma.api.v1.CargoType
import nl.teqplay.poma.api.v1.Country
import nl.teqplay.poma.api.v1.CustomArea
import nl.teqplay.poma.api.v1.FunctionType
import nl.teqplay.poma.api.v1.Location
import nl.teqplay.poma.api.v1.Lock
import nl.teqplay.poma.api.v1.MooringType
import nl.teqplay.poma.api.v1.Port
import nl.teqplay.poma.api.v1.VesselTypeAllowed
import java.time.Instant
import java.util.Date
import java.util.UUID
import nl.teqplay.aisengine.portreportermonitor.model.Location as PortReporterLocation
import nl.teqplay.skeleton.model.Location as SkeletonLocation

fun createPort(
    name: String = "defaultName",
    displayName: String = "default name",
    unlocode: String? = "NLDFTN",
    countryCode: String? = "NL",
    country: Country? = null,
    location: Location = Location(1.0, 1.0),
    area: List<Location> = listOf(Location(1.0, 1.0), Location(1.0, 3.0), Location(3.0, 1.0)),
    areaSizeInM2: Long? = null,
    manualOverriddenArea: Boolean = false,
    outerArea: List<Location> = emptyList(),
    nm12Area: List<Location> = emptyList(),
    nm60Area: List<Location> = emptyList(),
    nm80Area: List<Location> = emptyList(),
    nm120Area: List<Location> = emptyList(),
    eosArea: List<Location> = emptyList(),
    alternativeNames: List<String> = emptyList(),
    destinations: List<String> = emptyList(),
    mainPort: String? = null,
    margin: Double = 0.0,
    uniqueId: String? = "uniqueId",
    modelType: String = "",
    source: String? = null,
    sourceType: String? = null,
    _id: String? = "_id",
    validatedByUser: Boolean = true,
) = Port(
    name = name,
    displayName = displayName,
    unlocode = unlocode,
    countryCode = countryCode,
    country = country,
    location = location,
    area = area,
    areaSizeInM2 = areaSizeInM2,
    manualOverriddenArea = manualOverriddenArea,
    outerArea = outerArea,
    nm12Area = nm12Area,
    nm60Area = nm60Area,
    nm80Area = nm80Area,
    nm120Area = nm120Area,
    eosArea = eosArea,
    alternativeNames = alternativeNames,
    destinations = destinations,
    mainPort = mainPort,
    margin = margin,
    uniqueId = uniqueId,
    modelType = modelType,
    source = source,
    sourceType = sourceType,
    _id = _id,
    validatedByUser = validatedByUser,
)

fun createShipAdministration(
    constructionYear: Int? = null,
    flag: String? = null,
    flagCode: String? = null,
    classRegister: String? = null,
    owner: ShipCompany? = null,
    techManager: ShipCompany? = null,
    shipCat: String? = null,
    subCat: String? = null,
    subCatCode: String? = null,
) = ShipAdministration(constructionYear, flag, flagCode, classRegister, owner, techManager, shipCat, subCat, subCatCode)

fun createShipIdentifiers(
    mmsi: String? = null,
    imo: String? = null,
    eni: String? = null,
    callSign: String? = null,
    name: String? = null,
) = ShipIdentifiers(mmsi, imo, eni, callSign, name)

fun createShipScores(
    greenAward: Boolean? = null,
    esiScore: Double? = null,
) = ShipScores(greenAward, esiScore)

fun createShipRegisterInfo(mmsi: Int, imo: Int, name: String): ShipRegisterInfo {
    return createShipRegisterInfo(
        identifiers = ShipIdentifiers(
            mmsi = mmsi.toString(),
            imo = imo.toString(),
            eni = null,
            callSign = null,
            name = name,
        ),
    )
}

fun createShipRegisterInfo(
    identifiers: ShipIdentifiers = createShipIdentifiers(),
    types: ShipTypes = ShipTypes(),
    categories: ShipCategories = ShipCategories(),
    dimensions: ShipDimensions = ShipDimensions(),
    administration: ShipAdministration = createShipAdministration(),
    specification: ShipSpecification = ShipSpecification(),
    communication: ShipCommunication = ShipCommunication(emptyList()),
    scores: ShipScores = createShipScores(),

    updatedTime: Long? = null,
    syncedAt: Instant? = null,
    ticketId: String? = null,
    metadata: ShipMetadata = ShipMetadata(),
    _id: String = "",
) = ShipRegisterInfo(identifiers, types, categories, dimensions, administration, specification, communication, scores, updatedTime, syncedAt, ticketId, metadata, _id)

fun createBerth(
    uniqueId: String? = null,
    authorityId: String? = null,
    name: String = "",
    displayName: String = "",
    nameLong: String? = null,
    terminalName: String? = null,
    terminalId: String? = null,
    harbourName: String? = null,
    harbourId: String? = null,
    ports: List<String> = emptyList(),
    length: Double? = null,
    width: Double? = null,
    draught: Double? = null,
    owner: String? = null,
    quayId: String? = null,
    dangerousGoodsLevel: Int? = null,
    vesselTypeAllowed: Set<VesselTypeAllowed>? = null,
    availabilityType: AvailabilityType? = null,
    mooringType: MooringType? = null,
    cargoCategoryType: Set<CargoCategoryType>? = null,
    cargoType: Set<CargoType>? = null,
    functionType: Set<FunctionType>? = null,
    bollardIds: List<String>? = null,
    location: Location = Location(1.0, 1.0),
    area: List<Location> = emptyList(),
    areaSizeInM2: Long? = null,
    manualOverriddenArea: Boolean = false,
    modelType: String = "",
    source: String? = null,
    sourceType: String? = null,
    _id: String? = null,
    validatedByUser: Boolean = true,
    mainPort: String? = null,
) = Berth(uniqueId, authorityId, name, displayName, nameLong, terminalName, terminalId, harbourName, harbourId, ports, length, width, draught, owner, quayId, dangerousGoodsLevel, vesselTypeAllowed, availabilityType, mooringType, cargoCategoryType, cargoType, functionType, bollardIds, location, area, areaSizeInM2, manualOverriddenArea, modelType, source, sourceType, _id, validatedByUser, mainPort)

fun createAnchorage(
    name: String = "",
    ports: List<String> = listOf(""),
    margin: Double = 0.0,
    ownerId: String? = null,
    maxLength: Int? = null,
    draught: Float? = null,
    location: Location = Location(1.0, 1.0),
    area: List<Location> = emptyList(),
    areaSizeInM2: Long? = null,
    manualOverriddenArea: Boolean = false,
    humanReadableName: String? = null,
    uniqueId: String? = null,
    modelType: String = "",
    source: String? = null,
    sourceType: String? = null,
    _id: String? = null,
    validatedByUser: Boolean = false,
) = Anchorage(name, ports, margin, ownerId, maxLength, draught, location, area, areaSizeInM2, manualOverriddenArea, humanReadableName, uniqueId, modelType, source, sourceType, _id)

fun createLock(
    name: String = "",
    isrsCode: String? = null,
    ports: List<String> = emptyList(),
    margin: Double = 0.0,
    location: Location = Location(1.0, 1.0),
    area: List<Location> = emptyList(),
    /** Size of the area in square meters */
    areaSizeInM2: Long = 1L,
    manualOverriddenArea: Boolean = false,
    uniqueId: String? = null,
    modelType: String = "",
    source: String? = null,
    sourceType: String? = null,
    _id: String? = null,
    validatedByUser: Boolean = true,
) = Lock(name, isrsCode, ports, margin, location, area, areaSizeInM2, manualOverriddenArea, uniqueId, modelType, source, sourceType, _id)

fun createCustomArea(
    name: String = "",
    /** the ports that are in the region, set of unlocodes */
    ports: List<String> = emptyList(),
    /** The countries the region involves, set of countryCodes */
    countries: Set<String>? = null,
    location: Location = Location(1.0, 1.0),
    area: List<Location> = emptyList(),
    /** Size of the area in square meters */
    areaSizeInM2: Long? = null,
    manualOverriddenArea: Boolean = false,
    /** The area of the region */
    outerArea: List<Location> = emptyList(),
    /** The other names the region is known by */
    alternativeNames: List<String> = emptyList(),
    /** The destinations the ship can have when going to the region */
    destinations: List<String> = emptyList(),
    note: String = "",
    uniqueId: String? = null,
    validatedByUser: Boolean = true,
    modelType: String = "",
    source: String? = null,
    sourceType: String? = null,
    _id: String? = null,
) = CustomArea(name, ports, countries, location, area, areaSizeInM2, manualOverriddenArea, outerArea, alternativeNames, destinations, note, uniqueId, validatedByUser, modelType, source, sourceType, _id)

fun createPortReporterEvent(
    uuid: String = UUID.randomUUID().toString(),
    eventType: String = "",
    source: String? = null,
    portcallId: String? = null,
    port: String? = null,
    recordTime: Date = Date(),
    eventTime: Date = recordTime,
    ship: PortcallShipDetails = createPortcallShipDetails(),
    location: NamedLocation = createNamedLocation(),
    context: PortcallEventContext? = null,
) = PortReporterEvent(uuid, eventType, source, portcallId, port, recordTime, eventTime, ship, location, context = context)

fun createPortcallShipDetails(
    mmsi: String? = null,
    imo: String? = null,
    eni: String? = null,
    name: String? = null,
    location: PortReporterLocation = createPortreporterLocation(),
    length: Double? = null,
    width: Double? = null,
) = PortcallShipDetails(mmsi, imo, eni, name, location, length, width)

fun createNamedLocation(
    name: String = "",
    type: LocationType = LocationType.LOCK,
    id: String = "",
) = NamedLocation(name, type, id)

fun createPortreporterLocation(
    lat: Double = 1.0,
    long: Double = 1.0,
) = PortReporterLocation(lat, long)

fun createEncounterPortReporterEvent(
    eventType: String,
    serviceVesselName: String,
    location: SkeletonLocation = SkeletonLocation(0.0, 0.0),
    port: String? = "NLDFTN",
): PortReporterEvent {
    return PortReporterEvent(
        uuid = "TEST_ID",
        eventType = eventType,
        source = "Teqplay AIS",
        portcallId = null,
        port = port,
        recordTime = Date.from(defaultTime),
        eventTime = Date.from(defaultTime),
        ship = PortcallShipDetails(
            mmsi = "111111111",
            imo = "1111111",
            name = defaultShipName,
            location = PortReporterLocation(
                latitude = location.lat,
                longitude = location.lon,
            ),
        ),
        location = null,
        context = PortcallEventContext(
            serviceShip = PortcallShipDetails(
                mmsi = "222222222",
                imo = "2222222",
                name = serviceVesselName,
                location = PortReporterLocation(
                    latitude = location.lat,
                    longitude = location.lon,
                ),
            ),
            serviceShipName = serviceVesselName,
        ),
    )
}

fun createEnrichEncounterStartEvent(
    encounterType: EncounterEvent.EncounterType? = null,
    location: SkeletonLocation = defaultLocation,
    port: Port = createPort(),
    shipRegisterInfo: ShipRegisterInfo? = createShipRegisterInfo(defaultMmsi, defaultImo, defaultShipName),
    metadata: EncounterMetadata? = null,
    getEventMetadata: GetEventMetadata,
): EnrichEvent<EncounterEvent> {
    val event = if (encounterType != null) {
        createEncounterStartEvent(encounterType = encounterType, location = location, metadata = metadata)
    } else {
        createEncounterStartEvent(location = location, metadata = metadata)
    }

    return EnrichEvent(
        originalEvent = event,
        portReporterEvents = emptyList(),
        shipRegisterInfo = shipRegisterInfo,
        port = port,
        getEventMetadata = getEventMetadata,
    )
}

fun createEnrichEncounterEndEvent(
    encounterType: EncounterEvent.EncounterType? = null,
    location: SkeletonLocation = defaultLocation,
    port: Port = createPort(),
    shipRegisterInfo: ShipRegisterInfo? = createShipRegisterInfo(defaultMmsi, defaultImo, defaultShipName),
    metadata: EncounterMetadata? = null,
    getEventMetadata: GetEventMetadata,
): EnrichEvent<EncounterEvent> {
    val event = if (encounterType != null) {
        createEncounterEndEvent(encounterType = encounterType, location = location, metadata = metadata)
    } else {
        createEncounterEndEvent(location = location, metadata = metadata)
    }

    return EnrichEvent(
        originalEvent = event,
        portReporterEvents = emptyList(),
        shipRegisterInfo = shipRegisterInfo,
        port = port,
        getEventMetadata = getEventMetadata,
    )
}

fun createEnrichAreaStartEvent(
    area: AreaIdentifier = defaultPortAreaIdentifier,
    heading: Int? = null,
    draught: Float? = null,
    location: SkeletonLocation = defaultLocation,
    port: Port = createPort(),
    shipRegisterInfo: ShipRegisterInfo? = createShipRegisterInfo(defaultMmsi, defaultImo, defaultShipName),
    getEventMetadata: GetEventMetadata,
): EnrichEvent<AreaEvent> {
    val event = createAreaStartEvent(
        area = area,
        location = location,
        heading = heading,
        draught = draught
    )

    return EnrichEvent(
        originalEvent = event,
        portReporterEvents = emptyList(),
        shipRegisterInfo = shipRegisterInfo,
        port = port,
        getEventMetadata = getEventMetadata,
    )
}

fun createEnrichAreaEndEvent(
    area: AreaIdentifier = defaultPortAreaIdentifier,
    heading: Int? = null,
    draught: Float? = null,
    location: SkeletonLocation = defaultLocation,
    port: Port = createPort(),
    shipRegisterInfo: ShipRegisterInfo? = createShipRegisterInfo(defaultMmsi, defaultImo, defaultShipName),
    getEventMetadata: GetEventMetadata,
): EnrichEvent<AreaEvent> {
    val event = createAreaEndEvent(
        area = area,
        location = location,
        heading = heading,
        draught = draught
    )

    return EnrichEvent(
        originalEvent = event,
        portReporterEvents = emptyList(),
        shipRegisterInfo = shipRegisterInfo,
        port = port,
        getEventMetadata = getEventMetadata,
    )
}

fun createAisCurrentMessage(
    mmsi: Int = 1,
    messageTime: Instant = Instant.now(),
    sources: Set<String> = emptySet(),
    location: Location = Location(1.0, 1.0),
    heading: Int? = null,
    positionAccuracy: AisMessage.PositionAccuracy? = null,
    speedOverGround: Float? = null,
    courseOverGround: Float? = null,
    status: AisMessage.ShipStatus? = null,
    rateOfTurn: Int? = null,
    specialManeuverStatus: AisMessage.SpecialManeuverStatus? = null,
    imo: Int? = null,
    name: String? = null,
    callSign: String? = null,
    shipType: AisMessage.ShipType? = null,
    draught: Float? = null,
    eta: Instant? = null,
    destination: String? = null,
    transponderPosition: TransponderPosition? = null,
    positionSensorType: AisMessage.PositionSensorType? = null,
    aisVersion: AisMessage.AisVersionIndicator? = null,
    usingDataTerminal: Boolean? = null,
    eni: Int? = null,
    derivedMessage: AisDerivedMessage? = null
): AisCurrentMessage = AisCurrentMessage(mmsi, messageTime, sources, location.toSkeletonLocation(), heading, positionAccuracy, speedOverGround, courseOverGround, status, rateOfTurn, specialManeuverStatus, imo, name, callSign, shipType, draught, eta, destination, transponderPosition, positionSensorType, aisVersion, usingDataTerminal, eni, derivedMessage)
