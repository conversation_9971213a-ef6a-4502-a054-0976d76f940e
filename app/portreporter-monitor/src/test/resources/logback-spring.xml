<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <springProperty scope="context" name="separate-request-logging" source="common.separate-request-logging"/>

    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">

        <encoder>
            <pattern>%d{ISO8601} %highlight(%-5level) [%cyan(%thread)] %yellow(%C{5}): %msg%n%throwable</pattern>
        </encoder>

    </appender>

    <!-- LOG everything at INFO level -->
    <root level="info">

        <appender-ref ref="console"/>

    </root>

    <if condition='isDefined("separate-request-logging") &amp;&amp; property("separate-request-logging").equals("true")'>
        <then>
            <!-- Log all the request logging stuff to a separate file -->
            <include resource="requestlogging.xml"/>
        </then>
    </if>

</configuration>
