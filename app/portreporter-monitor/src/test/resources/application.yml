spring:
  main:
    allow-bean-definition-overriding: true

portreporter-monitor:
  nats-subject: event.>
nats:
  enabled: true
  url:
  username:
  password:

  event-stream:
    enabled: true
    url:
    username:
    password:
poma:
  url:
  realm:
  clientId:
  clientSecret:
  domain:
csi:
  url:
  realm:
  clientId:
  clientSecret:
  domain:
rabbitmq:
  uri:
  exchange: AIS_ENGINE_PORTREPORTING_DEV
internal-api:
  url:
  realm:
  clientId:
  clientSecret:
  domain:
