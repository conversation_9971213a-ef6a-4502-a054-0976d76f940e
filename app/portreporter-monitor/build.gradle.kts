buildscript {
    project.extra.set("baseVersion", "2.1.0")
}

plugins {
    id("ais-engine.kotlin-app-webmvc-conventions")
    id("org.cyclonedx.bom") version cyclonedxBomVersion
}

dependencies {
    implementation(project(":lib:platform"))
    implementation(project(":api:nats-stream-event"))
    implementation(project(":api:client-ship-history"))

    implementation("org.locationtech.jts:jts-core:$jtsVersion")

    implementation("nl.teqplay.skeleton:poma-client:$skeletonVersion")
    implementation("nl.teqplay.skeleton:csi-client:$skeletonVersion")
    implementation("nl.teqplay.skeleton:util-location2:$skeletonVersion")
    implementation("nl.teqplay.skeleton:common-network:$skeletonVersion")
    implementation("nl.teqplay.skeleton:rabbitmq:$skeletonVersion")

    implementation("nl.teqplay.skeleton:nats:$skeletonVersion")
    testImplementation("nl.teqplay.skeleton:nats-test:$skeletonVersion")

    implementation("nl.teqplay.smartfleet:api:$smartFleetVersion") {
        exclude(group = "nl.teqplay.csi", module = "api")
        exclude(group = "nl.teqplay.platform", module = "api")
        exclude(group = "nl.teqplay.vesselvoyage", module = "api")
    }

    testImplementation(project(":lib:testing-event"))
}

tasks.cyclonedxBom {
    setIncludeConfigs(listOf("runtimeClasspath"))
    setOutputFormat("json")
    setOutputName("bom")
}
