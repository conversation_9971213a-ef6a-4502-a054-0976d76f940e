rootProject.name = "ais-engine"

buildCache {
    local {
        directory = File(rootDir, "build-cache")
        removeUnusedEntriesAfterDays = 14
    }
}

lib("ais-stream-history-consumer")
lib("bucketing")
lib("bucketing-event")
lib("bucketing-ship")
lib("common")
lib("encounter-monitor-common")
lib("keycloak-s2s-server-any-verified-user")
lib("platform")
lib("platform-keycloak-auth")
lib("platform-keycloak-role-auth")
lib("revents-csi-common")
lib("revents-engine-common")
lib("revents-global")
lib("ship-history-common")
lib("ship-state")
lib("testing-event")
lib("testing-platform")

api("client-common")
api("client-revents-engine-api")
api("client-ship-history")
api("client-event-history")
api("models")
api("nats-stream-ais-common")
api("nats-stream-ais-consume-diff")
api("nats-stream-ais-consume-history")
api("nats-stream-ais-publish")
api("nats-stream-event")
api("revents-profile")

app("ais-diff")
app("ais-rabbitmq")
app("ais-stream")
app("anchor-monitor")
app("area-monitor")
app("berth-monitor")
app("encounter-monitor")
app("event-converter")
app("event-history")
app("event-history-migrator")
app("event-history-processor")
app("portreporter-monitor")
app("revents-engine")
app("revents-engine-api")
app("revents-engine-orchestrator")
app("ship-history")
app("ship-history-area-index")
app("ship-history-bucket-copier")
app("ship-history-migrator")
app("ship-history-processor")
app("stop-monitor")

util()

fun lib(name: String) {
    include(":lib:$name")
}

fun api(name: String) {
    include(":api:$name")
}

fun app(name: String) {
    include(":app:$name")
}

fun util() {
    include(":util")
}
