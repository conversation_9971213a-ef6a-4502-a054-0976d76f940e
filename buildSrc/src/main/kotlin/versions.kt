// TODO: use project extra instead, so the dependencies from :api can be overwritten from the outside

val skeletonVersion = "2.9.1-b122.1"
val pomaVersion = "20250304-b34.1"
val smartFleetVersion = "20241226-b12505406988.6.1"
val vesselVoyageVersion = "20250602-b548.1"
val platformVersion = "9.6.4"
val keycloakVersion = "26.2.5"
val kotlinCoroutinesVersion = "1.10.2"

val junitVersion = "5.13.0"
val mockitoVersion = "5.18.0"
val mockitoKotlinVersion = "5.4.0"
val jacksonVersion = "2.19.0"

val mongodbVersion = "5.5.0"

val kotlinLoggingVersion = "7.0.7"
val janinoVersion = "3.1.12"

// 4.3.0 has an issue where the compiler doesn't recognize org.awaitility.kotlin.await
val awaitilityVersion = "4.2.2"
val rtreeVersion = "0.12"
val jtsVersion = "1.20.0"
val rxjavaReactiveAdapter = "1.2.1"

val awsVersion = "1.12.783"
val jaxbVersion = "2.3.1"
val guavaVersion = "33.4.8-jre"
val xsyncVersion = "1.3"

val springdocVersion = "2.8.8"
val springKubernetesVersion = "3.2.1"

// integration test dependencies
val testContainersVersion = "1.21.1"
val jnatsVersion = "2.21.1"

// ----- ais-stream -----
val aisLibVersion = "2.4.2-TEQPLAY"
val eclipseMqttvVersion = "1.2.5"
// ----------------------

// ----- revents-engine-orchestrator -----
val fabric8KubernetesClientVersion = "7.3.1"
// ------------------------

val cyclonedxBomVersion = "2.3.1"
