val kotlinVersion = "1.9.25"
val springBootVersion = "3.4.6"

val ktlintVersion = "10.3.0"
val koverVersion = "0.9.1"

val dockerVersion = "9.4.0"
val ecrVersion = "0.7.0"

plugins {
    `kotlin-dsl`
}

repositories {
    gradlePluginPortal()
}

dependencies {
    implementation("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion")
    implementation("org.jetbrains.kotlin:kotlin-allopen:$kotlinVersion")

    implementation("org.springframework.boot:spring-boot-gradle-plugin:$springBootVersion")

    implementation("org.jlleitschuh.gradle:ktlint-gradle:$ktlintVersion")
    implementation("org.jetbrains.kotlinx:kover-gradle-plugin:$koverVersion")

    implementation("com.bmuschko:gradle-docker-plugin:$dockerVersion")
    implementation("gradle.plugin.com.patdouble:gradle-aws-ecr-plugin:$ecrVersion")
}