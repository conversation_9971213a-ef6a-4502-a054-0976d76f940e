name: Teqplay AIS Engine backend workflow
run-name : ${{ github.event.head_commit.message }}
on: [push]

jobs:
  main:
    name: " "
    uses: teqplay/actions/.github/workflows/backend-ais-engine.yml@master
    with:
      java_version: 17
      dependency_track: true
      override_gradle_properties: false
    secrets:
      aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
      aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      cm_username: ${{ secrets.CM_USERNAME }}
      cm_password: ${{ secrets.CM_PASSWORD }}
      dt_api_key: ${{ secrets.DT_API_KEY }}
