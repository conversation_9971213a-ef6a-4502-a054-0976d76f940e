options:
  size: 2x
definitions:
  steps:
    - step: &codecov-report
        image:
          name: openjdk:11
        name: Run Kover & Upload to CodeCov
        script:
          # Add S3 credentials
          - PROPFILE=~/.gradle/gradle.properties
          - mkdir -p `dirname $PROPFILE`
          - "[ -e $PROPFILE ] || touch $PROPFILE"
          - grep -q s3_access_key $PROPFILE || echo "s3_access_key = $AWS_ACCESS_KEY" >> $PROPFILE
          - grep -q s3_secret_key $PROPFILE || echo "s3_secret_key = $AWS_SECRET_KEY" >> $PROPFILE

          # Add AWS credentials file
          - FILE=~/.aws/credentials
          - mkdir -p `dirname $FILE`
          - echo "[default]" > $FILE
          - echo "aws_access_key_id = $AWS_ACCESS_KEY" >> $FILE
          - echo "aws_secret_access_key = $AWS_SECRET_KEY" >> $FILE
          - echo "region = eu-west-1" >> $FILE

          # Create Kover report
          - bash ./gradlew koverXmlReport -Dorg.gradle.jvmargs=-Xmx4g

          # Upload report via CodeCov
          - curl https://keybase.io/codecovsecurity/pgp_keys.asc | gpg --no-default-keyring --keyring trustedkeys.gpg --import # One-time step
          - curl -Os https://uploader.codecov.io/latest/linux/codecov
          - curl -Os https://uploader.codecov.io/latest/linux/codecov.SHA256SUM
          - curl -Os https://uploader.codecov.io/latest/linux/codecov.SHA256SUM.sig
          - gpgv codecov.SHA256SUM.sig codecov.SHA256SUM
          - shasum -a 256 -c codecov.SHA256SUM
          - chmod +x codecov
          - ./codecov -t ${CODECOV_TOKEN} -f ./build/reports/kover/report.xml
pipelines:
  branches:
    master:
      - step:
          <<: *codecov-report
    develop:
      - step:
          <<: *codecov-report
  pull-requests:
    '**':
      - step:
          <<: *codecov-report