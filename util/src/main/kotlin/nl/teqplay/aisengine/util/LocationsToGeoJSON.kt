package nl.teqplay.aisengine.util

import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.aisengine.ObjectMapperConfiguration
import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.event.interfaces.LocationBasedEvent
import nl.teqplay.aisengine.testing.event.createStopEndEvent
import nl.teqplay.aisengine.testing.event.createStopStartEvent
import nl.teqplay.skeleton.model.Location
import java.awt.Color
import java.io.File

fun main() {
    val result = LocationsToGeoJSON().getGeoJSON()

    println(result)
}

class LocationsToGeoJSON {
    fun getGeoJSON(): String {
        val objectMapper = ObjectMapperConfiguration().objectMapper()
        val diffMessages = objectMapper.readValue<Array<AisDiffMessage>>(
            File("app/stop-monitor/src/test/resources/trace/205269790.json")
        )
        val oldEvents = listOf(
            createStopStartEvent(location = Location(51.45315333333333, 4.228296666666667)),
            createStopEndEvent(location = Location(51.45306333333334, 4.22838388888889)),

            createStopStartEvent(location = Location(51.44797333333334, 4.229926666666667)),
            createStopEndEvent(location = Location(51.44779, 4.230019166666667)),

            createStopStartEvent(location = Location(51.347229999999996, 4.29831)),
            createStopEndEvent(location = Location(51.347300555555556, 4.297778888888889)),

            createStopStartEvent(location = Location(51.34697333333334, 4.296608333333333)),
            createStopEndEvent(location = Location(51.34627750000001, 4.295572777777777)),

            createStopStartEvent(location = Location(51.345956666666666, 4.287796666666667)),
            createStopEndEvent(location = Location(51.34601555555556, 4.2873938888888885)),
        )
        val newEvents = listOf(
            createStopStartEvent(location = Location(51.453136666666666, 4.228295)),
            createStopEndEvent(location = Location(51.45301833333333, 4.2284275000000004)),
            createStopStartEvent(location = Location(51.347229999999996, 4.297491666666667)),
            createStopEndEvent(location = Location(51.347300555555556, 4.297778888888889)),
            createStopStartEvent(location = Location(51.345956666666666, 4.287796666666667)),
            createStopEndEvent(location = Location(51.34601555555556, 4.2873938888888885))
        )
        val traceFeature = diffMessagesToGeoJSON(diffMessages.toList())
        val oldEventsPointFeature = eventsToGeoJSON(oldEvents, Color.ORANGE)
        val newEventsPointFeature = eventsToGeoJSON(newEvents, Color.GREEN.darker())
        val features = listOf(
            traceFeature
        ) + oldEventsPointFeature + newEventsPointFeature

        return objectMapper.writeValueAsString(
            GeoJSON(features = features)
        )
    }

    private fun eventsToGeoJSON(events: List<LocationBasedEvent>, color: Color? = null): List<GeoJSON.Feature> {
        val eventFeatures = events.map { event ->
            toSingleGeoJSON(event.location, color)
        }
        return eventFeatures
    }

    private fun diffMessagesToGeoJSON(diffs: List<AisDiffMessage>): GeoJSON.Feature {
        // NOTE: this only takes location changes
        val allChangedLocations = diffs.mapNotNull { diff -> diff.location.changed?.new }
        return toLineGeoJSON(allChangedLocations)
    }

    private fun toSingleGeoJSON(location: Location, color: Color?): GeoJSON.Feature {
        val geoLocation = listOf(
            location.lon,
            location.lat
        )
        val properties = if (color != null) {
            val buf = Integer.toHexString(color.rgb)
            val hex = "#" + buf.substring(buf.length - 6)
            mapOf("marker-color" to hex)
        } else {
            emptyMap()
        }
        return GeoJSON.Feature(
            properties = properties,
            geometry = GeoJSON.Feature.Geometry(
                coordinates = geoLocation,
                type = "Point"
            )
        )
    }

    private fun toLineGeoJSON(locations: List<Location>): GeoJSON.Feature {
        val geoLocations = locationsToGeoJSONLocation(locations)
        return GeoJSON.Feature(
            properties = emptyMap(),
            geometry = GeoJSON.Feature.Geometry(
                coordinates = geoLocations
            )
        )
    }

    private fun locationsToGeoJSONLocation(locations: List<Location>): List<List<Double>> {
        return locations.map { location ->
            listOf(location.lon, location.lat)
        }
    }

    private data class GeoJSON(
        val type: String = "FeatureCollection",
        val features: List<Feature>
    ) {
        data class Feature(
            val type: String = "Feature",
            val properties: Map<String, Any>,
            val geometry: Geometry
        ) {
            data class Geometry(
                val coordinates: List<Any>,
                val type: String = "LineString"
            )
        }
    }
}
