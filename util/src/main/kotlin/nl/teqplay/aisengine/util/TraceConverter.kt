package nl.teqplay.aisengine.util

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.module.kotlin.readValue
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import java.io.File

/**
 * Small utility that converts a json file containing a list of [HistoricShipInfo] objects into a GeoJSON file with a
 * LineString representing the trace of the ship.
 *
 * Usage:
 *
 * ./gradlew :util:traceconverter --args <filename.json>
 */
fun main(argv: Array<String>) {
    val mapper = ObjectMapper()
        .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
        .enable(SerializationFeature.INDENT_OUTPUT)
        .registerKotlinModule()

    if (argv.isEmpty()) {
        println("Please specify trace file(s) to load")
        return
    }

    val traces = argv.flatMap { filename ->
        with(File(filename)) {
            mapper.readValue<List<HistoricShipInfo>>(this)
        }
    }
        .groupBy { it.mmsi }
        .mapValues { entry ->
            entry.value.sortedBy { it.timeLastUpdate }
                .map { listOf(it.location.longitude, it.location.latitude) }
        }

    val geojson = mapOf(
        "type" to "FeatureCollection",
        "features" to traces.entries.map { (mmsi, trace) ->
            mapOf(
                "type" to "Feature",
                "geometry" to mapOf(
                    "type" to "LineString",
                    "coordinates" to trace
                ),
                "properties" to mapOf(
                    "mmsi" to mmsi
                )
            )
        }
    )

    println(mapper.writeValueAsString(geojson))
}

data class HistoricShipInfo(
    val mmsi: String,
    val speedOverGround: Float?,
    val location: PlatformLocation,
    val courseOverGround: Float?,
    val trueHeading: Float?,
    val timeLastUpdate: Long
)

data class PlatformLocation(
    val latitude: Double,
    val longitude: Double
)
