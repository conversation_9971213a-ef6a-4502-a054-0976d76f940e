package nl.teqplay.aisengine.util

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import nl.teqplay.platform.model.HistoricShipInfo
import java.io.File
import java.util.zip.ZipInputStream
import kotlin.time.ExperimentalTime
import kotlin.time.measureTime

private val objectMapper: ObjectMapper = jacksonObjectMapper()
    .registerKotlinModule()
    .registerModule(JavaTimeModule())
    .setSerializationInclusion(JsonInclude.Include.NON_NULL)
    .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
    .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
    .disable(JsonGenerator.Feature.AUTO_CLOSE_TARGET)

@OptIn(ExperimentalTime::class)
fun main() {
    val file = File("/home/<USER>/tmp/s3/output/2023.zip_datajson")
    val zipInputStream = ZipInputStream(file.inputStream())

    val time = measureTime {
        val data = mutableMapOf<String, List<HistoricShipInfo>>()

        do {
            val entry = zipInputStream.nextEntry
            if (entry != null) {
                val content = zipInputStream.readAllBytes()
                val iterator = objectMapper
                    .readerForMapOf(Array<HistoricShipInfo>::class.java)
                    .readValue<Map<String, List<HistoricShipInfo>>>(content)
                data += iterator
            }
        } while (entry != null)

        zipInputStream.close()
    }
    println(time.inWholeMilliseconds)
}
