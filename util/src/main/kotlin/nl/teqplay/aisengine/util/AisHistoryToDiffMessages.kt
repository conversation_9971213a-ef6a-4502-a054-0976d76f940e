package nl.teqplay.aisengine.util

import com.fasterxml.jackson.annotation.JsonProperty
import nl.teqplay.aisengine.ObjectMapperConfiguration
import nl.teqplay.aisengine.aisstream.model.AisDiffField
import nl.teqplay.aisengine.aisstream.model.AisDiffFieldInitialNullable
import nl.teqplay.aisengine.aisstream.model.AisDiffFieldNullable
import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessage
import nl.teqplay.skeleton.common.exception.BadRequestException
import nl.teqplay.skeleton.common.network.buildUri
import nl.teqplay.skeleton.common.network.postForObjectWithParams
import nl.teqplay.skeleton.model.TimeWindow
import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.MediaType
import org.springframework.http.client.ClientHttpRequestInterceptor
import org.springframework.web.client.HttpClientErrorException
import org.springframework.web.client.exchange
import java.io.File
import java.time.Instant

fun main() {
    println("Start")

    val mmsi = ""
    val clientId = ""
    val clientSecret = ""
    val includeHistoric = false

    val window = TimeWindow(
        from = Instant.ofEpochMilli(0),
        to = Instant.ofEpochMilli(0)
    )

    val token = token(clientId, clientSecret)
    val shipHistory = history(token.accessToken, mmsi, window).filter { !it.historic }

    val resultFilePath = "app/stop-monitor/src/test/resources/trace/$mmsi.json"

    val objectMapper = ObjectMapperConfiguration().objectMapper()

    val result = mutableListOf<AisDiffMessage>()
    var previousDiffMessage: AisDiffMessage? = null

    val filteredShipHistory = if (includeHistoric) {
        shipHistory
    } else {
        shipHistory.filter { !it.historic }
    }

    for (message in filteredShipHistory) {
        val newDiffMessage = toDiffMessage(message, previousDiffMessage)

        result.add(newDiffMessage)
        previousDiffMessage = newDiffMessage
    }

    println("Writing ${result.size} diff messages to $resultFilePath")
    val resultAsBytes = objectMapper.writeValueAsBytes(result)
    File(resultFilePath).writeBytes(resultAsBytes)
    println("Finished writing diff messages")

    return
}

private fun toDiffMessage(shipHistory: AisHistoricMessage, previousDiffMessage: AisDiffMessage?): AisDiffMessage {
    return AisDiffMessage(
        mmsi = shipHistory.mmsi,
        messageTime = shipHistory.messageTime,
        oldMessageTime = previousDiffMessage?.messageTime,
        sources = setOf(shipHistory.source),

        location = diffNonNullNew(previousDiffMessage?.location?.latest(), shipHistory.location),
        heading = diffIfNotNullChange(previousDiffMessage?.heading?.latest(), shipHistory.heading),
        speedOverGround = diff(previousDiffMessage?.speedOverGround?.latest(), shipHistory.speedOverGround),
        courseOverGround = diff(previousDiffMessage?.courseOverGround?.latest(), shipHistory.courseOverGround),
        status = diffIfNotNullChange(previousDiffMessage?.status?.latest(), shipHistory.status),

        imo = diff(previousDiffMessage?.imo?.latest(), shipHistory.imo),
        name = emptyDiff(),
        callSign = emptyDiff(),
        shipType = diff(previousDiffMessage?.shipType?.latest(), shipHistory.shipType),
        draught = diff(previousDiffMessage?.draught?.latest(), shipHistory.draught),
        eta = diff(previousDiffMessage?.eta?.latest(), shipHistory.eta),
        destination = diff(previousDiffMessage?.destination?.latest(), shipHistory.destination),
        transponderPosition = diff(
            previousDiffMessage?.transponderPosition?.latest(),
            shipHistory.transponderPosition
        ),

        // Ship history doesn't have those
        positionAccuracy = emptyDiff(),
        rateOfTurn = AisDiffFieldInitialNullable(null, null),
        specialManeuverStatus = AisDiffFieldInitialNullable(null, null),
        positionSensorType = emptyDiff(),
        aisVersion = emptyDiff(),
        usingDataTerminal = emptyDiff(),
        eni = emptyDiff()
    )
}

private inline fun <reified T : Any> diff(old: T?, new: T?) =
    AisDiffField(old, if (old != new) AisDiffField.Change(new) else null)

private inline fun <reified T : Any> diffNonNullNew(old: T?, new: T) =
    AisDiffField(old ?: new, if (old == null || old != new) AisDiffField.Change(new) else null)

private inline fun <reified T> emptyDiff() = AisDiffFieldNullable<T>(null, null)

private val requestInterceptor = ClientHttpRequestInterceptor { request, body, execution ->
    println("Request URI: ${request.uri}")
    println("Request headers: ${request.headers}")
    execution.execute(request, body)
}

private val internalApiRestTemplate = RestTemplateBuilder()
    .rootUri("https://internalapidev.teqplay.dev")
    .additionalInterceptors(requestInterceptor)
    .build()

private fun token(clientId: String, clientSecret: String): Token {
    val headers = HttpHeaders()
    headers.contentType = MediaType.APPLICATION_JSON

    val credentials = ClientCredentials(clientId, clientSecret)

    val entity = HttpEntity<ClientCredentials>(
        credentials, headers
    )

    try {
        val response = internalApiRestTemplate.postForObjectWithParams<Token>(
            "/v1/auth/token",
            entity
        )
        return response
    } catch (e: HttpClientErrorException) {
        println("Error response: ${e.responseBodyAsString}")
        throw e
    }
}

private fun history(token: String, mmsi: String, window: TimeWindow): List<AisHistoricMessage> {
    val headers = HttpHeaders()
    headers.set("Authorization", "Bearer $token")
    headers.contentType = MediaType.APPLICATION_JSON

    println(headers)

    val requestEntity = HttpEntity<Void>(headers)

    return internalApiRestTemplate.exchange<Array<AisHistoricMessage>>(
        url = buildUri(
            path = "/v1/ship/history/mmsi/$mmsi",
            queryParams = mapOf(
                "from" to window.from,
                "to" to window.to
            )
        ),
        HttpMethod.GET,
        requestEntity,
        Array<AisHistoricMessage>::class
    ).body?.toList() ?: throw BadRequestException("Response is empty")
}

data class ClientCredentials(
    @JsonProperty("client_id")
    val clientId: String,
    @JsonProperty("client_secret")
    val clientSecret: String
)

data class Token(
    @JsonProperty("access_token")
    val accessToken: String
)
