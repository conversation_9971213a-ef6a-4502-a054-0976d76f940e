package nl.teqplay.aisengine.util

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import nl.teqplay.platform.model.HistoricShipInfo
import java.io.ByteArrayOutputStream
import java.io.File
import java.time.LocalDate
import java.time.temporal.IsoFields
import java.util.zip.ZipEntry
import java.util.zip.ZipInputStream
import java.util.zip.ZipOutputStream

private val objectMapper: ObjectMapper = jacksonObjectMapper()
    .registerKotlinModule()
    .registerModule(JavaTimeModule())
    .setSerializationInclusion(JsonInclude.Include.NON_NULL)
    .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
    .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
    .disable(JsonGenerator.Feature.AUTO_CLOSE_TARGET)

private const val pathPrefix = "/home/<USER>/tmp/s3"

fun main() {
    val filesGroup = File(pathPrefix)
        .listFiles { file -> file.extension == "zip" }!!
        .groupBy { file ->
            val date = LocalDate.parse(file.nameWithoutExtension.removePrefix("ship_").substringBefore(","))
            val weekYear = date.get(IsoFields.WEEK_BASED_YEAR)
            val weekNumber = date.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR)
            "$weekYear-W$weekNumber"
        }

    val totalOutput = mutableMapOf<String, List<HistoricShipInfo>>()
    filesGroup.forEach { (key, files) ->

        val output = mutableMapOf<String, List<HistoricShipInfo>>()
        files.forEach { file ->
            val date = file.name.removePrefix("ship_").substringBefore(",")
            val zipInputStream = ZipInputStream(file.inputStream())

            val data = mutableListOf<HistoricShipInfo>()

            do {
                val entry = zipInputStream.nextEntry
                if (entry != null) {
                    val content = zipInputStream.readAllBytes()
                    val iterator =
                        objectMapper.readerFor(HistoricShipInfo::class.java).readValues<HistoricShipInfo>(content)
                    data.addAll(iterator.readAll())
                }
            } while (entry != null)

            zipInputStream.close()

            writeZip("$pathPrefix/output/${file.name}", mapOf(date to data))

            output[date] = data
        }

        val archiveFileName = "$key.zip"
        writeZip("$pathPrefix/output/$archiveFileName", output)

        totalOutput += output
    }

    val archiveFileName = "2023.zip"
    writeZip("$pathPrefix/output/$archiveFileName", totalOutput)
}

private fun writeZip(path: String, data: Map<String, List<HistoricShipInfo>>) {
    val outputStream = ByteArrayOutputStream()
    val zipOutputStream = ZipOutputStream(outputStream)

    data.forEach { (key, history) ->
        zipOutputStream.putNextEntry(ZipEntry("$key.json"))
        objectMapper.writeValue(zipOutputStream, history)
        zipOutputStream.closeEntry()
    }
    zipOutputStream.close()

    val bytes = outputStream.toByteArray()

    File(path).writeBytes(bytes)

    writeZipGrouped(path, data)
}

private fun writeZipGrouped(path: String, data: Map<String, List<HistoricShipInfo>>) {
    val outputStream = ByteArrayOutputStream()
    val zipOutputStream = ZipOutputStream(outputStream)

    zipOutputStream.putNextEntry(ZipEntry("data.json"))
    objectMapper.writeValue(zipOutputStream, data)
    zipOutputStream.closeEntry()
    zipOutputStream.close()

    val bytes = outputStream.toByteArray()

    File("${path}_datajson").writeBytes(bytes)
}
