package nl.teqplay.aisengine.util

import nl.teqplay.skeleton.model.TimeWindow
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit

class WindowUtilsKtTest {

    @Test
    fun `mergeTimeWindows - empty list returns empty list`() {
        assertEquals(
            emptyList<TimeWindow>(),
            mergeTimeWindows(emptyList())
        )
    }

    @Test
    fun `mergeTimeWindows - one entry stays the same`() {
        val window = TimeWindow(Instant.EPOCH, Duration.ofDays(7))
        assertEquals(
            listOf(window),
            mergeTimeWindows(listOf(window))
        )
    }

    @Test
    fun `mergeTimeWindows - two 'touching' entries merge into one`() {
        val window1 = TimeWindow(Instant.EPOCH, Duration.ofDays(7))
        val window2 = TimeWindow(window1.to, Duration.ofDays(7))
        assertEquals(
            listOf(TimeWindow(window1.from, window2.to)),
            mergeTimeWindows(listOf(window1, window2))
        )
    }

    @Test
    fun `mergeTimeWindows - two 'touching' entries merge into one (reversed order)`() {
        val window1 = TimeWindow(Instant.EPOCH, Duration.ofDays(7))
        val window2 = TimeWindow(window1.to, Duration.ofDays(7))
        assertEquals(
            listOf(TimeWindow(window1.from, window2.to)),
            mergeTimeWindows(listOf(window2, window1))
        )
    }

    @Test
    fun `mergeTimeWindows - two 'non-touching' entries stay the same`() {
        val window1 = TimeWindow(Instant.EPOCH, Duration.ofDays(7))
        val window2 = TimeWindow(window1.to.plusSeconds(1), Duration.ofDays(7))
        assertEquals(
            listOf(window1, window2),
            mergeTimeWindows(listOf(window1, window2))
        )
    }

    @Test
    fun `mergeTimeWindows - two 'non-touching' entries stay the same (reversed order)`() {
        val window1 = TimeWindow(Instant.EPOCH, Duration.ofDays(7))
        val window2 = TimeWindow(window1.to.plusSeconds(1), Duration.ofDays(7))
        assertEquals(
            listOf(window2, window1),
            mergeTimeWindows(listOf(window2, window1))
        )
    }

    @Test
    fun `mergeTimeWindows - two exactly overlapping entries should be merged into one`() {
        val window = TimeWindow(Instant.EPOCH, Duration.ofDays(7))
        assertEquals(
            listOf(window),
            mergeTimeWindows(listOf(window, window))
        )
    }

    @Test
    fun `mergeTimeWindows - two partially inner overlapping entries should be merged into one`() {
        val window1 = TimeWindow(Instant.EPOCH, Duration.ofDays(7))
        val window2 = TimeWindow(window1.from.plusSeconds(1), window1.to.minusSeconds(1))
        val result = TimeWindow(Instant.EPOCH, Duration.ofDays(7))
        assertEquals(
            listOf(result),
            mergeTimeWindows(listOf(window1, window2))
        )
    }

    @Test
    fun `mergeTimeWindows - three partially outer overlapping entries should be merged into one`() {
        val shift = Duration.ofDays(2)
        val window = TimeWindow(Instant.EPOCH, Duration.ofDays(7))
        val windowLeft = window.withMargin(shift, shift.negated())
        val windowRight = window.withMargin(shift.negated(), shift)
        val result = TimeWindow(windowLeft.from, windowRight.to)
        assertEquals(
            listOf(result),
            mergeTimeWindows(listOf(window, windowLeft, windowRight))
        )
    }

    @Test
    fun `coerceTimeWindowWithinBoundary - window equals boundary`() {
        val window = TimeWindow(Instant.EPOCH, Duration.ofDays(7))
        val actual = coerceTimeWindowWithinBoundary(window, window)
        assertThat(actual).isEqualTo(window)
    }

    @Test
    fun `coerceTimeWindowWithinBoundary - boundary larger than window, doesn't change`() {
        val window = TimeWindow(Instant.EPOCH, Duration.ofDays(7))
        val boundary = window.withMargin(Duration.ofDays(1))
        val actual = coerceTimeWindowWithinBoundary(window, boundary)
        assertThat(actual).isEqualTo(window)
    }

    @Test
    fun `coerceTimeWindowWithinBoundary - window larger than boundary, changes to boundary`() {
        val window = TimeWindow(Instant.EPOCH, Duration.ofDays(7))
        val boundary = window.removeMargin(Duration.ofDays(1))
        val actual = coerceTimeWindowWithinBoundary(window, boundary)
        assertThat(actual).isEqualTo(boundary)
    }

    @Test
    fun `coerceTimeWindowWithinBoundary - window is to the right of boundary, should equal boundary to`() {
        val boundary = TimeWindow(Instant.EPOCH, Duration.ofDays(1))
        val window = TimeWindow(Instant.EPOCH.plus(2, ChronoUnit.DAYS), Duration.ofDays(1))
        val expected = TimeWindow(
            from = boundary.to,
            to = boundary.to
        )
        val actual = coerceTimeWindowWithinBoundary(window, boundary)
        assertThat(actual).isEqualTo(expected)
    }

    @Test
    fun `coerceTimeWindowWithinBoundary - window is to the left of boundary, should equal boundary from`() {
        val window = TimeWindow(Instant.EPOCH, Duration.ofDays(1))
        val boundary = TimeWindow(Instant.EPOCH.plus(2, ChronoUnit.DAYS), Duration.ofDays(1))
        val expected = TimeWindow(
            from = boundary.from,
            to = boundary.from
        )
        val actual = coerceTimeWindowWithinBoundary(window, boundary)
        assertThat(actual).isEqualTo(expected)
    }
}
