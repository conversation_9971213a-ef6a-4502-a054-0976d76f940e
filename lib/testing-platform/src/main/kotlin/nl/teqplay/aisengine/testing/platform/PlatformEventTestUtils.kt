package nl.teqplay.aisengine.testing.platform

import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.testing.event.defaultAisShipIdentifier
import nl.teqplay.aisengine.testing.event.defaultBerthAreaIdentifier
import nl.teqplay.aisengine.testing.event.defaultBerthId
import nl.teqplay.aisengine.testing.event.defaultBerthName
import nl.teqplay.aisengine.testing.event.defaultEventIdentifier
import nl.teqplay.aisengine.testing.event.defaultExternalId
import nl.teqplay.aisengine.testing.event.defaultLocation
import nl.teqplay.aisengine.testing.event.defaultLockName
import nl.teqplay.aisengine.testing.event.defaultMovementId
import nl.teqplay.aisengine.testing.event.defaultOrder
import nl.teqplay.aisengine.testing.event.defaultOtherAisShipIdentifier
import nl.teqplay.aisengine.testing.event.defaultOtherBerthId
import nl.teqplay.aisengine.testing.event.defaultOtherBerthName
import nl.teqplay.aisengine.testing.event.defaultOtherPort
import nl.teqplay.aisengine.testing.event.defaultOtherPortAreaIdentifier
import nl.teqplay.aisengine.testing.event.defaultPilotStation
import nl.teqplay.aisengine.testing.event.defaultPort
import nl.teqplay.aisengine.testing.event.defaultPortAreaIdentifier
import nl.teqplay.aisengine.testing.event.defaultProbability
import nl.teqplay.aisengine.testing.event.defaultReporter
import nl.teqplay.aisengine.testing.event.defaultShipName
import nl.teqplay.aisengine.testing.event.defaultSource
import nl.teqplay.aisengine.testing.event.defaultTerminalName
import nl.teqplay.aisengine.testing.event.defaultUcrn
import nl.teqplay.aisengine.testing.event.defaultVesselAgent
import nl.teqplay.platform.model.Location
import nl.teqplay.platform.model.ShipInfo
import nl.teqplay.platform.model.event.AisEtaChangedEvent
import nl.teqplay.platform.model.event.AisLostEvent
import nl.teqplay.platform.model.event.AisRecoveredEvent
import nl.teqplay.platform.model.event.AtaEvent
import nl.teqplay.platform.model.event.AtdEvent
import nl.teqplay.platform.model.event.AuthorityEvent
import nl.teqplay.platform.model.event.BargeBunkerEvent
import nl.teqplay.platform.model.event.BargeWaterEvent
import nl.teqplay.platform.model.event.BerthEvent
import nl.teqplay.platform.model.event.BoatmanEvent
import nl.teqplay.platform.model.event.BunkerEvent
import nl.teqplay.platform.model.event.CargoBargeEvent
import nl.teqplay.platform.model.event.ConfirmedBerthEvent
import nl.teqplay.platform.model.event.CraneEvent
import nl.teqplay.platform.model.event.DestinationChangedEvent
import nl.teqplay.platform.model.event.DraughtChangedEvent
import nl.teqplay.platform.model.event.EndSeaPassageEvent
import nl.teqplay.platform.model.event.EtaEvent
import nl.teqplay.platform.model.event.EtdEvent
import nl.teqplay.platform.model.event.FenderEvent
import nl.teqplay.platform.model.event.LockEvent
import nl.teqplay.platform.model.event.LubesEvent
import nl.teqplay.platform.model.event.PilotEvent
import nl.teqplay.platform.model.event.PortAtaAtdEvent
import nl.teqplay.platform.model.event.PortcallPilotBoardingEtaEvent
import nl.teqplay.platform.model.event.PushBargeEvent
import nl.teqplay.platform.model.event.ShipInfraEncounterEvent
import nl.teqplay.platform.model.event.ShipMovingEvent
import nl.teqplay.platform.model.event.SpeedChangedEvent
import nl.teqplay.platform.model.event.StatusChangedEvent
import nl.teqplay.platform.model.event.SupplyBargeEvent
import nl.teqplay.platform.model.event.SwogEvent
import nl.teqplay.platform.model.event.TankerBargeEvent
import nl.teqplay.platform.model.event.TenderEvent
import nl.teqplay.platform.model.event.TeqperimentEvent
import nl.teqplay.platform.model.event.TeqplayEvent
import nl.teqplay.platform.model.event.TeqplayLocationBasedEvent
import nl.teqplay.platform.model.event.TerminalEvent
import nl.teqplay.platform.model.event.TugEvent
import nl.teqplay.platform.model.event.TugWaitingForDepartureEvent
import nl.teqplay.platform.model.event.UniqueBerthEvent
import nl.teqplay.platform.model.event.VhfEvent
import nl.teqplay.platform.model.event.WasteEvent
import nl.teqplay.platform.model.event.WaterEvent
import nl.teqplay.platform.model.event.hbr.AddPortcallVisitEvent
import nl.teqplay.platform.model.event.hbr.AgentOrderEvent
import nl.teqplay.platform.model.event.hbr.AgentReportsTugsEvent
import nl.teqplay.platform.model.event.hbr.CancelPortcallVisitEvent
import nl.teqplay.platform.model.event.hbr.EtaCancelEvent
import nl.teqplay.platform.model.event.hbr.EtaRequestEvent
import nl.teqplay.platform.model.event.hbr.EtdRequestEvent
import nl.teqplay.platform.model.event.hbr.HamisPilotBoardingEtaEvent
import nl.teqplay.platform.model.event.hbr.NauticalOrderEvent
import nl.teqplay.platform.model.event.hbr.PilotOnBoardEvent
import nl.teqplay.platform.model.event.hbr.UpdatePortcallVisitEvent
import nl.teqplay.platform.model.event.hbr.VisitCancellationEvent
import nl.teqplay.platform.model.event.hbr.VisitDeclarationEvent
import nl.teqplay.platform.model.lockplanning.LockDirection
import java.time.Instant
import java.time.temporal.ChronoUnit
import nl.teqplay.skeleton.model.Location as SkeletonLocation

const val defaultDescription = "TEST_DESCRIPTION"
const val defaultTitle = "TEST_TITLE"
const val defaultSummary = "TEST_SUMMARY"
const val defaultIsrsId = "TEST_ISRS_ID"
val defaultPlatformTime = Instant.ofEpochMilli(1682899200000)
val defaultPlatformTimeLong = defaultPlatformTime.toEpochMilli()
val defaultPlatformTime2 = defaultPlatformTime.plus(1, ChronoUnit.DAYS)
val defaultPlatformTime2Long = defaultPlatformTime2.toEpochMilli()
val defaultPlatformTime3 = defaultPlatformTime2.plus(5, ChronoUnit.DAYS)
val defaultPlatformTime3Long = defaultPlatformTime3.toEpochMilli()
val defaultPlatformMmsi = defaultAisShipIdentifier.mmsi.toString()
val defaultPlatformImo = defaultAisShipIdentifier.imo.toString()
val defaultPlatformLocation = defaultLocation.toPlatformLocation()
val defaultShipInfo = defaultAisShipIdentifier.toShipInfo()
val defaultOtherShipInfo = defaultOtherAisShipIdentifier.toShipInfo()
val defaultShipIdentifierWithoutImo = defaultAisShipIdentifier.copy(imo = null)
val defaultPlatformPortAreaIdentifier = defaultPortAreaIdentifier.copy(
    id = null,
    name = defaultPort,
    unlocode = defaultPort
)
val defaultPlatformOtherPortAreaIdentifier = defaultOtherPortAreaIdentifier.copy(
    id = null,
    name = defaultOtherPort,
    unlocode = defaultOtherPort
)
val defaultPlatformBerthAreaIdentifier = defaultBerthAreaIdentifier.copy(
    id = null,
    name = defaultBerthName,
    unlocode = defaultPort
)

fun AisShipIdentifier.toShipInfo(): ShipInfo = ShipInfo(this.mmsi.toString()).also {
    it.imoNumber = this.imo?.toString()
}

fun SkeletonLocation.toPlatformLocation(): Location = Location(this.lat, this.lon)

fun createPlatformDestinationChangedEvent(
    uuid: String = defaultEventIdentifier,
    timestamp: Long = defaultPlatformTimeLong,
    mmsi: String = defaultPlatformMmsi,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    location: Location = defaultPlatformLocation,
    imo: String? = defaultPlatformImo,
    oldDestination: String? = "to NLRTM",
    newDestination: String? = "NLRTM => NLAMS",
    newTrueDestination: String? = "NLAMS"
): DestinationChangedEvent {
    return DestinationChangedEvent(
        uuid = uuid,
        timestamp = timestamp,
        shipMmsi = mmsi,
        description = description,
        title = title,
        summary = summary,
        location = location,
        imo = imo,
        oldDestination = oldDestination,
        newDestination = newDestination,
        newTrueDestination = newTrueDestination
    )
}

fun createPlatformDraughtChangedEvent(
    uuid: String = defaultEventIdentifier,
    timestamp: Long = defaultPlatformTimeLong,
    shipInfo: ShipInfo = defaultShipInfo,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    location: Location = defaultPlatformLocation,
    old: Float? = 10.0F,
    new: Float? = 15.0F
): DraughtChangedEvent {
    return DraughtChangedEvent(uuid, timestamp, shipInfo, description, title, summary, location, old, new)
}

fun createPlatformSpeedChangedEvent(
    uuid: String = defaultEventIdentifier,
    timestamp: Long = defaultPlatformTimeLong,
    shipInfo: ShipInfo = defaultShipInfo,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    location: Location = defaultPlatformLocation
): SpeedChangedEvent {
    return SpeedChangedEvent(uuid, timestamp, shipInfo, description, title, summary, location)
}

fun createPlatformAisEtaChangedEvent(
    uuid: String = defaultEventIdentifier,
    timestamp: Long = defaultPlatformTimeLong,
    mmsi: String = defaultPlatformMmsi,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    location: Location = defaultPlatformLocation,
    old: Long? = defaultPlatformTime2Long,
    new: Long? = defaultPlatformTime3Long
): AisEtaChangedEvent {
    return AisEtaChangedEvent(uuid, timestamp, mmsi, description, title, summary, location, old, new)
}

fun createPlatformStatusChangedEvent(
    uuid: String = defaultEventIdentifier,
    timestamp: Long = defaultPlatformTimeLong,
    shipInfo: ShipInfo = defaultShipInfo,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    location: Location = defaultPlatformLocation,
    old: ShipInfo.ShipStatus? = ShipInfo.ShipStatus.AT_ANCHOR,
    new: ShipInfo.ShipStatus? = ShipInfo.ShipStatus.MOORED
): StatusChangedEvent {
    return StatusChangedEvent(uuid, timestamp, shipInfo, description, title, summary, location, old, new)
}

fun createTeqplayLocationBasedEvent(
    type: String,
    uuid: String = defaultEventIdentifier,
    recordTime: Long = defaultPlatformTimeLong,
    eventTime: Long = defaultPlatformTimeLong,
    generatedTime: Long = defaultPlatformTimeLong,
    shipInfo: ShipInfo = defaultShipInfo,
    otherShipInfo: ShipInfo? = null,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    isPlannedEvent: Boolean = false,
    relatedEventId: String? = null,
    location: Location = defaultPlatformLocation
): TeqplayLocationBasedEvent {
    return TeqplayLocationBasedEvent(
        uuid, type, recordTime, eventTime, generatedTime, description, title, summary, isPlannedEvent,
        relatedEventId, shipInfo, otherShipInfo, location, null, null, null
    )
}

fun createPlatformPortAtaAtdEvent(
    type: String,
    uuid: String = defaultEventIdentifier,
    recordTime: Long = defaultPlatformTimeLong,
    eventTime: Long = defaultPlatformTimeLong,
    generatedTime: Long = defaultPlatformTimeLong,
    shipInfo: ShipInfo = defaultShipInfo,
    otherShipInfo: ShipInfo? = null,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    isPlannedEvent: Boolean = false,
    relatedEventId: String? = null,
    location: Location = defaultPlatformLocation,
    maxDraught: Float = 10.0f
): PortAtaAtdEvent {
    return PortAtaAtdEvent(
        uuid, type, recordTime, eventTime, generatedTime, description, title, summary,
        isPlannedEvent, relatedEventId, shipInfo, otherShipInfo, location, maxDraught
    )
}

fun createPlatformVhfEvent(
    type: String,
    uuid: String = defaultEventIdentifier,
    recordTime: Long = defaultPlatformTimeLong,
    eventTime: Long = defaultPlatformTimeLong,
    generatedTime: Long = defaultPlatformTimeLong,
    shipInfo: ShipInfo = defaultShipInfo,
    otherShipInfo: ShipInfo? = null,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    relatedEventId: String? = null,
    location: Location = defaultPlatformLocation
): VhfEvent {
    return VhfEvent(
        uuid, type, recordTime, eventTime, generatedTime, description, title, summary,
        relatedEventId, shipInfo, otherShipInfo, location
    )
}

fun createPlatformTerminalEvent(
    uuid: String = defaultEventIdentifier,
    recordTime: Long = defaultPlatformTimeLong,
    eventTime: Long = defaultPlatformTimeLong,
    generatedTime: Long = defaultPlatformTimeLong,
    shipInfo: ShipInfo = defaultShipInfo,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    relatedEventId: String? = null,
    location: Location = defaultPlatformLocation,
    terminalName: String = defaultTerminalName
): TerminalEvent {
    val isStartEvent = relatedEventId == null

    return TerminalEvent(
        uuid, isStartEvent, recordTime, eventTime, generatedTime, relatedEventId, shipInfo,
        description, title, summary, location, terminalName
    )
}

fun createPlatformEndSeaPassageEvent(
    type: String,
    uuid: String = defaultEventIdentifier,
    recordTime: Long = defaultPlatformTimeLong,
    eventTime: Long = defaultPlatformTimeLong,
    generatedTime: Long = defaultPlatformTimeLong,
    shipInfo: ShipInfo = defaultShipInfo,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    relatedEventId: String? = null,
    location: Location = defaultPlatformLocation
): EndSeaPassageEvent {
    return EndSeaPassageEvent(
        uuid, type, recordTime, eventTime, generatedTime, description, title, summary,
        relatedEventId, shipInfo, location
    )
}

fun createPlatformShipInfraEncounterEvent(
    type: String,
    uuid: String = defaultEventIdentifier,
    timestamp: Long = defaultPlatformTimeLong,
    shipInfo: ShipInfo = defaultShipInfo,
    isrsId: String = defaultIsrsId,
    rwsId: Long = 1L,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    location: Location = defaultPlatformLocation,
    relatedEventId: String? = null,
    shipHeading: Int? = null,
): ShipInfraEncounterEvent {
    return ShipInfraEncounterEvent(
        uuid, type, timestamp, shipInfo, isrsId, rwsId, description, title, summary,
        location, relatedEventId, shipHeading
    )
}

fun createPlatformBerthEvent(
    uuid: String = defaultEventIdentifier,
    timestamp: Long = defaultPlatformTimeLong,
    relatedEventId: String? = null,
    shipInfo: ShipInfo = defaultShipInfo,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    location: Location = defaultPlatformLocation,
    berthId: String? = defaultBerthAreaIdentifier.id,
    berthName: String = defaultBerthName,
    startBollard: Float? = null,
    endBollard: Float? = null,
    harbourName: String = "Rotterdam",
    harbour: String = "Harbour NLRTM",
    port: String = defaultPort,
    orientation: String? = "starboard",
    draught: Float = 10f,
    providingService: Boolean? = null,
    adjacentBerthIds: Set<String>? = setOf("BERTH_2", "BERTH_3"),
    terminalName: String = defaultTerminalName,
    heading: Int = 180
): BerthEvent {
    val type = if (relatedEventId == null) {
        TeqplayEvent.MOORED + TeqplayEvent.START
    } else {
        TeqplayEvent.MOORED + TeqplayEvent.END
    }

    return BerthEvent(
        uuid, type, timestamp, relatedEventId, shipInfo, description, title, summary,
        location, berthId, berthName, startBollard, endBollard, harbourName, harbour, port,
        orientation, draught, providingService, adjacentBerthIds, terminalName, heading
    )
}

fun createPlatformConfirmedBerthEvent(
    uuid: String = defaultEventIdentifier,
    berthEventUuid: String = defaultEventIdentifier + "_BERTH",
    timestamp: Long = defaultPlatformTimeLong,
    relatedEventId: String? = null,
    shipInfo: ShipInfo = defaultShipInfo,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    location: Location = defaultPlatformLocation,
    berthId: String? = defaultBerthAreaIdentifier.id,
    berthName: String = defaultBerthName,
    startBollard: Float? = null,
    endBollard: Float? = null,
    harbourName: String = "Rotterdam",
    harbour: String = "Harbour NLRTM",
    port: String = defaultPort,
    orientation: String? = "starboard",
    draught: Float = 10f,
    providingService: Boolean? = null,
    adjacentBerthIds: Set<String>? = setOf("BERTH_2", "BERTH_3"),
    terminalName: String = defaultTerminalName,
    heading: Int = 180
): ConfirmedBerthEvent {
    val berthEvent = createPlatformBerthEvent(
        berthEventUuid, timestamp, relatedEventId, shipInfo, description, title, summary,
        location, berthId, berthName, startBollard, endBollard, harbourName, harbour, port,
        orientation, draught, providingService, adjacentBerthIds, terminalName, heading
    )

    return ConfirmedBerthEvent(uuid, berthEvent, shipInfo, timestamp, startBollard, endBollard)
}

fun createPlatformUniqueBerthEvent(
    uuid: String = defaultEventIdentifier,
    berthEventUuid: String = defaultEventIdentifier + "_BERTH",
    confirmedBerthEventUuid: String = defaultEventIdentifier + "_CONFIRMED",
    timestamp: Long = defaultPlatformTimeLong,
    relatedEventId: String? = null,
    shipInfo: ShipInfo = defaultShipInfo,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    location: Location = defaultPlatformLocation,
    berthId: String? = defaultBerthAreaIdentifier.id,
    berthName: String = defaultBerthName,
    startBollard: Float? = null,
    endBollard: Float? = null,
    harbourName: String = "Rotterdam",
    harbour: String = "Harbour NLRTM",
    port: String = defaultPort,
    orientation: String? = "starboard",
    draught: Float = 10f,
    providingService: Boolean? = null,
    adjacentBerthIds: Set<String>? = setOf("BERTH_2", "BERTH_3"),
    terminalName: String = defaultTerminalName,
    heading: Int = 180
): UniqueBerthEvent {
    val confirmedBerthEvent = createPlatformConfirmedBerthEvent(
        confirmedBerthEventUuid, berthEventUuid, timestamp, relatedEventId, shipInfo, description, title, summary,
        location, berthId, berthName, startBollard, endBollard, harbourName, harbour, port,
        orientation, draught, providingService, adjacentBerthIds, terminalName, heading
    )

    return UniqueBerthEvent(uuid, confirmedBerthEvent, shipInfo, timestamp, startBollard, endBollard)
}

fun createPlatformShipMovingEvent(
    uuid: String = defaultEventIdentifier,
    timestamp: Long = defaultPlatformTimeLong,
    shipInfo: ShipInfo = defaultShipInfo,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    relatedEventId: String? = null,
    location: Location = defaultPlatformLocation
): ShipMovingEvent {
    val isStartEvent = relatedEventId == null

    return ShipMovingEvent(
        uuid, isStartEvent, timestamp, relatedEventId, shipInfo, description, title, summary, location
    )
}

fun createPlatformTugEvent(
    uuid: String = defaultEventIdentifier,
    arrival: Boolean = false,
    hasSimultaneousTugEncounter: Boolean = true,
    timestamp: Long = defaultPlatformTimeLong,
    relatedEventId: String? = null,
    shipInfo: ShipInfo = defaultShipInfo,
    otherShipInfo: ShipInfo = defaultOtherShipInfo,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    location: Location = defaultPlatformLocation,
    probability: Double = defaultProbability
): TugEvent {
    val isStartEvent = relatedEventId == null

    return TugEvent(
        uuid, isStartEvent, arrival, hasSimultaneousTugEncounter, timestamp, relatedEventId,
        shipInfo, otherShipInfo, description, title, summary, location, probability
    )
}

fun createPlatformPilotEvent(
    uuid: String = defaultEventIdentifier,
    recordTime: Long = defaultPlatformTimeLong,
    eventTime: Long = defaultPlatformTimeLong,
    relatedEventId: String? = null,
    shipInfo: ShipInfo = defaultShipInfo,
    otherShipInfo: ShipInfo = defaultOtherShipInfo,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    location: Location = defaultPlatformLocation,
    probability: Double = defaultProbability,
    isFallback: Boolean = false,
    isPilotDisembarkedEvent: Boolean = false,
    source: String? = null
): PilotEvent {
    val isStartEvent = relatedEventId == null

    return PilotEvent(
        uuid, isStartEvent, recordTime, eventTime, relatedEventId, shipInfo, otherShipInfo,
        description, title, summary, location, probability, isFallback, isPilotDisembarkedEvent, source
    )
}

fun createPlatformBoatmanEvent(
    uuid: String = defaultEventIdentifier,
    timestamp: Long = defaultPlatformTimeLong,
    relatedEventId: String? = null,
    shipInfo: ShipInfo = defaultShipInfo,
    otherShipInfo: ShipInfo = defaultOtherShipInfo,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    location: Location = defaultPlatformLocation,
    probability: Double = defaultProbability,
    hasSimultaneousTugEncounter: Boolean = true,
    arrival: Boolean = true,
    numberOfBoatman: Int = 1
): BoatmanEvent {
    val isStartEvent = relatedEventId == null

    return BoatmanEvent(
        uuid, isStartEvent, arrival, timestamp, relatedEventId, shipInfo, otherShipInfo,
        description, title, summary, location, probability, numberOfBoatman, hasSimultaneousTugEncounter
    )
}

fun createPlatformBunkerEvent(
    uuid: String = defaultEventIdentifier,
    timestamp: Long = defaultPlatformTimeLong,
    relatedEventId: String? = null,
    shipInfo: ShipInfo = defaultShipInfo,
    otherShipInfo: ShipInfo = defaultOtherShipInfo,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    location: Location = defaultPlatformLocation,
    probability: Double = defaultProbability,
): BunkerEvent {
    val isStartEvent = relatedEventId == null

    return BunkerEvent(
        uuid, isStartEvent, timestamp, relatedEventId, shipInfo, otherShipInfo,
        description, title, summary, location, probability
    )
}

fun createPlatformAuthorityEvent(
    uuid: String = defaultEventIdentifier,
    timestamp: Long = defaultPlatformTimeLong,
    relatedEventId: String? = null,
    shipInfo: ShipInfo = defaultShipInfo,
    otherShipInfo: ShipInfo = defaultOtherShipInfo,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    location: Location = defaultPlatformLocation,
    probability: Double = defaultProbability,
): AuthorityEvent {
    val isStartEvent = relatedEventId == null

    return AuthorityEvent(
        uuid, isStartEvent, timestamp, relatedEventId, shipInfo, otherShipInfo,
        description, title, summary, location, probability
    )
}

fun createPlatformWasteEvent(
    uuid: String = defaultEventIdentifier,
    timestamp: Long = defaultPlatformTimeLong,
    relatedEventId: String? = null,
    shipInfo: ShipInfo = defaultShipInfo,
    otherShipInfo: ShipInfo = defaultOtherShipInfo,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    location: Location = defaultPlatformLocation,
    probability: Double = defaultProbability,
): WasteEvent {
    val isStartEvent = relatedEventId == null

    return WasteEvent(
        uuid, isStartEvent, timestamp, relatedEventId, shipInfo, otherShipInfo,
        description, title, summary, location, probability
    )
}

fun createPlatformSwogEvent(
    uuid: String = defaultEventIdentifier,
    timestamp: Long = defaultPlatformTimeLong,
    relatedEventId: String? = null,
    shipInfo: ShipInfo = defaultShipInfo,
    otherShipInfo: ShipInfo = defaultOtherShipInfo,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    location: Location = defaultPlatformLocation,
    probability: Double = defaultProbability,
): SwogEvent {
    val isStartEvent = relatedEventId == null

    return SwogEvent(
        uuid, isStartEvent, timestamp, relatedEventId, shipInfo, otherShipInfo,
        description, title, summary, location, probability
    )
}

fun createPlatformWaterEvent(
    uuid: String = defaultEventIdentifier,
    timestamp: Long = defaultPlatformTimeLong,
    relatedEventId: String? = null,
    shipInfo: ShipInfo = defaultShipInfo,
    otherShipInfo: ShipInfo = defaultOtherShipInfo,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    location: Location = defaultPlatformLocation,
    probability: Double = defaultProbability,
): WaterEvent {
    val isStartEvent = relatedEventId == null

    return WaterEvent(
        uuid, isStartEvent, timestamp, relatedEventId, shipInfo, otherShipInfo,
        description, title, summary, location, probability
    )
}

fun createPlatformTenderEvent(
    uuid: String = defaultEventIdentifier,
    timestamp: Long = defaultPlatformTimeLong,
    relatedEventId: String? = null,
    shipInfo: ShipInfo = defaultShipInfo,
    otherShipInfo: ShipInfo = defaultOtherShipInfo,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    location: Location = defaultPlatformLocation,
    probability: Double = defaultProbability,
): TenderEvent {
    val isStartEvent = relatedEventId == null

    return TenderEvent(
        uuid, isStartEvent, timestamp, relatedEventId, shipInfo, otherShipInfo,
        description, title, summary, location, probability
    )
}

fun createPlatformFenderEvent(
    uuid: String = defaultEventIdentifier,
    timestamp: Long = defaultPlatformTimeLong,
    relatedEventId: String? = null,
    shipInfo: ShipInfo = defaultShipInfo,
    otherShipInfo: ShipInfo = defaultOtherShipInfo,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    location: Location = defaultPlatformLocation,
    probability: Double = defaultProbability,
): FenderEvent {
    val isStartEvent = relatedEventId == null

    return FenderEvent(
        uuid, isStartEvent, timestamp, relatedEventId, shipInfo, otherShipInfo,
        description, title, summary, location, probability
    )
}

fun createPlatformCraneEvent(
    uuid: String = defaultEventIdentifier,
    timestamp: Long = defaultPlatformTimeLong,
    relatedEventId: String? = null,
    shipInfo: ShipInfo = defaultShipInfo,
    otherShipInfo: ShipInfo = defaultOtherShipInfo,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    location: Location = defaultPlatformLocation,
    probability: Double = defaultProbability,
): CraneEvent {
    val isStartEvent = relatedEventId == null

    return CraneEvent(
        uuid, isStartEvent, timestamp, relatedEventId, shipInfo, otherShipInfo,
        description, title, summary, location, probability
    )
}

fun createPlatformLubesEvent(
    uuid: String = defaultEventIdentifier,
    timestamp: Long = defaultPlatformTimeLong,
    relatedEventId: String? = null,
    shipInfo: ShipInfo = defaultShipInfo,
    otherShipInfo: ShipInfo = defaultOtherShipInfo,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    location: Location = defaultPlatformLocation,
    probability: Double = defaultProbability,
): LubesEvent {
    val isStartEvent = relatedEventId == null

    return LubesEvent(
        uuid, isStartEvent, timestamp, relatedEventId, shipInfo, otherShipInfo,
        description, title, summary, location, probability
    )
}

fun createPlatformTugWaitingForDepartureEvent(
    uuid: String = defaultEventIdentifier,
    timestamp: Long = defaultPlatformTimeLong,
    relatedEventId: String? = null,
    shipInfo: ShipInfo = defaultShipInfo,
    otherShipInfo: ShipInfo = defaultOtherShipInfo,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    location: Location = defaultPlatformLocation,
    probability: Double = defaultProbability,
    wasValid: Boolean = true
): TugWaitingForDepartureEvent {
    val isStartEvent = relatedEventId == null

    return TugWaitingForDepartureEvent(
        uuid, isStartEvent, timestamp, relatedEventId, shipInfo, otherShipInfo,
        description, title, summary, location, probability, wasValid
    )
}

fun createPlatformSupplyBargeEvent(
    uuid: String = defaultEventIdentifier,
    timestamp: Long = defaultPlatformTimeLong,
    relatedEventId: String? = null,
    shipInfo: ShipInfo = defaultShipInfo,
    otherShipInfo: ShipInfo = defaultOtherShipInfo,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    location: Location = defaultPlatformLocation,
    probability: Double = defaultProbability,
): SupplyBargeEvent {
    val isStartEvent = relatedEventId == null

    return SupplyBargeEvent(
        uuid, isStartEvent, timestamp, relatedEventId, shipInfo, otherShipInfo,
        description, title, summary, location, probability
    )
}

fun createPlatformCargoBargeEvent(
    uuid: String = defaultEventIdentifier,
    timestamp: Long = defaultPlatformTimeLong,
    relatedEventId: String? = null,
    shipInfo: ShipInfo = defaultShipInfo,
    otherShipInfo: ShipInfo = defaultOtherShipInfo,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    location: Location = defaultPlatformLocation,
    probability: Double = defaultProbability,
): CargoBargeEvent {
    val isStartEvent = relatedEventId == null

    return CargoBargeEvent(
        uuid, isStartEvent, timestamp, relatedEventId, shipInfo, otherShipInfo,
        description, title, summary, location, probability
    )
}

fun createPlatformTankerBargeEvent(
    uuid: String = defaultEventIdentifier,
    timestamp: Long = defaultPlatformTimeLong,
    relatedEventId: String? = null,
    shipInfo: ShipInfo = defaultShipInfo,
    otherShipInfo: ShipInfo = defaultOtherShipInfo,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    location: Location = defaultPlatformLocation,
    probability: Double = defaultProbability,
): TankerBargeEvent {
    val isStartEvent = relatedEventId == null

    return TankerBargeEvent(
        uuid, isStartEvent, timestamp, relatedEventId, shipInfo, otherShipInfo,
        description, title, summary, location, probability
    )
}

fun createPlatformPushBargeEvent(
    uuid: String = defaultEventIdentifier,
    timestamp: Long = defaultPlatformTimeLong,
    relatedEventId: String? = null,
    shipInfo: ShipInfo = defaultShipInfo,
    otherShipInfo: ShipInfo = defaultOtherShipInfo,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    location: Location = defaultPlatformLocation,
    probability: Double = defaultProbability,
): PushBargeEvent {
    val isStartEvent = relatedEventId == null

    return PushBargeEvent(
        uuid, isStartEvent, timestamp, relatedEventId, shipInfo, otherShipInfo,
        description, title, summary, location, probability
    )
}

fun createPlatformBargeWaterEvent(
    uuid: String = defaultEventIdentifier,
    timestamp: Long = defaultPlatformTimeLong,
    relatedEventId: String? = null,
    shipInfo: ShipInfo = defaultShipInfo,
    otherShipInfo: ShipInfo = defaultOtherShipInfo,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    location: Location = defaultPlatformLocation,
    probability: Double = defaultProbability,
): BargeWaterEvent {
    val isStartEvent = relatedEventId == null

    return BargeWaterEvent(
        uuid, isStartEvent, timestamp, relatedEventId, shipInfo, otherShipInfo,
        description, title, summary, location, probability
    )
}

fun createPlatformBargeBunkerEvent(
    uuid: String = defaultEventIdentifier,
    timestamp: Long = defaultPlatformTimeLong,
    relatedEventId: String? = null,
    shipInfo: ShipInfo = defaultShipInfo,
    otherShipInfo: ShipInfo = defaultOtherShipInfo,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    location: Location = defaultPlatformLocation,
    probability: Double = defaultProbability,
): BargeBunkerEvent {
    val isStartEvent = relatedEventId == null

    return BargeBunkerEvent(
        uuid, isStartEvent, timestamp, relatedEventId, shipInfo, otherShipInfo,
        description, title, summary, location, probability
    )
}

fun createPlatformTeqperimentEvent(
    uuid: String = defaultEventIdentifier,
    timestamp: Long = defaultPlatformTimeLong,
    relatedEventId: String? = null,
    shipInfo: ShipInfo = defaultShipInfo,
    otherShipInfo: ShipInfo = defaultOtherShipInfo,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    location: Location = defaultPlatformLocation,
    probability: Double = defaultProbability,
): TeqperimentEvent {
    val isStartEvent = relatedEventId == null

    return TeqperimentEvent(
        uuid, isStartEvent, timestamp, relatedEventId, shipInfo, otherShipInfo,
        description, title, summary, location, probability
    )
}

fun createPlatformAisRecoveredEvent(
    uuid: String = defaultEventIdentifier,
    timestamp: Long = defaultPlatformTimeLong,
    relatedEventId: String? = null,
    shipInfo: ShipInfo = defaultShipInfo,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    location: Location = defaultPlatformLocation,
): AisRecoveredEvent {
    return AisRecoveredEvent(uuid, timestamp, shipInfo, relatedEventId, description, title, summary, location)
}

fun createPlatformAisLostEvent(
    uuid: String = defaultEventIdentifier,
    timestamp: Long = defaultPlatformTimeLong,
    shipInfo: ShipInfo = defaultShipInfo,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    location: Location = defaultPlatformLocation,
): AisLostEvent {
    return AisLostEvent(uuid, timestamp, shipInfo, description, title, summary, location)
}

fun createPlatformAtaEvent(
    uuid: String = defaultEventIdentifier,
    reportedTime: Long = defaultPlatformTimeLong,
    realizedTime: Long = defaultPlatformTimeLong,
    shipInfo: ShipInfo = defaultShipInfo,
    description: String = defaultDescription,
    title: String = defaultTitle,
    location: String = defaultBerthName,
    locationId: String = defaultBerthId,
    externalVisitId: String? = defaultExternalId,
    movementId: Long? = 1000L,
    port: String = defaultPort,
    ucrn: String = defaultUcrn,
): AtaEvent {
    return AtaEvent(uuid, reportedTime, realizedTime, shipInfo, description, title, location, locationId, externalVisitId, movementId, port, ucrn)
}

fun createPlatformAtdEvent(
    uuid: String = defaultEventIdentifier,
    reportedTime: Long = defaultPlatformTimeLong,
    realizedTime: Long = defaultPlatformTimeLong,
    shipInfo: ShipInfo = defaultShipInfo,
    description: String = defaultDescription,
    title: String = defaultTitle,
    location: String = defaultBerthName,
    locationId: String = defaultBerthId,
    externalVisitId: String? = defaultExternalId,
    movementId: String? = defaultMovementId,
    port: String = defaultPort,
    ucrn: String = defaultUcrn,
): AtdEvent {
    return AtdEvent(uuid, reportedTime, realizedTime, shipInfo, description, title, location, locationId, externalVisitId, movementId, port, ucrn)
}

fun createPlatformPortcallPilotBoardingEtaEvent(
    uuid: String = defaultEventIdentifier,
    reportedTime: Long = defaultPlatformTimeLong,
    plannedTime: Long = defaultPlatformTimeLong,
    mmsi: String = defaultPlatformMmsi,
    imo: String? = defaultPlatformImo,
    description: String = defaultDescription,
    title: String = defaultTitle,
    location: String = "TEST_LOCATION",
    locationId: String = "TEST_LOCATION_ID",
    ucrn: String = defaultUcrn,
    distanceInNm: Long? = 10,
    port: String = defaultPort,
    source: String = defaultSource
): PortcallPilotBoardingEtaEvent {
    return PortcallPilotBoardingEtaEvent(uuid, reportedTime, plannedTime, mmsi, imo, description, title, location, locationId, ucrn, distanceInNm, source, port)
}

fun createPlatformEtaEvent(
    type: String,
    uuid: String = defaultEventIdentifier,
    reportedTime: Long = defaultPlatformTimeLong,
    plannedTime: Long = defaultPlatformTimeLong,
    shipInfo: ShipInfo = defaultShipInfo,
    location: String = defaultBerthName,
    locationId: String = defaultBerthId,
    ucrn: String = defaultUcrn,
    externalVisitId: String? = defaultExternalId,
    movementId: String? = defaultMovementId,
    vesselAgent: String = defaultVesselAgent,
    port: String = defaultPort,
    source: String = defaultSource
): EtaEvent {
    val description = EtaEvent.generateDescription(
        shipInfo.mmsi.toString(),
        Instant.ofEpochMilli(plannedTime).toString(),
        location,
        port,
        Instant.ofEpochMilli(reportedTime).toString()
    )
    val title = EtaEvent.generateTitle(
        Instant.ofEpochMilli(plannedTime).toString(),
        location,
        port,
        Instant.ofEpochMilli(reportedTime).toString()
    )

    return EtaEvent(uuid, type, reportedTime, plannedTime, shipInfo, source, description, title, location, locationId, externalVisitId, ucrn, movementId, vesselAgent, port)
}

fun createPlatformEtdEvent(
    type: String,
    uuid: String = defaultEventIdentifier,
    reportedTime: Long = defaultPlatformTimeLong,
    plannedTime: Long = defaultPlatformTimeLong,
    shipInfo: ShipInfo = defaultShipInfo,
    location: String = defaultBerthName,
    locationId: String = defaultBerthId,
    ucrn: String = defaultUcrn,
    externalVisitId: String? = defaultExternalId,
    vesselAgent: String = defaultVesselAgent,
    port: String = defaultPort,
    source: String = defaultSource
): EtdEvent {
    val description = EtdEvent.generateDescription(
        shipInfo.mmsi.toString(),
        Instant.ofEpochMilli(plannedTime).toString(),
        location,
        port,
        Instant.ofEpochMilli(reportedTime).toString()
    )
    val title = EtdEvent.generateTitle(
        Instant.ofEpochMilli(plannedTime).toString(),
        location,
        port,
        Instant.ofEpochMilli(reportedTime).toString()
    )

    return EtdEvent(uuid, type, reportedTime, plannedTime, shipInfo, source, description, title, location, locationId, externalVisitId, ucrn, vesselAgent, port)
}

fun createPlatformAddPortcallVisitEvent(
    uuid: String = defaultEventIdentifier,
    ucrn: String = defaultUcrn,
    port: String = defaultPort,
    shipInfo: ShipInfo = defaultShipInfo,
    index: Int = 1,
    berthName: String = defaultBerthName,
    berthOwnerId: String = defaultBerthId,
    timestamp: Long = defaultPlatformTimeLong,
): AddPortcallVisitEvent {
    return AddPortcallVisitEvent(uuid, ucrn, port, shipInfo, index, berthName, berthOwnerId, timestamp)
}

fun createPlatformUpdatePortcallVisitEvent(
    uuid: String = defaultEventIdentifier,
    ucrn: String = defaultUcrn,
    port: String = defaultPort,
    shipInfo: ShipInfo = defaultShipInfo,
    index: Int = 1,
    berthName: String = defaultBerthName,
    berthOwnerId: String = defaultBerthId,
    previousBerthName: String = defaultOtherBerthName,
    previousBerthOwnerId: String = defaultOtherBerthId,
    timestamp: Long = defaultPlatformTimeLong,
): UpdatePortcallVisitEvent {
    return UpdatePortcallVisitEvent(uuid, ucrn, port, shipInfo, index, berthName, berthOwnerId, previousBerthName, previousBerthOwnerId, timestamp)
}

fun createPlatformCancelPortcallVisitEvent(
    uuid: String = defaultEventIdentifier,
    ucrn: String = defaultUcrn,
    port: String = defaultPort,
    shipInfo: ShipInfo = defaultShipInfo,
    index: Int = 1,
    berthName: String = defaultBerthName,
    berthOwnerId: String = defaultBerthId,
    timestamp: Long = defaultPlatformTimeLong,
): CancelPortcallVisitEvent {
    return CancelPortcallVisitEvent(uuid, ucrn, port, shipInfo, index, berthName, berthOwnerId, timestamp)
}

fun createPlatformAgentOrderEvent(
    uuid: String = defaultEventIdentifier,
    ucrn: String = defaultUcrn,
    port: String = defaultPort,
    vesselAgent: String = defaultVesselAgent,
    shipInfo: ShipInfo = defaultShipInfo,
    order: String = defaultOrder,
    movementId: String = defaultMovementId,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    timestamp: Long = defaultPlatformTimeLong,
): AgentOrderEvent {
    return AgentOrderEvent(uuid, timestamp, description, title, summary, shipInfo, ucrn, port, vesselAgent, order, movementId)
}

fun createPlatformAgentReportsTugsEvent(
    uuid: String = defaultEventIdentifier,
    ucrn: String = defaultUcrn,
    movementId: String? = defaultMovementId,
    externalVisitId: String? = null,
    vesselAgent: String? = defaultVesselAgent,
    towingCompany: String? = null,
    port: String = defaultPort,
    shipInfo: ShipInfo = defaultShipInfo,
    serviceShip: String = "SERVICE_SHIP",
    berth: String = defaultBerthName,
    description: String = defaultDescription,
    title: String = defaultTitle,
    timestamp: Long = defaultPlatformTimeLong,
    reportedTime: Long = defaultPlatformTimeLong,
): AgentReportsTugsEvent {
    return AgentReportsTugsEvent(uuid, timestamp, description, title, shipInfo, ucrn, port, serviceShip, berth, reportedTime, vesselAgent, externalVisitId, towingCompany, movementId)
}

fun createPlatformEtaCancelEvent(
    uuid: String = defaultEventIdentifier,
    ucrn: String = defaultUcrn,
    movementId: String? = defaultMovementId,
    vesselAgent: String? = defaultVesselAgent,
    port: String = defaultPort,
    shipInfo: ShipInfo = defaultShipInfo,
    source: String = defaultSource,
    description: String = defaultDescription,
    title: String = defaultTitle,
    timestamp: Long = defaultPlatformTimeLong,
): EtaCancelEvent {
    return EtaCancelEvent(uuid, timestamp, description, title, shipInfo, ucrn, port, vesselAgent, source, movementId)
}

fun createPlatformEtaRequestEvent(
    uuid: String = defaultEventIdentifier,
    ucrn: String = defaultUcrn,
    movementId: String? = defaultMovementId,
    vesselAgent: String? = defaultVesselAgent,
    port: String = defaultPort,
    shipInfo: ShipInfo = defaultShipInfo,
    pilotStation: String? = defaultPilotStation,
    berth: String = defaultBerthName,
    berthOwnerId: String? = defaultBerthId,
    externalVisitId: String? = defaultExternalId,
    description: String = defaultDescription,
    title: String = defaultTitle,
    timestamp: Long = defaultPlatformTimeLong,
    eta: Long = defaultPlatformTimeLong,
): EtaRequestEvent {
    return EtaRequestEvent(uuid, timestamp, description, title, shipInfo, ucrn, port, pilotStation, berth, berthOwnerId, eta, vesselAgent, externalVisitId, movementId)
}

fun createPlatformEtdRequestEvent(
    uuid: String = defaultEventIdentifier,
    ucrn: String = defaultUcrn,
    movementId: String? = defaultMovementId,
    vesselAgent: String? = defaultVesselAgent,
    port: String = defaultPort,
    shipInfo: ShipInfo = defaultShipInfo,
    pilotStation: String? = defaultPilotStation,
    berth: String = defaultBerthName,
    berthOwnerId: String? = defaultBerthId,
    externalVisitId: String? = defaultExternalId,
    description: String = defaultDescription,
    title: String = defaultTitle,
    timestamp: Long = defaultPlatformTimeLong,
    etd: Long = defaultPlatformTimeLong,
): EtdRequestEvent {
    return EtdRequestEvent(uuid, timestamp, description, title, shipInfo, ucrn, port, pilotStation, berth, berthOwnerId, etd, vesselAgent, externalVisitId, movementId)
}

fun createPlatformHamisPilotBoardingEtaEvent(
    uuid: String = defaultEventIdentifier,
    ucrn: String = defaultUcrn,
    movementId: String? = defaultMovementId,
    vesselAgent: String? = defaultVesselAgent,
    port: String = defaultPort,
    shipInfo: ShipInfo = defaultShipInfo,
    pilotStation: String? = defaultPilotStation,
    source: String? = defaultSource,
    description: String = defaultDescription,
    title: String = defaultTitle,
    timestamp: Long = defaultPlatformTimeLong,
    eta: Long = defaultPlatformTimeLong,
): HamisPilotBoardingEtaEvent {
    return HamisPilotBoardingEtaEvent(uuid, timestamp, description, title, shipInfo, ucrn, port, pilotStation, eta, vesselAgent, source, movementId)
}

fun createPlatformNauticalOrderEvent(
    uuid: String = defaultEventIdentifier,
    ucrn: String = defaultUcrn,
    movementId: String? = defaultMovementId,
    shipInfo: ShipInfo = defaultShipInfo,
    port: String = defaultPort,
    reporter: String = defaultReporter,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    timestamp: Long = defaultPlatformTimeLong,
): NauticalOrderEvent {
    return NauticalOrderEvent(uuid, timestamp, description, title, summary, shipInfo, ucrn, port, reporter, movementId)
}

fun createPlatformPilotOnBoardEvent(
    uuid: String = defaultEventIdentifier,
    relatedEventId: String? = null,
    ucrn: String = defaultUcrn,
    movementId: String? = defaultMovementId,
    shipInfo: ShipInfo = defaultShipInfo,
    port: String = defaultPort,
    description: String = defaultDescription,
    title: String = defaultTitle,
    summary: String = defaultSummary,
    timestamp: Long = defaultPlatformTimeLong,
    reportedTime: Long = defaultPlatformTimeLong,
    reason: PilotOnBoardEvent.MovementType = PilotOnBoardEvent.MovementType.DEPARTURE,
    fromBerth: String? = defaultBerthName,
    toBerth: String? = defaultOtherBerthName,
    vesselAgent: String? = defaultVesselAgent,
): PilotOnBoardEvent {
    val isStartEvent = relatedEventId == null

    return PilotOnBoardEvent(
        uuid, timestamp, description, title, summary, shipInfo, ucrn, port, isStartEvent,
        reportedTime, reason, fromBerth, toBerth, relatedEventId, movementId, vesselAgent
    )
}

fun createPlatformVisitCancellationEvent(
    uuid: String = defaultEventIdentifier,
    ucrn: String = defaultUcrn,
    movementId: String? = defaultMovementId,
    shipInfo: ShipInfo = defaultShipInfo,
    port: String = defaultPort,
    description: String = defaultDescription,
    title: String = defaultTitle,
    timestamp: Long = defaultPlatformTimeLong,
): VisitCancellationEvent {
    return VisitCancellationEvent(uuid, timestamp, description, title, shipInfo, ucrn, port, movementId)
}

fun createPlatformVisitDeclarationEvent(
    uuid: String = defaultEventIdentifier,
    ucrn: String = defaultUcrn,
    vesselAgent: String? = defaultVesselAgent,
    shipInfo: ShipInfo = defaultShipInfo,
    port: String = defaultPort,
    shipName: String = defaultShipName,
    previousPort: String = defaultOtherPort,
    description: String = defaultDescription,
    title: String = defaultTitle,
    timestamp: Long = defaultPlatformTimeLong,
): VisitDeclarationEvent {
    return VisitDeclarationEvent(uuid, timestamp, description, title, shipInfo, ucrn, port, shipName, previousPort, vesselAgent)
}

fun createPlatformLockEtaEvent(
    uuid: String = defaultEventIdentifier,
    description: String = defaultDescription,
    title: String = defaultTitle,
    shipInfo: ShipInfo = defaultShipInfo,
    isPlanning: Boolean = true,
    isrsId: String = defaultIsrsId,
    lockName: String = defaultLockName,
    direction: LockDirection = LockDirection.INBOUND,
    location: Location = defaultPlatformLocation,
    timestamp: Long = defaultPlatformTimeLong,
    dateTimePlan: Long = defaultPlatformTimeLong,
    probability: Double = defaultProbability
): LockEvent {
    return LockEvent(
        uuid,
        "ship.eta.lock",
        timestamp,
        description,
        title,
        shipInfo,
        isPlanning,
        isrsId,
        lockName,
        direction,
        location,
        dateTimePlan,
        probability
    )
}

fun createPlatformLockEtdEvent(
    uuid: String = defaultEventIdentifier,
    description: String = defaultDescription,
    timestamp: Long = defaultPlatformTimeLong,
    title: String = defaultTitle,
    shipInfo: ShipInfo = defaultShipInfo,
    isPlanning: Boolean = true,
    isrsId: String = defaultIsrsId,
    lockName: String = defaultLockName,
    direction: LockDirection = LockDirection.INBOUND,
    location: Location = defaultPlatformLocation,
    dateTimePlan: Long = defaultPlatformTimeLong,
    probability: Double = defaultProbability
): LockEvent {
    return LockEvent(
        uuid,
        "ship.etd.lock",
        timestamp,
        description,
        title,
        shipInfo,
        isPlanning,
        isrsId,
        lockName,
        direction,
        location,
        dateTimePlan,
        probability
    )
}
