package nl.teqplay.aisengine.reventsengine.global

import nl.teqplay.csi.model.ship.mapping.ShipRegisterMapping
import java.time.Instant

fun getImoAtTime(
    mmsi: Int,
    time: Instant,
    mappingsByMmsi: Map<Int, List<ShipRegisterMapping>>
): Int? {
    val mmsiMappings = mappingsByMmsi[mmsi]
        ?: return null

    val imoMapping = mmsiMappings.firstOrNull { it.containsMmsi(mmsi.toString(), time.toEpochMilli()) }
    return imoMapping?.imo?.toIntOrNull()
}
