package nl.teqplay.aisengine.reventsengine.global.model

import nl.teqplay.skeleton.model.Polygon

data class InterestResolvedInternal(
    val ships: List<InterestShipInternal>,
    val polygons: List<Polygon>,

    /**
     * Unlocodes that were resolved based on areas.
     */
    val unlocodes: List<String> = emptyList(),
) {
    companion object {
        fun empty() = InterestResolvedInternal(emptyList(), emptyList())
    }
}
