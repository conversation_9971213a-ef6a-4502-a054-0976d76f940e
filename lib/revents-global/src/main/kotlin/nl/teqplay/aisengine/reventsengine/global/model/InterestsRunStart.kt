package nl.teqplay.aisengine.reventsengine.global.model

import nl.teqplay.aisengine.reventsengine.model.scenario.Scenario
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioEvent
import nl.teqplay.skeleton.model.Polygon
import nl.teqplay.skeleton.model.TimeWindow

data class InterestsRunStart(
    val id: String,
    val events: Set<ScenarioEvent>,
    val interests: List<RelevantShip>,
    val otherVessels: OtherVessels?,
    val settings: Settings,
) {
    data class RelevantShip(
        val mmsi: Int,
        val window: TimeWindow,
    )

    data class OtherVessels(
        val areas: List<Polygon>,
    )

    data class Settings(
        val filterHistory: Scenario.FilterHistory,
    )
}
