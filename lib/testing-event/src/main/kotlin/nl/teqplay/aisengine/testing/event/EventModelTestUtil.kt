package nl.teqplay.aisengine.testing.event

import nl.teqplay.aisengine.aisstream.model.AisMessage
import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.aisengine.event.interfaces.EncounterMetadata
import nl.teqplay.aisengine.event.interfaces.EventIdentifier
import nl.teqplay.aisengine.event.interfaces.HamisPilotOnBoardEvent
import nl.teqplay.aisengine.event.interfaces.LockEvent
import nl.teqplay.aisengine.event.interfaces.SpeedEvent
import nl.teqplay.aisengine.event.model.AisDestinationChangedEvent
import nl.teqplay.aisengine.event.model.AisDraughtChangedEvent
import nl.teqplay.aisengine.event.model.AisEtaChangedEvent
import nl.teqplay.aisengine.event.model.AisLostEvent
import nl.teqplay.aisengine.event.model.AisRecoverEvent
import nl.teqplay.aisengine.event.model.AisStatusChangedEvent
import nl.teqplay.aisengine.event.model.AnchoredEndEvent
import nl.teqplay.aisengine.event.model.AnchoredStartEvent
import nl.teqplay.aisengine.event.model.AreaEndEvent
import nl.teqplay.aisengine.event.model.AreaStartEvent
import nl.teqplay.aisengine.event.model.ConfirmedBerthEndEvent
import nl.teqplay.aisengine.event.model.ConfirmedBerthStartEvent
import nl.teqplay.aisengine.event.model.EncounterEndEvent
import nl.teqplay.aisengine.event.model.EncounterStartEvent
import nl.teqplay.aisengine.event.model.EtaEvent
import nl.teqplay.aisengine.event.model.EtdEvent
import nl.teqplay.aisengine.event.model.LockEtaEvent
import nl.teqplay.aisengine.event.model.LockEtdEvent
import nl.teqplay.aisengine.event.model.PortcallPilotBoardingEtaEvent
import nl.teqplay.aisengine.event.model.ShipMovingEndEvent
import nl.teqplay.aisengine.event.model.ShipMovingStartEvent
import nl.teqplay.aisengine.event.model.SpeedChangedEvent
import nl.teqplay.aisengine.event.model.StateAreaInsideEvent
import nl.teqplay.aisengine.event.model.StopEndEvent
import nl.teqplay.aisengine.event.model.StopStartEvent
import nl.teqplay.aisengine.event.model.TrueDestinationChangedEvent
import nl.teqplay.aisengine.event.model.UniqueBerthEndEvent
import nl.teqplay.aisengine.event.model.UniqueBerthStartEvent
import nl.teqplay.aisengine.event.model.hamis.HamisAddPortcallVisitEvent
import nl.teqplay.aisengine.event.model.hamis.HamisAgentOrderEvent
import nl.teqplay.aisengine.event.model.hamis.HamisAgentReportsTugsEvent
import nl.teqplay.aisengine.event.model.hamis.HamisAtaEvent
import nl.teqplay.aisengine.event.model.hamis.HamisAtdEvent
import nl.teqplay.aisengine.event.model.hamis.HamisCancelPortcallVisitEvent
import nl.teqplay.aisengine.event.model.hamis.HamisEtaBerthEvent
import nl.teqplay.aisengine.event.model.hamis.HamisEtaCancelEvent
import nl.teqplay.aisengine.event.model.hamis.HamisEtaEvent
import nl.teqplay.aisengine.event.model.hamis.HamisEtaRequestEvent
import nl.teqplay.aisengine.event.model.hamis.HamisEtdEvent
import nl.teqplay.aisengine.event.model.hamis.HamisEtdRequestEvent
import nl.teqplay.aisengine.event.model.hamis.HamisNauticalOrderEvent
import nl.teqplay.aisengine.event.model.hamis.HamisPilotBoardingEtaEvent
import nl.teqplay.aisengine.event.model.hamis.HamisPilotOnBoardEndEvent
import nl.teqplay.aisengine.event.model.hamis.HamisPilotOnBoardStartEvent
import nl.teqplay.aisengine.event.model.hamis.HamisUpdatePortcallVisitEvent
import nl.teqplay.aisengine.event.model.hamis.HamisVisitCancellationEvent
import nl.teqplay.aisengine.event.model.hamis.HamisVisitDeclarationEvent
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.event.model.identifier.BerthIdentifier
import nl.teqplay.aisengine.event.model.identifier.GeneralShipIdentifier
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusAgentChangedEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusAtaEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusAtdEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusEtaBerthEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusEtaEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusEtdEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusPortcallFinishEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusPortcallVisit
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusShipChangedEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusVisitsUpdateEvent
import nl.teqplay.skeleton.model.Location
import java.time.Duration
import java.time.Instant

const val defaultMmsi = 111111111
const val defaultMmsiString = "111111111"
const val defaultOtherMmsi = 222222222
const val defaultImo = 1111111
const val defaultOtherImo = 2222222
const val defaultEventIdentifier = "DEFAULT_EVENT_ID"
const val defaultStartEventIdentifier = "DEFAULT_START_EVENT_ID"
const val defaultPort = "NLRTM"
const val defaultOtherPort = "BEANR"
const val defaultIsrsCode = "DEFAULT_ISRS_CODE"
const val defaultUcrn = "DEFAULT_UCRN"
const val defaultMovementId = "DEFAULT_MOVEMENT_ID"
const val defaultExternalId = "DEFAULT_EXTERNAL_ID"
const val defaultVesselAgent = "DEFAULT_VESSEL_AGENT"
const val defaultOrder = "DEFAULT_ORDER"
const val defaultPilotStation = "DEFAULT_PILOT_STATION"
const val defaultReporter = "DEFAULT_REPORTER"
const val defaultSource = "DEFAULT_SOURCE"
const val defaultShipName = "DEFAULT_SHIP_NAME"
const val defaultProbability = 1.0
const val defaultTerminalName = "DEFAULT_TERMINAL_NAME"
val defaultAisShipIdentifier = AisShipIdentifier(defaultMmsi, defaultImo)
val defaultOtherAisShipIdentifier = AisShipIdentifier(defaultOtherMmsi, defaultOtherImo)
val defaultGeneralShipIdentifier = GeneralShipIdentifier(defaultMmsi, defaultImo)
val defaultOtherGeneralShipIdentifier = GeneralShipIdentifier(defaultOtherMmsi, defaultOtherImo)
val defaultPortAreaIdentifier = AreaIdentifier(defaultPort, AreaIdentifier.AreaType.PORT)
val defaultAnchorageAreaIdentifier = AreaIdentifier("ANCHOR_ID", AreaIdentifier.AreaType.ANCHOR)
val defaultOtherPortAreaIdentifier = AreaIdentifier(defaultOtherPort, AreaIdentifier.AreaType.PORT)
const val defaultBerthId = "BERT"
const val defaultBerthName = "ERNIE"
const val defaultOtherBerthId = "OTHER_BERTH"
const val defaultOtherBerthName = "OTHER_ERNIE"
val defaultBerthAreaIdentifier = AreaIdentifier(defaultBerthId, AreaIdentifier.AreaType.BERTH)
val defaultBerthIdentifier = BerthIdentifier(null, defaultTerminalName)
val defaultLocation = Location(0.0, 0.0)
val defaultTime: Instant = Instant.ofEpochMilli(1682899200000)
val timeOneHourLater: Instant = defaultTime.plusSeconds(Duration.ofHours(1).toSeconds())
const val defaultLockName = "DEFAULT_LOCK_NAME"
val defaultLockAreaIdentifier = AreaIdentifier(null, AreaIdentifier.AreaType.LOCK, defaultLockName)

fun createAisDestinationChangedEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ship: AisShipIdentifier = defaultAisShipIdentifier,
    location: Location = defaultLocation,
    oldValue: String? = "old",
    newValue: String? = "new",
    actualTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): AisDestinationChangedEvent {
    return AisDestinationChangedEvent(
        _id = _id,
        ship = ship,
        location = location,
        oldValue = oldValue,
        newValue = newValue,
        actualTime = actualTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createAisDraughtChangedEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ship: AisShipIdentifier = defaultAisShipIdentifier,
    location: Location = defaultLocation,
    oldValue: Float? = 10f,
    newValue: Float? = 15f,
    actualTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): AisDraughtChangedEvent {
    return AisDraughtChangedEvent(
        _id = _id,
        ship = ship,
        location = location,
        oldValue = oldValue,
        newValue = newValue,
        actualTime = actualTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createAisEtaChangedEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ship: AisShipIdentifier = defaultAisShipIdentifier,
    location: Location = defaultLocation,
    oldValue: Instant? = defaultTime,
    newValue: Instant? = timeOneHourLater,
    actualTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): AisEtaChangedEvent {
    return AisEtaChangedEvent(
        _id = _id,
        ship = ship,
        location = location,
        oldValue = oldValue,
        newValue = newValue,
        actualTime = actualTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createAisStatusChangedEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ship: AisShipIdentifier = defaultAisShipIdentifier,
    location: Location = defaultLocation,
    oldValue: AisMessage.ShipStatus? = AisMessage.ShipStatus.UNDEFINED,
    newValue: AisMessage.ShipStatus = AisMessage.ShipStatus.MOORED,
    actualTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): AisStatusChangedEvent {
    return AisStatusChangedEvent(
        _id = _id,
        ship = ship,
        location = location,
        oldValue = oldValue,
        newValue = newValue,
        actualTime = actualTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createConfirmedBerthStartEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    berthEventId: EventIdentifier = defaultEventIdentifier,
    ship: AisShipIdentifier = defaultAisShipIdentifier,
    area: AreaIdentifier = defaultBerthAreaIdentifier,
    berth: BerthIdentifier = defaultBerthIdentifier,
    heading: Int? = null,
    draught: Float? = 10f,
    location: Location = defaultLocation,
    actualTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): ConfirmedBerthStartEvent {
    return ConfirmedBerthStartEvent(
        _id = _id,
        berthEventId = berthEventId,
        ship = ship,
        area = area,
        berth = berth,
        heading = heading,
        draught = draught,
        location = location,
        actualTime = actualTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createConfirmedBerthEndEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    startEventId: EventIdentifier? = defaultStartEventIdentifier,
    berthEventId: EventIdentifier = defaultEventIdentifier,
    ship: AisShipIdentifier = defaultAisShipIdentifier,
    area: AreaIdentifier = defaultBerthAreaIdentifier,
    berth: BerthIdentifier = defaultBerthIdentifier,
    heading: Int? = null,
    draught: Float? = 10f,
    location: Location = defaultLocation,
    actualTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): ConfirmedBerthEndEvent {
    return ConfirmedBerthEndEvent(
        _id = _id,
        startEventId = startEventId,
        berthEventId = berthEventId,
        ship = ship,
        area = area,
        berth = berth,
        heading = heading,
        draught = draught,
        location = location,
        actualTime = actualTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createUniqueBerthStartEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    berthEventId: EventIdentifier = defaultEventIdentifier,
    ship: AisShipIdentifier = defaultAisShipIdentifier,
    area: AreaIdentifier = defaultBerthAreaIdentifier,
    berth: BerthIdentifier = defaultBerthIdentifier,
    heading: Int? = null,
    draught: Float? = 10f,
    location: Location = defaultLocation,
    actualTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): UniqueBerthStartEvent {
    return UniqueBerthStartEvent(
        _id = _id,
        berthEventId = berthEventId,
        berthConfirmedEventId = berthEventId,
        ship = ship,
        area = area,
        berth = berth,
        heading = heading,
        draught = draught,
        location = location,
        actualTime = actualTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createUniqueBerthEndEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    startEventId: EventIdentifier? = defaultStartEventIdentifier,
    berthEventId: EventIdentifier = defaultEventIdentifier,
    ship: AisShipIdentifier = defaultAisShipIdentifier,
    area: AreaIdentifier = defaultBerthAreaIdentifier,
    berth: BerthIdentifier = defaultBerthIdentifier,
    heading: Int? = null,
    draught: Float? = 10f,
    location: Location = defaultLocation,
    actualTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): UniqueBerthEndEvent {
    return UniqueBerthEndEvent(
        _id = _id,
        startEventId = startEventId,
        berthEventId = berthEventId,
        berthConfirmedEventId = berthEventId,
        ship = ship,
        area = area,
        berth = berth,
        heading = heading,
        draught = draught,
        location = location,
        actualTime = actualTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createTrueDestinationChangedEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ship: AisShipIdentifier = defaultAisShipIdentifier,
    oldValue: String? = "to NLRTM",
    newValue: String? = "NLRTM => NLAMS",
    newTrueDestination: String? = "NLAMS",
    location: Location = defaultLocation,
    actualTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): TrueDestinationChangedEvent {
    return TrueDestinationChangedEvent(
        _id = _id,
        ship = ship,
        oldValue = oldValue,
        newValue = newValue,
        trueDestination = newTrueDestination,
        location = location,
        actualTime = actualTime, createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createSpeedChangedEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ship: AisShipIdentifier = defaultAisShipIdentifier,
    location: Location = defaultLocation,
    speedType: SpeedEvent.SpeedType = SpeedEvent.SpeedType.ACCELERATING,
    actualTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): SpeedChangedEvent {
    return SpeedChangedEvent(
        _id = _id,
        ship = ship,
        location = location,
        speedType = speedType,
        actualTime = actualTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createShipMovingStartEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ship: AisShipIdentifier = defaultAisShipIdentifier,
    location: Location = defaultLocation,
    actualTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): ShipMovingStartEvent {
    return ShipMovingStartEvent(
        _id = _id,
        ship = ship,
        location = location,
        actualTime = actualTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createShipMovingEndEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    startEventId: EventIdentifier = defaultStartEventIdentifier,
    ship: AisShipIdentifier = defaultAisShipIdentifier,
    location: Location = defaultLocation,
    actualTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): ShipMovingEndEvent {
    return ShipMovingEndEvent(
        _id = _id,
        startEventId = startEventId,
        ship = ship,
        location = location,
        actualTime = actualTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createStopStartEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ship: AisShipIdentifier = defaultAisShipIdentifier,
    location: Location = defaultLocation,
    actualTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): StopStartEvent {
    return StopStartEvent(
        _id = _id,
        ship = ship,
        location = location,
        actualTime = actualTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createStopEndEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    startEventId: EventIdentifier = defaultStartEventIdentifier,
    ship: AisShipIdentifier = defaultAisShipIdentifier,
    location: Location = defaultLocation,
    stopLocation: Location = defaultLocation,
    actualTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): StopEndEvent {
    return StopEndEvent(
        _id = _id,
        startEventId = startEventId,
        ship = ship,
        location = location,
        stopLocation = stopLocation,
        actualTime = actualTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createPortcallPilotBoardingEtaEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    portcallId: String = defaultUcrn,
    ship: AisShipIdentifier = defaultAisShipIdentifier,
    area: AreaIdentifier = defaultPortAreaIdentifier,
    source: String = defaultSource,
    predictedTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): PortcallPilotBoardingEtaEvent {
    return PortcallPilotBoardingEtaEvent(
        _id = _id,
        portcallId = portcallId,
        ship = ship,
        area = area,
        source = source,
        predictedTime = predictedTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createLockEtaEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    isrsId: String = defaultIsrsCode,
    ship: AisShipIdentifier = defaultAisShipIdentifier,
    area: AreaIdentifier = defaultLockAreaIdentifier,
    location: Location = defaultLocation,
    direction: LockEvent.LockDirection = LockEvent.LockDirection.INBOUND,
    predictedTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): LockEtaEvent {
    return LockEtaEvent(
        _id = _id,
        isrsId = isrsId,
        ship = ship,
        area = area,
        location = location,
        direction = direction,
        predictedTime = predictedTime,
        actualTime = createdTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createLockEtdEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    isrsCode: String = defaultIsrsCode,
    ship: AisShipIdentifier = defaultAisShipIdentifier,
    area: AreaIdentifier = defaultLockAreaIdentifier,
    location: Location = defaultLocation,
    direction: LockEvent.LockDirection = LockEvent.LockDirection.INBOUND,
    predictedTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): LockEtdEvent {
    return LockEtdEvent(
        _id = _id,
        isrsId = isrsCode,
        ship = ship,
        area = area,
        location = location,
        direction = direction,
        predictedTime = predictedTime,
        actualTime = createdTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createEtaEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ucrn: String = defaultUcrn,
    ship: AisShipIdentifier = defaultAisShipIdentifier,
    area: AreaIdentifier = defaultPortAreaIdentifier,
    vesselAgent: String? = defaultVesselAgent,
    predictedTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): EtaEvent {
    return EtaEvent(
        _id = _id,
        portcallId = ucrn,
        ship = ship,
        area = area,
        vesselAgent = vesselAgent,
        predictedTime = predictedTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createEtdEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ucrn: String = defaultUcrn,
    ship: AisShipIdentifier = defaultAisShipIdentifier,
    area: AreaIdentifier = defaultPortAreaIdentifier,
    vesselAgent: String? = defaultVesselAgent,
    predictedTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): EtdEvent {
    return EtdEvent(
        _id = _id,
        portcallId = ucrn,
        ship = ship,
        area = area,
        vesselAgent = vesselAgent,
        predictedTime = predictedTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createHamisEtaEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ucrn: String = defaultUcrn,
    ship: GeneralShipIdentifier = defaultGeneralShipIdentifier,
    area: AreaIdentifier = defaultBerthAreaIdentifier,
    port: AreaIdentifier = defaultPortAreaIdentifier,
    source: String? = defaultSource,
    berth: String? = defaultBerthName,
    berthId: String? = defaultBerthId,
    vesselAgent: String? = defaultVesselAgent,
    predictedTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): HamisEtaEvent {
    return HamisEtaEvent(
        _id = _id,
        portcallId = ucrn,
        ship = ship,
        area = area,
        port = port,
        source = source,
        berth = berth,
        berthId = berthId,
        vesselAgent = vesselAgent,
        predictedTime = predictedTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createHamisEtaBerthEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ucrn: String = defaultUcrn,
    ship: GeneralShipIdentifier = defaultGeneralShipIdentifier,
    area: AreaIdentifier = defaultBerthAreaIdentifier,
    port: AreaIdentifier = defaultPortAreaIdentifier,
    source: String? = defaultSource,
    berth: String? = defaultBerthName,
    berthId: String? = defaultBerthId,
    vesselAgent: String? = defaultVesselAgent,
    distanceInNm: Long = 0L,
    portcallBerthId: String? = defaultBerthId,
    predictedTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): HamisEtaBerthEvent {
    return HamisEtaBerthEvent(
        _id = _id,
        portcallId = ucrn,
        ship = ship,
        area = area,
        port = port,
        source = source,
        berth = berth,
        berthId = berthId,
        vesselAgent = vesselAgent,
        distanceInNm = distanceInNm,
        portcallBerthId = portcallBerthId,
        predictedTime = predictedTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createHamisEtdEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ucrn: String = defaultUcrn,
    ship: GeneralShipIdentifier = defaultGeneralShipIdentifier,
    area: AreaIdentifier = defaultBerthAreaIdentifier,
    port: AreaIdentifier = defaultPortAreaIdentifier,
    source: String? = defaultSource,
    berth: String? = defaultBerthName,
    berthId: String? = defaultBerthId,
    vesselAgent: String? = defaultVesselAgent,
    predictedTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): HamisEtdEvent {
    return HamisEtdEvent(
        _id = _id,
        portcallId = ucrn,
        ship = ship,
        area = area,
        port = port,
        source = source,
        berth = berth,
        berthId = berthId,
        vesselAgent = vesselAgent,
        predictedTime = predictedTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createEncounterStartEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ship: AisShipIdentifier = defaultAisShipIdentifier,
    otherShip: AisShipIdentifier = defaultOtherAisShipIdentifier,
    encounterType: EncounterEvent.EncounterType = EncounterEvent.EncounterType.BUNKER,
    location: Location = defaultLocation,
    actualTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null,
    probability: Double = defaultProbability,
    metadata: EncounterMetadata? = null
): EncounterStartEvent {
    return EncounterStartEvent(
        _id = _id,
        ship = ship,
        otherShip = otherShip,
        encounterType = encounterType,
        location = location,
        actualTime = actualTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated,
        probability = probability,
        metadata = metadata
    )
}

fun createEncounterEndEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    startEventId: EventIdentifier = defaultStartEventIdentifier,
    ship: AisShipIdentifier = defaultAisShipIdentifier,
    otherShip: AisShipIdentifier = defaultOtherAisShipIdentifier,
    encounterType: EncounterEvent.EncounterType = EncounterEvent.EncounterType.BUNKER,
    location: Location = defaultLocation,
    actualTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null,
    probability: Double = defaultProbability,
    metadata: EncounterMetadata? = null
): EncounterEndEvent {
    return EncounterEndEvent(
        _id = _id,
        startEventId = startEventId,
        ship = ship,
        otherShip = otherShip,
        encounterType = encounterType,
        location = location,
        actualTime = actualTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated,
        probability = probability,
        metadata = metadata
    )
}

fun createHamisAtaEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ucrn: String = defaultUcrn,
    externalVisitId: String = defaultExternalId,
    movementId: String = defaultMovementId,
    ship: GeneralShipIdentifier = defaultGeneralShipIdentifier,
    area: AreaIdentifier = defaultBerthAreaIdentifier,
    port: AreaIdentifier = defaultPortAreaIdentifier,
    actualTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): HamisAtaEvent {
    return HamisAtaEvent(
        _id = _id,
        portcallId = ucrn,
        externalVisitId = externalVisitId,
        movementId = movementId,
        ship = ship,
        area = area,
        port = port,
        actualTime = actualTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createHamisAtdEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ucrn: String = defaultUcrn,
    externalVisitId: String = defaultExternalId,
    movementId: String = defaultMovementId,
    ship: GeneralShipIdentifier = defaultGeneralShipIdentifier,
    area: AreaIdentifier = defaultBerthAreaIdentifier,
    port: AreaIdentifier = defaultPortAreaIdentifier,
    actualTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): HamisAtdEvent {
    return HamisAtdEvent(
        _id = _id,
        portcallId = ucrn,
        externalVisitId = externalVisitId,
        movementId = movementId,
        ship = ship,
        area = area,
        port = port,
        actualTime = actualTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createAreaStartEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ship: AisShipIdentifier = defaultAisShipIdentifier,
    area: AreaIdentifier = defaultPortAreaIdentifier,
    berth: BerthIdentifier? = null,
    heading: Int? = null,
    draught: Float? = null,
    speedOverGround: Float? = null,
    location: Location = defaultLocation,
    actualTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): AreaStartEvent {
    return AreaStartEvent(
        _id = _id,
        ship = ship,
        area = area,
        berth = berth,
        heading = heading,
        draught = draught,
        speedOverGround = speedOverGround,
        location = location,
        actualTime = actualTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createAreaEndEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    startEventId: EventIdentifier = defaultStartEventIdentifier,
    ship: AisShipIdentifier = defaultAisShipIdentifier,
    area: AreaIdentifier = defaultPortAreaIdentifier,
    berth: BerthIdentifier? = null,
    heading: Int? = null,
    draught: Float? = null,
    speedOverGround: Float? = null,
    location: Location = defaultLocation,
    actualTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): AreaEndEvent {
    return AreaEndEvent(
        _id = _id,
        startEventId = startEventId,
        ship = ship,
        area = area,
        berth = berth,
        heading = heading,
        draught = draught,
        speedOverGround = speedOverGround,
        location = location,
        actualTime = actualTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createStateAreaInsideEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ship: AisShipIdentifier = defaultAisShipIdentifier,
    area: AreaIdentifier = defaultPortAreaIdentifier,
    berth: BerthIdentifier? = null,
    heading: Int? = null,
    draught: Float? = null,
    speedOverGround: Float? = null,
    location: Location = defaultLocation,
    actualTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): StateAreaInsideEvent {
    return StateAreaInsideEvent(
        _id = _id,
        ship = ship,
        area = area,
        berth = berth,
        heading = heading,
        draught = draught,
        speedOverGround = speedOverGround,
        location = location,
        actualTime = actualTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createAnchoredStartEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ship: AisShipIdentifier = defaultAisShipIdentifier,
    area: AreaIdentifier = defaultAnchorageAreaIdentifier,
    location: Location = defaultLocation,
    actualTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): AnchoredStartEvent {
    return AnchoredStartEvent(
        _id = _id,
        ship = ship,
        area = area,
        location = location,
        actualTime = actualTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createAnchoredEndEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    startEventId: EventIdentifier = defaultStartEventIdentifier,
    ship: AisShipIdentifier = defaultAisShipIdentifier,
    area: AreaIdentifier = defaultAnchorageAreaIdentifier,
    location: Location = defaultLocation,
    actualTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): AnchoredEndEvent {
    return AnchoredEndEvent(
        _id = _id,
        startEventId = startEventId,
        ship = ship,
        area = area,
        location = location,
        actualTime = actualTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createAisRecoverEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ship: AisShipIdentifier = defaultAisShipIdentifier,
    location: Location = defaultLocation,
    actualTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): AisRecoverEvent {
    return AisRecoverEvent(
        _id = _id,
        ship = ship,
        location = location,
        actualTime = actualTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createAisLostEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ship: AisShipIdentifier = defaultAisShipIdentifier,
    location: Location = defaultLocation,
    actualTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): AisLostEvent {
    return AisLostEvent(
        _id = _id,
        ship = ship,
        location = location,
        actualTime = actualTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createHamisAddPortcallVisitEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ship: GeneralShipIdentifier = defaultGeneralShipIdentifier,
    ucrn: String = defaultUcrn,
    port: AreaIdentifier = defaultPortAreaIdentifier,
    berthName: String = defaultBerthName,
    berthOwnerId: String? = defaultBerthId,
    visitIndex: Int = 1,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): HamisAddPortcallVisitEvent {
    return HamisAddPortcallVisitEvent(
        _id = _id,
        ship = ship,
        portcallId = ucrn,
        port = port,
        berthName = berthName,
        berthOwnerId = berthOwnerId,
        visitIndex = visitIndex,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createHamisUpdatePortcallVisitEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ship: GeneralShipIdentifier = defaultGeneralShipIdentifier,
    ucrn: String = defaultUcrn,
    port: AreaIdentifier = defaultPortAreaIdentifier,
    berthName: String = defaultBerthName,
    berthOwnerId: String? = defaultBerthAreaIdentifier.id,
    previousBerthName: String = defaultOtherBerthName,
    previousBerthOwnerId: String? = defaultOtherBerthId,
    visitIndex: Int = 1,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): HamisUpdatePortcallVisitEvent {
    return HamisUpdatePortcallVisitEvent(
        _id = _id,
        ship = ship,
        portcallId = ucrn,
        port = port,
        berthName = berthName,
        berthOwnerId = berthOwnerId,
        previousBerthName = previousBerthName,
        previousBerthOwnerId = previousBerthOwnerId,
        visitIndex = visitIndex,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createHamisCancelPortcallVisitEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ship: GeneralShipIdentifier = defaultGeneralShipIdentifier,
    ucrn: String = defaultUcrn,
    port: AreaIdentifier = defaultPortAreaIdentifier,
    berthName: String = defaultBerthName,
    berthOwnerId: String? = defaultBerthAreaIdentifier.id,
    visitIndex: Int = 1,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): HamisCancelPortcallVisitEvent {
    return HamisCancelPortcallVisitEvent(
        _id = _id,
        ship = ship,
        portcallId = ucrn,
        port = port,
        berthName = berthName,
        berthOwnerId = berthOwnerId,
        visitIndex = visitIndex,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createHamisAgentOrderEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ship: GeneralShipIdentifier = defaultGeneralShipIdentifier,
    ucrn: String = defaultUcrn,
    port: AreaIdentifier = defaultPortAreaIdentifier,
    order: String = defaultOrder,
    vesselAgent: String? = defaultVesselAgent,
    movementId: String? = defaultMovementId,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): HamisAgentOrderEvent {
    return HamisAgentOrderEvent(
        _id = _id,
        ship = ship,
        portcallId = ucrn,
        port = port,
        order = order,
        vesselAgent = vesselAgent,
        movementId = movementId,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createHamisAgentReportsTugsEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ship: GeneralShipIdentifier = defaultGeneralShipIdentifier,
    serviceShip: String = "SERVICE_SHIP",
    ucrn: String = defaultUcrn,
    port: AreaIdentifier = defaultPortAreaIdentifier,
    berth: AreaIdentifier = defaultBerthAreaIdentifier,
    reportedTime: Instant = defaultTime,
    movementId: String? = defaultMovementId,
    externalVisitId: String? = null,
    vesselAgent: String? = defaultVesselAgent,
    towingCompany: String? = null,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): HamisAgentReportsTugsEvent {
    return HamisAgentReportsTugsEvent(
        _id = _id,
        ship = ship,
        serviceShip = serviceShip,
        portcallId = ucrn,
        port = port,
        berth = berth,
        reportedTime = reportedTime,
        movementId = movementId,
        externalVisitId = externalVisitId,
        vesselAgent = vesselAgent,
        towingCompany = towingCompany,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createHamisEtaCancelEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ship: GeneralShipIdentifier = defaultGeneralShipIdentifier,
    ucrn: String = defaultUcrn,
    port: AreaIdentifier = defaultPortAreaIdentifier,
    movementId: String = defaultMovementId,
    vesselAgent: String? = defaultVesselAgent,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): HamisEtaCancelEvent {
    return HamisEtaCancelEvent(
        _id = _id,
        ship = ship,
        portcallId = ucrn,
        port = port,
        movementId = movementId,
        vesselAgent = vesselAgent,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createHamisEtaRequestEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ship: GeneralShipIdentifier = defaultGeneralShipIdentifier,
    ucrn: String = defaultUcrn,
    port: AreaIdentifier = defaultPortAreaIdentifier,
    movementId: String = defaultMovementId,
    vesselAgent: String? = defaultVesselAgent,
    pilotStation: String? = defaultPilotStation,
    berth: AreaIdentifier = defaultBerthAreaIdentifier,
    berthOwnerId: String? = defaultBerthAreaIdentifier.id,
    externalVisitId: String? = defaultExternalId,
    predictedTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): HamisEtaRequestEvent {
    return HamisEtaRequestEvent(
        _id = _id,
        ship = ship,
        portcallId = ucrn,
        port = port,
        movementId = movementId,
        vesselAgent = vesselAgent,
        pilotStation = pilotStation,
        berth = berth,
        berthOwnerId = berthOwnerId,
        externalVisitId = externalVisitId,
        predictedTime = predictedTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createHamisEtdRequestEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ship: GeneralShipIdentifier = defaultGeneralShipIdentifier,
    ucrn: String = defaultUcrn,
    port: AreaIdentifier = defaultPortAreaIdentifier,
    movementId: String = defaultMovementId,
    vesselAgent: String? = defaultVesselAgent,
    pilotStation: String? = defaultPilotStation,
    berth: AreaIdentifier = defaultBerthAreaIdentifier,
    berthOwnerId: String? = defaultBerthAreaIdentifier.id,
    externalVisitId: String? = defaultExternalId,
    predictedTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): HamisEtdRequestEvent {
    return HamisEtdRequestEvent(
        _id = _id,
        ship = ship,
        portcallId = ucrn,
        port = port,
        movementId = movementId,
        vesselAgent = vesselAgent,
        pilotStation = pilotStation,
        berth = berth,
        berthOwnerId = berthOwnerId,
        externalVisitId = externalVisitId,
        predictedTime = predictedTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createHamisNauticalOrderEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ship: GeneralShipIdentifier = defaultGeneralShipIdentifier,
    ucrn: String = defaultUcrn,
    port: AreaIdentifier = defaultPortAreaIdentifier,
    reporter: String = defaultReporter,
    movementId: String = defaultMovementId,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): HamisNauticalOrderEvent {
    return HamisNauticalOrderEvent(
        _id = _id,
        ship = ship,
        portcallId = ucrn,
        port = port,
        reporter = reporter,
        movementId = movementId,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createHamisPilotBoardingEtaEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ship: GeneralShipIdentifier = defaultGeneralShipIdentifier,
    ucrn: String = defaultUcrn,
    port: AreaIdentifier = defaultPortAreaIdentifier,
    movementId: String = defaultMovementId,
    vesselAgent: String? = defaultVesselAgent,
    pilotStation: String = defaultPilotStation,
    source: String = defaultSource,
    predictedTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): HamisPilotBoardingEtaEvent {
    return HamisPilotBoardingEtaEvent(
        _id = _id,
        ship = ship,
        portcallId = ucrn,
        port = port,
        movementId = movementId,
        vesselAgent = vesselAgent,
        pilotStation = pilotStation,
        source = source,
        predictedTime = predictedTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createHamisPilotOnBoardStartEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ship: GeneralShipIdentifier = defaultGeneralShipIdentifier,
    ucrn: String = defaultUcrn,
    port: AreaIdentifier = defaultPortAreaIdentifier,
    movementId: String = defaultMovementId,
    vesselAgent: String? = defaultVesselAgent,
    pilotReason: HamisPilotOnBoardEvent.MovementType = HamisPilotOnBoardEvent.MovementType.DEPARTURE,
    fromBerth: String? = defaultBerthName,
    toBerth: String? = defaultOtherBerthName,
    actualTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): HamisPilotOnBoardStartEvent {
    return HamisPilotOnBoardStartEvent(
        _id = _id,
        ship = ship,
        portcallId = ucrn,
        port = port,
        movementId = movementId,
        vesselAgent = vesselAgent,
        pilotReason = pilotReason,
        fromBerth = fromBerth,
        toBerth = toBerth,
        actualTime = actualTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createHamisPilotOnBoardEndEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    startEventId: EventIdentifier = defaultStartEventIdentifier,
    ship: GeneralShipIdentifier = defaultGeneralShipIdentifier,
    ucrn: String = defaultUcrn,
    port: AreaIdentifier = defaultPortAreaIdentifier,
    movementId: String = defaultMovementId,
    vesselAgent: String? = defaultVesselAgent,
    pilotReason: HamisPilotOnBoardEvent.MovementType = HamisPilotOnBoardEvent.MovementType.DEPARTURE,
    fromBerth: String? = defaultBerthName,
    toBerth: String? = defaultOtherBerthName,
    actualTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): HamisPilotOnBoardEndEvent {
    return HamisPilotOnBoardEndEvent(
        _id = _id,
        startEventId = startEventId,
        ship = ship,
        portcallId = ucrn,
        port = port,
        movementId = movementId,
        vesselAgent = vesselAgent,
        pilotReason = pilotReason,
        fromBerth = fromBerth,
        toBerth = toBerth,
        actualTime = actualTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createHamisVisitCancellationEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ship: GeneralShipIdentifier = defaultGeneralShipIdentifier,
    shipName: String? = defaultShipName,
    ucrn: String = defaultUcrn,
    port: AreaIdentifier = defaultPortAreaIdentifier,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): HamisVisitCancellationEvent {
    return HamisVisitCancellationEvent(
        _id = _id,
        ship = ship,
        shipName = shipName,
        portcallId = ucrn,
        port = port,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createHamisVisitDeclarationEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ship: GeneralShipIdentifier = defaultGeneralShipIdentifier,
    shipName: String? = defaultShipName,
    ucrn: String = defaultUcrn,
    port: AreaIdentifier = defaultPortAreaIdentifier,
    previousPort: AreaIdentifier = defaultOtherPortAreaIdentifier,
    vesselAgent: String? = defaultVesselAgent,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): HamisVisitDeclarationEvent {
    return HamisVisitDeclarationEvent(
        _id = _id,
        ship = ship,
        shipName = shipName,
        portcallId = ucrn,
        port = port,
        previousPort = previousPort,
        vesselAgent = vesselAgent,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createPortcallPlusAtaEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ucrn: String = defaultUcrn,
    ship: GeneralShipIdentifier = defaultGeneralShipIdentifier,
    area: AreaIdentifier = defaultBerthAreaIdentifier,
    port: AreaIdentifier = defaultPortAreaIdentifier,
    actualTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): PortcallPlusAtaEvent {
    return PortcallPlusAtaEvent(
        _id = _id,
        portcallId = ucrn,
        ship = ship,
        area = area,
        port = port,
        actualTime = actualTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createPortcallPlusAtdEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ucrn: String = defaultUcrn,
    ship: GeneralShipIdentifier = defaultGeneralShipIdentifier,
    area: AreaIdentifier = defaultBerthAreaIdentifier,
    port: AreaIdentifier = defaultPortAreaIdentifier,
    actualTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): PortcallPlusAtdEvent {
    return PortcallPlusAtdEvent(
        _id = _id,
        portcallId = ucrn,
        ship = ship,
        area = area,
        port = port,
        actualTime = actualTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createPortcallPlusEtaEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ucrn: String = defaultUcrn,
    ship: GeneralShipIdentifier = defaultGeneralShipIdentifier,
    area: AreaIdentifier = defaultBerthAreaIdentifier,
    port: AreaIdentifier = defaultPortAreaIdentifier,
    source: String? = defaultSource,
    berth: String? = defaultBerthName,
    berthId: String? = defaultBerthId,
    vesselAgent: String? = defaultVesselAgent,
    predictedTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null,
    nomination: Boolean? = null
): PortcallPlusEtaEvent {
    return PortcallPlusEtaEvent(
        _id = _id,
        portcallId = ucrn,
        ship = ship,
        area = area,
        port = port,
        source = source,
        berth = berth,
        berthId = berthId,
        vesselAgent = vesselAgent,
        predictedTime = predictedTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated,
        nomination = nomination
    )
}

fun createPortcallPlusEtaBerthEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ucrn: String = defaultUcrn,
    ship: GeneralShipIdentifier = defaultGeneralShipIdentifier,
    area: AreaIdentifier = defaultBerthAreaIdentifier,
    port: AreaIdentifier = defaultPortAreaIdentifier,
    source: String? = defaultSource,
    berth: String? = defaultBerthName,
    berthId: String? = defaultBerthId,
    vesselAgent: String? = defaultVesselAgent,
    distanceInNm: Long = 0L,
    portcallBerthId: String? = defaultBerthId,
    predictedTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): PortcallPlusEtaBerthEvent {
    return PortcallPlusEtaBerthEvent(
        _id = _id,
        portcallId = ucrn,
        ship = ship,
        area = area,
        port = port,
        source = source,
        berth = berth,
        berthId = berthId,
        vesselAgent = vesselAgent,
        distanceInNm = distanceInNm,
        portcallBerthId = portcallBerthId,
        predictedTime = predictedTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createPortcallPlusEtdEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ucrn: String = defaultUcrn,
    ship: GeneralShipIdentifier = defaultGeneralShipIdentifier,
    area: AreaIdentifier = defaultBerthAreaIdentifier,
    port: AreaIdentifier = defaultPortAreaIdentifier,
    source: String? = defaultSource,
    berth: String? = defaultBerthName,
    berthId: String? = defaultBerthId,
    vesselAgent: String? = defaultVesselAgent,
    predictedTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null,
    nomination: Boolean? = null
): PortcallPlusEtdEvent {
    return PortcallPlusEtdEvent(
        _id = _id,
        portcallId = ucrn,
        ship = ship,
        area = area,
        port = port,
        source = source,
        berth = berth,
        berthId = berthId,
        vesselAgent = vesselAgent,
        predictedTime = predictedTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated,
        nomination = nomination
    )
}

fun createPortcallPlusAgentChangedEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ucrn: String = defaultUcrn,
    ship: GeneralShipIdentifier = defaultGeneralShipIdentifier,
    port: AreaIdentifier = defaultPortAreaIdentifier,
    vesselAgent: String = defaultVesselAgent,
    source: String? = defaultSource,
    actualtime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): PortcallPlusAgentChangedEvent {
    return PortcallPlusAgentChangedEvent(
        _id = _id,
        portcallId = ucrn,
        ship = ship,
        port = port,
        vesselAgent = vesselAgent,
        source = source,
        actualTime = actualtime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createPortcallPlusShipChangedEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ucrn: String = defaultUcrn,
    ship: GeneralShipIdentifier = defaultGeneralShipIdentifier,
    newShip: GeneralShipIdentifier = defaultOtherGeneralShipIdentifier,
    port: AreaIdentifier = defaultPortAreaIdentifier,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): PortcallPlusShipChangedEvent {
    return PortcallPlusShipChangedEvent(
        _id = _id,
        portcallId = ucrn,
        ship = ship,
        newShip = newShip,
        port = port,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createPortcallPlusVisitsUpdateEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ucrn: String = defaultUcrn,
    ship: GeneralShipIdentifier = defaultGeneralShipIdentifier,
    port: AreaIdentifier = defaultPortAreaIdentifier,
    visits: List<PortcallPlusPortcallVisit> = emptyList(),
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): PortcallPlusVisitsUpdateEvent {
    return PortcallPlusVisitsUpdateEvent(
        _id = _id,
        portcallId = ucrn,
        ship = ship,
        port = port,
        visits = visits,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}

fun createPortcallPlusPortcallFinishEvent(
    _id: EventIdentifier = defaultEventIdentifier,
    ucrn: String = defaultUcrn,
    ship: GeneralShipIdentifier = defaultGeneralShipIdentifier,
    port: AreaIdentifier = defaultPortAreaIdentifier,
    actualTime: Instant = defaultTime,
    createdTime: Instant = defaultTime,
    deleted: Boolean? = false,
    regenerated: Boolean? = null
): PortcallPlusPortcallFinishEvent {
    return PortcallPlusPortcallFinishEvent(
        _id = _id,
        portcallId = ucrn,
        ship = ship,
        port = port,
        actualTime = actualTime,
        createdTime = createdTime,
        deleted = deleted,
        regenerated = regenerated
    )
}
