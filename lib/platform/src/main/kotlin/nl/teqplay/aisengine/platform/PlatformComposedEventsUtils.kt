package nl.teqplay.aisengine.platform

import nl.teqplay.platform.model.event.AtaEvent
import nl.teqplay.platform.model.event.AtdEvent
import nl.teqplay.platform.model.event.ComposedEventView
import nl.teqplay.platform.model.event.ComposedEventView.ComposedEventStatus
import nl.teqplay.platform.model.event.ComposedTeqplayEvent
import nl.teqplay.platform.model.event.EtaEvent
import nl.teqplay.platform.model.event.EtdEvent
import nl.teqplay.platform.model.event.TeqplayEvent

/**
 * Loop over all events and compose them together into [ComposedTeqplayEvent] when possible.
 *
 * @return A sequence of [ComposedTeqplayEvent] of events that could be linked to each other, and [TeqplayEvent] that couldn't be converted into composed events.
 */
fun composeEvents(singleEvents: Sequence<TeqplayEvent>): Sequence<TeqplayEvent> {
    val startEventsByUuid = mutableMapOf<String, TeqplayEvent>()

    val composedEvents = singleEvents.mapNotNull { event ->
        if (event.isStartEvent) {
            startEventsByUuid[event.uuid] = event
            return@mapNotNull null
        }

        if (event.isEndEvent) {
            val startEvent = startEventsByUuid[event.uuid]

            if (startEvent != null) {
                // We found a matching start event, compose it together and remove it from the map of known events
                startEventsByUuid.remove(startEvent.uuid)
                ComposedTeqplayEvent(startEvent, event)
            } else {
                val type = removeStartStopComposedExtension(event.type)

                // We couldn't find a related start event, try using type, shipMmsi, otherMmsi and smaller time
                val relatedStartEvent = startEventsByUuid.values.firstOrNull { relatedEvent ->
                    type == removeStartStopComposedExtension(relatedEvent.type) &&
                        event.shipMmsi == relatedEvent.shipMmsi &&
                        event.otherMmsi == relatedEvent.otherMmsi &&
                        event.datetime > relatedEvent.datetime
                }

                if (relatedStartEvent != null) {
                    // We found a related event, meaning we can compose them together
                    startEventsByUuid.remove(relatedStartEvent.uuid)
                    ComposedTeqplayEvent(relatedStartEvent, event)
                } else {
                    // Could not compose the end event to a related start event
                    ComposedTeqplayEvent(null, event)
                }
            }
        } else {
            // Event isn't a start or end event, shouldn't be a composed event
            event
        }
    }

    val remainingStartEvents = startEventsByUuid.values.map { startEvent -> ComposedTeqplayEvent(startEvent, null) }
    return composedEvents + remainingStartEvents
}

private fun removeStartStopComposedExtension(event: String): String {
    return event.replace(".start", "")
        .replace(".end", "")
        .replace(".composed", "")
}

/**
 * Convert all [TeqplayEvent] to a [ComposedEventView]. When the event is a [ComposedTeqplayEvent],
 *  determine the [ComposedEventStatus] and convert it to the composed view. Otherwise, still make a composed view without an end event id
 *
 * @return The mutated Sequence containing the [ComposedEventView]s.
 */
fun Sequence<TeqplayEvent>.getAllAsComposedEventView(): Sequence<ComposedEventView> {
    return this.map { event ->
        if (event is ComposedTeqplayEvent) {
            val status = if (event.endTime != null) {
                ComposedEventStatus.FINISHED
            } else {
                ComposedEventStatus.STARTED
            }

            val title = determineTitle(event)
            ComposedEventView(
                event.eventType,
                event.startTime,
                event.endTime,
                event.shipMmsi,
                event.otherMmsi,
                event.startEvent?.uuid,
                event.endEvent?.uuid,
                title,
                status,
                null
            )
        } else {

            // if not composed, make composed
            val status: ComposedEventStatus
            var eventTime: Long? = null

            // for ETA or ETD, capture actual event reported time
            if (event.eventType == TeqplayEvent.EventType.ETA || event.eventType == TeqplayEvent.EventType.ETD) {
                status = ComposedEventStatus.PLANNED

                if (event is EtaEvent) {
                    eventTime = event.datetimePlan
                } else if (event is EtdEvent) {
                    eventTime = event.datetimePlan
                }
            } else {
                status = ComposedEventStatus.FINISHED

                if (event.eventType == TeqplayEvent.EventType.ATA) {
                    eventTime = (event as AtaEvent).realizedTime
                } else if (event.eventType == TeqplayEvent.EventType.ATD) {
                    eventTime = (event as AtdEvent).realizedTime
                }
            }

            ComposedEventView(
                event.eventType,
                event.datetime,
                event.datetime,
                event.shipMmsi,
                event.otherMmsi,
                event.uuid,
                null,
                event.description,
                status,
                eventTime
            )
        }
    }
}

/**
 * Helper method to determine the title of a composite event
 *
 * @param composed the originating composite event
 * @return a string reflecting the title
 */
fun determineTitle(composed: ComposedTeqplayEvent): String {
    return if (composed.type.contains(TeqplayEvent.AREA)) {
        "${composed.shipMmsi} is inside ${composed.eventType.toString().lowercase()}"
    } else if (composed.type.contains(TeqplayEvent.MOORED)) {
        "${composed.shipMmsi} is moored"
    } else if (composed.type.contains(TeqplayEvent.MOVEMENT)) {
        "${composed.shipMmsi} is moving"
    } else if (composed.type.contains(TeqplayEvent.TEQPERIMENT)) {
        "Unknown encounter ${composed.otherMmsi} is alongside ${composed.shipMmsi}"
    } else {
        "${composed.eventType.toString().lowercase()} ${composed.otherMmsi} is alongside ${composed.shipMmsi}"
    }
}
