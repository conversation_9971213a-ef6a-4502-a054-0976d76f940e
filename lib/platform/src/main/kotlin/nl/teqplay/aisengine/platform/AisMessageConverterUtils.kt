package nl.teqplay.aisengine.platform

import nl.teqplay.aisengine.aisstream.model.AisCommonMessage
import nl.teqplay.aisengine.aisstream.model.AisMessage
import nl.teqplay.aisengine.aisstream.model.TransponderPosition
import nl.teqplay.platform.model.ShipInfo
import nl.teqplay.platform.model.TransponderPositionOnBoard
import nl.teqplay.skeleton.model.Location
import nl.teqplay.platform.model.Location as LegacyLocation

/**
 * Extension functions which can convert the new [AisCommonMessage] model to the [ShipInfo] model of platform.
 * This does not set any of the derived fields such as trueDestination and should be handled elsewhere.
 */
fun AisCommonMessage.toLegacyShipInfo(isNewUpdate: Boolean): ShipInfo {
    val legacyShipInfo = ShipInfo(this.mmsi.toString())
    legacyShipInfo.timeLastUpdate = this.messageTime.toEpochMilli()
    legacyShipInfo.visible = true
    legacyShipInfo.source = this.sources.joinToString().ifBlank { null }
    legacyShipInfo.imoNumber = this.imo?.toString()
    legacyShipInfo.eni = this.eni?.toString()
    legacyShipInfo.name = this.name
    legacyShipInfo.callSign = this.callSign
    legacyShipInfo.location = this.location.toLegacyLocation()
    legacyShipInfo.destination = this.destination
    legacyShipInfo.eta = this.eta?.toEpochMilli()
    legacyShipInfo.speedOverGround = this.speedOverGround
    legacyShipInfo.courseOverGround = this.courseOverGround
    legacyShipInfo.rateOfTurn = this.rateOfTurn
    legacyShipInfo.trueHeading = this.heading ?: 511 // default to 511=not available
    legacyShipInfo.maxDraught = this.draught
    legacyShipInfo.positionAccuracy = this.positionAccuracy.toLegacyPositionAccuracy()
    legacyShipInfo.positionOfTransponder = this.transponderPosition?.toLegacyTransponderPosition()
    legacyShipInfo.positionSensorType = this.positionSensorType?.toLegacyPositionSensorType()
    legacyShipInfo.aisVersion = this.aisVersion?.toLegacyAisVersion()
    legacyShipInfo.shipType = this.shipType?.toLegacyShipType()
    legacyShipInfo.status = this.status?.toLegacyStatus()
    legacyShipInfo.specialManeuverIndicator = this.specialManeuverStatus.toLegacySpecialManeuverStatus()
    legacyShipInfo.dataTerminalEquipped = this.usingDataTerminal
    legacyShipInfo.streamingIsNewUpdate = isNewUpdate

    return legacyShipInfo
}

private fun Location.toLegacyLocation(): LegacyLocation {
    return LegacyLocation(
        this.lat,
        this.lon
    )
}

private fun AisMessage.PositionAccuracy?.toLegacyPositionAccuracy(): ShipInfo.PositionAccuracy {
    return when (this) {
        AisMessage.PositionAccuracy.HIGH -> ShipInfo.PositionAccuracy.HIGH
        AisMessage.PositionAccuracy.LOW -> ShipInfo.PositionAccuracy.LOW
        else -> ShipInfo.PositionAccuracy.DEFAULT
    }
}

private fun TransponderPosition.toLegacyTransponderPosition(): TransponderPositionOnBoard {
    return TransponderPositionOnBoard(
        this.distanceToBow,
        this.distanceToStern,
        this.distanceToPort,
        this.distanceToStarboard
    )
}

private fun AisMessage.PositionSensorType.toLegacyPositionSensorType(): ShipInfo.PositionSensorType {
    return when (this) {
        AisMessage.PositionSensorType.GPS -> ShipInfo.PositionSensorType.GPS
        AisMessage.PositionSensorType.GLONASS -> ShipInfo.PositionSensorType.GLONASS
        AisMessage.PositionSensorType.COMBINED_GPS_GLONASS -> ShipInfo.PositionSensorType.COMBINED_GPS_GLONASS
        AisMessage.PositionSensorType.LORAN_C -> ShipInfo.PositionSensorType.LORAN_C
        AisMessage.PositionSensorType.CHAYKA -> ShipInfo.PositionSensorType.CHAYKA
        AisMessage.PositionSensorType.INTEGRATED_NAVIGATION_SYSTEM -> ShipInfo.PositionSensorType.INTEGRATED_NAVIGATION_SYSTEM
        AisMessage.PositionSensorType.SURVEYED -> ShipInfo.PositionSensorType.SURVEYED
        AisMessage.PositionSensorType.GALILEO -> ShipInfo.PositionSensorType.GALILEO
        AisMessage.PositionSensorType.INTERNAL_GNSS -> ShipInfo.PositionSensorType.INTERNAL_GNSS
    }
}

private fun AisMessage.AisVersionIndicator.toLegacyAisVersion(): ShipInfo.AisVersionIndicator {
    return when (this) {
        AisMessage.AisVersionIndicator.ITU_R_M1371_1 -> ShipInfo.AisVersionIndicator.ITU_R_M1371_1
        AisMessage.AisVersionIndicator.ITU_R_M1371_3 -> ShipInfo.AisVersionIndicator.ITU_R_M1371_3
        AisMessage.AisVersionIndicator.ITU_R_M1371_5 -> ShipInfo.AisVersionIndicator.ITU_R_M1371_5
        AisMessage.AisVersionIndicator.FUTURE -> ShipInfo.AisVersionIndicator.FUTURE
    }
}

private fun AisMessage.ShipType.toLegacyShipType(): ShipInfo.ShipType {
    return when (this) {
        AisMessage.ShipType.PILOT_VESSEL -> ShipInfo.ShipType.PILOT_VESSEL
        AisMessage.ShipType.SEARCH_AND_RESCUE_VESSEL -> ShipInfo.ShipType.SEARCH_AND_RESCUE_VESSEL
        AisMessage.ShipType.TUG -> ShipInfo.ShipType.TUG
        AisMessage.ShipType.PORT_TENDER -> ShipInfo.ShipType.PORT_TENDER
        AisMessage.ShipType.VESSEL_WITH_ANTI_POLLUTION_FACILITIES -> ShipInfo.ShipType.VESSEL_WITH_ANTI_POLLUTION_FACILITIES
        AisMessage.ShipType.LAW_ENFORCEMENT_VESSEL -> ShipInfo.ShipType.LAW_ENFORCEMENT_VESSEL
        AisMessage.ShipType.MEDICAL_TRANSPORT -> ShipInfo.ShipType.MEDICAL_TRANSPORT
        AisMessage.ShipType.NOT_PARTIES_TO_AN_ARMED_CONFLICT -> ShipInfo.ShipType.NOT_PARTIES_TO_AN_ARMED_CONFLICT
        AisMessage.ShipType.PASSENGER -> ShipInfo.ShipType.PASSENGER
        AisMessage.ShipType.CARGO -> ShipInfo.ShipType.CARGO
        AisMessage.ShipType.TANKER -> ShipInfo.ShipType.TANKER
        AisMessage.ShipType.HIGHSPEED -> ShipInfo.ShipType.HIGHSPEED
        AisMessage.ShipType.WING_IN_GROUND -> ShipInfo.ShipType.WING_IN_GROUND
        AisMessage.ShipType.OTHER_TYPE -> ShipInfo.ShipType.OTHER_TYPE
        AisMessage.ShipType.PASSENGER_HAZCAT_A -> ShipInfo.ShipType.PASSENGER_HAZCAT_A
        AisMessage.ShipType.PASSENGER_HAZCAT_B -> ShipInfo.ShipType.PASSENGER_HAZCAT_B
        AisMessage.ShipType.PASSENGER_HAZCAT_C -> ShipInfo.ShipType.PASSENGER_HAZCAT_C
        AisMessage.ShipType.PASSENGER_HAZCAT_D -> ShipInfo.ShipType.PASSENGER_HAZCAT_D
        AisMessage.ShipType.HIGHSPEED_HAZCAT_A -> ShipInfo.ShipType.HIGHSPEED_HAZCAT_A
        AisMessage.ShipType.HIGHSPEED_HAZCAT_B -> ShipInfo.ShipType.HIGHSPEED_HAZCAT_B
        AisMessage.ShipType.HIGHSPEED_HAZCAT_C -> ShipInfo.ShipType.HIGHSPEED_HAZCAT_C
        AisMessage.ShipType.HIGHSPEED_HAZCAT_D -> ShipInfo.ShipType.HIGHSPEED_HAZCAT_D
        AisMessage.ShipType.TANKER_HAZCAT_A -> ShipInfo.ShipType.TANKER_HAZCAT_A
        AisMessage.ShipType.TANKER_HAZCAT_B -> ShipInfo.ShipType.TANKER_HAZCAT_B
        AisMessage.ShipType.TANKER_HAZCAT_C -> ShipInfo.ShipType.TANKER_HAZCAT_C
        AisMessage.ShipType.TANKER_HAZCAT_D -> ShipInfo.ShipType.TANKER_HAZCAT_D
        AisMessage.ShipType.CARGO_HAZCAT_A -> ShipInfo.ShipType.CARGO_HAZCAT_A
        AisMessage.ShipType.CARGO_HAZCAT_B -> ShipInfo.ShipType.CARGO_HAZCAT_B
        AisMessage.ShipType.CARGO_HAZCAT_C -> ShipInfo.ShipType.CARGO_HAZCAT_C
        AisMessage.ShipType.CARGO_HAZCAT_D -> ShipInfo.ShipType.CARGO_HAZCAT_D
        AisMessage.ShipType.OTHER_TYPE_HAZCAT_A -> ShipInfo.ShipType.OTHER_TYPE_HAZCAT_A
        AisMessage.ShipType.OTHER_TYPE_HAZCAT_B -> ShipInfo.ShipType.OTHER_TYPE_HAZCAT_B
        AisMessage.ShipType.OTHER_TYPE_HAZCAT_C -> ShipInfo.ShipType.OTHER_TYPE_HAZCAT_C
        AisMessage.ShipType.OTHER_TYPE_HAZCAT_D -> ShipInfo.ShipType.OTHER_TYPE_HAZCAT_D
        AisMessage.ShipType.WING_IN_GROUND_HAZCAT_A -> ShipInfo.ShipType.WING_IN_GROUND_HAZCAT_A
        AisMessage.ShipType.WING_IN_GROUND_HAZCAT_B -> ShipInfo.ShipType.WING_IN_GROUND_HAZCAT_B
        AisMessage.ShipType.WING_IN_GROUND_HAZCAT_C -> ShipInfo.ShipType.WING_IN_GROUND_HAZCAT_C
        AisMessage.ShipType.WING_IN_GROUND_HAZCAT_D -> ShipInfo.ShipType.WING_IN_GROUND_HAZCAT_D
        AisMessage.ShipType.FISHING -> ShipInfo.ShipType.FISHING
        AisMessage.ShipType.TOWING -> ShipInfo.ShipType.TOWING
        AisMessage.ShipType.TOWING_BIG -> ShipInfo.ShipType.TOWING_BIG
        AisMessage.ShipType.DREDGING_UNDERWATER_OPS -> ShipInfo.ShipType.DREDGING_UNDERWATER_OPS
        AisMessage.ShipType.DIVING_OPS -> ShipInfo.ShipType.DIVING_OPS
        AisMessage.ShipType.MILITARY_OPS -> ShipInfo.ShipType.MILITARY_OPS
        AisMessage.ShipType.SAILING -> ShipInfo.ShipType.SAILING
        AisMessage.ShipType.PLEASURE_CRAFT -> ShipInfo.ShipType.PLEASURE_CRAFT
        AisMessage.ShipType.NOT_AVAILABLE -> ShipInfo.ShipType.NOT_AVAILABLE
        AisMessage.ShipType.SPARE -> ShipInfo.ShipType.SPARE
        AisMessage.ShipType.UNDEFINED -> ShipInfo.ShipType.UNDEFINED
        AisMessage.ShipType.BASESTATION -> ShipInfo.ShipType.BASESTATION
        AisMessage.ShipType.MOTOR_YACHT -> ShipInfo.ShipType.MOTOR_YACHT
        AisMessage.ShipType.SPEEDBOAT -> ShipInfo.ShipType.SPEEDBOAT
        AisMessage.ShipType.SAILING_WITH_MOTOR -> ShipInfo.ShipType.SAILING_WITH_MOTOR
        AisMessage.ShipType.SPORT_FISHERMEN_VESSEL -> ShipInfo.ShipType.SPORT_FISHERMEN_VESSEL
        AisMessage.ShipType.SAILING_MOTOR_BIG -> ShipInfo.ShipType.SAILING_MOTOR_BIG
        AisMessage.ShipType.OTHER_RECREATIONAL_VESSEL -> ShipInfo.ShipType.OTHER_RECREATIONAL_VESSEL
    }
}

fun AisMessage.ShipStatus.toLegacyStatus(): ShipInfo.ShipStatus {
    return when (this) {
        AisMessage.ShipStatus.UNDER_WAY_USING_ENGINE -> ShipInfo.ShipStatus.UNDER_WAY_USING_ENGINE
        AisMessage.ShipStatus.AT_ANCHOR -> ShipInfo.ShipStatus.AT_ANCHOR
        AisMessage.ShipStatus.NOT_UNDER_COMMAND -> ShipInfo.ShipStatus.NOT_UNDER_COMMAND
        AisMessage.ShipStatus.RESTRICTED_MANEUVERABILITY -> ShipInfo.ShipStatus.RESTRICTED_MANEUVERABILITY
        AisMessage.ShipStatus.CONSTRAINED_BY_HER_DRAUGHT -> ShipInfo.ShipStatus.CONSTRAINED_BY_HER_DRAUGHT
        AisMessage.ShipStatus.MOORED -> ShipInfo.ShipStatus.MOORED
        AisMessage.ShipStatus.AGROUND -> ShipInfo.ShipStatus.AGROUND
        AisMessage.ShipStatus.ENGAGED_IN_FISHING -> ShipInfo.ShipStatus.ENGAGED_IN_FISHING
        AisMessage.ShipStatus.UNDER_WAY_SAILING -> ShipInfo.ShipStatus.UNDER_WAY_SAILING
        AisMessage.ShipStatus.RESERVED_FOR_DG -> ShipInfo.ShipStatus.RESERVED_FOR_DG
        AisMessage.ShipStatus.RESERVED_FOR_DG2 -> ShipInfo.ShipStatus.RESERVED_FOR_DG2
        AisMessage.ShipStatus.TOWING_ASTERN -> ShipInfo.ShipStatus.TOWING_ASTERN
        AisMessage.ShipStatus.TOWING_ALONGSIDE -> ShipInfo.ShipStatus.TOWING_ALONGSIDE
        AisMessage.ShipStatus.RESERVED_FUTURE -> ShipInfo.ShipStatus.RESERVED_FUTURE
        AisMessage.ShipStatus.ASI_SART -> ShipInfo.ShipStatus.ASI_SART
        AisMessage.ShipStatus.UNDEFINED -> ShipInfo.ShipStatus.UNDEFINED
    }
}

private fun AisMessage.SpecialManeuverStatus?.toLegacySpecialManeuverStatus(): ShipInfo.SpecialManeuvreStatus {
    return when (this) {
        AisMessage.SpecialManeuverStatus.NOT_ENGAGED_IN_SPECIAL_MANEUVER -> ShipInfo.SpecialManeuvreStatus.NOT_ENGAGED_IN_SPECIAL_MANEUVER
        AisMessage.SpecialManeuverStatus.ENGAGED_IN_SPECIAL_MANEUVER -> ShipInfo.SpecialManeuvreStatus.ENGAGED_IN_SPECIAL_MANEUVER
        null -> ShipInfo.SpecialManeuvreStatus.NOT_AVAILABLE
    }
}
