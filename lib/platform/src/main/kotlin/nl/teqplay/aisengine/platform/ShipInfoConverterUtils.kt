package nl.teqplay.aisengine.platform

import nl.teqplay.aisengine.aisstream.model.AisMessage
import nl.teqplay.aisengine.aisstream.model.TransponderPosition
import nl.teqplay.platform.model.ShipInfo
import nl.teqplay.platform.model.TransponderPositionOnBoard
import java.time.Instant

fun ShipInfo.toAisMessage(): AisMessage? {
    val mmsi = this.mmsi?.toIntOrNull() ?: return null
    val location = this.location ?: return null

    val messageTime = this.timeLastUpdate
        ?.let { Instant.ofEpochMilli(it) }
        ?: return null

    return AisMessage(
        mmsi = mmsi,
        messageTime = messageTime,
        sources = setOfNotNull(this.source),
        imo = this.imoNumber?.toIntOrNull(),
        eni = this.eni?.toIntOrNull(),
        name = this.name,
        callSign = this.callSign?.ifBlank { null },
        location = location.toLocation(),
        destination = this.destination?.ifBlank { null },
        eta = this.eta?.let { Instant.ofEpochMilli(it) },
        speedOverGround = this.speedOverGround,
        courseOverGround = this.courseOverGround,
        rateOfTurn = this.rateOfTurn,
        heading = if (this.trueHeading != 511) this.trueHeading else null,
        draught = this.maxDraught,
        positionAccuracy = this.positionAccuracy.toPositionAccuracy(),
        transponderPosition = this.positionOfTransponder?.toTransponderPosition(),
        positionSensorType = this.positionSensorType?.toPositionSensorType(),
        aisVersion = this.aisVersion?.toAisVersion(),
        shipType = this.shipType?.toShipType(),
        status = this.status?.toStatus(),
        specialManeuverStatus = this.specialManeuverIndicator.toSpecialManeuverStatus(),
        usingDataTerminal = this.dataTerminalEquipped
    )
}

private fun ShipInfo.PositionAccuracy?.toPositionAccuracy(): AisMessage.PositionAccuracy? {
    return when (this) {
        ShipInfo.PositionAccuracy.HIGH -> AisMessage.PositionAccuracy.HIGH
        ShipInfo.PositionAccuracy.LOW -> AisMessage.PositionAccuracy.LOW
        else -> null
    }
}

fun TransponderPositionOnBoard.toTransponderPosition(): TransponderPosition {
    return TransponderPosition(
        distanceToBow = this.distanceToBow,
        distanceToStern = this.distanceToStern,
        distanceToPort = this.distanceToPort,
        distanceToStarboard = this.distanceToStarboard
    )
}

private fun ShipInfo.PositionSensorType.toPositionSensorType(): AisMessage.PositionSensorType? {
    return when (this) {
        ShipInfo.PositionSensorType.GPS -> AisMessage.PositionSensorType.GPS
        ShipInfo.PositionSensorType.GLONASS -> AisMessage.PositionSensorType.GLONASS
        ShipInfo.PositionSensorType.COMBINED_GPS_GLONASS -> AisMessage.PositionSensorType.COMBINED_GPS_GLONASS
        ShipInfo.PositionSensorType.LORAN_C -> AisMessage.PositionSensorType.LORAN_C
        ShipInfo.PositionSensorType.CHAYKA -> AisMessage.PositionSensorType.CHAYKA
        ShipInfo.PositionSensorType.INTEGRATED_NAVIGATION_SYSTEM -> AisMessage.PositionSensorType.INTEGRATED_NAVIGATION_SYSTEM
        ShipInfo.PositionSensorType.SURVEYED -> AisMessage.PositionSensorType.SURVEYED
        ShipInfo.PositionSensorType.GALILEO -> AisMessage.PositionSensorType.GALILEO
        ShipInfo.PositionSensorType.INTERNAL_GNSS -> AisMessage.PositionSensorType.INTERNAL_GNSS
        ShipInfo.PositionSensorType.UNDEFINED -> null
    }
}

private fun ShipInfo.AisVersionIndicator.toAisVersion(): AisMessage.AisVersionIndicator {
    return when (this) {
        ShipInfo.AisVersionIndicator.ITU_R_M1371_1 -> AisMessage.AisVersionIndicator.ITU_R_M1371_1
        ShipInfo.AisVersionIndicator.ITU_R_M1371_3 -> AisMessage.AisVersionIndicator.ITU_R_M1371_3
        ShipInfo.AisVersionIndicator.ITU_R_M1371_5 -> AisMessage.AisVersionIndicator.ITU_R_M1371_5
        ShipInfo.AisVersionIndicator.FUTURE -> AisMessage.AisVersionIndicator.FUTURE
    }
}

fun ShipInfo.ShipType.toShipType(): AisMessage.ShipType {
    return when (this) {
        ShipInfo.ShipType.PILOT_VESSEL -> AisMessage.ShipType.PILOT_VESSEL
        ShipInfo.ShipType.SEARCH_AND_RESCUE_VESSEL -> AisMessage.ShipType.SEARCH_AND_RESCUE_VESSEL
        ShipInfo.ShipType.TUG -> AisMessage.ShipType.TUG
        ShipInfo.ShipType.PORT_TENDER -> AisMessage.ShipType.PORT_TENDER
        ShipInfo.ShipType.VESSEL_WITH_ANTI_POLLUTION_FACILITIES -> AisMessage.ShipType.VESSEL_WITH_ANTI_POLLUTION_FACILITIES
        ShipInfo.ShipType.LAW_ENFORCEMENT_VESSEL -> AisMessage.ShipType.LAW_ENFORCEMENT_VESSEL
        ShipInfo.ShipType.MEDICAL_TRANSPORT -> AisMessage.ShipType.MEDICAL_TRANSPORT
        ShipInfo.ShipType.NOT_PARTIES_TO_AN_ARMED_CONFLICT -> AisMessage.ShipType.NOT_PARTIES_TO_AN_ARMED_CONFLICT
        ShipInfo.ShipType.PASSENGER -> AisMessage.ShipType.PASSENGER
        ShipInfo.ShipType.CARGO -> AisMessage.ShipType.CARGO
        ShipInfo.ShipType.TANKER -> AisMessage.ShipType.TANKER
        ShipInfo.ShipType.HIGHSPEED -> AisMessage.ShipType.HIGHSPEED
        ShipInfo.ShipType.WING_IN_GROUND -> AisMessage.ShipType.WING_IN_GROUND
        ShipInfo.ShipType.OTHER_TYPE -> AisMessage.ShipType.OTHER_TYPE
        ShipInfo.ShipType.PASSENGER_HAZCAT_A -> AisMessage.ShipType.PASSENGER_HAZCAT_A
        ShipInfo.ShipType.PASSENGER_HAZCAT_B -> AisMessage.ShipType.PASSENGER_HAZCAT_B
        ShipInfo.ShipType.PASSENGER_HAZCAT_C -> AisMessage.ShipType.PASSENGER_HAZCAT_C
        ShipInfo.ShipType.PASSENGER_HAZCAT_D -> AisMessage.ShipType.PASSENGER_HAZCAT_D
        ShipInfo.ShipType.HIGHSPEED_HAZCAT_A -> AisMessage.ShipType.HIGHSPEED_HAZCAT_A
        ShipInfo.ShipType.HIGHSPEED_HAZCAT_B -> AisMessage.ShipType.HIGHSPEED_HAZCAT_B
        ShipInfo.ShipType.HIGHSPEED_HAZCAT_C -> AisMessage.ShipType.HIGHSPEED_HAZCAT_C
        ShipInfo.ShipType.HIGHSPEED_HAZCAT_D -> AisMessage.ShipType.HIGHSPEED_HAZCAT_D
        ShipInfo.ShipType.TANKER_HAZCAT_A -> AisMessage.ShipType.TANKER_HAZCAT_A
        ShipInfo.ShipType.TANKER_HAZCAT_B -> AisMessage.ShipType.TANKER_HAZCAT_B
        ShipInfo.ShipType.TANKER_HAZCAT_C -> AisMessage.ShipType.TANKER_HAZCAT_C
        ShipInfo.ShipType.TANKER_HAZCAT_D -> AisMessage.ShipType.TANKER_HAZCAT_D
        ShipInfo.ShipType.CARGO_HAZCAT_A -> AisMessage.ShipType.CARGO_HAZCAT_A
        ShipInfo.ShipType.CARGO_HAZCAT_B -> AisMessage.ShipType.CARGO_HAZCAT_B
        ShipInfo.ShipType.CARGO_HAZCAT_C -> AisMessage.ShipType.CARGO_HAZCAT_C
        ShipInfo.ShipType.CARGO_HAZCAT_D -> AisMessage.ShipType.CARGO_HAZCAT_D
        ShipInfo.ShipType.OTHER_TYPE_HAZCAT_A -> AisMessage.ShipType.OTHER_TYPE_HAZCAT_A
        ShipInfo.ShipType.OTHER_TYPE_HAZCAT_B -> AisMessage.ShipType.OTHER_TYPE_HAZCAT_B
        ShipInfo.ShipType.OTHER_TYPE_HAZCAT_C -> AisMessage.ShipType.OTHER_TYPE_HAZCAT_C
        ShipInfo.ShipType.OTHER_TYPE_HAZCAT_D -> AisMessage.ShipType.OTHER_TYPE_HAZCAT_D
        ShipInfo.ShipType.WING_IN_GROUND_HAZCAT_A -> AisMessage.ShipType.WING_IN_GROUND_HAZCAT_A
        ShipInfo.ShipType.WING_IN_GROUND_HAZCAT_B -> AisMessage.ShipType.WING_IN_GROUND_HAZCAT_B
        ShipInfo.ShipType.WING_IN_GROUND_HAZCAT_C -> AisMessage.ShipType.WING_IN_GROUND_HAZCAT_C
        ShipInfo.ShipType.WING_IN_GROUND_HAZCAT_D -> AisMessage.ShipType.WING_IN_GROUND_HAZCAT_D
        ShipInfo.ShipType.FISHING -> AisMessage.ShipType.FISHING
        ShipInfo.ShipType.TOWING -> AisMessage.ShipType.TOWING
        ShipInfo.ShipType.TOWING_BIG -> AisMessage.ShipType.TOWING_BIG
        ShipInfo.ShipType.DREDGING_UNDERWATER_OPS -> AisMessage.ShipType.DREDGING_UNDERWATER_OPS
        ShipInfo.ShipType.DIVING_OPS -> AisMessage.ShipType.DIVING_OPS
        ShipInfo.ShipType.MILITARY_OPS -> AisMessage.ShipType.MILITARY_OPS
        ShipInfo.ShipType.SAILING -> AisMessage.ShipType.SAILING
        ShipInfo.ShipType.PLEASURE_CRAFT -> AisMessage.ShipType.PLEASURE_CRAFT
        ShipInfo.ShipType.NOT_AVAILABLE -> AisMessage.ShipType.NOT_AVAILABLE
        ShipInfo.ShipType.SPARE -> AisMessage.ShipType.SPARE
        ShipInfo.ShipType.UNDEFINED -> AisMessage.ShipType.UNDEFINED
        ShipInfo.ShipType.BASESTATION -> AisMessage.ShipType.BASESTATION
        ShipInfo.ShipType.MOTOR_YACHT -> AisMessage.ShipType.MOTOR_YACHT
        ShipInfo.ShipType.SPEEDBOAT -> AisMessage.ShipType.SPEEDBOAT
        ShipInfo.ShipType.SAILING_WITH_MOTOR -> AisMessage.ShipType.SAILING_WITH_MOTOR
        ShipInfo.ShipType.SPORT_FISHERMEN_VESSEL -> AisMessage.ShipType.SPORT_FISHERMEN_VESSEL
        ShipInfo.ShipType.SAILING_MOTOR_BIG -> AisMessage.ShipType.SAILING_MOTOR_BIG
        ShipInfo.ShipType.OTHER_RECREATIONAL_VESSEL -> AisMessage.ShipType.OTHER_RECREATIONAL_VESSEL
    }
}

fun ShipInfo.ShipStatus.toStatus(): AisMessage.ShipStatus {
    return when (this) {
        ShipInfo.ShipStatus.UNDER_WAY_USING_ENGINE -> AisMessage.ShipStatus.UNDER_WAY_USING_ENGINE
        ShipInfo.ShipStatus.AT_ANCHOR -> AisMessage.ShipStatus.AT_ANCHOR
        ShipInfo.ShipStatus.NOT_UNDER_COMMAND -> AisMessage.ShipStatus.NOT_UNDER_COMMAND
        ShipInfo.ShipStatus.RESTRICTED_MANEUVERABILITY -> AisMessage.ShipStatus.RESTRICTED_MANEUVERABILITY
        ShipInfo.ShipStatus.CONSTRAINED_BY_HER_DRAUGHT -> AisMessage.ShipStatus.CONSTRAINED_BY_HER_DRAUGHT
        ShipInfo.ShipStatus.MOORED -> AisMessage.ShipStatus.MOORED
        ShipInfo.ShipStatus.AGROUND -> AisMessage.ShipStatus.AGROUND
        ShipInfo.ShipStatus.ENGAGED_IN_FISHING -> AisMessage.ShipStatus.ENGAGED_IN_FISHING
        ShipInfo.ShipStatus.UNDER_WAY_SAILING -> AisMessage.ShipStatus.UNDER_WAY_SAILING
        ShipInfo.ShipStatus.RESERVED_FOR_DG -> AisMessage.ShipStatus.RESERVED_FOR_DG
        ShipInfo.ShipStatus.RESERVED_FOR_DG2 -> AisMessage.ShipStatus.RESERVED_FOR_DG2
        ShipInfo.ShipStatus.TOWING_ASTERN -> AisMessage.ShipStatus.TOWING_ASTERN
        ShipInfo.ShipStatus.TOWING_ALONGSIDE -> AisMessage.ShipStatus.TOWING_ALONGSIDE
        ShipInfo.ShipStatus.RESERVED_FUTURE -> AisMessage.ShipStatus.RESERVED_FUTURE
        ShipInfo.ShipStatus.ASI_SART -> AisMessage.ShipStatus.ASI_SART
        ShipInfo.ShipStatus.UNDEFINED -> AisMessage.ShipStatus.UNDEFINED
    }
}

private fun ShipInfo.SpecialManeuvreStatus?.toSpecialManeuverStatus(): AisMessage.SpecialManeuverStatus? {
    return when (this) {
        ShipInfo.SpecialManeuvreStatus.NOT_ENGAGED_IN_SPECIAL_MANEUVER -> AisMessage.SpecialManeuverStatus.NOT_ENGAGED_IN_SPECIAL_MANEUVER
        ShipInfo.SpecialManeuvreStatus.ENGAGED_IN_SPECIAL_MANEUVER -> AisMessage.SpecialManeuverStatus.ENGAGED_IN_SPECIAL_MANEUVER
        else -> null
    }
}
