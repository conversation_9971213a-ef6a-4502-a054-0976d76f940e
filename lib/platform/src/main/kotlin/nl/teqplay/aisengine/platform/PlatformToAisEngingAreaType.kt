package nl.teqplay.aisengine.platform

import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier.AreaType

fun getAreaType(platformEventType: String): AreaType? {
    if (platformEventType.startsWith("area.port")) {
        return AreaType.PORT
    }

    if (platformEventType.startsWith("area.terminal")) {
        return AreaType.TERMINAL
    }

    if (platformEventType.contains(".basin.")) {
        return AreaType.BASIN
    }

    if (platformEventType.contains(".region.")) {
        return AreaType.CUSTOM
    }

    if (platformEventType.contains(".anchor.") || platformEventType.contains(".anchored.")) {
        return AreaType.ANCHOR
    }

    if (platformEventType.contains(".lock.")) {
        return AreaType.LOCK
    }

    val suffixRemovedAreaType = platformEventType
        .removeSuffix(".start")
        .removeSuffix(".end")

    return eventTypeToAreaType[suffixRemovedAreaType]
}

/**
 * Helper function to get the UNLO code of a port from the [platformEventType].
 */
fun getUnloCodeByEventType(platformEventType: String): String? {
    val eventParts = platformEventType.split(".")
    val secondPart = eventParts.elementAtOrNull(1)
        ?.uppercase()
        ?.takeIf { supportedUnloCodes.contains(it) }

    // Return the found unlocode if it was part of the start of the event type. e.g. area.sgsin
    if (secondPart != null) {
        return secondPart
    }

    // When nothing is found always fallback to the unlocode found in the 3rd part of the event type
    // Only taking it when it has exactly 5 characters and is provided in full upper case
    return eventParts.elementAtOrNull(2)
        ?.takeIf { result -> result.length == 5 && result.all { char -> char.isUpperCase() } }
}

/**
 * Map containing all custom event names paired with their area type
 */
private val eventTypeToAreaType = mapOf(
    "area.amazone.pilotarea" to AreaType.PILOT_BOARDING_PLACE,
    // "area.apm1" to AreaType.TERMINAL,
    // "area.apm2" to AreaType.TERMINAL,
    "area.beanr.approach.saeftinghe" to AreaType.CUSTOM,
    "area.beanr.pilotarea_steenbank" to AreaType.PILOT_BOARDING_PLACE,
    "area.beanr.pilotarea_wandelaar" to AreaType.PILOT_BOARDING_PLACE,
    "area.beanr.sea-entrance" to AreaType.CUSTOM,
    "area.brssa.pilotarea" to AreaType.PILOT_BOARDING_PLACE,
    // "area.delta" to AreaType.TERMINAL,
    // "area.dohai.pilotarea" to AreaType.PILOT_BOARDING_PLACE,
    "area.esalg.12nm" to AreaType.NAUTICAL_MILE,
    "area.esalg.60nm" to AreaType.NAUTICAL_MILE,
    "area.esalg.pilotarea" to AreaType.PILOT_BOARDING_PLACE,
    // "area.espadarte.pilotarea" to AreaType.PILOT_BOARDING_PLACE,
    // "area.euromax" to AreaType.TERMINAL,
    // "area.fairway.trombetas" to AreaType.CUSTOM,
    // "area.fihel.approach.east" to AreaType.CUSTOM,
    // "area.fihel.approach.south" to AreaType.CUSTOM,
    // "area.fihel.approach.west" to AreaType.CUSTOM,
    "area.fihel.pilotarea" to AreaType.PILOT_BOARDING_PLACE,
    // "area.fivss.approach.east" to AreaType.CUSTOM,
    // "area.fivss.approach.south" to AreaType.CUSTOM,
    "area.fivss.pilotarea" to AreaType.PILOT_BOARDING_PLACE,
    "area.maptm.12nm" to AreaType.NAUTICAL_MILE,
    "area.maptm.60nm" to AreaType.NAUTICAL_MILE,
    // "area.maptm.approach" to AreaType.CUSTOM,
    "area.maptm.pilotarea" to AreaType.PILOT_BOARDING_PLACE,
    "area.mosqueiro.pilotarea" to AreaType.PILOT_BOARDING_PLACE,
    "area.nlams.approach.portapproachpoint" to AreaType.CUSTOM,
    "area.nlams.pilotarea" to AreaType.PILOT_BOARDING_PLACE,
    "area.nlams.pilotarea.deepsea" to AreaType.PILOT_BOARDING_PLACE,
    "area.nlams.sea-entrance" to AreaType.CUSTOM,
    "area.nlrtm.approach.rtmmaasmond" to AreaType.CUSTOM,
    // "area.nlrtm.berthguide" to AreaType.CUSTOM,
    "area.nlrtm.lagelicht" to AreaType.CUSTOM,
    "area.nlrtm.sea-entrance" to AreaType.CUSTOM,
    // "area.region.curua_grande" to AreaType.CUSTOM,
    // "area.region.estirao_do_franca" to AreaType.CUSTOM,
    // "area.region.macapa" to AreaType.CUSTOM,
    // "area.region.obidos" to AreaType.CUSTOM,
    // "area.region.oriximina" to AreaType.CUSTOM,
    // "area.region.trombetas" to AreaType.CUSTOM,
    "area.rtm120nm" to AreaType.NAUTICAL_MILE,
    "area.rtm12nm" to AreaType.NAUTICAL_MILE,
    "area.rtm240nm" to AreaType.NAUTICAL_MILE,
    "area.rtm60nm" to AreaType.NAUTICAL_MILE,
    "area.rtm80nm" to AreaType.NAUTICAL_MILE,
    // "area.rtmfairway" to AreaType.CUSTOM,
    "area.rtmpilotarea.deepdraught" to AreaType.PILOT_BOARDING_PLACE,
    "area.rtmpilotarea.inland" to AreaType.PILOT_BOARDING_PLACE,
    "area.rtmpilotarea.lng" to AreaType.PILOT_BOARDING_PLACE,
    "area.rtmpilotarea.maasmond" to AreaType.PILOT_BOARDING_PLACE,
    // "area.rwg" to AreaType.TERMINAL,
    "area.segot.approach" to AreaType.CUSTOM,
    // "area.segot.fairway" to AreaType.CUSTOM,
    "area.segot.pilotarea" to AreaType.PILOT_BOARDING_PLACE,
    "area.sgsin.12nm" to AreaType.NAUTICAL_MILE,
    "area.sgsin.60nm" to AreaType.NAUTICAL_MILE,
    "area.sgsin.eosp" to AreaType.END_OF_SEA_PASSAGE,
    "area.sgsin.pilotarea_pebga" to AreaType.PILOT_BOARDING_PLACE,
    "area.sgsin.pilotarea_pebgb" to AreaType.PILOT_BOARDING_PLACE,
    "area.sgsin.pilotarea_pjsb" to AreaType.PILOT_BOARDING_PLACE,
    "area.sgsin.pilotarea_psbg" to AreaType.PILOT_BOARDING_PLACE,
    "area.sgsin.pilotarea_pwbga" to AreaType.PILOT_BOARDING_PLACE,
    "area.sgsin.pilotarea_pwbgb" to AreaType.PILOT_BOARDING_PLACE,
    "area.sluis" to AreaType.LOCK,
    "area.sluisoost" to AreaType.LOCK,
    "area.sluiswest" to AreaType.LOCK,
    // "area.terminalnearby.cargillbotlek" to AreaType.TERMINAL_NEARBY,
    // "area.terminalnearby.cargillizegem" to AreaType.TERMINAL_NEARBY,
    "area.terminalnearby.yarasluiskil" to AreaType.TERMINAL_NEARBY,
    // "area.tug.botlek" to AreaType.TUG,
    // "area.tug.east" to AreaType.TUG,
    // "area.tug.europoort-east" to AreaType.TUG,
    // "area.tug.europoort-west" to AreaType.TUG,
    // "area.tug.maasvlakte" to AreaType.TUG,
    "area.ushou.approach.fredhartman" to AreaType.CUSTOM,
    "area.ushou.approach.morganspoint" to AreaType.CUSTOM,
    "area.ushou.pilotarea" to AreaType.PILOT_BOARDING_PLACE,
    "area.vts.rtm" to AreaType.VHF,
    "area.volkeraksluizen" to AreaType.LOCK,
    "area.volkeraksluizenoost" to AreaType.LOCK,
    "area.volkeraksluizenwest" to AreaType.LOCK,
    "beanrpilotarea_flushing" to AreaType.PILOT_BOARDING_PLACE
)

/**
 * Set of supported unlo codes we allow to find when getting the unlo code from the event type via the [getUnloCodeByEventType] method.
 */
private val supportedUnloCodes = setOf(
    "SGSIN",
    "BEANR",
    "MAPTM",
    "ESALG",
    "USHOU",
    "SEGOT",
    "NLAMS",
    "NLRTM",
    "FIVSS",
    "FIHEL",
    "NLTNZ",
    "USCRP",
    "USLDJ"
)
