package nl.teqplay.aisengine.platform.model

import com.fasterxml.jackson.annotation.JsonInclude
import nl.teqplay.platform.model.HistoricShipInfo
import nl.teqplay.platform.model.Location
import nl.teqplay.platform.model.ShipInfo

/**
 * Custom implementation for platform's [HistoricShipInfo], to ensure non-nullability on required fields
 *
 * [JvmField] is used so the SQL parser can recognise the fields
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
open class LegacyHistoricShipInfo(
    @JvmField val mmsi: String,
    @JvmField val timeLastUpdate: Long,
    @JvmField val location: Location,

    @JvmField val status: ShipInfo.ShipStatus? = null,
    @JvmField val speedOverGround: Float? = null,
    @JvmField val courseOverGround: Float? = null,
    @JvmField val trueHeading: Int? = null,
    @JvmField val eta: Long? = null,
    @JvmField val destination: String? = null,
    @JvmField val source: String? = null,
    @JvmField val posAccuracyMeters: Double? = null,
    @JvmField val maxDraught: Float? = null,
    @JvmField val predicted: Boolean? = null,
    @JvmField val streamingIsNewUpdate: Boolean? = null,

    @JvmField val _id: String = mmsi
)
