package nl.teqplay.aisengine.platform.converter

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.event.interfaces.AnchoredEvent
import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType
import nl.teqplay.aisengine.event.interfaces.EncounterMetadata
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.interfaces.HamisPilotOnBoardEvent
import nl.teqplay.aisengine.event.interfaces.LockEvent
import nl.teqplay.aisengine.event.interfaces.LockEvent.LockDirection
import nl.teqplay.aisengine.event.interfaces.PortcallArrivalEvent
import nl.teqplay.aisengine.event.interfaces.PortcallDepartureEvent
import nl.teqplay.aisengine.event.interfaces.PortcallPredictionEvent
import nl.teqplay.aisengine.event.interfaces.RelatedBerthEvent
import nl.teqplay.aisengine.event.interfaces.ShipMovingEvent
import nl.teqplay.aisengine.event.interfaces.SpeedEvent
import nl.teqplay.aisengine.event.model.AisDraughtChangedEvent
import nl.teqplay.aisengine.event.model.AisEtaChangedEvent
import nl.teqplay.aisengine.event.model.AisLostEvent
import nl.teqplay.aisengine.event.model.AisRecoverEvent
import nl.teqplay.aisengine.event.model.AisStatusChangedEvent
import nl.teqplay.aisengine.event.model.AnchoredEndEvent
import nl.teqplay.aisengine.event.model.AnchoredStartEvent
import nl.teqplay.aisengine.event.model.AreaEndEvent
import nl.teqplay.aisengine.event.model.AreaStartEvent
import nl.teqplay.aisengine.event.model.ConfirmedBerthEndEvent
import nl.teqplay.aisengine.event.model.ConfirmedBerthStartEvent
import nl.teqplay.aisengine.event.model.EncounterEndEvent
import nl.teqplay.aisengine.event.model.EncounterStartEvent
import nl.teqplay.aisengine.event.model.LockEtaEvent
import nl.teqplay.aisengine.event.model.LockEtdEvent
import nl.teqplay.aisengine.event.model.PortcallPilotBoardingEtaEvent
import nl.teqplay.aisengine.event.model.ShipMovingEndEvent
import nl.teqplay.aisengine.event.model.ShipMovingStartEvent
import nl.teqplay.aisengine.event.model.SpeedChangedEvent
import nl.teqplay.aisengine.event.model.TrueDestinationChangedEvent
import nl.teqplay.aisengine.event.model.UniqueBerthEndEvent
import nl.teqplay.aisengine.event.model.UniqueBerthStartEvent
import nl.teqplay.aisengine.event.model.encountermetadata.BoatmanEncounterMetadata
import nl.teqplay.aisengine.event.model.encountermetadata.TugEncounterMetadata
import nl.teqplay.aisengine.event.model.hamis.HamisAddPortcallVisitEvent
import nl.teqplay.aisengine.event.model.hamis.HamisAgentOrderEvent
import nl.teqplay.aisengine.event.model.hamis.HamisAgentReportsTugsEvent
import nl.teqplay.aisengine.event.model.hamis.HamisAtaEvent
import nl.teqplay.aisengine.event.model.hamis.HamisAtdEvent
import nl.teqplay.aisengine.event.model.hamis.HamisCancelPortcallVisitEvent
import nl.teqplay.aisengine.event.model.hamis.HamisEtaBerthEvent
import nl.teqplay.aisengine.event.model.hamis.HamisEtaCancelEvent
import nl.teqplay.aisengine.event.model.hamis.HamisEtaEvent
import nl.teqplay.aisengine.event.model.hamis.HamisEtaRequestEvent
import nl.teqplay.aisengine.event.model.hamis.HamisEtdEvent
import nl.teqplay.aisengine.event.model.hamis.HamisEtdRequestEvent
import nl.teqplay.aisengine.event.model.hamis.HamisNauticalOrderEvent
import nl.teqplay.aisengine.event.model.hamis.HamisPilotBoardingEtaEvent
import nl.teqplay.aisengine.event.model.hamis.HamisPilotOnBoardEndEvent
import nl.teqplay.aisengine.event.model.hamis.HamisPilotOnBoardStartEvent
import nl.teqplay.aisengine.event.model.hamis.HamisUpdatePortcallVisitEvent
import nl.teqplay.aisengine.event.model.hamis.HamisVisitCancellationEvent
import nl.teqplay.aisengine.event.model.hamis.HamisVisitDeclarationEvent
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier.AreaType
import nl.teqplay.aisengine.event.model.identifier.BerthIdentifier
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusAgentChangedEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusAtaEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusAtdEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusEtaBerthEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusEtaEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusEtdEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusPortcallFinishEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusShipChangedEvent
import nl.teqplay.aisengine.platform.getAreaType
import nl.teqplay.aisengine.platform.getUnloCodeByEventType
import nl.teqplay.aisengine.platform.toAisShipIdentifier
import nl.teqplay.aisengine.platform.toGeneralShipIdentifier
import nl.teqplay.aisengine.platform.toLocation
import nl.teqplay.aisengine.platform.toOtherAisShipIdentifier
import nl.teqplay.aisengine.platform.toOtherGeneralShipIdentifier
import nl.teqplay.aisengine.platform.toStatus
import nl.teqplay.platform.model.event.AgentChangedEvent
import nl.teqplay.platform.model.event.AisRecoveredEvent
import nl.teqplay.platform.model.event.AuthorityEvent
import nl.teqplay.platform.model.event.BargeBunkerEvent
import nl.teqplay.platform.model.event.BargeWaterEvent
import nl.teqplay.platform.model.event.BerthEvent
import nl.teqplay.platform.model.event.BoatmanEvent
import nl.teqplay.platform.model.event.BunkerEvent
import nl.teqplay.platform.model.event.CargoBargeEvent
import nl.teqplay.platform.model.event.ConfirmedBerthEvent
import nl.teqplay.platform.model.event.CraneEvent
import nl.teqplay.platform.model.event.DestinationChangedEvent
import nl.teqplay.platform.model.event.DraughtChangedEvent
import nl.teqplay.platform.model.event.EndSeaPassageEvent
import nl.teqplay.platform.model.event.ExtendedTeqplayLocationBasedEvent
import nl.teqplay.platform.model.event.FenderEvent
import nl.teqplay.platform.model.event.LubesEvent
import nl.teqplay.platform.model.event.PilotEvent
import nl.teqplay.platform.model.event.PortAtaAtdEvent
import nl.teqplay.platform.model.event.PortcallBerthEtaEvent
import nl.teqplay.platform.model.event.PortcallFinishEvent
import nl.teqplay.platform.model.event.PortcallShipChangedEvent
import nl.teqplay.platform.model.event.PushBargeEvent
import nl.teqplay.platform.model.event.ShipInfraEncounterEvent
import nl.teqplay.platform.model.event.StatusChangedEvent
import nl.teqplay.platform.model.event.SupplyBargeEvent
import nl.teqplay.platform.model.event.SwogEvent
import nl.teqplay.platform.model.event.TankerBargeEvent
import nl.teqplay.platform.model.event.TenderEvent
import nl.teqplay.platform.model.event.TeqperimentEvent
import nl.teqplay.platform.model.event.TeqplayEvent
import nl.teqplay.platform.model.event.TeqplayLocationBasedEvent
import nl.teqplay.platform.model.event.TerminalEvent
import nl.teqplay.platform.model.event.TerminalNearbyEvent
import nl.teqplay.platform.model.event.TugEvent
import nl.teqplay.platform.model.event.TugWaitingForDepartureEvent
import nl.teqplay.platform.model.event.UniqueBerthEvent
import nl.teqplay.platform.model.event.VhfEvent
import nl.teqplay.platform.model.event.WasteEvent
import nl.teqplay.platform.model.event.WaterEvent
import nl.teqplay.platform.model.event.hbr.AddPortcallVisitEvent
import nl.teqplay.platform.model.event.hbr.AgentOrderEvent
import nl.teqplay.platform.model.event.hbr.AgentReportsTugsEvent
import nl.teqplay.platform.model.event.hbr.CancelPortcallVisitEvent
import nl.teqplay.platform.model.event.hbr.EtaCancelEvent
import nl.teqplay.platform.model.event.hbr.EtaRequestEvent
import nl.teqplay.platform.model.event.hbr.EtdRequestEvent
import nl.teqplay.platform.model.event.hbr.NauticalOrderEvent
import nl.teqplay.platform.model.event.hbr.UpdatePortcallVisitEvent
import nl.teqplay.platform.model.event.hbr.VisitCancellationEvent
import nl.teqplay.platform.model.event.hbr.VisitDeclarationEvent
import java.time.Instant
import nl.teqplay.platform.model.event.AisEtaChangedEvent as PlatformAisEtaChangedEvent
import nl.teqplay.platform.model.event.AisLostEvent as PlatformAisLostEvent
import nl.teqplay.platform.model.event.AnchoredEvent as PlatformAnchoredEvent
import nl.teqplay.platform.model.event.AtaEvent as PlatformAtaEvent
import nl.teqplay.platform.model.event.AtdEvent as PlatformAtdEvent
import nl.teqplay.platform.model.event.EtaEvent as PlatformEtaEvent
import nl.teqplay.platform.model.event.EtdEvent as PlatformEtdEvent
import nl.teqplay.platform.model.event.LockEvent as PlatformLockEvent
import nl.teqplay.platform.model.event.PortcallPilotBoardingEtaEvent as PlatformPortcallPilotBoardingEtaEvent
import nl.teqplay.platform.model.event.ShipMovingEvent as PlatfromShipMovingEvent
import nl.teqplay.platform.model.event.SpeedChangedEvent as PlatformSpeedChangedEvent
import nl.teqplay.platform.model.event.hbr.HamisPilotBoardingEtaEvent as PlatformHamisPilotBoardingEtaEvent
import nl.teqplay.platform.model.event.hbr.PilotOnBoardEvent as PlatformPilotOnBoardEvent
import nl.teqplay.platform.model.lockplanning.LockDirection as PlatformLockDirection

private val LOG = KotlinLogging.logger {}

class AisEngineEventConverter<out T : Event, in S : TeqplayEvent> {
    inline fun <reified U : Event> convert(input: S): T? {
        val convertedEvent = convertToAisEngineEvent(input)

        @Suppress("UNCHECKED_CAST")
        val castedEvent = convertedEvent as? T?

        return castedEvent?.takeIf { it is U }
    }
}

fun convertToAisEngineEvent(input: TeqplayEvent): Event? {
    // We sometimes had moments where the location on a location based event was not provided, they should be dropped
    if (input is TeqplayLocationBasedEvent && input.location == null) {
        return null
    }

    val convertedEvent = when (input) {
        is PlatformAnchoredEvent -> input.toAisEngineEvent()
        is DestinationChangedEvent -> input.toAisEngineEvent()
        is DraughtChangedEvent -> input.toAisEngineEvent()
        is PlatformSpeedChangedEvent -> input.toAisEngineEvent()
        is PlatformAisEtaChangedEvent -> input.toAisEngineEvent()
        is StatusChangedEvent -> input.toAisEngineEvent()
        is PortAtaAtdEvent -> input.toAisEngineAreaEvent(AreaType.PORT, input.maxDraught)
        is VhfEvent -> {
            val vhfAreaName = input.type.removePrefix("area.")
                .removeSuffix(".start")
                .removeSuffix(".end")
            input.toAisEngineAreaEvent(AreaType.VHF, areaName = vhfAreaName)
        }
        is TerminalEvent -> input.toAisEngineAreaEvent(AreaType.TERMINAL, areaName = input.terminalName)
        is EndSeaPassageEvent -> input.toAisEngineAreaEvent(AreaType.END_OF_SEA_PASSAGE)
        is TerminalNearbyEvent -> input.toAisEngineAreaEvent()
        is ShipInfraEncounterEvent -> input.toAisEngineEvent()
        is UniqueBerthEvent -> input.toAisEngineEvent()
        is ConfirmedBerthEvent -> input.toAisEngineEvent()
        is BerthEvent -> input.toAisEngineEvent()
        is PlatfromShipMovingEvent -> input.toAisEngineEvent()
        is TugEvent -> input.toAisEngineEncounterEvent(EncounterType.TUG)
        is PilotEvent -> input.toAisEngineEncounterEvent(EncounterType.PILOT)
        is BoatmanEvent -> input.toAisEngineEncounterEvent(EncounterType.BOATMAN)
        is BunkerEvent -> input.toAisEngineEncounterEvent(EncounterType.BUNKER)
        is AuthorityEvent -> input.toAisEngineEncounterEvent(EncounterType.AUTHORITY)
        is WasteEvent -> input.toAisEngineEncounterEvent(EncounterType.WASTE)
        is SwogEvent -> input.toAisEngineEncounterEvent(EncounterType.SWOG)
        is WaterEvent -> input.toAisEngineEncounterEvent(EncounterType.WATER)
        is TenderEvent -> input.toAisEngineEncounterEvent(EncounterType.TENDER)
        is FenderEvent -> input.toAisEngineEncounterEvent(EncounterType.FENDER)
        is CraneEvent -> input.toAisEngineEncounterEvent(EncounterType.CRANE)
        is LubesEvent -> input.toAisEngineEncounterEvent(EncounterType.LUBES)
        is TugWaitingForDepartureEvent -> input.toAisEngineEncounterEvent(EncounterType.TUG_WAITING_DEPARTURE)
        is SupplyBargeEvent -> input.toAisEngineEncounterEvent(EncounterType.BARGE_SUPPLY)
        is CargoBargeEvent -> input.toAisEngineEncounterEvent(EncounterType.BARGE_CARGO)
        is TankerBargeEvent -> input.toAisEngineEncounterEvent(EncounterType.BARGE_TANKER)
        is PushBargeEvent -> input.toAisEngineEncounterEvent(EncounterType.BARGE_PUSH)
        is BargeWaterEvent -> input.toAisEngineEncounterEvent(EncounterType.BARGE_WATER)
        is BargeBunkerEvent -> input.toAisEngineEncounterEvent(EncounterType.BARGE_BUNKER)
        is TeqperimentEvent -> input.toAisEngineEncounterEvent(EncounterType.TEQPERIMENT)
        is AisRecoveredEvent -> input.toAisEngineEvent()
        is PlatformAtaEvent -> input.toAisEngineEvent()
        is PlatformAtdEvent -> input.toAisEngineEvent()
        is PlatformAisLostEvent -> input.toAisEngineEvent()
        is PlatformPortcallPilotBoardingEtaEvent -> input.toAisEngineEvent()
        is PortcallBerthEtaEvent -> input.toAisEngineEvent()
        is PlatformEtaEvent -> input.toAisEngineEvent()
        is PlatformEtdEvent -> input.toAisEngineEvent()
        is PlatformLockEvent -> input.toAisEngineEvent()
        is ExtendedTeqplayLocationBasedEvent -> input.toAisEngineAreaEvent()
        is TeqplayLocationBasedEvent -> input.toAisEngineAreaEvent()
        is AddPortcallVisitEvent -> input.toAisEngineEvent()
        is UpdatePortcallVisitEvent -> input.toAisEngineEvent()
        is CancelPortcallVisitEvent -> input.toAisEngineEvent()
        is AgentOrderEvent -> input.toAisEngineEvent()
        is AgentReportsTugsEvent -> input.toAisEngineEvent()
        is EtaCancelEvent -> input.toAisEngineEvent()
        is EtaRequestEvent -> input.toAisEngineEvent()
        is EtdRequestEvent -> input.toAisEngineEvent()
        is PlatformHamisPilotBoardingEtaEvent -> input.toAisEngineEvent()
        is NauticalOrderEvent -> input.toAisEngineEvent()
        is PlatformPilotOnBoardEvent -> input.toAisEngineEvent()
        is VisitCancellationEvent -> input.toAisEngineEvent()
        is VisitDeclarationEvent -> input.toAisEngineEvent()
        is AgentChangedEvent -> input.toAisEngineEvent()
        is PortcallFinishEvent -> input.toAisEngineEvent()
        is PortcallShipChangedEvent -> input.toAisEngineEvent()

        else -> return null.also {
            LOG.warn {
                "Event can't be converted to an AisEngine event" +
                    " (type: ${input.type}, class: ${input::class.java.simpleName}, category: ${input.category})"
            }
        }
    }

    if (convertedEvent == null) {
        LOG.debug {
            "Attempt to convert event wasn't successful " +
                " (type: ${input.type}, class: ${input::class.java.simpleName}, category: ${input.category})"
        }
    }

    return convertedEvent
}

private fun PlatformAnchoredEvent.toAisEngineEvent(): AnchoredEvent {
    val ship = this.toAisShipIdentifier()
    if (this.isEndEvent) {
        return AnchoredEndEvent(
            this.uuid,
            this.relatedEvent,
            ship,
            AreaIdentifier(this.area, AreaType.ANCHOR, this.area, this.port),
            this.location.toLocation(),
            Instant.ofEpochMilli(this.eventTime),
            Instant.ofEpochMilli(this.datetime),
            this.isDeleted,
            null
        )
    } else {
        return AnchoredStartEvent(
            this.uuid,
            ship,
            AreaIdentifier(this.area, AreaType.ANCHOR, this.area, this.port),
            this.location.toLocation(),
            Instant.ofEpochMilli(this.eventTime),
            Instant.ofEpochMilli(this.datetime),
            this.isDeleted,
            null
        )
    }
}

fun PlatformAtaEvent.toAisEngineEvent(): PortcallArrivalEvent? {
    // We can't convert this event if it doesn't contain a portcall id
    if (this.ucrn == null) {
        return null
    }

    // When Hamis specific fields are null, instead return a PortcallPlus event
    if (isPortcallPlusEvent(this.movementId, this.externalVisitId)) {
        return PortcallPlusAtaEvent(
            _id = this.uuid,
            portcallId = this.ucrn,
            ship = this.toGeneralShipIdentifier(),
            area = AreaIdentifier(
                id = this.estLocationId,
                type = AreaType.BERTH,
                name = this.estLocation,
                unlocode = this.port
            ),
            port = AreaIdentifier(
                id = null,
                type = AreaType.PORT,
                name = this.port,
                unlocode = this.port
            ),
            createdTime = Instant.ofEpochMilli(this.datetime),
            actualTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
            deleted = this.isDeleted
        )
    } else {
        // Both the movement id and external visit id are provided, so we can make this into a HamisAtaEvent
        return HamisAtaEvent(
            _id = this.uuid,
            portcallId = this.ucrn,
            movementId = this.movementId.toString(),
            externalVisitId = this.externalVisitId,
            ship = this.toGeneralShipIdentifier(),
            area = AreaIdentifier(
                id = this.estLocationId,
                type = AreaType.BERTH,
                name = this.estLocation,
                unlocode = this.port
            ),
            port = AreaIdentifier(
                id = null,
                type = AreaType.PORT,
                name = this.port,
                unlocode = this.port
            ),
            createdTime = Instant.ofEpochMilli(this.datetime),
            actualTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
            deleted = this.isDeleted
        )
    }
}

fun PlatformAtdEvent.toAisEngineEvent(): PortcallDepartureEvent? {
    // We can't convert this event if it doesn't contain a portcall id
    if (this.ucrn == null) {
        return null
    }

    // When Hamis specific fields are null, instead return a PortcallPlus event
    if (isPortcallPlusEvent(this.movementId, this.externalVisitId)) {
        return PortcallPlusAtdEvent(
            _id = this.uuid,
            portcallId = this.ucrn,
            ship = this.toGeneralShipIdentifier(),
            area = AreaIdentifier(
                id = this.estLocationId,
                type = AreaType.BERTH,
                name = this.estLocation,
                unlocode = this.port
            ),
            port = AreaIdentifier(
                id = null,
                type = AreaType.PORT,
                name = this.port,
                unlocode = this.port
            ),
            createdTime = Instant.ofEpochMilli(this.datetime),
            actualTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
            deleted = this.isDeleted
        )
    } else {
        return HamisAtdEvent(
            _id = this.uuid,
            portcallId = this.ucrn,
            movementId = this.movementId.toString(),
            externalVisitId = this.externalVisitId,
            ship = this.toGeneralShipIdentifier(),
            area = AreaIdentifier(
                id = this.estLocationId,
                type = AreaType.BERTH,
                name = this.estLocation,
                unlocode = this.port
            ),
            port = AreaIdentifier(
                id = null,
                type = AreaType.PORT,
                name = this.port,
                unlocode = this.port
            ),
            createdTime = Instant.ofEpochMilli(this.datetime),
            actualTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
            deleted = this.isDeleted
        )
    }
}

/**
 * Check if the Hamis fields of Platform event are null.
 *
 * @return True when this is PortcallPlus event
 */
private fun isPortcallPlusEvent(movementId: Any?, externalVisitId: String?): Boolean {
    return movementId == null || externalVisitId == null
}

fun DestinationChangedEvent.toAisEngineEvent(): TrueDestinationChangedEvent {
    return TrueDestinationChangedEvent(
        _id = this.uuid,
        ship = this.toAisShipIdentifier(),
        location = this.location.toLocation(),
        createdTime = Instant.ofEpochMilli(this.datetime),
        actualTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
        deleted = this.isDeleted,
        oldValue = this.oldDestination,
        newValue = this.newDestination,
        trueDestination = this.newTrueDestination
    )
}

fun PlatformSpeedChangedEvent.toAisEngineEvent(): SpeedChangedEvent {
    val speedType = if (this.title == "is Accelerating") {
        SpeedEvent.SpeedType.ACCELERATING
    } else {
        SpeedEvent.SpeedType.SLOWING_DOWN
    }

    return SpeedChangedEvent(
        _id = this.uuid,
        ship = this.toAisShipIdentifier(),
        location = this.location.toLocation(),
        createdTime = Instant.ofEpochMilli(this.datetime),
        actualTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
        deleted = this.isDeleted,
        speedType = speedType
    )
}

fun DraughtChangedEvent.toAisEngineEvent(): AisDraughtChangedEvent {
    return AisDraughtChangedEvent(
        _id = this.uuid,
        ship = this.toAisShipIdentifier(),
        location = this.location.toLocation(),
        createdTime = Instant.ofEpochMilli(this.datetime),
        actualTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
        deleted = this.isDeleted,
        oldValue = this.oldDraught,
        newValue = this.newDraught
    )
}

fun PlatformAisEtaChangedEvent.toAisEngineEvent(): AisEtaChangedEvent {
    return AisEtaChangedEvent(
        _id = this.uuid,
        ship = this.toAisShipIdentifier(),
        location = this.location.toLocation(),
        createdTime = Instant.ofEpochMilli(this.datetime),
        actualTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
        deleted = this.isDeleted,
        oldValue = this.oldEta?.let { Instant.ofEpochMilli(it) },
        newValue = this.newEta?.let { Instant.ofEpochMilli(it) }
    )
}

fun StatusChangedEvent.toAisEngineEvent(): AisStatusChangedEvent? {
    val newStatus = this.newStatus
        // We can only convert the event if there is a status provided
        ?: return null

    return AisStatusChangedEvent(
        _id = this.uuid,
        ship = this.toAisShipIdentifier(),
        location = this.location.toLocation(),
        createdTime = Instant.ofEpochMilli(this.datetime),
        actualTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
        deleted = this.isDeleted,
        oldValue = this.oldStatus?.toStatus(),
        newValue = newStatus.toStatus()
    )
}

fun TeqplayLocationBasedEvent.toAisEngineEncounterEvent(type: EncounterType): EncounterEvent? {
    // There were times when we had no other mmsi, meaning this encounter should be invalid
    if (this.otherMmsi == null) {
        return null
    }

    if (this.isStartEvent) {
        return EncounterStartEvent(
            _id = this.uuid,
            ship = this.toAisShipIdentifier(),
            otherShip = this.toOtherAisShipIdentifier(),
            encounterType = type,
            location = this.location.toLocation(),
            createdTime = Instant.ofEpochMilli(this.datetime),
            actualTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
            deleted = this.isDeleted,
            probability = this.probability ?: 1.0,
            metadata = this.toAisEngineEncounterMetadata()
        )
    } else {
        return EncounterEndEvent(
            _id = this.uuid,
            startEventId = this.relatedEvent,
            ship = this.toAisShipIdentifier(),
            otherShip = this.toOtherAisShipIdentifier(),
            encounterType = type,
            location = this.location.toLocation(),
            createdTime = Instant.ofEpochMilli(this.datetime),
            actualTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
            deleted = this.isDeleted,
            probability = this.probability ?: 1.0,
            metadata = this.toAisEngineEncounterMetadata()
        )
    }
}

fun TeqplayLocationBasedEvent.toAisEngineEncounterMetadata(): EncounterMetadata? {
    return when (this) {
        is BoatmanEvent -> {
            return BoatmanEncounterMetadata(
                hasSimultaneousTugEncounter = this.hasSimultaneousTugEncounter,
                nrOfBoatmen = this.numberOfBoatman,
                arrival = this.arrival
            )
        }
        is TugEvent -> {
            return TugEncounterMetadata(
                hasSimultaneousTugEncounter = this.hasSimultaneousTugEncounter,
            )
        }
        else -> null
    }
}

fun TeqplayLocationBasedEvent.toAisEngineAreaEvent(type: AreaType? = null, draught: Float? = null, areaName: String? = null): AreaEvent? {
    val areaType = type ?: (getAreaType(this.type) ?: return null)
    val unlocode = getUnloCodeByEventType(this.type)
    val eventDraught = if (this is ExtendedTeqplayLocationBasedEvent) {
        this.maxDraught
    } else {
        draught
    }

    if (this.isStartEvent) {
        val titleAreaName = this.title.removePrefix("enters ")

        return AreaStartEvent(
            _id = this.uuid,
            ship = this.toAisShipIdentifier(),
            location = this.location.toLocation(),
            createdTime = Instant.ofEpochMilli(this.datetime),
            actualTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
            deleted = this.isDeleted,
            draught = eventDraught,
            area = AreaIdentifier(
                id = null,
                type = areaType,
                name = areaName ?: titleAreaName,
                unlocode = unlocode ?: this.port
            ),
            berth = null,
            heading = null,
            speedOverGround = null
        )
    } else {
        val titleAreaName = this.title.removePrefix("exits ")

        return AreaEndEvent(
            _id = this.uuid,
            startEventId = this.relatedEvent,
            ship = this.toAisShipIdentifier(),
            location = this.location.toLocation(),
            createdTime = Instant.ofEpochMilli(this.datetime),
            actualTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
            deleted = this.isDeleted,
            draught = eventDraught,
            area = AreaIdentifier(
                id = null,
                type = areaType,
                name = areaName ?: titleAreaName,
                unlocode = unlocode ?: this.port
            ),
            berth = null,
            heading = null,
            speedOverGround = null
        )
    }
}

fun ConfirmedBerthEvent.toAisEngineEvent(): RelatedBerthEvent {
    if (this.isStartEvent) {
        return ConfirmedBerthStartEvent(
            _id = this.uuid,
            berthEventId = this.relatedEvent,
            ship = this.toAisShipIdentifier(),
            location = this.location.toLocation(),
            createdTime = Instant.ofEpochMilli(this.datetime),
            actualTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
            deleted = this.isDeleted,
            draught = this.draught,
            area = AreaIdentifier(
                id = this.berthId,
                type = AreaType.BERTH,
                name = this.berthName,
                unlocode = this.port
            ),
            berth = BerthIdentifier(
                direction = null,
                terminalName = this.terminalName
            ),
            heading = this.heading
        )
    } else {
        return ConfirmedBerthEndEvent(
            _id = this.uuid,
            berthEventId = this.relatedEvent,
            startEventId = null,
            ship = this.toAisShipIdentifier(),
            location = this.location.toLocation(),
            createdTime = Instant.ofEpochMilli(this.datetime),
            actualTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
            deleted = this.isDeleted,
            draught = this.draught,
            area = AreaIdentifier(
                id = this.berthId,
                type = AreaType.BERTH,
                name = this.berthName,
                unlocode = this.port
            ),
            berth = BerthIdentifier(
                direction = null,
                terminalName = this.terminalName
            ),
            heading = this.heading
        )
    }
}

fun UniqueBerthEvent.toAisEngineEvent(): RelatedBerthEvent {
    if (this.isStartEvent) {
        return UniqueBerthStartEvent(
            _id = this.uuid,
            berthEventId = this.relatedEvent,
            berthConfirmedEventId = this.relatedEvent,
            ship = this.toAisShipIdentifier(),
            location = this.location.toLocation(),
            createdTime = Instant.ofEpochMilli(this.datetime),
            actualTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
            deleted = this.isDeleted,
            draught = this.draught,
            area = AreaIdentifier(
                id = this.berthId,
                type = AreaType.BERTH,
                name = this.berthName,
                unlocode = this.port
            ),
            berth = BerthIdentifier(
                direction = null,
                terminalName = this.terminalName
            ),
            heading = this.heading
        )
    } else {
        return UniqueBerthEndEvent(
            _id = this.uuid,
            berthEventId = this.relatedEvent,
            berthConfirmedEventId = this.relatedEvent,
            startEventId = null,
            ship = this.toAisShipIdentifier(),
            location = this.location.toLocation(),
            createdTime = Instant.ofEpochMilli(this.datetime),
            actualTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
            deleted = this.isDeleted,
            draught = this.draught,
            area = AreaIdentifier(
                id = this.berthId,
                type = AreaType.BERTH,
                name = this.berthName,
                unlocode = this.port
            ),
            berth = BerthIdentifier(
                direction = null,
                terminalName = this.terminalName
            ),
            heading = this.heading
        )
    }
}

fun BerthEvent.toAisEngineEvent(): AreaEvent {
    if (this.isStartEvent) {
        return AreaStartEvent(
            _id = this.uuid,
            ship = this.toAisShipIdentifier(),
            location = this.location.toLocation(),
            createdTime = Instant.ofEpochMilli(this.datetime),
            actualTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
            deleted = this.isDeleted,
            draught = this.draught,
            area = AreaIdentifier(
                id = this.berthId,
                type = AreaType.BERTH,
                name = this.berthName,
                unlocode = this.port
            ),
            berth = BerthIdentifier(
                direction = null,
                terminalName = this.terminalName
            ),
            heading = this.heading,
            speedOverGround = null
        )
    } else {
        return AreaEndEvent(
            _id = this.uuid,
            startEventId = this.relatedEvent,
            ship = this.toAisShipIdentifier(),
            location = this.location.toLocation(),
            createdTime = Instant.ofEpochMilli(this.datetime),
            actualTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
            deleted = this.isDeleted,
            draught = this.draught,
            area = AreaIdentifier(
                id = this.berthId,
                type = AreaType.BERTH,
                name = this.berthName,
                unlocode = this.port
            ),
            berth = BerthIdentifier(
                direction = null,
                terminalName = this.terminalName
            ),
            heading = this.heading,
            speedOverGround = null
        )
    }
}

fun ShipInfraEncounterEvent.toAisEngineEvent(): AreaEvent {
    val areaType = if (this.type == TeqplayEvent.ENCOUNTERBRIDGE) {
        AreaType.BRIDGE
    } else {
        AreaType.LOCK
    }

    if (this.relatedEvent == null) {
        return AreaStartEvent(
            _id = this.uuid,
            ship = this.toAisShipIdentifier(),
            location = this.location.toLocation(),
            createdTime = Instant.ofEpochMilli(this.datetime),
            actualTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
            deleted = this.isDeleted,
            draught = null,
            area = AreaIdentifier(
                id = null,
                type = areaType,
                name = this.isrsId,
                unlocode = null
            ),
            berth = null,
            heading = this.shipHeading,
            speedOverGround = null
        )
    } else {
        return AreaEndEvent(
            _id = this.uuid,
            startEventId = this.relatedEvent,
            ship = this.toAisShipIdentifier(),
            location = this.location.toLocation(),
            createdTime = Instant.ofEpochMilli(this.datetime),
            actualTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
            deleted = this.isDeleted,
            draught = null,
            area = AreaIdentifier(
                id = null,
                type = areaType,
                name = this.isrsId,
                unlocode = null
            ),
            berth = null,
            heading = this.shipHeading,
            speedOverGround = null
        )
    }
}

fun PlatfromShipMovingEvent.toAisEngineEvent(): ShipMovingEvent? {
    if (this.isStartEvent) {
        return ShipMovingStartEvent(
            _id = this.uuid,
            ship = this.toAisShipIdentifier(),
            location = this.location.toLocation(),
            createdTime = Instant.ofEpochMilli(this.datetime),
            actualTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
            deleted = this.isDeleted
        )
    } else {
        return ShipMovingEndEvent(
            _id = this.uuid,
            startEventId = this.relatedEvent,
            ship = this.toAisShipIdentifier(),
            location = this.location.toLocation(),
            createdTime = Instant.ofEpochMilli(this.datetime),
            actualTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
            deleted = this.isDeleted
        )
    }
}

fun AisRecoveredEvent.toAisEngineEvent(): AisRecoverEvent {
    return AisRecoverEvent(
        _id = this.uuid,
        ship = this.toAisShipIdentifier(),
        location = this.location.toLocation(),
        createdTime = Instant.ofEpochMilli(this.datetime),
        actualTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
        deleted = this.isDeleted
    )
}

fun PlatformAisLostEvent.toAisEngineEvent(): AisLostEvent {
    return AisLostEvent(
        _id = this.uuid,
        ship = this.toAisShipIdentifier(),
        location = this.location.toLocation(),
        createdTime = Instant.ofEpochMilli(this.datetime),
        actualTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
        deleted = this.isDeleted
    )
}

fun PlatformPortcallPilotBoardingEtaEvent.toAisEngineEvent(): PortcallPilotBoardingEtaEvent? {
    // We can't convert this event if it doesn't contain a portcall id
    if (this.ucrn == null) {
        return null
    }

    return PortcallPilotBoardingEtaEvent(
        _id = this.uuid,
        ship = AisShipIdentifier(this.shipMmsi.toInt(), this.imo?.toIntOrNull() ?: this.shipImo?.toIntOrNull()),
        portcallId = this.ucrn,
        area = AreaIdentifier(
            id = this.estLocationId,
            type = AreaType.PILOT_BOARDING_PLACE,
            name = this.estLocation,
            unlocode = this.port
        ),
        source = this.source,
        createdTime = Instant.ofEpochMilli(this.datetime),
        predictedTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
        deleted = this.isDeleted
    )
}

private fun determineAreaTypeForEtaEtd(areaName: String?): AreaType {
    val areaType = when (areaName) {
        "port" -> AreaType.PORT
        "berth" -> AreaType.BERTH
        "pilotBoardingPlace" -> AreaType.PILOT_BOARDING_PLACE
        "lock" -> AreaType.LOCK
        else -> AreaType.UNKNOWN
    }

    return areaType
}

fun PortcallBerthEtaEvent.toAisEngineEvent(): PortcallPredictionEvent? {
    // We can't convert this event if it doesn't contain a portcall id
    if (this.ucrn == null) {
        return null
    }

    val typeWithoutEtaPrefix = this.type.removePrefix("${TeqplayEvent.ETA}.")
    val areaType = determineAreaTypeForEtaEtd(typeWithoutEtaPrefix)

    if (isPortcallPlusEvent(this.movementId, this.externalVisitId)) {
        return PortcallPlusEtaBerthEvent(
            _id = this.uuid,
            portcallId = this.ucrn,
            ship = this.toGeneralShipIdentifier(),
            area = AreaIdentifier(
                id = this.estLocationId,
                type = areaType,
                name = this.estLocation,
                unlocode = this.port
            ),
            port = AreaIdentifier(
                id = null,
                type = AreaType.PORT,
                name = this.port,
                unlocode = this.port
            ),
            source = this.source,
            berth = this.estLocation,
            berthId = this.estLocationId,
            vesselAgent = this.vesselAgent,
            distanceInNm = this.distanceInNm,
            portcallBerthId = this.berthId,
            predictedTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
            createdTime = Instant.ofEpochMilli(this.datetime),
            deleted = this.isDeleted
        )
    } else {
        return HamisEtaBerthEvent(
            _id = this.uuid,
            portcallId = this.ucrn,
            ship = this.toGeneralShipIdentifier(),
            area = AreaIdentifier(
                id = this.estLocationId,
                type = areaType,
                name = this.estLocation,
                unlocode = this.port
            ),
            port = AreaIdentifier(
                id = null,
                type = AreaType.PORT,
                name = this.port,
                unlocode = this.port
            ),
            source = this.source,
            berth = this.estLocation,
            berthId = this.estLocationId,
            vesselAgent = this.vesselAgent,
            distanceInNm = this.distanceInNm,
            portcallBerthId = this.berthId,
            predictedTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
            createdTime = Instant.ofEpochMilli(this.datetime),
            deleted = this.isDeleted
        )
    }
}

fun PlatformEtaEvent.toAisEngineEvent(): PortcallPredictionEvent? {
    // We can't convert this event if it doesn't contain a portcall id
    if (this.ucrn == null) {
        return null
    }

    val typeWithoutEtaPrefix = this.type.removePrefix("${TeqplayEvent.ETA}.")
    val areaType = determineAreaTypeForEtaEtd(typeWithoutEtaPrefix)

    if (isPortcallPlusEvent(this.movementId, this.externalVisitId)) {
        return PortcallPlusEtaEvent(
            _id = this.uuid,
            portcallId = this.ucrn,
            ship = this.toGeneralShipIdentifier(),
            area = AreaIdentifier(
                id = this.estLocationId,
                type = areaType,
                name = this.estLocation,
                unlocode = this.port
            ),
            port = AreaIdentifier(
                id = null,
                type = AreaType.PORT,
                name = this.port,
                unlocode = this.port
            ),
            source = this.source,
            berth = this.estLocation,
            berthId = this.estLocationId,
            vesselAgent = this.vesselAgent,
            predictedTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
            createdTime = Instant.ofEpochMilli(this.datetime),
            deleted = this.isDeleted
        )
    } else {
        return HamisEtaEvent(
            _id = this.uuid,
            portcallId = this.ucrn,
            ship = this.toGeneralShipIdentifier(),
            area = AreaIdentifier(
                id = this.estLocationId,
                type = areaType,
                name = this.estLocation,
                unlocode = this.port
            ),
            port = AreaIdentifier(
                id = null,
                type = AreaType.PORT,
                name = this.port,
                unlocode = this.port
            ),
            source = this.source,
            berth = this.estLocation,
            berthId = this.estLocationId,
            vesselAgent = this.vesselAgent,
            predictedTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
            createdTime = Instant.ofEpochMilli(this.datetime),
            deleted = this.isDeleted
        )
    }
}

fun PlatformEtdEvent.toAisEngineEvent(): PortcallPredictionEvent? {
    // We can't convert this event if it doesn't contain a portcall id
    if (this.ucrn == null) {
        return null
    }

    val typeWithoutEtaPrefix = this.type.removePrefix("${TeqplayEvent.ETD}.")
    val areaType = determineAreaTypeForEtaEtd(typeWithoutEtaPrefix)

    // We only check externalVisitId here as this event doesn't include the movementId
    if (externalVisitId == null) {
        return PortcallPlusEtdEvent(
            _id = this.uuid,
            portcallId = this.ucrn,
            ship = this.toGeneralShipIdentifier(),
            area = AreaIdentifier(
                id = this.estLocationId,
                type = areaType,
                name = this.estLocation,
                unlocode = this.port
            ),
            port = AreaIdentifier(
                id = null,
                type = AreaType.PORT,
                name = this.port,
                unlocode = this.port
            ),
            source = this.source,
            berth = this.estLocation,
            berthId = this.estLocationId,
            vesselAgent = this.vesselAgent,
            predictedTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
            createdTime = Instant.ofEpochMilli(this.datetime),
            deleted = this.isDeleted
        )
    } else {
        return HamisEtdEvent(
            _id = this.uuid,
            portcallId = this.ucrn,
            ship = this.toGeneralShipIdentifier(),
            area = AreaIdentifier(
                id = this.estLocationId,
                type = areaType,
                name = this.estLocation,
                unlocode = this.port
            ),
            port = AreaIdentifier(
                id = null,
                type = AreaType.PORT,
                name = this.port,
                unlocode = this.port
            ),
            source = this.source,
            berth = this.estLocation,
            berthId = this.estLocationId,
            vesselAgent = this.vesselAgent,
            predictedTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
            createdTime = Instant.ofEpochMilli(this.datetime),
            deleted = this.isDeleted
        )
    }
}

fun AddPortcallVisitEvent.toAisEngineEvent(): HamisAddPortcallVisitEvent? {
    // We can't convert this event if it doesn't contain a portcall id
    if (this.ucrn == null) {
        return null
    }

    return HamisAddPortcallVisitEvent(
        _id = this.uuid,
        portcallId = this.ucrn,
        ship = this.toGeneralShipIdentifier(),
        port = AreaIdentifier(
            id = null,
            type = AreaType.PORT,
            name = this.port,
            unlocode = this.port
        ),
        berthName = this.berthName,
        berthOwnerId = this.berthOwnerId,
        visitIndex = this.visitIndex,
        createdTime = Instant.ofEpochMilli(this.datetime),
        deleted = this.isDeleted
    )
}

fun UpdatePortcallVisitEvent.toAisEngineEvent(): HamisUpdatePortcallVisitEvent? {
    // We can't convert this event if it doesn't contain a portcall id
    if (this.ucrn == null) {
        return null
    }

    return HamisUpdatePortcallVisitEvent(
        _id = this.uuid,
        portcallId = this.ucrn,
        ship = this.toGeneralShipIdentifier(),
        port = AreaIdentifier(
            id = null,
            type = AreaType.PORT,
            name = this.port,
            unlocode = this.port
        ),
        berthName = this.berthName,
        berthOwnerId = this.berthOwnerId,
        previousBerthName = this.previousBerthName,
        previousBerthOwnerId = this.previousBerthOwnerId,
        visitIndex = this.visitIndex,
        createdTime = Instant.ofEpochMilli(this.datetime),
        deleted = this.isDeleted
    )
}

fun CancelPortcallVisitEvent.toAisEngineEvent(): HamisCancelPortcallVisitEvent? {
    // We can't convert this event if it doesn't contain a portcall id
    if (this.ucrn == null) {
        return null
    }

    return HamisCancelPortcallVisitEvent(
        _id = this.uuid,
        portcallId = this.ucrn,
        ship = this.toGeneralShipIdentifier(),
        port = AreaIdentifier(
            id = null,
            type = AreaType.PORT,
            name = this.port,
            unlocode = this.port
        ),
        berthName = this.berthName,
        berthOwnerId = this.berthOwnerId,
        visitIndex = this.visitIndex,
        createdTime = Instant.ofEpochMilli(this.datetime),
        deleted = this.isDeleted
    )
}

fun AgentOrderEvent.toAisEngineEvent(): HamisAgentOrderEvent? {
    // We can't convert this event if it doesn't contain a portcall id
    // In the past orders sometimes had no order, no reason to support them
    if (this.ucrn == null || this.order == null) {
        return null
    }

    return HamisAgentOrderEvent(
        _id = this.uuid,
        portcallId = this.ucrn,
        ship = this.toGeneralShipIdentifier(),
        port = AreaIdentifier(
            id = null,
            type = AreaType.PORT,
            name = this.port,
            unlocode = this.port
        ),
        order = this.order,
        vesselAgent = this.vesselAgent,
        movementId = this.movementId,
        createdTime = Instant.ofEpochMilli(this.datetime),
        deleted = this.isDeleted
    )
}

fun AgentReportsTugsEvent.toAisEngineEvent(): HamisAgentReportsTugsEvent? {
    // We can't convert this event if it doesn't contain a portcall id
    if (this.ucrn == null) {
        return null
    }

    return HamisAgentReportsTugsEvent(
        _id = this.uuid,
        portcallId = this.ucrn,
        ship = this.toGeneralShipIdentifier(),
        serviceShip = this.serviceShip,
        port = AreaIdentifier(
            id = null,
            type = AreaType.PORT,
            name = this.port,
            unlocode = this.port
        ),
        berth = AreaIdentifier(
            id = null,
            type = AreaType.BERTH,
            name = this.berth,
            unlocode = this.port
        ),
        towingCompany = this.towingCompany,
        vesselAgent = this.vesselAgent,
        movementId = this.movementId,
        externalVisitId = this.externalVisitId,
        reportedTime = Instant.ofEpochMilli(this.reportedTime),
        createdTime = Instant.ofEpochMilli(this.datetime),
        deleted = this.isDeleted
    )
}

fun EtaCancelEvent.toAisEngineEvent(): HamisEtaCancelEvent? {
    // We can't convert this event if it doesn't contain a portcall id
    if (this.ucrn == null) {
        return null
    }

    return HamisEtaCancelEvent(
        _id = this.uuid,
        portcallId = this.ucrn,
        ship = this.toGeneralShipIdentifier(),
        port = AreaIdentifier(
            id = null,
            type = AreaType.PORT,
            name = this.port,
            unlocode = this.port
        ),
        vesselAgent = this.vesselAgent,
        movementId = this.movementId,
        createdTime = Instant.ofEpochMilli(this.datetime),
        deleted = this.isDeleted
    )
}

fun EtaRequestEvent.toAisEngineEvent(): HamisEtaRequestEvent? {
    // We can't convert this event if it doesn't contain a portcall id
    // The eta field is not filled if it couldn't be parsed
    if (this.ucrn == null || this.eta == null) {
        return null
    }

    return HamisEtaRequestEvent(
        _id = this.uuid,
        portcallId = this.ucrn,
        ship = this.toGeneralShipIdentifier(),
        port = AreaIdentifier(
            id = null,
            type = AreaType.PORT,
            name = this.port,
            unlocode = this.port
        ),
        berth = AreaIdentifier(
            id = null,
            type = AreaType.BERTH,
            name = this.berth,
            unlocode = this.port
        ),
        pilotStation = this.pilotStation,
        berthOwnerId = this.berthOwnerId,
        vesselAgent = this.vesselAgent,
        movementId = this.movementId,
        externalVisitId = this.externalVisitId,
        predictedTime = Instant.ofEpochMilli(this.eta),
        createdTime = Instant.ofEpochMilli(this.datetime),
        deleted = this.isDeleted
    )
}

fun EtdRequestEvent.toAisEngineEvent(): HamisEtdRequestEvent? {
    // We can't convert this event if it doesn't contain a portcall id
    // The etd field is not filled if it couldn't be parsed
    if (this.ucrn == null || this.etd == null) {
        return null
    }

    return HamisEtdRequestEvent(
        _id = this.uuid,
        portcallId = this.ucrn,
        ship = this.toGeneralShipIdentifier(),
        port = AreaIdentifier(
            id = null,
            type = AreaType.PORT,
            name = this.port,
            unlocode = this.port
        ),
        berth = AreaIdentifier(
            id = null,
            type = AreaType.BERTH,
            name = this.berth,
            unlocode = this.port
        ),
        pilotStation = this.pilotStation,
        berthOwnerId = this.berthOwnerId,
        vesselAgent = this.vesselAgent,
        movementId = this.movementId,
        externalVisitId = this.externalVisitId,
        predictedTime = Instant.ofEpochMilli(this.etd),
        createdTime = Instant.ofEpochMilli(this.datetime),
        deleted = this.isDeleted
    )
}

fun PlatformHamisPilotBoardingEtaEvent.toAisEngineEvent(): HamisPilotBoardingEtaEvent? {
    // We can't convert this event if it doesn't contain a portcall id
    // Some events are also bugged where the ETA in the event is null
    if (this.eta == null || this.ucrn == null) {
        return null
    }

    return HamisPilotBoardingEtaEvent(
        _id = this.uuid,
        portcallId = this.ucrn,
        ship = this.toGeneralShipIdentifier(),
        port = AreaIdentifier(
            id = null,
            type = AreaType.PORT,
            name = this.port,
            unlocode = this.port
        ),
        pilotStation = this.pilotStation,
        vesselAgent = this.vesselAgent,
        movementId = this.movementId,
        source = this.source,
        predictedTime = Instant.ofEpochMilli(this.eta),
        createdTime = Instant.ofEpochMilli(this.datetime),
        deleted = this.isDeleted
    )
}

fun NauticalOrderEvent.toAisEngineEvent(): HamisNauticalOrderEvent? {
    // We can't convert this event if it doesn't contain a portcall id
    if (this.ucrn == null) {
        return null
    }

    return HamisNauticalOrderEvent(
        _id = this.uuid,
        portcallId = this.ucrn,
        ship = this.toGeneralShipIdentifier(),
        port = AreaIdentifier(
            id = null,
            type = AreaType.PORT,
            name = this.port,
            unlocode = this.port
        ),
        movementId = this.movementId,
        reporter = this.reporter,
        createdTime = Instant.ofEpochMilli(this.datetime),
        deleted = this.isDeleted
    )
}

fun PlatformPilotOnBoardEvent.toAisEngineEvent(): Event? {
    // We can't convert this event if it doesn't contain a portcall id
    if (this.ucrn == null) {
        return null
    }

    if (this.isStartEvent) {
        return HamisPilotOnBoardStartEvent(
            _id = this.uuid,
            portcallId = this.ucrn,
            ship = this.toGeneralShipIdentifier(),
            port = AreaIdentifier(
                id = null,
                type = AreaType.PORT,
                name = this.port,
                unlocode = this.port
            ),
            vesselAgent = this.vesselAgent,
            movementId = this.movementId,
            pilotReason = this.pilotReason?.toMovementType(),
            fromBerth = this.fromBerth,
            toBerth = this.toBerth,
            actualTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
            createdTime = Instant.ofEpochMilli(this.datetime),
            deleted = this.isDeleted
        )
    } else {
        return HamisPilotOnBoardEndEvent(
            _id = this.uuid,
            startEventId = this.relatedEvent,
            portcallId = this.ucrn,
            ship = this.toGeneralShipIdentifier(),
            port = AreaIdentifier(
                id = null,
                type = AreaType.PORT,
                name = this.port,
                unlocode = this.port
            ),
            vesselAgent = this.vesselAgent,
            movementId = this.movementId,
            pilotReason = this.pilotReason?.toMovementType(),
            fromBerth = this.fromBerth,
            toBerth = this.toBerth,
            actualTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
            createdTime = Instant.ofEpochMilli(this.datetime),
            deleted = this.isDeleted
        )
    }
}

fun VisitCancellationEvent.toAisEngineEvent(): HamisVisitCancellationEvent? {
    // We can't convert this event if it doesn't contain a portcall id
    if (this.ucrn == null) {
        return null
    }

    return HamisVisitCancellationEvent(
        _id = this.uuid,
        portcallId = this.ucrn,
        ship = this.toGeneralShipIdentifier(),
        port = AreaIdentifier(
            id = null,
            type = AreaType.PORT,
            name = this.port,
            unlocode = this.port
        ),
        shipName = this.shipName,
        createdTime = Instant.ofEpochMilli(this.datetime),
        deleted = this.isDeleted
    )
}

fun VisitDeclarationEvent.toAisEngineEvent(): HamisVisitDeclarationEvent? {
    // We can't convert this event if it doesn't contain a portcall id
    if (this.ucrn == null) {
        return null
    }

    return HamisVisitDeclarationEvent(
        _id = this.uuid,
        portcallId = this.ucrn,
        ship = this.toGeneralShipIdentifier(),
        port = AreaIdentifier(
            id = null,
            type = AreaType.PORT,
            name = this.port,
            unlocode = this.port
        ),
        previousPort = AreaIdentifier(
            id = null,
            type = AreaType.PORT,
            name = this.previousPort,
            unlocode = this.previousPort
        ),
        vesselAgent = this.vesselAgent,
        shipName = this.shipName,
        createdTime = Instant.ofEpochMilli(this.datetime),
        deleted = this.isDeleted
    )
}

fun PlatformLockEvent.toAisEngineEvent(): LockEvent? {
    val lockArea = AreaIdentifier(
        id = null,
        type = AreaType.LOCK,
        name = this.lockName,
        unlocode = this.port
    )

    return when (this.type) {
        TeqplayEvent.ETA_LOCK -> {
            LockEtaEvent(
                _id = this.uuid,
                isrsId = this.lockId,
                ship = this.toAisShipIdentifier(),
                area = lockArea,
                location = this.location.toLocation(),
                direction = this.direction.toLockDirection(),
                predictedTime = Instant.ofEpochMilli(this.dateTimePlan),
                actualTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
                createdTime = Instant.ofEpochMilli(this.datetime),
                deleted = this.isDeleted
            )
        }
        TeqplayEvent.ETD_LOCK -> {
            LockEtdEvent(
                _id = this.uuid,
                isrsId = this.lockId,
                ship = this.toAisShipIdentifier(),
                area = lockArea,
                location = this.location.toLocation(),
                direction = this.direction.toLockDirection(),
                predictedTime = Instant.ofEpochMilli(this.dateTimePlan),
                actualTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
                createdTime = Instant.ofEpochMilli(this.datetime),
                deleted = this.isDeleted
            )
        }
        else -> null
    }
}

fun AgentChangedEvent.toAisEngineEvent(): PortcallPlusAgentChangedEvent? {
    // We can't convert to an AisEngine agent changed event if no agent is provided
    val vesselAgent = this.vesselAgent ?: return null

    return PortcallPlusAgentChangedEvent(
        _id = this.uuid,
        ship = this.toGeneralShipIdentifier(),
        portcallId = this.uuid,
        port = AreaIdentifier(
            id = null,
            type = AreaType.PORT,
            name = this.port,
            unlocode = this.port
        ),
        vesselAgent = vesselAgent,
        source = this.source,
        actualTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
        createdTime = Instant.ofEpochMilli(this.datetime),
        deleted = this.isDeleted,
        regenerated = null
    )
}

private fun PortcallFinishEvent.toAisEngineEvent(): PortcallPlusPortcallFinishEvent {
    return PortcallPlusPortcallFinishEvent(
        _id = this.uuid,
        ship = this.toGeneralShipIdentifier(),
        portcallId = this.portcallId,
        port = AreaIdentifier(
            id = null,
            type = AreaType.PORT,
            name = this.port,
            unlocode = this.port
        ),
        actualTime = Instant.ofEpochMilli(this.eventTime ?: this.datetime),
        createdTime = Instant.ofEpochMilli(this.datetime),
        deleted = this.isDeleted,
        regenerated = null
    )
}

private fun PortcallShipChangedEvent.toAisEngineEvent(): PortcallPlusShipChangedEvent? {
    val portcallId = this.portcallId ?: return null

    return PortcallPlusShipChangedEvent(
        _id = this.uuid,
        ship = this.toGeneralShipIdentifier(),
        portcallId = portcallId,
        port = AreaIdentifier(
            id = null,
            type = AreaType.PORT,
            name = this.port,
            unlocode = this.port
        ),
        newShip = this.toOtherGeneralShipIdentifier(),
        createdTime = Instant.ofEpochMilli(this.datetime),
        deleted = this.isDeleted,
        regenerated = null
    )
}

private fun PlatformPilotOnBoardEvent.MovementType.toMovementType(): HamisPilotOnBoardEvent.MovementType {
    return when (this) {
        PlatformPilotOnBoardEvent.MovementType.ARRIVAL -> HamisPilotOnBoardEvent.MovementType.ARRIVAL
        PlatformPilotOnBoardEvent.MovementType.DEPARTURE -> HamisPilotOnBoardEvent.MovementType.DEPARTURE
        PlatformPilotOnBoardEvent.MovementType.SHIFT -> HamisPilotOnBoardEvent.MovementType.SHIFT
        PlatformPilotOnBoardEvent.MovementType.TRANSIT -> HamisPilotOnBoardEvent.MovementType.TRANSIT
    }
}

private fun PlatformLockDirection.toLockDirection(): LockDirection {
    return when (this) {
        PlatformLockDirection.INBOUND -> LockDirection.INBOUND
        PlatformLockDirection.OUTBOUND -> LockDirection.OUTBOUND
    }
}
