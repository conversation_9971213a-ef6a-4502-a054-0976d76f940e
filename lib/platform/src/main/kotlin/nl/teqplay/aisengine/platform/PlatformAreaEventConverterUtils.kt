package nl.teqplay.aisengine.platform

import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.aisengine.event.model.AreaEndEvent
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier.AreaType
import nl.teqplay.platform.model.event.TeqplayEvent

fun AreaEvent.getCorrectedEventType(area: AreaIdentifier, start: Boolean = true): String {
    val suffix = if (start) {
        TeqplayEvent.START
    } else {
        TeqplayEvent.END
    }

    if (area.type == AreaType.NAUTICAL_MILE) {
        return getNauticalMileAreaEventType(area, suffix)
    }

    var eventType = when (area.name?.lowercase()) {
        "nlrtm_sea_entrance" -> "area.nlrtm.sea-entrance"
        "nlams_sea_entrance" -> "area.nlams.sea-entrance"
        "beanr_sea_entrance" -> "area.beanr.sea-entrance"
        "kanaal van gent naar terneuzen" -> "area.harbourorigin.south"
        "sea near vlissingen" -> "area.harbourorigin.sea"
        "kanaal door zuid-beverland" -> "area.harbourorigin.north"
        "north vlissingen" -> "area.harbourorigin.northvlissingen"
        "vts area rotterdam" -> "area.vts.rtm"
        "lagelicht" -> "area.nlrtm.lagelicht"
        "rtmpilotarea_lng" -> "area.rtmpilotarea.lng"
        "rtmpilotarea_inland" -> "area.rtmpilotarea.inland"
        "rtmpilotarea_deepdraught" -> "area.rtmpilotarea.deepdraught"
        "rtmpilotarea" -> "area.rtmpilotarea.maasmond"
        "rotterdam maasmond" -> "area.nlrtm.approach.rtmmaasmond"
        "rtmfairway" -> "area.rtmfairway"
        "conde anchorage" -> "area.anchor.conde"
        "usnyc pilot area" -> "area.usnyc.anchor.newyorkpilotarea"
        "houston pilot area" -> "area.ushou.pilotarea"
        "morgans point" -> "area.ushou.approach.morganspoint"
        "fred hartman bridge" -> "area.ushou.approach.fredhartman"
        "sgsin_pilotarea_pwbgb" -> "area.sgsin.pilotarea_pwbgb"
        "sgsin_pilotarea_pwbga" -> "area.sgsin.pilotarea_pwbga"
        "sgsinpilotarea_psbg" -> "area.sgsin.pilotarea_psbg"
        "sgsinpilotarea_pjsb" -> "area.sgsin.pilotarea_pjsb"
        "sgsin_pilotarea_pebgb" -> "area.sgsin.pilotarea_pebgb"
        "sgsin_pilotarea_pebga" -> "area.sgsin.pilotarea_pebga"
        "segotpilotarea" -> "area.segot.pilotarea"
        "fairway_segot" -> "area.segot.fairway"
        "port_approach_segot" -> "area.segot.approach"
        "westsluis terneuzen" -> "area.nltnz.lock.westsluis_terneuzen"
        "oostsluis terneuzen" -> "area.nltnz.lock.oostsluis_terneuzen"
        "middensluis terneuzen" -> "area.nltnz.lock.middensluis_terneuzen"
        "zeesluis_ijmuiden" -> "area.nlams.lock.zeesluisijmuiden"
        "wilhelminasluis" -> "area.nlams.lock.wilhelminasluis"
        "spaarndam_grotesluis" -> "area.nlams.lock.spaarndam.grotesluis"
        "rijnlandsluizen" -> "area.nlams.lock.rijnlandsluizen"
        "prinswillemalexandersluis" -> "area.nlams.lock.prinswillemalexandersluis"
        "nlamspilotareadeepsea" -> "area.nlams.pilotarea.deepsea"
        "nlamspilotarea" -> "area.nlams.pilotarea"
        "oranjesluizen_zuid" -> "area.nlams.lock.oranjesluizen.zuid"
        "oranjesluizen_noord" -> "area.nlams.lock.oranjesluizen.noord"
        "oranjesluizen_midden" -> "area.nlams.lock.oranjesluizen.midden"
        "noordersluis_zuid" -> "area.nlams.lock.noordersluis.zuid"
        "noordersluis_noord" -> "area.nlams.lock.noordersluis.noord"
        "noordersluis_midden" -> "area.nlams.lock.noordersluis.midden"
        "amsterdam port approach point" -> "area.nlams.approach.portapproachpoint"
        "tangier med passengers port (maptm)" -> "area.maptm.basin.med_passengers"
        "tangier med 2 (maptm)" -> "area.maptm.basin.med_2"
        "tangier med 1 (maptm)" -> "area.maptm.basin.med_1"
        "maptmpilotarea" -> "area.maptm.pilotarea"
        "port_approach_maptm" -> "area.maptm.approach"
        "vuosaari pilot boarding place" -> "area.fivss.pilotarea"
        "vuosaari approach south" -> "area.fivss.approach.south"
        "vuosaari approach east" -> "area.fivss.approach.east"
        "helsinki west harbour" -> "area.fihel.basin.west"
        "helsinki south harbour" -> "area.fihel.basin.south"
        "helsinki north pilot boarding place" -> "area.fihel.pilotarea"
        "helsinki approach west" -> "area.fihel.approach.west"
        "helsinki approach south" -> "area.fihel.approach.south"
        "helsinki approach east" -> "area.fihel.approach.east"
        "esalgpilotarea" -> "area.esalg.pilotarea"
        "north basin (esalg)" -> "area.esalg.basin.nort_basin"
        // pronto
        "emergency" -> "area.anchor.emergency"
        "santana anchor 1" -> "area.anchor.santana2"
        "santana anchor 2" -> "area.anchor.santana1"
        "macapa anchorage" -> "area.anchor.macapa"
        "belem anchorage 2" -> "area.anchor.conde"
        "5" -> "area.anchor.5west"
        "4west" -> "area.anchor.4west"
        "4east" -> "area.anchor.4east"
        "3south" -> "area.anchor.3south"
        "3north" -> "area.anchor.3north"
        "2" -> "area.anchor.2"
        "1" -> "area.anchor.1"
        "houston south anchorage" -> "area.anchor.USHOU_anchor_south"
        "houston north anchorage" -> "area.anchor.USHOU_anchor_north"
        "houston center anchorage" -> "area.anchor.USHOU_anchor_center"
        "western anchorage" -> "area.sgsin.anchor.western"
        "west jurong anchorage" -> "area.sgsin.anchor.west_jurong"
        "sudong anchorage" -> "area.sgsin.anchor.sudong"
        "selat pauh anchorage" -> "area.sgsin.anchor.selat_pauh"
        "raffles anchorage" -> "area.sgsin.anchor.raffles"
        "eastern pet anchorage" -> "area.sgsin.anchor.eastern_pet"
        "eastern bunkering a anchorage" -> "area.sgsin.anchor.eastern_bunkering_a"
        "eastern anchorage" -> "area.sgsin.anchor.eastern"
        "anchorc" -> "area.segot.anchor.area_c"
        "anchorb" -> "area.segot.anchor.area_b"
        "anchora" -> "area.segot.anchor.area_a"
        "anchorplace 8" -> "area.nlams.anchor.place8"
        "anchorplace 7" -> "area.nlams.anchor.place7"
        "anchorplace 6" -> "area.nlams.anchor.place6"
        "anchorage" -> "area.maptm.anchor.anchorage"
        "foxtrot" -> "area.fivss.anchor.foxtrot"
        "anchor d" -> "area.fihel.anchor.d"
        "anchor area d (esalg)" -> "area.esalg.anchor.anchor_d"
        "anchor area c (esalg)" -> "area.esalg.anchor.anchor_c"
        "anchorage (esalg)" -> "area.esalg.anchor.anchorage"
        "anchor westhinder (beanr)" -> "area.beanr.anchor.westhinder"
        "anchor schouwenbank (beanr)" -> "area.beanr.anchor.schouwenbank"
        "anchor oostdyck (beanr)" -> "area.beanr.anchor.oostdyck"
        "anchor everingen (beanr)" -> "area.beanr.anchor.everingen"
        "new york anchorage 1" -> "area.usnyc.anchor.newyorkanchorage1"
        "new york anchorage - 6" -> "area.usldj.anchor.newyorkanchorage6"
        "new york anchorage south" -> "area.usnyc.anchor.newyorkanchoragesouth"
        "corpus christi anchorage 2" -> "area.uscrp.anchor.corpuschristianchorage2"
        "corpus christi anchorage 1" -> "area.uscrp.anchor.corpuschristianchorage1"
        "zeesluis ijmuiden" -> "area.nlams.lock.zeesluisijmuiden"
        "zandvlietsluis" -> "area.beanr.lock.zandvlietsluis"
        "van_cauwelaertsluis" -> "area.beanr.lock.vancauwelaertsluis"
        "royerssluis" -> "area.beanr.lock.royerssluis"
        "kieldrechtsluis" -> "area.beanr.lock.kieldrechtsluis"
        "kattendijksluis" -> "area.beanr.lock.kattendijksluis"
        "kallosluis" -> "area.beanr.lock.kallosluis"
        "boudewijnsluis" -> "area.beanr.lock.boudewijnsluis"
        "berendrechtsluis" -> "area.beanr.lock.berendrechtsluis"
        "pilot area mosqueiro" -> "area.mosqueiro.pilotarea"
        "pilot area espadarte" -> "area.espadarte.pilotarea"
        "pilot area amazone" -> "area.amazone.pilotarea"
        "vuosaari pbp" -> "area.fivss.pilotarea"
        "helsinki south pilot boarding place" -> "area.fihel.pilotarea"
        "beanrpilotarea_wndlaar" -> "area.beanr.pilotarea_wandelaar"
        "beanrpilotarea_sb" -> "area.beanr.pilotarea_steenbank"
        "pbp flushing" -> "beanrpilotarea_flushing"
        "vuosari approach" -> "area.fivss.approach.south"
        "vuosaari approach area east" -> "area.fivss.approach.east"
        "helsinki approach area west" -> "area.fihel.approach.west"
        "helsinki approach area south" -> "area.fihel.approach.south"
        "helsinki approach area east" -> "area.fihel.approach.east"
        "saeftinghe" -> "area.beanr.approach.saeftinghe"
        else -> null
    }

    if (eventType == null) {
        eventType = "${TeqplayEvent.AREA}.${this.area.type.name.lowercase()}"
    }

    return "$eventType$suffix"
}

private fun getNauticalMileAreaEventType(area: AreaIdentifier, suffix: String): String {
    val nauticalMileDistance = area.id?.split('.')
        ?.lastOrNull()
        ?.removeSuffix("nm")
        ?.toIntOrNull()

    // NLRTM is the only port we have weird nm area event types
    if (area.unlocode == "NLRTM" && nauticalMileDistance != null) {
        val eventType = when (nauticalMileDistance) {
            12 -> "area.rtm12nm"
            60 -> "area.rtm60nm"
            80 -> "area.rtm80nm"
            120 -> "area.rtm120nm"
            // We only need backwards compatibility for the old nm areas, new ones can convert to the constant structure
            else -> null
        }

        if (eventType != null) {
            return "$eventType$suffix"
        }
    }

    val unlocode = area.unlocode

    // Only lower case ESALG, MAPTM and SGSIN for backwards compatibility
    val preparedUnlocode = if (unlocode == "ESALG" || unlocode == "MAPTM" || unlocode == "SGSIN") {
        unlocode.lowercase()
    } else {
        unlocode
    }

    // No custom event type, so we can build a constant event type instead
    return "area.$preparedUnlocode.${nauticalMileDistance}nm$suffix"
}

fun AreaEvent.getStartEventId(): String? {
    if (this is AreaEndEvent) {
        return this.startEventId
    }
    return null
}
