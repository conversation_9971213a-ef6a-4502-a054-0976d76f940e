package nl.teqplay.aisengine.platform.model

import com.fasterxml.jackson.annotation.JsonInclude
import nl.teqplay.platform.model.Location
import nl.teqplay.platform.model.NamedHistoricShipInfo
import nl.teqplay.platform.model.ShipInfo

/**
 * Custom implementation for platform's [NamedHistoricShipInfo], to ensure non-nullability on required fields
 *
 * [JvmField] is used so the SQL parser can recognise the fields
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
class LegacyNamedHistoricShipInfo(
    mmsi: String,
    timeLastUpdate: Long,
    location: Location,

    status: ShipInfo.ShipStatus? = null,
    speedOverGround: Float? = null,
    courseOverGround: Float? = null,
    trueHeading: Int? = null,
    eta: Long? = null,
    destination: String? = null,
    source: String? = null,
    posAccuracyMeters: Double? = null,
    maxDraught: Float? = null,
    predicted: Boolean? = null,
    streamingIsNewUpdate: Boolean? = null,

    @JvmField val name: String? = null,
    @JvmField val shipType: ShipInfo.ShipType? = null,
    @JvmField val role: ShipInfo.ShipRole? = null,
) : LegacyHistoricShipInfo(
    mmsi,
    timeLastUpdate,
    location,
    status,
    speedOverGround,
    courseOverGround,
    trueHeading,
    eta,
    destination,
    source,
    posAccuracyMeters,
    maxDraught,
    predicted,
    streamingIsNewUpdate,
) {
    constructor(
        historicShipInfo: LegacyHistoricShipInfo,
        name: String?,
        shipType: ShipInfo.ShipType?,
        role: ShipInfo.ShipRole
    ) : this(
        mmsi = historicShipInfo.mmsi,
        timeLastUpdate = historicShipInfo.timeLastUpdate,
        location = historicShipInfo.location,
        status = historicShipInfo.status,
        speedOverGround = historicShipInfo.speedOverGround,
        courseOverGround = historicShipInfo.courseOverGround,
        trueHeading = historicShipInfo.trueHeading,
        eta = historicShipInfo.eta,
        destination = historicShipInfo.destination,
        source = historicShipInfo.source,
        posAccuracyMeters = historicShipInfo.posAccuracyMeters,
        maxDraught = historicShipInfo.maxDraught,
        predicted = historicShipInfo.predicted,
        streamingIsNewUpdate = historicShipInfo.streamingIsNewUpdate,
        name = name,
        shipType = shipType,
        role = role
    )
}
