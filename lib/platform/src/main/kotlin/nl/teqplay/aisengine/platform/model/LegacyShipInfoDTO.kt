package nl.teqplay.aisengine.platform.model

import com.fasterxml.jackson.annotation.JsonProperty
import nl.teqplay.platform.model.Location
import nl.teqplay.platform.model.ShipInfo

data class LegacyShipInfoDTO(
    @JsonProperty("_id")
    val uuid: String?,
    val teqplayId: String?,
    val mmsi: String,
    var eni: String?,
    val category: ShipInfo.ShipCat?,
    val status: ShipInfo.ShipStatus?,
    val speedOverGround: Float?,
    val location: Location?,
    val courseOverGround: Float?,
    val timeLastUpdate: Long?,
    val imoNumber: String?,
    val callSign: String?,
    val name: String?,
    val shipType: ShipInfo.ShipType?,
    val destination: String?,
    var trueDestination: String?,
    var calculatedHeading: Int?
) {

    constructor(
        shipInfo: ShipInfo
    ) : this(
        uuid = shipInfo.uuid,
        teqplayId = shipInfo.teqplayId,
        mmsi = shipInfo.mmsi,
        eni = shipInfo.eni,
        category = shipInfo.category,
        status = shipInfo.status,
        speedOverGround = shipInfo.speedOverGround,
        location = shipInfo.location,
        courseOverGround = shipInfo.courseOverGround,
        timeLastUpdate = shipInfo.timeLastUpdate,
        imoNumber = shipInfo.imoNumber,
        callSign = shipInfo.callSign,
        name = shipInfo.name,
        shipType = shipInfo.shipType,
        destination = shipInfo.destination,
        trueDestination = shipInfo.trueDestination,
        calculatedHeading = shipInfo.calculatedHeading
    )
}
