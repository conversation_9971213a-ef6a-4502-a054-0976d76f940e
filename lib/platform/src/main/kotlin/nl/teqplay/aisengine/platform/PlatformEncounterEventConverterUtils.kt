package nl.teqplay.aisengine.platform

import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType.AUTHORITY
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType.BARGE_BUNKER
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType.BARGE_CARGO
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType.BARGE_PUSH
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType.BARGE_SUPPLY
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType.BARGE_TANKER
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType.BARGE_WATER
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType.BOATMAN
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType.BUNKER
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType.CRANE
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType.FENDER
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType.LUBES
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType.PILOT
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType.SWOG
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType.TENDER
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType.TEQPERIMENT
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType.TUG
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType.TUG_WAITING_DEPARTURE
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType.UNCLASSIFIED
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType.WASTE
import nl.teqplay.aisengine.event.interfaces.EncounterEvent.EncounterType.WATER
import nl.teqplay.aisengine.event.model.EncounterEndEvent
import nl.teqplay.aisengine.event.model.EncounterStartEvent
import nl.teqplay.aisengine.event.model.encountermetadata.BoatmanEncounterMetadata
import nl.teqplay.aisengine.event.model.encountermetadata.TugEncounterMetadata
import nl.teqplay.platform.model.event.AuthorityEvent
import nl.teqplay.platform.model.event.BargeBunkerEvent
import nl.teqplay.platform.model.event.BargeWaterEvent
import nl.teqplay.platform.model.event.BoatmanEvent
import nl.teqplay.platform.model.event.BunkerEvent
import nl.teqplay.platform.model.event.CargoBargeEvent
import nl.teqplay.platform.model.event.CraneEvent
import nl.teqplay.platform.model.event.FenderEvent
import nl.teqplay.platform.model.event.LubesEvent
import nl.teqplay.platform.model.event.PilotEvent
import nl.teqplay.platform.model.event.PushBargeEvent
import nl.teqplay.platform.model.event.SupplyBargeEvent
import nl.teqplay.platform.model.event.SwogEvent
import nl.teqplay.platform.model.event.TankerBargeEvent
import nl.teqplay.platform.model.event.TenderEvent
import nl.teqplay.platform.model.event.TeqperimentEvent
import nl.teqplay.platform.model.event.TeqplayLocationBasedEvent
import nl.teqplay.platform.model.event.TugEvent
import nl.teqplay.platform.model.event.TugWaitingForDepartureEvent
import nl.teqplay.platform.model.event.WasteEvent
import nl.teqplay.platform.model.event.WaterEvent
import java.time.Instant

fun EncounterEvent.toPlatformEvent(): TeqplayLocationBasedEvent? {
    val summary = getTypeDescription(encounterType) + " " + otherShip.mmsi
    val (verb, direction) =
        if (this is EncounterStartEvent) "arrives" to "at"
        else "departs" to "from"
    val title = "$summary $verb"
    val description = "$title $direction ${ship.mmsi}"
    return when (encounterType) {
        TUG -> TugEvent(
            _id,
            this is EncounterStartEvent,
            false,
            (this.metadata as? TugEncounterMetadata)?.hasSimultaneousTugEncounter ?: false,
            actualTime.toEpochMilli(),
            (this as? EncounterEndEvent)?.startEventId,
            ship.toShipInfo(),
            otherShip.toShipInfo(),
            description, title, summary,
            location.toPlatformLocation(),
            probability
        )
        PILOT -> PilotEvent(
            _id,
            this is EncounterStartEvent,
            createdTime.toEpochMilli(),
            actualTime.toEpochMilli(),
            (this as? EncounterEndEvent)?.startEventId,
            ship.toShipInfo(),
            otherShip.toShipInfo(),
            description, title, summary,
            location.toPlatformLocation(),
            probability,
            false, false, null
        )
        BOATMAN -> {
            val metadata = this.metadata as? BoatmanEncounterMetadata

            BoatmanEvent(
                _id,
                this is EncounterStartEvent,
                metadata?.arrival ?: false,
                actualTime.toEpochMilli(),
                (this as? EncounterEndEvent)?.startEventId,
                ship.toShipInfo(),
                otherShip.toShipInfo(),
                description, title, summary,
                location.toPlatformLocation(),
                probability,
                metadata?.nrOfBoatmen ?: 0,
                metadata?.hasSimultaneousTugEncounter ?: false,
            )
        }
        BUNKER -> BunkerEvent(
            _id,
            this is EncounterStartEvent,
            actualTime.toEpochMilli(),
            (this as? EncounterEndEvent)?.startEventId,
            ship.toShipInfo(),
            otherShip.toShipInfo(),
            description, title, summary,
            location.toPlatformLocation(),
            probability
        )
        AUTHORITY -> AuthorityEvent(
            _id,
            this is EncounterStartEvent,
            actualTime.toEpochMilli(),
            (this as? EncounterEndEvent)?.startEventId,
            ship.toShipInfo(),
            otherShip.toShipInfo(),
            description, title, summary,
            location.toPlatformLocation(),
            probability
        )
        WASTE -> WasteEvent(
            _id,
            this is EncounterStartEvent,
            actualTime.toEpochMilli(),
            (this as? EncounterEndEvent)?.startEventId,
            ship.toShipInfo(),
            otherShip.toShipInfo(),
            description, title, summary,
            location.toPlatformLocation(),
            probability
        )
        SWOG -> SwogEvent(
            _id,
            this is EncounterStartEvent,
            actualTime.toEpochMilli(),
            (this as? EncounterEndEvent)?.startEventId,
            ship.toShipInfo(),
            otherShip.toShipInfo(),
            description, title, summary,
            location.toPlatformLocation(),
            probability
        )
        WATER -> WaterEvent(
            _id,
            this is EncounterStartEvent,
            actualTime.toEpochMilli(),
            (this as? EncounterEndEvent)?.startEventId,
            ship.toShipInfo(),
            otherShip.toShipInfo(),
            description, title, summary,
            location.toPlatformLocation(),
            probability
        )
        TENDER -> TenderEvent(
            _id,
            this is EncounterStartEvent,
            actualTime.toEpochMilli(),
            (this as? EncounterEndEvent)?.startEventId,
            ship.toShipInfo(),
            otherShip.toShipInfo(),
            description, title, summary,
            location.toPlatformLocation(),
            probability
        )
        FENDER -> FenderEvent(
            _id,
            this is EncounterStartEvent,
            actualTime.toEpochMilli(),
            (this as? EncounterEndEvent)?.startEventId,
            ship.toShipInfo(),
            otherShip.toShipInfo(),
            description, title, summary,
            location.toPlatformLocation(),
            probability
        )
        CRANE -> CraneEvent(
            _id,
            this is EncounterStartEvent,
            actualTime.toEpochMilli(),
            (this as? EncounterEndEvent)?.startEventId,
            ship.toShipInfo(),
            otherShip.toShipInfo(),
            description, title, summary,
            location.toPlatformLocation(),
            probability
        )
        LUBES -> LubesEvent(
            _id,
            this is EncounterStartEvent,
            actualTime.toEpochMilli(),
            (this as? EncounterEndEvent)?.startEventId,
            ship.toShipInfo(),
            otherShip.toShipInfo(),
            description, title, summary,
            location.toPlatformLocation(),
            probability
        )
        TUG_WAITING_DEPARTURE -> TugWaitingForDepartureEvent(
            _id,
            this is EncounterStartEvent,
            actualTime.toEpochMilli(),
            (this as? EncounterEndEvent)?.startEventId,
            ship.toShipInfo(),
            otherShip.toShipInfo(),
            description, title, summary,
            location.toPlatformLocation(),
            probability,
            if (this is EncounterEndEvent) valid else true
        )
        BARGE_SUPPLY -> SupplyBargeEvent(
            _id,
            this is EncounterStartEvent,
            actualTime.toEpochMilli(),
            (this as? EncounterEndEvent)?.startEventId,
            ship.toShipInfo(),
            otherShip.toShipInfo(),
            description, title, summary,
            location.toPlatformLocation(),
            probability
        )
        BARGE_CARGO -> CargoBargeEvent(
            _id,
            this is EncounterStartEvent,
            actualTime.toEpochMilli(),
            (this as? EncounterEndEvent)?.startEventId,
            ship.toShipInfo(),
            otherShip.toShipInfo(),
            description, title, summary,
            location.toPlatformLocation(),
            probability
        )
        BARGE_TANKER -> TankerBargeEvent(
            _id,
            this is EncounterStartEvent,
            actualTime.toEpochMilli(),
            (this as? EncounterEndEvent)?.startEventId,
            ship.toShipInfo(),
            otherShip.toShipInfo(),
            description, title, summary,
            location.toPlatformLocation(),
            probability
        )
        BARGE_PUSH -> PushBargeEvent(
            _id,
            this is EncounterStartEvent,
            actualTime.toEpochMilli(),
            (this as? EncounterEndEvent)?.startEventId,
            ship.toShipInfo(),
            otherShip.toShipInfo(),
            description, title, summary,
            location.toPlatformLocation(),
            probability
        )
        BARGE_WATER -> BargeWaterEvent(
            _id,
            this is EncounterStartEvent,
            actualTime.toEpochMilli(),
            (this as? EncounterEndEvent)?.startEventId,
            ship.toShipInfo(),
            otherShip.toShipInfo(),
            description, title, summary,
            location.toPlatformLocation(),
            probability
        )
        BARGE_BUNKER -> BargeBunkerEvent(
            _id,
            this is EncounterStartEvent,
            actualTime.toEpochMilli(),
            (this as? EncounterEndEvent)?.startEventId,
            ship.toShipInfo(),
            otherShip.toShipInfo(),
            description, title, summary,
            location.toPlatformLocation(),
            probability
        )
        TEQPERIMENT -> TeqperimentEvent(
            _id,
            this is EncounterStartEvent,
            actualTime.toEpochMilli(),
            (this as? EncounterEndEvent)?.startEventId,
            ship.toShipInfo(),
            otherShip.toShipInfo(),
            description, title, summary,
            location.toPlatformLocation(),
            probability
        )
        UNCLASSIFIED -> null
        else -> null
    }
}

fun fromPlatform(event: TeqplayLocationBasedEvent): EncounterEvent? {
    val encounterType = when (event) {
        is TugEvent -> TUG
        is PilotEvent -> PILOT
        is BoatmanEvent -> BOATMAN
        is BunkerEvent -> BUNKER
        is AuthorityEvent -> AUTHORITY
        is WasteEvent -> WASTE
        is SwogEvent -> SWOG
        is WaterEvent -> WATER
        is TenderEvent -> TENDER
        is FenderEvent -> FENDER
        is CraneEvent -> CRANE
        is LubesEvent -> LUBES
        is TugWaitingForDepartureEvent -> TUG_WAITING_DEPARTURE
        is SupplyBargeEvent -> BARGE_SUPPLY
        is CargoBargeEvent -> BARGE_CARGO
        is TankerBargeEvent -> BARGE_TANKER
        is PushBargeEvent -> BARGE_PUSH
        is BargeWaterEvent -> BARGE_WATER
        is BargeBunkerEvent -> BARGE_BUNKER
        else -> null
    }
    val ship = event.toAisShipIdentifier()
    val otherShip = event.toOtherAisShipIdentifier()

    return when {
        encounterType == null -> null
        event.isStartEvent -> EncounterStartEvent(
            _id = event.uuid,
            ship = ship,
            otherShip = otherShip,
            encounterType = encounterType,
            location = event.location.toLocation(),
            actualTime = Instant.ofEpochMilli(event.eventTime),
            createdTime = event.generatedTime?.let(Instant::ofEpochMilli) ?: Instant.now(),
            deleted = false,
            regenerated = false,
            probability = event.probability
        )
        event.isEndEvent -> EncounterEndEvent(
            _id = event.uuid,
            startEventId = event.relatedEvent,
            ship = ship,
            otherShip = otherShip,
            encounterType = encounterType,
            location = event.location.toLocation(),
            actualTime = Instant.ofEpochMilli(event.eventTime),
            createdTime = event.generatedTime?.let(Instant::ofEpochMilli) ?: Instant.now(),
            deleted = false,
            regenerated = false,
            probability = event.probability
        )
        else -> null
    }
}

fun getTypeDescription(encounterType: EncounterEvent.EncounterType): String = when (encounterType) {
    TUG -> "Tug"
    PILOT -> "Pilot"
    BOATMAN -> "Boatman"
    BUNKER -> "Bunker barge"
    AUTHORITY -> "Authority"
    WASTE -> "Waste barge"
    SWOG -> "Slops barge"
    WATER -> "Water barge"
    TENDER -> "Tender barge"
    FENDER -> "Fender barge"
    CRANE -> "Crane boat"
    LUBES -> "Lubes barge"
    TUG_WAITING_DEPARTURE -> "Tug"
    BARGE_SUPPLY -> "Supply barge"
    BARGE_CARGO -> "CargoOperations barge"
    BARGE_TANKER -> "Tanker barge"
    BARGE_PUSH -> "Push barge"
    BARGE_WATER -> "Water barge"
    BARGE_BUNKER -> "Bunker barge"
    UNCLASSIFIED -> "Unknown"
    TEQPERIMENT -> "Teqperiment"
    else -> "Unknown"
}
