package nl.teqplay.aisengine.platform.converter

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.event.annotation.PlatformConvertable
import nl.teqplay.aisengine.event.interfaces.AnchoredEvent
import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.interfaces.EventIdentifier
import nl.teqplay.aisengine.event.interfaces.HamisPilotOnBoardEvent
import nl.teqplay.aisengine.event.interfaces.LockEvent
import nl.teqplay.aisengine.event.interfaces.PortcallAgentChangedEvent
import nl.teqplay.aisengine.event.interfaces.PortcallFinishEvent
import nl.teqplay.aisengine.event.interfaces.RelatedBerthEvent
import nl.teqplay.aisengine.event.interfaces.ShipMovingEvent
import nl.teqplay.aisengine.event.interfaces.SpeedEvent
import nl.teqplay.aisengine.event.interfaces.StartEvent
import nl.teqplay.aisengine.event.model.AisDraughtChangedEvent
import nl.teqplay.aisengine.event.model.AisEtaChangedEvent
import nl.teqplay.aisengine.event.model.AisLostEvent
import nl.teqplay.aisengine.event.model.AisRecoverEvent
import nl.teqplay.aisengine.event.model.AisStatusChangedEvent
import nl.teqplay.aisengine.event.model.AnchoredEndEvent
import nl.teqplay.aisengine.event.model.AnchoredStartEvent
import nl.teqplay.aisengine.event.model.ConfirmedBerthEndEvent
import nl.teqplay.aisengine.event.model.ConfirmedBerthStartEvent
import nl.teqplay.aisengine.event.model.LockEtaEvent
import nl.teqplay.aisengine.event.model.LockEtdEvent
import nl.teqplay.aisengine.event.model.PortcallPilotBoardingEtaEvent
import nl.teqplay.aisengine.event.model.ShipMovingEndEvent
import nl.teqplay.aisengine.event.model.ShipMovingStartEvent
import nl.teqplay.aisengine.event.model.SpeedChangedEvent
import nl.teqplay.aisengine.event.model.TrueDestinationChangedEvent
import nl.teqplay.aisengine.event.model.UniqueBerthEndEvent
import nl.teqplay.aisengine.event.model.UniqueBerthStartEvent
import nl.teqplay.aisengine.event.model.hamis.HamisAddPortcallVisitEvent
import nl.teqplay.aisengine.event.model.hamis.HamisAgentOrderEvent
import nl.teqplay.aisengine.event.model.hamis.HamisAgentReportsTugsEvent
import nl.teqplay.aisengine.event.model.hamis.HamisAtaEvent
import nl.teqplay.aisengine.event.model.hamis.HamisAtdEvent
import nl.teqplay.aisengine.event.model.hamis.HamisCancelPortcallVisitEvent
import nl.teqplay.aisengine.event.model.hamis.HamisEtaCancelEvent
import nl.teqplay.aisengine.event.model.hamis.HamisEtaEvent
import nl.teqplay.aisengine.event.model.hamis.HamisEtaRequestEvent
import nl.teqplay.aisengine.event.model.hamis.HamisEtdEvent
import nl.teqplay.aisengine.event.model.hamis.HamisEtdRequestEvent
import nl.teqplay.aisengine.event.model.hamis.HamisNauticalOrderEvent
import nl.teqplay.aisengine.event.model.hamis.HamisPilotBoardingEtaEvent
import nl.teqplay.aisengine.event.model.hamis.HamisPilotOnBoardEndEvent
import nl.teqplay.aisengine.event.model.hamis.HamisPilotOnBoardStartEvent
import nl.teqplay.aisengine.event.model.hamis.HamisUpdatePortcallVisitEvent
import nl.teqplay.aisengine.event.model.hamis.HamisVisitCancellationEvent
import nl.teqplay.aisengine.event.model.hamis.HamisVisitDeclarationEvent
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier.AreaType
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusAtaEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusAtdEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusEtaBerthEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusEtaEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusEtdEvent
import nl.teqplay.aisengine.platform.getCorrectedEventType
import nl.teqplay.aisengine.platform.getStartEventId
import nl.teqplay.aisengine.platform.toLegacyStatus
import nl.teqplay.aisengine.platform.toPlatformEvent
import nl.teqplay.aisengine.platform.toPlatformLocation
import nl.teqplay.aisengine.platform.toShipInfo
import nl.teqplay.platform.model.event.AgentChangedEvent
import nl.teqplay.platform.model.event.AisRecoveredEvent
import nl.teqplay.platform.model.event.AtaEvent
import nl.teqplay.platform.model.event.AtdEvent
import nl.teqplay.platform.model.event.BerthEvent
import nl.teqplay.platform.model.event.ConfirmedBerthEvent
import nl.teqplay.platform.model.event.DestinationChangedEvent
import nl.teqplay.platform.model.event.DraughtChangedEvent
import nl.teqplay.platform.model.event.EndSeaPassageEvent
import nl.teqplay.platform.model.event.EtaEvent
import nl.teqplay.platform.model.event.EtdEvent
import nl.teqplay.platform.model.event.PortAtaAtdEvent
import nl.teqplay.platform.model.event.PortcallBerthEtaEvent
import nl.teqplay.platform.model.event.StatusChangedEvent
import nl.teqplay.platform.model.event.TeqplayEvent
import nl.teqplay.platform.model.event.TeqplayLocationBasedEvent
import nl.teqplay.platform.model.event.TerminalEvent
import nl.teqplay.platform.model.event.UniqueBerthEvent
import nl.teqplay.platform.model.event.VhfEvent
import nl.teqplay.platform.model.event.hbr.AddPortcallVisitEvent
import nl.teqplay.platform.model.event.hbr.AgentOrderEvent
import nl.teqplay.platform.model.event.hbr.AgentReportsTugsEvent
import nl.teqplay.platform.model.event.hbr.CancelPortcallVisitEvent
import nl.teqplay.platform.model.event.hbr.EtaCancelEvent
import nl.teqplay.platform.model.event.hbr.EtaRequestEvent
import nl.teqplay.platform.model.event.hbr.EtdRequestEvent
import nl.teqplay.platform.model.event.hbr.NauticalOrderEvent
import nl.teqplay.platform.model.event.hbr.PilotOnBoardEvent
import nl.teqplay.platform.model.event.hbr.UpdatePortcallVisitEvent
import nl.teqplay.platform.model.event.hbr.VisitCancellationEvent
import nl.teqplay.platform.model.event.hbr.VisitDeclarationEvent
import java.time.ZoneId
import kotlin.reflect.full.hasAnnotation
import nl.teqplay.platform.model.event.AisEtaChangedEvent as PlatformAisEtaChangedEvent
import nl.teqplay.platform.model.event.AisLostEvent as PlatformAisLostEvent
import nl.teqplay.platform.model.event.AnchoredEvent as PlatformAnchoredEvent
import nl.teqplay.platform.model.event.LockEvent as PlatformLockEvent
import nl.teqplay.platform.model.event.PortcallFinishEvent as PlatformPortcallFinishEvent
import nl.teqplay.platform.model.event.PortcallPilotBoardingEtaEvent as PlatformPortcallPilotBoardingEtaEvent
import nl.teqplay.platform.model.event.ShipMovingEvent as PlatformShipMovingEvent
import nl.teqplay.platform.model.event.SpeedChangedEvent as PlatformSpeedChangedEvent
import nl.teqplay.platform.model.event.hbr.HamisPilotBoardingEtaEvent as PlatformHamisPilotBoardingEtaEvent
import nl.teqplay.platform.model.lockplanning.LockDirection as PlatformLockDirection

private val LOG = KotlinLogging.logger {}

class PlatformEventConverter<out T : TeqplayEvent, in S : Event> {
    inline fun <reified U : TeqplayEvent> convert(input: S, berthIdConverter: BerthInformationResolver? = null): T? {
        val convertedEvent = convertToPlatformEvent(
            input = input,
            berthConverter = berthIdConverter
        )

        @Suppress("UNCHECKED_CAST")
        val castedEvent = convertedEvent as? T?

        return castedEvent?.takeIf { it is U }
    }
}

fun convertToPlatformEvent(input: Event, berthConverter: BerthInformationResolver? = null): TeqplayEvent? {
    val isConvertable = input::class.hasAnnotation<PlatformConvertable>()

    // New events can be ignored and returned directly
    if (!isConvertable) {
        return null
    }

    val convertedEvent = when (input) {
        is TrueDestinationChangedEvent -> input.toPlatformEvent()
        is AisDraughtChangedEvent -> input.toPlatformEvent()
        is AisEtaChangedEvent -> input.toPlatformEvent()
        is AisStatusChangedEvent -> input.toPlatformEvent()
        is SpeedChangedEvent -> input.toPlatformEvent()
        is AnchoredEvent -> input.toPlatformEvent()
        is ShipMovingEvent -> input.toPlatformEvent()
        is RelatedBerthEvent -> input.toPlatformEvent(berthConverter = berthConverter)
        is AreaEvent -> input.toPlatformAreaEvent(berthConverter = berthConverter)
        is EncounterEvent -> input.toPlatformEvent()
        is AisRecoverEvent -> input.toPlatformEvent()
        is AisLostEvent -> input.toPlatformEvent()
        is PortcallPilotBoardingEtaEvent -> input.toPlatformEvent()
        is HamisEtaEvent -> input.toPlatformEvent()
        is HamisEtdEvent -> input.toPlatformEvent()
        is HamisAtaEvent -> input.toPlatformEvent()
        is HamisAtdEvent -> input.toPlatformEvent()
        is HamisAddPortcallVisitEvent -> input.toPlatformEvent()
        is HamisUpdatePortcallVisitEvent -> input.toPlatformEvent()
        is HamisCancelPortcallVisitEvent -> input.toPlatformEvent()
        is HamisAgentOrderEvent -> input.toPlatformEvent()
        is HamisAgentReportsTugsEvent -> input.toPlatformEvent()
        is HamisEtaCancelEvent -> input.toPlatformEvent()
        is HamisEtaRequestEvent -> input.toPlatformEvent()
        is HamisEtdRequestEvent -> input.toPlatformEvent()
        is HamisPilotBoardingEtaEvent -> input.toPlatformEvent()
        is HamisNauticalOrderEvent -> input.toPlatformEvent()
        is HamisPilotOnBoardEvent -> input.toPlatformEvent()
        is HamisVisitCancellationEvent -> input.toPlatformEvent()
        is HamisVisitDeclarationEvent -> input.toPlatformEvent()
        is PortcallPlusAtaEvent -> input.toPlatformEvent()
        is PortcallPlusAtdEvent -> input.toPlatformEvent()
        is PortcallPlusEtaEvent -> input.toPlatformEvent()
        is PortcallPlusEtdEvent -> input.toPlatformEvent()
        is PortcallFinishEvent -> input.toPlatformEvent()
        is PortcallAgentChangedEvent -> input.toPlatformEvent()
        is PortcallPlusEtaBerthEvent -> input.toPlatformEvent()
        is LockEvent -> input.toPlatformEvent()

        // Event is not covered
        else -> null
    }

    if (convertedEvent == null) {
        LOG.warn {
            "Attempt to convert event wasn't successful." +
                " (subject: ${input.getSubject()}, class: ${input::class.java.simpleName})"
        }
    }

    return convertedEvent
}

fun TrueDestinationChangedEvent.toPlatformEvent(): DestinationChangedEvent {
    val description = "Destination changed (${this.oldValue} to ${this.newValue})"

    return DestinationChangedEvent(
        uuid = this._id,
        timestamp = this.createdTime.toEpochMilli(),
        shipMmsi = this.ship.mmsi.toString(),
        description = description,
        title = description,
        summary = description,
        location = this.location.toPlatformLocation(),
        imo = this.ship.imo?.toString(),
        oldDestination = this.oldValue,
        newDestination = this.newValue,
        newTrueDestination = this.trueDestination
    )
}

fun AisDraughtChangedEvent.toPlatformEvent(): DraughtChangedEvent {
    val description = "Draught changed (${this.oldValue}m to ${this.newValue}m)"

    return DraughtChangedEvent(
        this._id,
        this.createdTime.toEpochMilli(),
        this.ship.toShipInfo(),
        description,
        description,
        description,
        this.location.toPlatformLocation(),
        this.oldValue,
        this.newValue
    )
}

fun AisEtaChangedEvent.toPlatformEvent(): PlatformAisEtaChangedEvent {
    val description = "Eta changed in AIS (" + this.oldValue + " to " + this.newValue + ")"

    return PlatformAisEtaChangedEvent(
        uuid = this._id,
        timestamp = this.createdTime.toEpochMilli(),
        shipMmsi = this.ship.mmsi.toString(),
        description = description,
        title = description,
        summary = description,
        location = this.location.toPlatformLocation(),
        oldEta = this.oldValue?.toEpochMilli(),
        newEta = this.newValue?.toEpochMilli()
    )
}

fun AisStatusChangedEvent.toPlatformEvent(): StatusChangedEvent {
    val description = "AIS status changed (${this.oldValue} to ${this.newValue})"

    return StatusChangedEvent(
        uuid = this._id,
        timestamp = this.createdTime.toEpochMilli(),
        description = description,
        title = description,
        summary = description,
        ship = this.ship.toShipInfo(),
        location = this.location.toPlatformLocation(),
        oldStatus = this.oldValue?.toLegacyStatus(),
        newStatus = this.newValue?.toLegacyStatus()
    )
}

fun SpeedChangedEvent.toPlatformEvent(): PlatformSpeedChangedEvent {
    val speedType = if (this.speedType == SpeedEvent.SpeedType.ACCELERATING) {
        "Accelerating"
    } else {
        "Slowing down"
    }

    val description = "[${this.ship.mmsi}] is $speedType"
    val summary = "${this.ship.mmsi} is $speedType"
    val title = "is $speedType"

    return PlatformSpeedChangedEvent(
        this._id,
        this.createdTime.toEpochMilli(),
        this.ship.toShipInfo(),
        description,
        title,
        summary,
        this.location.toPlatformLocation()
    )
}

fun AnchoredEvent.toPlatformEvent(): PlatformAnchoredEvent {
    if (this is AnchoredStartEvent) {
        val description = "${this.ship.mmsi} started to be anchored at ${this.area.name}"
        return nl.teqplay.platform.model.event.AnchoredEvent(
            this._id,
            true,
            this.actualTime.toEpochMilli(),
            this.createdTime.toEpochMilli(),
            null,
            this.ship.toShipInfo(),
            this.ship.imo.toString(),
            description,
            description,
            description,
            this.location.toPlatformLocation(),
            this.area.name
        )
    } else {
        val description = "${this.ship.mmsi} stopped to be anchored at ${this.area.name}"
        val event = this as AnchoredEndEvent
        return nl.teqplay.platform.model.event.AnchoredEvent(
            this._id,
            false,
            event.actualTime.toEpochMilli(),
            event.createdTime.toEpochMilli(),
            event.startEventId,
            event.ship.toShipInfo(),
            event.ship.imo.toString(),
            description,
            description,
            description,
            event.location.toPlatformLocation(),
            event.area.name
        )
    }
}

fun ShipMovingEvent.toPlatformEvent(): PlatformShipMovingEvent {
    val description: String
    val title: String
    val summary = "moving"

    if (this is ShipMovingStartEvent) {
        description = "[${this.ship.mmsi}] started moving"
        title = "Started moving"
    } else {
        description = "[${this.ship.mmsi}] stopped moving"
        title = "Stopped moving"
    }

    return PlatformShipMovingEvent(
        this._id,
        (this is ShipMovingStartEvent),
        this.actualTime.toEpochMilli(),
        (this as? ShipMovingEndEvent)?.startEventId,
        this.ship.toShipInfo(),
        description,
        title,
        summary,
        this.location.toPlatformLocation()
    )
}

fun RelatedBerthEvent.toPlatformEvent(berthConverter: BerthInformationResolver?): TeqplayLocationBasedEvent? {
    val berthEvent = this.toPlatformAreaEvent(id = this.berthEventId, berthConverter = berthConverter) as? BerthEvent ?: return null

    return when (this) {
        is ConfirmedBerthStartEvent,
        is ConfirmedBerthEndEvent -> {
            ConfirmedBerthEvent(
                this._id,
                berthEvent,
                this.ship.toShipInfo(),
                this.createdTime.toEpochMilli(),
                null,
                null
            )
        }
        is UniqueBerthStartEvent,
        is UniqueBerthEndEvent -> {
            val confirmedBerthEvent = ConfirmedBerthEvent(
                this.getConfirmedEventId(),
                berthEvent,
                this.ship.toShipInfo(),
                this.createdTime.toEpochMilli(),
                null,
                null
            )

            UniqueBerthEvent(
                this._id,
                confirmedBerthEvent,
                this.ship.toShipInfo(),
                this.createdTime.toEpochMilli(),
                null,
                null
            )
        }
        else -> null
    }
}

private fun RelatedBerthEvent.getConfirmedEventId(): String? {
    when (this) {
        is UniqueBerthStartEvent -> {
            return this.berthConfirmedEventId
        }
        is UniqueBerthEndEvent -> {
            return this.berthConfirmedEventId
        }
    }
    return null
}

fun AreaEvent.toPlatformAreaEvent(id: EventIdentifier? = null, berthConverter: BerthInformationResolver?): TeqplayLocationBasedEvent {
    val title: String
    val description: String
    val summary: String
    val suffix: String
    val eventType: String

    val areaName = when (this.area.type) {
        AreaType.PORT -> this.area.unlocode ?: this.area.name
        AreaType.BERTH -> this.area.id?.let { berthConverter?.getPomaLongName(it) } ?: this.area.name
        else -> this.area.name
    }

    if (this is StartEvent) {
        title = "enters $areaName"
        description = "${this.ship.mmsi} enters $areaName"
        summary = "${this.ship.mmsi} in $areaName"
        suffix = TeqplayEvent.START
        eventType = this.getCorrectedEventType(this.area)
    } else {
        title = "exits $areaName"
        description = "${this.ship.mmsi} exits $areaName"
        summary = "${this.ship.mmsi} at $areaName"
        suffix = TeqplayEvent.END
        eventType = this.getCorrectedEventType(this.area, false)
    }

    return when (this.area.type) {
        AreaType.BERTH -> {
            val eventBerthId = this.area.id
            val actualBerthId = if (berthConverter != null && eventBerthId != null) {
                berthConverter.getPlatformBerthId(eventBerthId) ?: eventBerthId
            } else {
                eventBerthId
            }

            BerthEvent(
                id ?: this._id,
                TeqplayEvent.MOORED + suffix, this.actualTime.toEpochMilli(), this.getStartEventId(),
                ship.toShipInfo(), description, title, summary, this.location.toPlatformLocation(), actualBerthId,
                areaName, null, null, this.berth?.terminalName, this.berth?.terminalName ?: "",
                this.area.unlocode, null, this.draught, null, null,
                this.berth?.terminalName, this.heading
            )
        }
        AreaType.VHF -> VhfEvent(
            this._id,
            "${TeqplayEvent.AREA}.${this.area.name}$suffix", this.createdTime.toEpochMilli(),
            this.actualTime.toEpochMilli(), this.createdTime.toEpochMilli(), description, title, summary,
            this.getStartEventId(), ship.toShipInfo(), null, this.location.toPlatformLocation()
        )
        AreaType.TERMINAL -> TerminalEvent(
            this._id,
            this.getStartEventId() == null, this.actualTime.toEpochMilli(), this.createdTime.toEpochMilli(),
            this.actualTime.toEpochMilli(), this.getStartEventId(), ship.toShipInfo(), description, title, summary,
            this.location.toPlatformLocation(), this.area.name
        )
        AreaType.PORT -> PortAtaAtdEvent(
            this._id,
            "${TeqplayEvent.PORT}.${this.area.unlocode}$suffix", this.createdTime.toEpochMilli(),
            this.actualTime.toEpochMilli(), this.actualTime.toEpochMilli(),
            description, title, summary, false, this.getStartEventId(), ship.toShipInfo(), null,
            this.location.toPlatformLocation(), this.draught
        )
        AreaType.END_OF_SEA_PASSAGE -> EndSeaPassageEvent(
            this._id,
            "${TeqplayEvent.AREA}.${this.area.unlocode?.lowercase()}.eosp$suffix", this.createdTime.toEpochMilli(),
            this.actualTime.toEpochMilli(), description, title, summary, this.getStartEventId(), ship.toShipInfo(),
            this.location.toPlatformLocation()
        )
        else -> TeqplayLocationBasedEvent(
            this._id,
            eventType, this.createdTime.toEpochMilli(),
            this.actualTime.toEpochMilli(), this.createdTime.toEpochMilli(), description, title, summary, false,
            this.getStartEventId(), ship.toShipInfo(), null, this.location.toPlatformLocation(), null, null, null
        )
    }
}

fun AisRecoverEvent.toPlatformEvent(): AisRecoveredEvent {
    return AisRecoveredEvent(
        this._id,
        this.createdTime.toEpochMilli(),
        this.ship.toShipInfo(),
        null,
        "AIS Signal recovered for ${this.ship.mmsi}",
        "AIS recovered",
        "AIS Signal recovered",
        this.location.toPlatformLocation()
    )
}

fun AisLostEvent.toPlatformEvent(): PlatformAisLostEvent {
    return PlatformAisLostEvent(
        this._id,
        this.createdTime.toEpochMilli(),
        this.ship.toShipInfo(),
        "AIS Signal lost for ${this.ship.mmsi}",
        "AIS lost",
        "AIS Signal lost",
        this.location.toPlatformLocation()
    )
}

fun HamisEtaEvent.toPlatformEvent(): EtaEvent {
    val description = EtaEvent.generateDescription(
        this.ship.mmsi.toString(),
        this.predictedTime.toString(),
        this.berth,
        this.area.unlocode,
        this.createdTime.toString()
    )
    val title = EtaEvent.generateTitle(
        this.predictedTime.toString(),
        this.berth,
        this.area.unlocode,
        this.createdTime.toString()
    )

    return EtaEvent(
        this._id,
        determineEventTypeForEtaEtd(this.area.type, isEta = true),
        this.createdTime.toEpochMilli(),
        this.predictedTime.toEpochMilli(),
        this.ship.toShipInfo(),
        this.source,
        description,
        title,
        this.berth,
        this.berthId,
        null,
        this.portcallId,
        null,
        this.vesselAgent,
        this.area.unlocode
    )
}

fun PortcallPlusEtaEvent.toPlatformEvent(): EtaEvent {
    val description = EtaEvent.generateDescription(
        this.ship.mmsi.toString(),
        this.predictedTime.toString(),
        this.berth,
        this.area.unlocode,
        this.createdTime.toString()
    )
    val title = EtaEvent.generateTitle(
        this.predictedTime.toString(),
        this.berth,
        this.area.unlocode,
        this.createdTime.toString()
    )

    val type = if (this.nomination == true) {
        TeqplayEvent.ETA_NOMINATION
    } else {
        determineEventTypeForEtaEtd(this.area.type, isEta = true)
    }

    return EtaEvent(
        this._id,
        type,
        this.createdTime.toEpochMilli(),
        this.predictedTime.toEpochMilli(),
        this.ship.toShipInfo(),
        this.source,
        description,
        title,
        this.berth,
        this.berthId,
        null,
        this.portcallId,
        null,
        this.vesselAgent,
        this.area.unlocode
    )
}

fun HamisEtdEvent.toPlatformEvent(): EtdEvent {
    val description = EtdEvent.generateDescription(
        this.ship.mmsi.toString(),
        this.predictedTime.toString(),
        this.berth,
        this.area.unlocode,
        this.createdTime.toString()
    )
    val title = EtdEvent.generateTitle(
        this.predictedTime.toString(),
        this.berth,
        this.area.unlocode,
        this.createdTime.toString()
    )

    return EtdEvent(
        this._id,
        determineEventTypeForEtaEtd(this.area.type, isEta = false),
        this.createdTime.toEpochMilli(),
        this.predictedTime.toEpochMilli(),
        this.ship.toShipInfo(),
        this.source,
        description,
        title,
        this.berth,
        this.berthId,
        null,
        this.portcallId,
        this.vesselAgent,
        this.area.unlocode
    )
}

fun PortcallPlusEtdEvent.toPlatformEvent(): EtdEvent {
    val description = EtdEvent.generateDescription(
        this.ship.mmsi.toString(),
        this.predictedTime.toString(),
        this.berth,
        this.area.unlocode,
        this.createdTime.toString()
    )
    val title = EtdEvent.generateTitle(
        this.predictedTime.toString(),
        this.berth,
        this.area.unlocode,
        this.createdTime.toString()
    )

    val type = if (this.nomination == true) {
        TeqplayEvent.ETD_NOMINATION
    } else {
        determineEventTypeForEtaEtd(this.area.type, isEta = false)
    }

    return EtdEvent(
        this._id,
        type,
        this.createdTime.toEpochMilli(),
        this.predictedTime.toEpochMilli(),
        this.ship.toShipInfo(),
        this.source,
        description,
        title,
        this.berth,
        this.berthId,
        null,
        this.portcallId,
        this.vesselAgent,
        this.area.unlocode
    )
}

fun PortcallFinishEvent.toPlatformEvent(): PlatformPortcallFinishEvent {
    return PlatformPortcallFinishEvent(
        shipMmsi = this.ship.mmsi?.toString(),
        shipImo = this.ship.imo?.toString(),
        shipInfo = this.ship.toShipInfo(),
        portcallId = this.portcallId,
        port = this.port.unlocode,
        endTime = this.actualTime.toEpochMilli()
    )
}

fun PortcallAgentChangedEvent.toPlatformEvent(): AgentChangedEvent {
    return AgentChangedEvent(
        recordTime = this.createdTime.toEpochMilli(),
        eventTime = this.actualTime.toEpochMilli(),
        shipInfo = this.ship.toShipInfo(),
        port = this.port.unlocode,
        vesselAgent = this.vesselAgent,
        ucrn = this.portcallId,
        source = this.source
    )
}

fun PortcallPlusEtaBerthEvent.toPlatformEvent(): PortcallBerthEtaEvent {
    val description = "ETA: ${this.predictedTime} at ${this.berth} in ${port.name}, reported at ${this.createdTime}"

    return PortcallBerthEtaEvent(
        this.createdTime.toEpochMilli(),
        this.predictedTime.toEpochMilli(),
        this.ship.toShipInfo(),
        description,
        description,
        this.berth,
        this.berthId,
        this.port.name,
        this.portcallId,
        this.distanceInNm,
        this.source,
        this.portcallBerthId
    )
}

private fun determineEventTypeForEtaEtd(areaType: AreaType?, isEta: Boolean): String {
    // Platform doesn't support creating ETD events for all area types
    // We always make ETA_PBP for an anchor/pilot area and ETA_PORT if they are for a port
    when (areaType) {
        AreaType.ANCHOR,
        AreaType.PILOT_BOARDING_PLACE -> return TeqplayEvent.ETA_PBP
        AreaType.PORT -> return TeqplayEvent.ETA_PORT
        else -> {
            return if (isEta) {
                when (areaType) {
                    AreaType.BERTH -> TeqplayEvent.ETA_BERTH
                    AreaType.LOCK -> TeqplayEvent.ETA_LOCK
                    else -> TeqplayEvent.ETA
                }
            } else {
                when (areaType) {
                    AreaType.BERTH -> TeqplayEvent.ETD_BERTH
                    AreaType.LOCK -> TeqplayEvent.ETD_LOCK
                    else -> TeqplayEvent.ETD
                }
            }
        }
    }
}

fun HamisAtaEvent.toPlatformEvent(): AtaEvent {
    val title = "ATA: " + this.actualTime + " at berth " + this.area.name
    val description = "ATA ${this.ship.mmsi} : ${this.actualTime} at ${this.area.name}"

    return AtaEvent(
        this._id,
        this.createdTime.toEpochMilli(),
        this.actualTime.toEpochMilli(),
        this.ship.toShipInfo(),
        description,
        title,
        this.area.name,
        this.area.id,
        this.externalVisitId,
        this.movementId.toLongOrNull(),
        this.port.unlocode,
        this.portcallId
    )
}

fun HamisAtdEvent.toPlatformEvent(): AtdEvent {
    val title = "ATD: " + this.actualTime + " from berth " + this.area.name
    val description = "ATD ${this.ship.mmsi} : ${this.actualTime} from ${this.area.name}"

    return AtdEvent(
        this._id,
        this.createdTime.toEpochMilli(),
        this.actualTime.toEpochMilli(),
        this.ship.toShipInfo(),
        description,
        title,
        this.area.name,
        this.area.id,
        this.externalVisitId,
        this.movementId,
        this.port.unlocode,
        this.portcallId
    )
}

fun PortcallPlusAtaEvent.toPlatformEvent(): AtaEvent {
    val title = "ATA: " + this.actualTime + " at berth " + this.area.name
    val description = "ATA ${this.ship.mmsi} : ${this.actualTime} at ${this.area.name}"

    return AtaEvent(
        this._id,
        this.createdTime.toEpochMilli(),
        this.actualTime.toEpochMilli(),
        this.ship.toShipInfo(),
        description,
        title,
        this.area.name,
        this.area.id,
        null,
        null,
        this.port.unlocode,
        this.portcallId
    )
}

fun PortcallPlusAtdEvent.toPlatformEvent(): AtdEvent {
    val title = "ATD: " + this.actualTime + " from berth " + this.area.name
    val description = "ATD ${this.ship.mmsi} : ${this.actualTime} from ${this.area.name}"

    return AtdEvent(
        this._id,
        this.createdTime.toEpochMilli(),
        this.actualTime.toEpochMilli(),
        this.ship.toShipInfo(),
        description,
        title,
        this.area.name,
        this.area.id,
        null,
        null,
        this.port.unlocode,
        this.portcallId
    )
}

fun PortcallPilotBoardingEtaEvent.toPlatformEvent(): PlatformPortcallPilotBoardingEtaEvent {
    var title = "ETA Pbp ${this.predictedTime} for ship [IMO:${this.ship.imo}]"
    if (PlatformPortcallPilotBoardingEtaEvent.SRC_TEQPLAY_PREDICTION_CANCEL == this.source) {
        title = "CANCELLED ETA Pilot Boarding place for ship with IMO " + this.ship.imo
    } else if (PlatformPortcallPilotBoardingEtaEvent.SRC_TEQPLAY_PREDICTION_LOW_SPEED == this.source) {
        title = "SLOW: $title"
    }

    return PlatformPortcallPilotBoardingEtaEvent(
        this._id,
        this.createdTime.toEpochMilli(),
        this.predictedTime.toEpochMilli(),
        this.ship.mmsi.toString(),
        this.ship.imo.toString(),
        title,
        title,
        this.area.name,
        this.area.id,
        this.portcallId,
        null,
        this.source,
        this.area.unlocode
    )
}

fun HamisAddPortcallVisitEvent.toPlatformEvent(): AddPortcallVisitEvent {
    return AddPortcallVisitEvent(
        this._id,
        this.portcallId,
        this.port.unlocode,
        this.ship.toShipInfo(),
        this.visitIndex,
        this.berthName,
        this.berthOwnerId,
        this.createdTime.toEpochMilli()
    )
}

fun HamisUpdatePortcallVisitEvent.toPlatformEvent(): UpdatePortcallVisitEvent {
    return UpdatePortcallVisitEvent(
        this._id,
        this.portcallId,
        this.port.unlocode,
        this.ship.toShipInfo(),
        this.visitIndex,
        this.berthName,
        this.berthOwnerId,
        this.previousBerthName,
        this.previousBerthOwnerId,
        this.createdTime.toEpochMilli()
    )
}

fun HamisCancelPortcallVisitEvent.toPlatformEvent(): CancelPortcallVisitEvent {
    return CancelPortcallVisitEvent(
        this._id,
        this.portcallId,
        this.port.unlocode,
        this.ship.toShipInfo(),
        this.visitIndex,
        this.berthName,
        this.berthOwnerId,
        this.createdTime.toEpochMilli()
    )
}

fun HamisAgentOrderEvent.toPlatformEvent(): AgentOrderEvent {
    val description = "Agent Order ${this.order} for ${this.ship.mmsi} portcall ${this.portcallId}"
    val title = "Agent Order: ${this.order}"

    return AgentOrderEvent(
        this._id,
        this.createdTime.toEpochMilli(),
        description,
        title,
        title,
        this.ship.toShipInfo(),
        this.portcallId,
        this.port.unlocode,
        this.vesselAgent,
        this.order,
        this.movementId
    )
}

fun HamisAgentReportsTugsEvent.toPlatformEvent(): AgentReportsTugsEvent {
    val description = "Agent declares tug: ${this.ship.mmsi} from berth: ${this.berth.name} at time: ${this.createdTime}"
    val title = "AgentReportsTugs: ${this.createdTime}"

    return AgentReportsTugsEvent(
        this._id,
        this.createdTime.toEpochMilli(),
        description,
        title,
        this.ship.toShipInfo(),
        this.portcallId,
        this.port.unlocode,
        this.serviceShip,
        this.berth.name,
        this.createdTime.toEpochMilli(),
        this.vesselAgent,
        this.towingCompany,
        this.externalVisitId,
        this.movementId
    )
}

fun HamisEtaCancelEvent.toPlatformEvent(): EtaCancelEvent {
    val description = "ETA authority cancelled for ${this.ship.mmsi}"
    val title = "ETA Authority cancelled"

    return EtaCancelEvent(
        this._id,
        this.createdTime.toEpochMilli(),
        description,
        title,
        this.ship.toShipInfo(),
        this.portcallId,
        this.port.unlocode,
        this.vesselAgent,
        this.source,
        this.movementId
    )
}

fun HamisEtaRequestEvent.toPlatformEvent(): EtaRequestEvent {
    val description = "ETA Berth request for ${this.ship.mmsi} in Rotterdam at ${this.predictedTime}"
    val title = "ETA Berth Request: ${this.predictedTime}"

    return EtaRequestEvent(
        this._id,
        this.createdTime.toEpochMilli(),
        description,
        title,
        this.ship.toShipInfo(),
        this.portcallId,
        this.port.unlocode,
        this.pilotStation,
        this.berth.name,
        this.berthOwnerId,
        this.predictedTime.toEpochMilli(),
        this.vesselAgent,
        this.externalVisitId,
        this.movementId
    )
}

fun HamisEtdRequestEvent.toPlatformEvent(): EtdRequestEvent {
    val description = "ETD request for ${this.ship.mmsi} in Rotterdam at ${this.predictedTime}"
    val title = "ETD Request: ${this.predictedTime}"
    return EtdRequestEvent(
        this._id,
        this.createdTime.toEpochMilli(),
        description,
        title,
        this.ship.toShipInfo(),
        this.portcallId,
        this.port.unlocode,
        this.pilotStation,
        this.berth.name,
        this.berthOwnerId,
        this.predictedTime.toEpochMilli(),
        this.vesselAgent,
        this.externalVisitId,
        this.movementId
    )
}

fun HamisPilotBoardingEtaEvent.toPlatformEvent(): PlatformHamisPilotBoardingEtaEvent {
    val description = "ETA autority for ${this.ship.mmsi} in ${this.pilotStation} at ${this.predictedTime}"
    val title = "ETA Authority: ${this.predictedTime}"

    return PlatformHamisPilotBoardingEtaEvent(
        this._id,
        this.createdTime.toEpochMilli(),
        description,
        title,
        this.ship.toShipInfo(),
        this.portcallId,
        this.port.unlocode,
        this.pilotStation,
        this.predictedTime.toEpochMilli(),
        this.vesselAgent,
        this.source,
        this.movementId
    )
}

fun HamisNauticalOrderEvent.toPlatformEvent(): NauticalOrderEvent {
    val title = "${this.reporter} ordered"
    val description = "$title nautical services for ${this.ship.mmsi} portcall ${this.portcallId}"

    return NauticalOrderEvent(
        this._id,
        this.createdTime.toEpochMilli(),
        description,
        title,
        title,
        this.ship.toShipInfo(),
        this.portcallId,
        this.port.unlocode,
        this.reporter,
        this.movementId
    )
}

fun HamisPilotOnBoardEvent.toPlatformEvent(): PilotOnBoardEvent? {
    when (this) {
        is HamisPilotOnBoardStartEvent -> {
            val summary = "Pilot on board"
            val description = "Pilot on board of ${this.ship.mmsi} at ${this.actualTime}"
            val title = "Pilot on board ${this.actualTime}"

            return PilotOnBoardEvent(
                this._id,
                this.createdTime.toEpochMilli(),
                description,
                title,
                summary,
                this.ship.toShipInfo(),
                this.portcallId,
                this.port.unlocode,
                true,
                this.actualTime.toEpochMilli(),
                this.pilotReason?.toMovementType(),
                this.fromBerth,
                this.toBerth,
                null,
                this.movementId,
                this.vesselAgent
            )
        }
        is HamisPilotOnBoardEndEvent -> {
            val summary = "Pilot left ship"
            val description = "Pilot left ship ${this.ship.mmsi} at ${this.actualTime}"
            val title = "Pilot left ship at ${this.actualTime}"

            return PilotOnBoardEvent(
                this._id,
                this.createdTime.toEpochMilli(),
                description,
                title,
                summary,
                this.ship.toShipInfo(),
                this.portcallId,
                this.port.unlocode,
                false,
                this.actualTime.toEpochMilli(),
                this.pilotReason?.toMovementType(),
                this.fromBerth,
                this.toBerth,
                this.startEventId,
                this.movementId,
                this.vesselAgent
            )
        }
        else -> return null
    }
}

private fun HamisPilotOnBoardEvent.MovementType.toMovementType(): PilotOnBoardEvent.MovementType {
    return when (this) {
        HamisPilotOnBoardEvent.MovementType.ARRIVAL -> PilotOnBoardEvent.MovementType.ARRIVAL
        HamisPilotOnBoardEvent.MovementType.DEPARTURE -> PilotOnBoardEvent.MovementType.DEPARTURE
        HamisPilotOnBoardEvent.MovementType.SHIFT -> PilotOnBoardEvent.MovementType.SHIFT
        HamisPilotOnBoardEvent.MovementType.TRANSIT -> PilotOnBoardEvent.MovementType.TRANSIT
    }
}

fun HamisVisitCancellationEvent.toPlatformEvent(): VisitCancellationEvent {
    val description = "${this.shipName ?: this.ship.mmsi} will cancel portcall to Rotterdam with UCRN ${this.portcallId}"
    val title = "$portcallId port call cancellation"

    return VisitCancellationEvent(
        this._id,
        this.createdTime.toEpochMilli(),
        description,
        title,
        this.ship.toShipInfo(),
        this.portcallId,
        this.port.unlocode,
        null
    )
}

fun HamisVisitDeclarationEvent.toPlatformEvent(): VisitDeclarationEvent {
    val description = "${this.shipName} will visit Rotterdam with UCRN ${this.portcallId}"
    val title = "${this.portcallId} port call declaration"

    return VisitDeclarationEvent(
        this._id,
        this.createdTime.toEpochMilli(),
        description,
        title,
        this.ship.toShipInfo(),
        this.portcallId,
        this.port.unlocode,
        this.shipName,
        this.previousPort.unlocode,
        this.vesselAgent
    )
}

fun LockEvent.toPlatformEvent(): PlatformLockEvent? {
    val predictionTime = this.predictedTime.atZone(ZoneId.of("Europe/Amsterdam")).toLocalTime()
    val descriptionPart = this.area.name ?: this.isrsId
    val titlePart = "${this.ship.mmsi} $predictionTime"

    return when (this) {
        is LockEtaEvent -> {
            PlatformLockEvent(
                this._id,
                PlatformLockEvent.ETA_LOCK,
                this.createdTime.toEpochMilli(),
                "ETA: $descriptionPart",
                "Lock Opening $titlePart",
                this.ship.toShipInfo(),
                true,
                this.isrsId,
                this.area.name,
                this.direction.toLockDirection(),
                this.location.toPlatformLocation(),
                this.predictedTime.toEpochMilli(),
                1.0
            )
        }
        is LockEtdEvent -> {
            PlatformLockEvent(
                this._id,
                PlatformLockEvent.ETD_LOCK,
                this.createdTime.toEpochMilli(),
                "ETD: $descriptionPart",
                "Lock Exit $titlePart",
                this.ship.toShipInfo(),
                true,
                this.isrsId,
                this.area.name,
                this.direction.toLockDirection(),
                this.location.toPlatformLocation(),
                this.predictedTime.toEpochMilli(),
                1.0
            )
        }
        else -> null
    }
}

private fun LockEvent.LockDirection.toLockDirection(): PlatformLockDirection {
    return when (this) {
        LockEvent.LockDirection.INBOUND -> PlatformLockDirection.INBOUND
        LockEvent.LockDirection.OUTBOUND -> PlatformLockDirection.OUTBOUND
    }
}
