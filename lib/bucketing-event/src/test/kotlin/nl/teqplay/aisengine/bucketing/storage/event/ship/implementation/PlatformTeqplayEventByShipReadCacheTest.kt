package nl.teqplay.aisengine.bucketing.storage.event.ship.implementation

import nl.teqplay.aisengine.bucketing.factory.TeqplayEventBucketFactory
import nl.teqplay.aisengine.bucketing.model.PlatformEventBucketKeyPairByMmsi
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveConfiguration
import nl.teqplay.aisengine.bucketing.properties.BucketProperties
import nl.teqplay.aisengine.bucketing.storage.BucketFactory
import nl.teqplay.aisengine.bucketing.storage.archive.ArchiveReadStorage
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.cache.BucketReadCacheFactory
import nl.teqplay.aisengine.bucketing.storage.cache.impl.ArchiveStorageFactory
import nl.teqplay.aisengine.bucketing.storage.cache.impl.BucketCacheFactory
import nl.teqplay.aisengine.bucketing.storage.mongo.MongoBucketReadStorage
import nl.teqplay.aisengine.bucketing.storage.mongo.UnorderedMongoBucketReadStorage
import nl.teqplay.platform.model.event.TeqplayEvent
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever

class PlatformTeqplayEventByShipReadCacheTest {

    @Test
    fun init() {
        var collectionName: String? = null
        var unorderedCollectionSuffix: String? = null

        val bucketCacheFactory = mock<BucketCacheFactory>().apply {
            whenever(
                bucketReadCacheFactory<TeqplayEvent, PlatformEventBucketKeyPairByMmsi, BucketId<PlatformEventBucketKeyPairByMmsi>>(
                    collectionName = any(),
                    mongoDatabase = any(),
                    objectMapper = any(),
                    bucket = any(),
                    archive = any(),
                    archiveGlobal = any(),
                    factory = any(),
                    createArchiveReadStorage = any(),
                    unorderedCollectionSuffix = any()
                )
            ).thenAnswer {
                collectionName = it.getArgument<String>(0)
                unorderedCollectionSuffix = it.getArgument<String>(8)
                val factory =
                    it.getArgument<BucketFactory<TeqplayEvent, PlatformEventBucketKeyPairByMmsi, BucketId<PlatformEventBucketKeyPairByMmsi>>>(
                        6
                    )
                object :
                    BucketReadCacheFactory<TeqplayEvent, PlatformEventBucketKeyPairByMmsi, BucketId<PlatformEventBucketKeyPairByMmsi>> {
                    override val collectionName: String = collectionName!!
                    override val bucket = mock<BucketProperties>()
                    override val archive = mock<BucketArchiveConfiguration>()
                    override val factory = factory

                    override fun mongoStorage() =
                        mock<MongoBucketReadStorage<TeqplayEvent, TeqplayEvent>>()

                    override fun unorderedStorage() =
                        mock<UnorderedMongoBucketReadStorage<TeqplayEvent, TeqplayEvent, TeqplayEvent>>()

                    override fun archiveStorage() =
                        mock<ArchiveReadStorage<TeqplayEvent>>()
                }
            }
        }

        val archiveStorageFactory = mock<ArchiveStorageFactory>().apply {
            whenever(archiveReadStoragePerDay<TeqplayEvent, PlatformEventBucketKeyPairByMmsi, BucketId<PlatformEventBucketKeyPairByMmsi>>())
                .thenReturn(mock())
        }

        val platformByEntityCache = PlatformTeqplayEventByShipReadCache(
            bucketCacheFactory = bucketCacheFactory,
            archiveStorageFactory = archiveStorageFactory,
            mongoDatabase = mock(),
            objectMapper = mock(),
            bucket = mock(),
            archive = mock(),
            archiveGlobal = mock(),
            platformBucketProperties = mock()
        )

        assertNotNull(platformByEntityCache)
        assertEquals("eventsByEntity", collectionName)
        assertEquals("new_data", unorderedCollectionSuffix)
        assertEquals(TeqplayEventBucketFactory, platformByEntityCache.factory)
    }
}
