package nl.teqplay.aisengine.bucketing

import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.platform.toPlatformLocation
import nl.teqplay.aisengine.platform.toShipInfo
import nl.teqplay.platform.model.event.ShipMovingEvent
import nl.teqplay.skeleton.model.Location
import java.time.Instant

const val ID = "TEST_ID"
val MMSI = 222222222
val SHIP = AisShipIdentifier(MMSI)
val LOCATION = Location(lat = 1.0, lon = 1.0)
val EVENT_TIME: Instant = Instant.ofEpochMilli(1664582400000)

fun createPlatformBasicEvent(): ShipMovingEvent {
    val description = "[$MMSI] stopped moving"

    return ShipMovingEvent(
        ID,
        false,
        EVENT_TIME.toEpochMilli(),
        null,
        SHIP.toShipInfo(),
        description,
        "Stopped moving",
        "moving",
        LOCATION.toPlatformLocation()
    )
}
