package nl.teqplay.aisengine.bucketing.model

import nl.teqplay.aisengine.event.interfaces.AisEvent
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.time.Instant

class EventResolverTest {

    private val item = mock<AisEvent>().apply {
        whenever(actualTime).thenReturn(Instant.EPOCH)
        whenever(_id).thenReturn("id")
    }

    @Test
    fun toTimestamp() {
        assertEquals(Instant.EPOCH, EventResolver.toTimestamp(item))
    }

    @Test
    fun toId() {
        assertEquals("id", EventResolver.toId(item))
    }
}
