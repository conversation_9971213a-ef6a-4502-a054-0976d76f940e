package nl.teqplay.aisengine.bucketing

import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.bucketing.properties.EventByAreaArchiveProperties
import nl.teqplay.aisengine.bucketing.properties.EventByShipArchiveProperties
import nl.teqplay.aisengine.bucketing.properties.PlatformTeqplayEventByAreaArchiveProperties
import nl.teqplay.aisengine.bucketing.properties.PlatformTeqplayEventByShipArchiveProperties
import nl.teqplay.skeleton.common.BaseTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.TestConstructor
import org.springframework.test.context.bean.override.mockito.MockitoBean

@SpringBootApplication
class Application

@SpringBootTest
@MockitoBean(
    types = [MongoDatabase::class]
)
@TestConstructor(autowireMode = TestConstructor.AutowireMode.ALL)
class ApplicationTest(
    private val eventByShipArchiveProperties: EventByShipArchiveProperties,
    private val eventByAreaArchiveProperties: EventByAreaArchiveProperties,
    private val platformTeqplayEventByShipArchiveProperties: PlatformTeqplayEventByShipArchiveProperties,
    private val platformTeqplayEventByAreaArchiveProperties: PlatformTeqplayEventByAreaArchiveProperties,
) : BaseTest() {

    @Test
    fun contextLoads() {
        assertEquals(",", eventByShipArchiveProperties.prefixSeparator)
        assertEquals(",", eventByAreaArchiveProperties.prefixSeparator)
        assertEquals("_", platformTeqplayEventByShipArchiveProperties.prefixSeparator)
        assertEquals("_", platformTeqplayEventByAreaArchiveProperties.prefixSeparator)
    }
}
