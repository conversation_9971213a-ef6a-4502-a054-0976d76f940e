package nl.teqplay.aisengine.bucketing.storage.event.ship.implementation

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class PlatformTeqplayEventByShipBucketDetailsTest {

    @Test
    fun collectionName() {
        assertEquals(
            "eventsByEntity",
            PlatformTeqplayEventByShipBucketDetails.collectionName
        )
    }

    @Test
    fun unorderedCollectionSuffix() {
        assertEquals(
            "new_data",
            PlatformTeqplayEventByShipBucketDetails.unorderedCollectionSuffix
        )
    }
}
