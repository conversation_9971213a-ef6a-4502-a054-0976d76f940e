package nl.teqplay.aisengine.bucketing.factory

import nl.teqplay.aisengine.bucketing.createPlatformBasicEvent
import nl.teqplay.aisengine.bucketing.model.PlatformEventBucketKeyPairByMmsi
import nl.teqplay.platform.model.event.TeqplayEvent
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

internal class TeqplayEventBucketFactoryTest {

    @Test
    fun getDataClass() {
        assertEquals(
            TeqplayEvent::class.java,
            TeqplayEventBucketFactory.dataClass
        )
    }

    @Test
    fun getBucketKey() {
        val result = TeqplayEventBucketFactory.bucketIdentifier.key.getBucketKey(
            createPlatformBasicEvent()
        )
        val expected = PlatformEventBucketKeyPairByMmsi(222222222, TeqplayEvent.EventCategory.BASIC)
        assertEquals(expected, result)
    }
}
