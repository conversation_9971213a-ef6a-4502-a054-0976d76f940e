package nl.teqplay.aisengine.bucketing.storage.event.area.implementation

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class EventByAreaBucketDetailsTest {

    @Test
    fun collectionName() {
        assertEquals(
            "eventByArea",
            EventByAreaBucketDetails.collectionName
        )
    }

    @Test
    fun unorderedCollectionSuffix() {
        assertEquals(
            "unordered",
            EventByAreaBucketDetails.unorderedCollectionSuffix
        )
    }
}
