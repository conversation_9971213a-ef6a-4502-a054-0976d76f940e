package nl.teqplay.aisengine.bucketing.storage.event.area.bucket

import nl.teqplay.aisengine.bucketing.storage.bucket.shared.BucketIdByArea
import nl.teqplay.skeleton.model.BoundingBox
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.TimeWindow
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.Instant
import java.time.LocalDate
import java.time.temporal.ChronoUnit

class EventBucketIdByAreaTest {

    @Test
    fun getBucketId() {
        assertEquals(
            "1970-01-01,4.00,52.00",
            BucketIdByArea.getBucketId(
                date = LocalDate.EPOCH,
                key = Location(52.0, 4.0),
                decimals = 2
            )
        )
    }

    @Test
    fun getArchiveBucketId() {
        assertEquals(
            "1970-01-01,4.0,52.0",
            BucketIdByArea.getArchiveBucketId("1970-01-01,4.00,52.00")
        )
    }

    @Test
    fun getBucketIds() {
        assertEquals(
            listOf("1970-01-01,4.00,52.00"),
            BucketIdByArea.getBucketIds(
                window = TimeWindow(Instant.EPOCH, Instant.EPOCH.plus(1, ChronoUnit.DAYS)),
                boundingBox = BoundingBox(
                    bottomleft = Location(52.0, 4.0),
                    topright = Location(52.01, 4.01),
                )
            ).asSequence().toList()
        )
    }

    @Test
    fun getArchiveBucketIds() {
        assertEquals(
            listOf("1970-01-01,4.0,52.0"),
            BucketIdByArea.getArchiveBucketIds(
                window = TimeWindow(Instant.EPOCH, Instant.EPOCH.plus(1, ChronoUnit.DAYS)),
                boundingBox = BoundingBox(
                    bottomleft = Location(52.0, 4.0),
                    topright = Location(52.1, 4.1),
                )
            ).asSequence().toList()
        )
    }
}
