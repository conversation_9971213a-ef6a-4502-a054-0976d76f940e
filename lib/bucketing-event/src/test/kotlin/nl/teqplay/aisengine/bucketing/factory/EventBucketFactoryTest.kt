package nl.teqplay.aisengine.bucketing.factory

import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.interfaces.SpeedEvent
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.testing.event.createAisDestinationChangedEvent
import nl.teqplay.aisengine.testing.event.createAisDraughtChangedEvent
import nl.teqplay.aisengine.testing.event.createAisEtaChangedEvent
import nl.teqplay.aisengine.testing.event.createAisLostEvent
import nl.teqplay.aisengine.testing.event.createAisRecoverEvent
import nl.teqplay.aisengine.testing.event.createAisStatusChangedEvent
import nl.teqplay.aisengine.testing.event.createAnchoredEndEvent
import nl.teqplay.aisengine.testing.event.createAnchoredStartEvent
import nl.teqplay.aisengine.testing.event.createAreaEndEvent
import nl.teqplay.aisengine.testing.event.createAreaStartEvent
import nl.teqplay.aisengine.testing.event.createConfirmedBerthEndEvent
import nl.teqplay.aisengine.testing.event.createConfirmedBerthStartEvent
import nl.teqplay.aisengine.testing.event.createEncounterEndEvent
import nl.teqplay.aisengine.testing.event.createEncounterStartEvent
import nl.teqplay.aisengine.testing.event.createEtaEvent
import nl.teqplay.aisengine.testing.event.createEtdEvent
import nl.teqplay.aisengine.testing.event.createHamisAtaEvent
import nl.teqplay.aisengine.testing.event.createHamisAtdEvent
import nl.teqplay.aisengine.testing.event.createLockEtaEvent
import nl.teqplay.aisengine.testing.event.createLockEtdEvent
import nl.teqplay.aisengine.testing.event.createPortcallPilotBoardingEtaEvent
import nl.teqplay.aisengine.testing.event.createShipMovingEndEvent
import nl.teqplay.aisengine.testing.event.createShipMovingStartEvent
import nl.teqplay.aisengine.testing.event.createSpeedChangedEvent
import nl.teqplay.aisengine.testing.event.createTrueDestinationChangedEvent
import nl.teqplay.aisengine.testing.event.createUniqueBerthEndEvent
import nl.teqplay.aisengine.testing.event.createUniqueBerthStartEvent
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.time.LocalDate
import java.util.stream.Stream

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
internal class EventBucketFactoryTest {

    @Test
    fun getDataClass() {
        assertEquals(
            Event::class.java,
            EventBucketFactory.dataClass
        )
    }

    companion object {
        private const val TEST_ID = "NLRTM"
        private const val BUCKET_KEY = "111111111"
    }

    private fun bucketKeyTestData() = Stream.of(
        Arguments.of(createShipMovingStartEvent(), BUCKET_KEY),
        Arguments.of(createAisDestinationChangedEvent(), BUCKET_KEY),
        Arguments.of(createAisDraughtChangedEvent(), BUCKET_KEY),
        Arguments.of(createAisEtaChangedEvent(), BUCKET_KEY),
        Arguments.of(createAisStatusChangedEvent(), BUCKET_KEY),
        Arguments.of(createAisLostEvent(), BUCKET_KEY),
        Arguments.of(createAisRecoverEvent(), BUCKET_KEY),
        Arguments.of(createAnchoredStartEvent(), BUCKET_KEY),
        Arguments.of(createAnchoredEndEvent(), BUCKET_KEY),
        Arguments.of(createAreaStartEvent(), BUCKET_KEY),
        Arguments.of(createAreaEndEvent(), BUCKET_KEY),
        Arguments.of(createAreaStartEvent(area = AreaIdentifier(TEST_ID, AreaIdentifier.AreaType.BERTH)), BUCKET_KEY),
        Arguments.of(createAreaStartEvent(area = AreaIdentifier(TEST_ID, AreaIdentifier.AreaType.ANCHOR)), BUCKET_KEY),
        Arguments.of(createAreaStartEvent(area = AreaIdentifier(TEST_ID, AreaIdentifier.AreaType.PILOT_BOARDING_PLACE)), BUCKET_KEY),
        Arguments.of(createAreaStartEvent(area = AreaIdentifier(TEST_ID, AreaIdentifier.AreaType.TERMINAL)), BUCKET_KEY),
        Arguments.of(createAreaStartEvent(area = AreaIdentifier(TEST_ID, AreaIdentifier.AreaType.VHF)), BUCKET_KEY),
        Arguments.of(createAreaStartEvent(area = AreaIdentifier(TEST_ID, AreaIdentifier.AreaType.END_OF_SEA_PASSAGE)), BUCKET_KEY),
        Arguments.of(createAreaStartEvent(area = AreaIdentifier(TEST_ID, AreaIdentifier.AreaType.LOCK)), BUCKET_KEY),
        Arguments.of(createAreaStartEvent(area = AreaIdentifier(TEST_ID, AreaIdentifier.AreaType.BASIN)), BUCKET_KEY),
        Arguments.of(createAreaStartEvent(area = AreaIdentifier(TEST_ID, AreaIdentifier.AreaType.TUG)), BUCKET_KEY),
        Arguments.of(createAreaStartEvent(area = AreaIdentifier(TEST_ID, AreaIdentifier.AreaType.NAUTICAL_MILE)), BUCKET_KEY),
        Arguments.of(createAreaStartEvent(area = AreaIdentifier(TEST_ID, AreaIdentifier.AreaType.TERMINAL_NEARBY)), BUCKET_KEY),
        Arguments.of(createAreaStartEvent(area = AreaIdentifier(TEST_ID, AreaIdentifier.AreaType.APPROACH_POINT)), BUCKET_KEY),
        Arguments.of(createAreaStartEvent(area = AreaIdentifier(TEST_ID, AreaIdentifier.AreaType.FAIRWAY)), BUCKET_KEY),
        Arguments.of(createAreaStartEvent(area = AreaIdentifier(TEST_ID, AreaIdentifier.AreaType.CUSTOM)), BUCKET_KEY),
        Arguments.of(createHamisAtaEvent(), BUCKET_KEY),
        Arguments.of(createHamisAtdEvent(), BUCKET_KEY),
        Arguments.of(createEncounterStartEvent(), BUCKET_KEY),
        Arguments.of(createEncounterEndEvent(), BUCKET_KEY),
        Arguments.of(createEtaEvent(), BUCKET_KEY),
        Arguments.of(createEtdEvent(), BUCKET_KEY),
        Arguments.of(createLockEtaEvent(), BUCKET_KEY),
        Arguments.of(createLockEtdEvent(), BUCKET_KEY),
        Arguments.of(createPortcallPilotBoardingEtaEvent(), BUCKET_KEY),
        Arguments.of(createShipMovingStartEvent(), BUCKET_KEY),
        Arguments.of(createShipMovingEndEvent(), BUCKET_KEY),
        Arguments.of(createSpeedChangedEvent(speedType = SpeedEvent.SpeedType.ACCELERATING), BUCKET_KEY),
        Arguments.of(createSpeedChangedEvent(speedType = SpeedEvent.SpeedType.SLOWING_DOWN), BUCKET_KEY),
        Arguments.of(createTrueDestinationChangedEvent(), BUCKET_KEY),
        Arguments.of(createConfirmedBerthStartEvent(), BUCKET_KEY),
        Arguments.of(createConfirmedBerthEndEvent(), BUCKET_KEY),
        Arguments.of(createUniqueBerthStartEvent(), BUCKET_KEY),
        Arguments.of(createUniqueBerthEndEvent(), BUCKET_KEY)
    )

    @ParameterizedTest
    @MethodSource("bucketKeyTestData")
    fun `should create bucket key correctly`(event: Event, expected: String) {
        val result = EventBucketFactory.bucketIdentifier.key.getBucketKey(event)
        assertEquals(expected, result)
    }

    @Test
    fun `should create bucket id correctly`() {
        val result = EventBucketFactory.bucketIdentifier.id.getBucketId(LocalDate.EPOCH, BUCKET_KEY)
        val expected = "1970-01-01,111111111"
        assertEquals(expected, result)
    }
}
