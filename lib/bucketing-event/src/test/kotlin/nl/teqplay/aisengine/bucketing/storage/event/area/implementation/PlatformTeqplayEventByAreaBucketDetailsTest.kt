package nl.teqplay.aisengine.bucketing.storage.event.area.implementation

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class PlatformTeqplayEventByAreaBucketDetailsTest {

    @Test
    fun collectionName() {
        assertEquals(
            "eventsByArea",
            PlatformTeqplayEventByAreaBucketDetails.collectionName
        )
    }

    @Test
    fun unorderedCollectionSuffix() {
        assertEquals(
            "new_data",
            PlatformTeqplayEventByAreaBucketDetails.unorderedCollectionSuffix
        )
    }
}
