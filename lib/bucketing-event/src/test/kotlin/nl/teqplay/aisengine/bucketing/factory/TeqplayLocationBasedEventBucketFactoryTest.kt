package nl.teqplay.aisengine.bucketing.factory

import nl.teqplay.aisengine.bucketing.createPlatformBasicEvent
import nl.teqplay.aisengine.bucketing.model.PlatformEventBucketKeyPairByArea
import nl.teqplay.platform.model.event.TeqplayEvent
import nl.teqplay.platform.model.event.TeqplayLocationBasedEvent
import nl.teqplay.skeleton.model.Location
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

internal class TeqplayLocationBasedEventBucketFactoryTest {

    @Test
    fun getDataClass() {
        assertEquals(
            TeqplayLocationBasedEvent::class.java,
            TeqplayLocationBasedEventBucketFactory.dataClass
        )
    }

    @Test
    fun getBucketKey() {
        val result =
            TeqplayLocationBasedEventBucketFactory.bucketIdentifier.key.getBucketKey(createPlatformBasicEvent())
        val expected = PlatformEventBucketKeyPairByArea(Location(1.0, 1.0), TeqplayEvent.EventCategory.BASIC)
        assertEquals(expected, result)
    }
}
