package nl.teqplay.aisengine.bucketing.storage.event.area.implementation

import nl.teqplay.aisengine.bucketing.factory.TeqplayLocationBasedEventBucketFactory
import nl.teqplay.aisengine.bucketing.model.PlatformEventBucketKeyPairByArea
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveConfiguration
import nl.teqplay.aisengine.bucketing.properties.BucketProperties
import nl.teqplay.aisengine.bucketing.storage.BucketFactory
import nl.teqplay.aisengine.bucketing.storage.archive.ArchiveReadStorage
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.cache.BucketReadCacheFactory
import nl.teqplay.aisengine.bucketing.storage.cache.impl.ArchiveStorageFactory
import nl.teqplay.aisengine.bucketing.storage.cache.impl.BucketCacheFactory
import nl.teqplay.aisengine.bucketing.storage.event.area.bucket.EventAreaBucketId
import nl.teqplay.aisengine.bucketing.storage.mongo.MongoBucketReadStorage
import nl.teqplay.aisengine.bucketing.storage.mongo.UnorderedMongoBucketReadStorage
import nl.teqplay.platform.model.event.TeqplayEvent
import nl.teqplay.platform.model.event.TeqplayLocationBasedEvent
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever

class PlatformTeqplayEventByAreaReadCacheTest {

    @Test
    fun init() {
        var collectionName: String? = null
        var unorderedCollectionSuffix: String? = null

        val bucketCacheFactory = mock<BucketCacheFactory>().apply {
            whenever(
                bucketReadCacheFactory<TeqplayLocationBasedEvent, PlatformEventBucketKeyPairByArea, EventAreaBucketId<PlatformEventBucketKeyPairByArea>>(
                    collectionName = any(),
                    mongoDatabase = any(),
                    objectMapper = any(),
                    bucket = any(),
                    archive = any(),
                    archiveGlobal = any(),
                    factory = any(),
                    createArchiveReadStorage = any(),
                    unorderedCollectionSuffix = any()
                )
            ).thenAnswer {
                collectionName = it.getArgument<String>(0)
                unorderedCollectionSuffix = it.getArgument<String>(8)
                val factory =
                    it.getArgument<BucketFactory<TeqplayLocationBasedEvent, PlatformEventBucketKeyPairByArea, EventAreaBucketId<PlatformEventBucketKeyPairByArea>>>(
                        6
                    )
                object :
                    BucketReadCacheFactory<TeqplayLocationBasedEvent, PlatformEventBucketKeyPairByArea, EventAreaBucketId<PlatformEventBucketKeyPairByArea>> {
                    override val collectionName: String = collectionName!!
                    override val bucket = mock<BucketProperties>()
                    override val archive = mock<BucketArchiveConfiguration>()
                    override val factory = factory

                    override fun mongoStorage() =
                        mock<MongoBucketReadStorage<TeqplayLocationBasedEvent, TeqplayLocationBasedEvent>>()

                    override fun unorderedStorage() =
                        mock<UnorderedMongoBucketReadStorage<TeqplayLocationBasedEvent, TeqplayLocationBasedEvent, TeqplayLocationBasedEvent>>()

                    override fun archiveStorage() =
                        mock<ArchiveReadStorage<TeqplayLocationBasedEvent>>()
                }
            }
        }

        val archiveStorageFactory = mock<ArchiveStorageFactory>().apply {
            whenever(archiveReadStoragePerDay<TeqplayEvent, PlatformEventBucketKeyPairByArea, BucketId<PlatformEventBucketKeyPairByArea>>())
                .thenReturn(mock())
        }

        val platformByAreaReadCache = PlatformTeqplayEventByAreaReadCache(
            bucketCacheFactory = bucketCacheFactory,
            archiveStorageFactory = archiveStorageFactory,
            mongoDatabase = mock(),
            objectMapper = mock(),
            bucket = mock(),
            archive = mock(),
            archiveGlobal = mock(),
            platformBucketProperties = mock()
        )

        assertNotNull(platformByAreaReadCache)
        assertEquals("eventsByArea", collectionName)
        assertEquals("new_data", unorderedCollectionSuffix)
        assertEquals(TeqplayLocationBasedEventBucketFactory, platformByAreaReadCache.factory)
    }
}
