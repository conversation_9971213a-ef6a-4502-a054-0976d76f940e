package nl.teqplay.aisengine.bucketing.storage.event.ship.implementation

import nl.teqplay.aisengine.bucketing.factory.EventBucketFactory
import nl.teqplay.aisengine.bucketing.model.bucket.OrderedBucket
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveConfiguration
import nl.teqplay.aisengine.bucketing.properties.BucketProperties
import nl.teqplay.aisengine.bucketing.storage.BucketFactory
import nl.teqplay.aisengine.bucketing.storage.archive.ArchiveReadStorage
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.cache.BucketReadCacheFactory
import nl.teqplay.aisengine.bucketing.storage.cache.impl.ArchiveStorageFactory
import nl.teqplay.aisengine.bucketing.storage.cache.impl.BucketCacheFactory
import nl.teqplay.aisengine.bucketing.storage.mongo.MongoBucketReadStorage
import nl.teqplay.aisengine.bucketing.storage.mongo.UnorderedMongoBucketReadStorage
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.interfaces.LocationBasedEvent
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.testing.event.createAisDestinationChangedEvent
import nl.teqplay.aisengine.testing.event.createAreaStartEvent
import nl.teqplay.aisengine.testing.event.createEtaEvent
import nl.teqplay.skeleton.model.TimeWindow
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.ArgumentMatchers
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit

class EventByShipReadCacheTest {

    private var collectionNameCheck: String? = null
    private var unorderedCollectionSuffix: String? = null

    private val event1 = createAisDestinationChangedEvent(
        _id = "event1",
        ship = AisShipIdentifier(0),
        createdTime = Instant.EPOCH,
        actualTime = Instant.EPOCH
    )

    private val event2 = createAreaStartEvent(
        _id = "event2",
        ship = AisShipIdentifier(0),
        createdTime = Instant.EPOCH,
        actualTime = Instant.EPOCH
    )

    private val event3 = createEtaEvent(
        _id = "event3",
        ship = AisShipIdentifier(0),
        createdTime = Instant.EPOCH,
        predictedTime = Instant.EPOCH
    )

    private fun mockEventByShipReadCache(buckets: List<OrderedBucket<Event>>): EventByShipReadCache {
        val mongoStorage = mock<MongoBucketReadStorage<Event, Event>>().apply {
            whenever(load(ArgumentMatchers.anyList())).thenAnswer { buckets.iterator() }
        }

        val unorderedStorage = mock<UnorderedMongoBucketReadStorage<Event, Event, Event>>().apply {
            whenever(load(ArgumentMatchers.anyList())).thenAnswer { emptyList<Event>().iterator() }
        }

        val bucketCacheFactory = mock<BucketCacheFactory>().apply {
            whenever(
                bucketReadCacheFactory<Event, Int, BucketId<Int>>(
                    collectionName = any(),
                    mongoDatabase = any(),
                    objectMapper = any(),
                    bucket = any(),
                    archive = any(),
                    archiveGlobal = any(),
                    factory = any(),
                    createArchiveReadStorage = any(),
                    unorderedCollectionSuffix = any()
                )
            ).thenAnswer {
                collectionNameCheck = it.getArgument<String>(0)
                unorderedCollectionSuffix = it.getArgument<String>(8)
                val factory =
                    it.getArgument<BucketFactory<Event, Int, BucketId<Int>>>(
                        6
                    )
                object :
                    BucketReadCacheFactory<Event, Int, BucketId<Int>> {
                    override val collectionName: String = collectionNameCheck!!
                    override val bucket = BucketProperties(
                        sweeper = BucketProperties.Sweeper(Duration.ZERO),
                        mongo = BucketProperties.Mongo(10, mock())
                    )
                    override val archive = mock<BucketArchiveConfiguration>()
                    override val factory = factory

                    override fun mongoStorage() = mongoStorage

                    override fun unorderedStorage() = unorderedStorage

                    override fun archiveStorage() =
                        mock<ArchiveReadStorage<Event>>()
                }
            }
        }

        val archiveStorageFactory = mock<ArchiveStorageFactory>().apply {
            whenever(archiveReadStoragePerMonth<LocationBasedEvent, Int, BucketId<Int>>())
                .thenReturn(mock())
        }

        return EventByShipReadCache(
            bucketCacheFactory = bucketCacheFactory,
            archiveStorageFactory = archiveStorageFactory,
            mongoDatabase = mock(),
            objectMapper = mock(),
            bucket = mock(),
            archive = mock(),
            archiveGlobal = mock()
        )
    }

    @Test
    fun validate() {
        val eventByShipReadCache = mockEventByShipReadCache(emptyList())

        assertEquals("eventByShip", collectionNameCheck)
        assertEquals("unordered", unorderedCollectionSuffix)
        assertEquals(EventBucketFactory, eventByShipReadCache.factory)
    }

    @Test
    fun `Should findHistory`() {
        val eventByShipReadCache = mockEventByShipReadCache(
            buckets = listOf(
                OrderedBucket("1970-01-01,0", mutableListOf(event1, event3, event2))
            )
        )

        val result = eventByShipReadCache.findHistoryByMmsi(
            window = TimeWindow(Instant.EPOCH, Instant.EPOCH.plus(1, ChronoUnit.DAYS)),
            mmsi = 0,
            extendToLongTerm = false
        ).toList()

        val expected = listOf(event3, event2, event1)

        assertEquals(expected, result)
    }
}
