package nl.teqplay.aisengine.bucketing.factory

import nl.teqplay.aisengine.bucketing.model.LegacyHistoricShipInfoResolver
import nl.teqplay.aisengine.bucketing.storage.BucketFactory
import nl.teqplay.aisengine.bucketing.storage.DefaultBucketFormatter
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.platform.model.LegacyHistoricShipInfo

/**
 * A [BucketFactory] for handling [LegacyHistoricShipInfo]
 */
interface HistoricShipInfoBucketFactory<Key : Any, ID : BucketId<Key>> :
    BucketFactory<LegacyHistoricShipInfo, Key, ID> {

    override val dataClass: Class<LegacyHistoricShipInfo>
        get() = LegacyHistoricShipInfo::class.java

    override val resolver: LegacyHistoricShipInfoResolver
        get() = LegacyHistoricShipInfoResolver

    override val bucketFormatter: DefaultBucketFormatter<LegacyHistoricShipInfo>
        get() = DefaultBucketFormatter()
}
