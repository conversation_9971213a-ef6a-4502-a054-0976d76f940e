package nl.teqplay.aisengine.bucketing.model

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import nl.teqplay.aisengine.aisstream.model.AisMessage
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessageInterface
import java.time.Instant

@JsonInclude(JsonInclude.Include.NON_NULL)
data class AisHistoricUnorderedDiffMessage(
    // always included in a diff message
    @JsonProperty("mmsi") override val mmsi: Int,
    @JsonProperty("time") override val messageTime: Instant,
    @JsonProperty("rec") override val receptionTime: Instant?,

    // metadata
    @JsonProperty("old") val historic: Bo<PERSON>an,
    @JsonProperty("src") val source: String,
    @JsonProperty("sub") val subSource: String?,
    @JsonProperty("mst") val messageType: String?,

    // position
    @JsonProperty("lat") val lat: Double,
    @JsonProperty("lon") val lon: Double,
    @JsonProperty("hea") val heading: Int? = null,
    @JsonProperty("sog") val speedOverGround: Float? = null,
    @JsonProperty("cog") val courseOverGround: Float? = null,
    @JsonProperty("sts") val status: AisMessage.ShipStatus? = null,

    // static
    @JsonProperty("imo") var imo: Int? = null,
    @JsonProperty("typ") val shipType: AisMessage.ShipType? = null,
    @JsonProperty("dra") val draught: Float? = null,
    @JsonProperty("eta") val eta: Instant? = null,
    @JsonProperty("dst") val destination: String? = null,
    @JsonProperty("tpp") val transponderPosition: TransponderPositionBucketing? = null,
) : AisHistoricMessageInterface
