package nl.teqplay.aisengine.bucketing.storage.ship.area

import nl.teqplay.aisengine.bucketing.model.AisHistoricOrderedDiffMessage
import nl.teqplay.aisengine.bucketing.model.AisHistoricUnorderedDiffMessage
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessage
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessageInterface

interface ShipHistoryByAreaCache :
    ShipHistoryByAreaCacheInterfaceDual<AisHistoricOrderedDiffMessage, AisHistoricUnorderedDiffMessage, AisHistoricMessageInterface, AisHistoricMessage>
