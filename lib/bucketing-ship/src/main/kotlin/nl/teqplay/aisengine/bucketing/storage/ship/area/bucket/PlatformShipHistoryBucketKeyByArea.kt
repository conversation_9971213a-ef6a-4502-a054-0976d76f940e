package nl.teqplay.aisengine.bucketing.storage.ship.area.bucket

import nl.teqplay.aisengine.bucketing.storage.bucket.BucketKey
import nl.teqplay.aisengine.platform.model.LegacyHistoricShipInfo
import nl.teqplay.skeleton.model.Location

object PlatformShipHistoryBucketKeyByArea : <PERSON>et<PERSON>ey<LegacyHistoricShipInfo, Location> {

    /**
     * Ship history by area, so the location of the [item] functions as key
     * Converting platform's location in the process
     */
    override fun getBucketKey(item: LegacyHistoricShipInfo) = Location(item.location.latitude, item.location.longitude)
}
