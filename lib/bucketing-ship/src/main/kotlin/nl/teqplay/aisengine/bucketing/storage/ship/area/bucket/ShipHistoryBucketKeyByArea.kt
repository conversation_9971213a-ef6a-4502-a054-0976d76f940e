package nl.teqplay.aisengine.bucketing.storage.ship.area.bucket

import nl.teqplay.aisengine.bucketing.model.AisHistoricUnorderedDiffMessage
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketKeyDual
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessage
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessageInterface
import nl.teqplay.skeleton.model.Location

object ShipHistoryBucketKeyByArea :
    BucketKeyDual<AisHistoricUnorderedDiffMessage, AisHistoricMessageInterface, AisHistoricMessage, Location> {
    /**
     * Ship history by area, so the location of the [item] functions as key
     */
    override fun getBucketKey(item: AisHistoricUnorderedDiffMessage) = Location(item.lat, item.lon)
    override fun getOutputBucketKey(item: AisHistoricMessage) = item.location
}
