package nl.teqplay.aisengine.bucketing.model

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonProperty

// TODO: @JsonAliases can be removed once data on DEV has been purged
data class TransponderPositionBucketing(
    @JsonProperty("bow") @JsonAlias("distanceToBow") val distanceToBow: Int,
    @JsonProperty("srn") @JsonAlias("distanceToStern") val distanceToStern: Int,
    @JsonProperty("prt") @JsonAlias("distanceToPort") val distanceToPort: Int,
    @JsonProperty("str") @JsonAlias("distanceToStarboard") val distanceToStarboard: Int
)
