package nl.teqplay.aisengine.bucketing.model

import nl.teqplay.aisengine.bucketing.storage.BucketItemResolver
import nl.teqplay.aisengine.platform.model.LegacyHistoricShipInfo
import java.time.Instant

object LegacyHistoricShipInfoResolver : BucketItemResolver<LegacyHistoricShipInfo> {
    override fun toTimestamp(item: LegacyHistoricShipInfo): Instant = Instant.ofEpochMilli(item.timeLastUpdate)
    override fun toId(item: LegacyHistoricShipInfo): String = item.mmsi
}
