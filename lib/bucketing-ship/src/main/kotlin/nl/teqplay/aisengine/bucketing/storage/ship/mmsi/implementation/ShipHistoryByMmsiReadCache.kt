package nl.teqplay.aisengine.bucketing.storage.ship.mmsi.implementation

import com.fasterxml.jackson.databind.ObjectMapper
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.bucketing.factory.AisHistoricDiffMessageBucketFactoryByMmsi
import nl.teqplay.aisengine.bucketing.model.AisHistoricOrderedDiffMessage
import nl.teqplay.aisengine.bucketing.model.AisHistoricUnorderedDiffMessage
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveGlobalProperties
import nl.teqplay.aisengine.bucketing.properties.BucketProperties
import nl.teqplay.aisengine.bucketing.properties.ShipHistoryByMmsiArchiveProperties
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.cache.BucketReadCacheDual
import nl.teqplay.aisengine.bucketing.storage.cache.impl.ArchiveStorageFactory
import nl.teqplay.aisengine.bucketing.storage.cache.impl.BucketCacheFactory
import nl.teqplay.aisengine.bucketing.storage.ship.mmsi.ShipHistoryByMmsiCache
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessage
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessageInterface

/**
 * Read-only cache for ship history by mmsi
 */
class ShipHistoryByMmsiReadCache(
    bucketCacheFactory: BucketCacheFactory,
    archiveStorageFactory: ArchiveStorageFactory,
    mongoDatabase: MongoDatabase?,
    objectMapper: ObjectMapper,
    bucket: BucketProperties,
    archive: ShipHistoryByMmsiArchiveProperties,
    archiveGlobal: BucketArchiveGlobalProperties
) : ShipHistoryByMmsiCache,
    BucketReadCacheDual<AisHistoricOrderedDiffMessage, AisHistoricUnorderedDiffMessage, AisHistoricMessageInterface, AisHistoricMessage, Int, BucketId<Int>>(
        bucketCacheFactory.bucketReadCacheFactoryDual(
            collectionName = ShipHistoryByMmsiBucketDetails.collectionName,
            unorderedCollectionSuffix = ShipHistoryByMmsiBucketDetails.unorderedCollectionSuffix,
            mongoDatabase = mongoDatabase,
            objectMapper = objectMapper,

            bucket = bucket,
            archive = archive,
            archiveGlobal = archiveGlobal,
            factory = AisHistoricDiffMessageBucketFactoryByMmsi,
            createArchiveReadStorage = archiveStorageFactory.archiveReadStoragePerMonthDual()
        )
    )
