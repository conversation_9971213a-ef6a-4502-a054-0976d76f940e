package nl.teqplay.aisengine.bucketing.factory

import nl.teqplay.aisengine.bucketing.storage.bucket.AreaBucketId
import nl.teqplay.aisengine.bucketing.storage.ship.area.bucket.ShipHistoryBucketIdentifierByArea
import nl.teqplay.skeleton.model.Location

object AisHistoricDiffMessageBucketFactoryByArea :
    AisHistoricDiffMessageBucketFactory<Location, AreaBucketId<Location>>() {
    override val bucketIdentifier = ShipHistoryBucketIdentifierByArea
}
