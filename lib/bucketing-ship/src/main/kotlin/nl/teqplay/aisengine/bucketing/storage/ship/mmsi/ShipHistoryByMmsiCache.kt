package nl.teqplay.aisengine.bucketing.storage.ship.mmsi

import nl.teqplay.aisengine.bucketing.model.AisHistoricOrderedDiffMessage
import nl.teqplay.aisengine.bucketing.model.AisHistoricUnorderedDiffMessage
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessage
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessageInterface

interface ShipHistoryByMmsiCache :
    ShipHistoryByMmsiCacheInterfaceDual<AisHistoricOrderedDiffMessage, AisHistoricUnorderedDiffMessage, AisHistoricMessageInterface, AisHistoricMessage>
