package nl.teqplay.aisengine.bucketing.storage.ship.mmsi.bucket

import nl.teqplay.aisengine.bucketing.model.AisHistoricUnorderedDiffMessage
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketIdentifierDual
import nl.teqplay.aisengine.bucketing.storage.bucket.shared.BucketIdByMmsi
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessage
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessageInterface

object ShipHistoryBucketIdentifierByMmsi :
    BucketIdentifierDual<AisHistoricUnorderedDiffMessage, AisHistoricMessageInterface, AisHistoricMessage, Int, BucketId<Int>> {
    override val id = BucketIdByMmsi
    override val key = ShipHistoryBucketKeyByMmsi
    override val archive = defaultArchive()
}
