package nl.teqplay.aisengine.bucketing.storage.ship.mmsi.bucket

import nl.teqplay.aisengine.bucketing.model.AisHistoricUnorderedDiffMessage
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketKeyDual
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessage
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessageInterface

object ShipHistoryBucketKeyByMmsi :
    BucketKeyDual<AisHistoricUnorderedDiffMessage, AisHistoricMessageInterface, AisHistoricMessage, Int> {
    /**
     * Ship history by mmsi, so the mmsi of the [item] functions as key
     */
    override fun getBucketKey(item: AisHistoricUnorderedDiffMessage) = item.mmsi
    override fun getOutputBucketKey(item: AisHistoricMessage) = item.mmsi
}
