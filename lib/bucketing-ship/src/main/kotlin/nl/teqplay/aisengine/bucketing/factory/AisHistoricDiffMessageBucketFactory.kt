package nl.teqplay.aisengine.bucketing.factory

import nl.teqplay.aisengine.bucketing.model.AisHistoricMessageResolver
import nl.teqplay.aisengine.bucketing.model.AisHistoricOrderedDiffMessage
import nl.teqplay.aisengine.bucketing.model.AisHistoricUnorderedDiffMessage
import nl.teqplay.aisengine.bucketing.storage.BucketFactory
import nl.teqplay.aisengine.bucketing.storage.BucketFactoryDual
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.ship.ShipHistoryBucketFormatter
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessage
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessageInterface

/**
 * A [BucketFactory] for handling [AisHistoricOrderedDiffMessage]
 */
abstract class AisHistoricDiffMessageBucketFactory<Key : Any, ID : BucketId<Key>> :
    BucketFactoryDual<AisHistoricOrderedDiffMessage, AisHistoricUnorderedDiffMessage, AisHistoricMessageInterface, AisHistoricMessage, Key, ID> {

    override fun convertToOrdered(unordered: AisHistoricUnorderedDiffMessage) =
        AisHistoricOrderedDiffMessage(
            mmsi = unordered.mmsi,
            messageTime = unordered.messageTime,
            receptionTime = unordered.receptionTime,
            historic = unordered.historic,
            source = unordered.source,
            subSource = unordered.subSource,
            messageType = unordered.messageType,
            lat = unordered.lat,
            lon = unordered.lon,
            heading = unordered.heading,
            speedOverGround = unordered.speedOverGround,
            courseOverGround = unordered.courseOverGround,
            status = unordered.status,
            imo = unordered.imo,
            shipType = unordered.shipType,
            draught = unordered.draught,
            eta = unordered.eta,
            destination = unordered.destination,
            transponderPosition = unordered.transponderPosition
        )

    override val orderedDataClass = AisHistoricOrderedDiffMessage::class.java
    override val unorderedDataClass = AisHistoricUnorderedDiffMessage::class.java

    override val resolver = AisHistoricMessageResolver

    override val bucketFormatter = ShipHistoryBucketFormatter
}
