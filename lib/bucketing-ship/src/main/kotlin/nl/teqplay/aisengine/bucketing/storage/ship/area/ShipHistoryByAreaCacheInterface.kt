package nl.teqplay.aisengine.bucketing.storage.ship.area

import nl.teqplay.aisengine.bucketing.storage.bucket.AreaBucketId
import nl.teqplay.aisengine.bucketing.storage.cache.BucketCacheDual
import nl.teqplay.skeleton.model.BoundingBox
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.Polygon
import nl.teqplay.skeleton.model.TimeWindow
import nl.teqplay.skeleton.util.createBoundingBoxFromCircle
import nl.teqplay.skeleton.util.haversineDistance
import nl.teqplay.skeleton.util.pointInBoundingBox
import nl.teqplay.skeleton.util.pointInPolygon

/**
 * Interface specifying re-usable logic for ship history by area
 */
interface ShipHistoryByAreaCacheInterface<Data : Any> : ShipHistoryByAreaCacheInterfaceDual<Data, Data, Data, Data>

interface ShipHistoryByAreaCacheInterfaceDual<OrderedData : Data, UnorderedData : Data, Data : Any, Output : Data> :
    BucketCacheDual<OrderedData, UnorderedData, Data, Output, Location, AreaBucketId<Location>> {

    fun findHistoryInBoundingBox(
        window: TimeWindow,
        boundingBox: BoundingBox,
        sort: Boolean,
        extendToLongTerm: Boolean = true
    ): Sequence<Output> = fetchHistoryInTimeWindow(
        window = window,
        getBucketIds = { factory.bucketIdentifier.id.getBucketIds(it, boundingBox) },
        getArchiveBucketIds = { factory.bucketIdentifier.id.getArchiveBucketIds(it, boundingBox) },
        sort = sort,
        extendToLongTerm = extendToLongTerm
    ).filter { pointInBoundingBox(boundingBox, factory.bucketIdentifier.key.getOutputBucketKey(it)) }

    fun findHistoryInPolygon(
        window: TimeWindow,
        polygon: Polygon,
        sort: Boolean,
        extendToLongTerm: Boolean = true
    ): Sequence<Output> {
        val boundingBox = BoundingBox.fromPolygon(polygon)
        return findHistoryInBoundingBox(window, boundingBox, sort, extendToLongTerm)
            .filter { pointInPolygon(polygon, factory.bucketIdentifier.key.getOutputBucketKey(it)) }
    }

    fun findHistoryInCircle(
        window: TimeWindow,
        center: Location,
        radiusInKm: Double,
        sort: Boolean,
        extendToLongTerm: Boolean = true
    ): Sequence<Output> {
        // Find the bounding box enclosing this circle
        // TODO: this method can be optimized by more accurately calculating the buckets inside the circle instead of a bounding box around the circle
        val radiusInMeters = radiusInKm * 1000
        val boundingBox = createBoundingBoxFromCircle(center, radiusInMeters)
        return findHistoryInBoundingBox(window, boundingBox, sort, extendToLongTerm)
            .filter { haversineDistance(center, factory.bucketIdentifier.key.getOutputBucketKey(it)) <= radiusInMeters }
    }
}
