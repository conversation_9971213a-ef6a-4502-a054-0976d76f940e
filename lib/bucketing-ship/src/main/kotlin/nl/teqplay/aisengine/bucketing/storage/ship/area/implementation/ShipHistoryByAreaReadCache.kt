package nl.teqplay.aisengine.bucketing.storage.ship.area.implementation

import com.fasterxml.jackson.databind.ObjectMapper
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.bucketing.factory.AisHistoricDiffMessageBucketFactoryByArea
import nl.teqplay.aisengine.bucketing.model.AisHistoricOrderedDiffMessage
import nl.teqplay.aisengine.bucketing.model.AisHistoricUnorderedDiffMessage
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveGlobalProperties
import nl.teqplay.aisengine.bucketing.properties.BucketProperties
import nl.teqplay.aisengine.bucketing.properties.ShipHistoryByAreaArchiveProperties
import nl.teqplay.aisengine.bucketing.storage.bucket.AreaBucketId
import nl.teqplay.aisengine.bucketing.storage.cache.BucketReadCacheDual
import nl.teqplay.aisengine.bucketing.storage.cache.impl.ArchiveStorageFactory
import nl.teqplay.aisengine.bucketing.storage.cache.impl.BucketCacheFactory
import nl.teqplay.aisengine.bucketing.storage.ship.area.ShipHistoryByAreaCache
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessage
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessageInterface
import nl.teqplay.skeleton.model.Location

/**
 * Read-only cache for ship history by area
 */
class ShipHistoryByAreaReadCache(
    bucketCacheFactory: BucketCacheFactory,
    archiveStorageFactory: ArchiveStorageFactory,
    mongoDatabase: MongoDatabase?,
    objectMapper: ObjectMapper,
    bucket: BucketProperties,
    archive: ShipHistoryByAreaArchiveProperties,
    archiveGlobal: BucketArchiveGlobalProperties,
) : ShipHistoryByAreaCache,
    BucketReadCacheDual<AisHistoricOrderedDiffMessage, AisHistoricUnorderedDiffMessage, AisHistoricMessageInterface, AisHistoricMessage, Location, AreaBucketId<Location>>(
        bucketCacheFactory.bucketReadCacheFactoryDual(
            collectionName = ShipHistoryByAreaBucketDetails.collectionName,
            unorderedCollectionSuffix = ShipHistoryByAreaBucketDetails.unorderedCollectionSuffix,
            mongoDatabase = mongoDatabase,
            objectMapper = objectMapper,

            bucket = bucket,
            archive = archive,
            archiveGlobal = archiveGlobal,
            factory = AisHistoricDiffMessageBucketFactoryByArea,
            createArchiveReadStorage = archiveStorageFactory.archiveReadStoragePerMonthDual()
        )
    )
