package nl.teqplay.aisengine.bucketing.properties

import com.amazonaws.regions.Regions
import org.springframework.boot.context.properties.ConfigurationProperties
import java.time.LocalDate

/**
 * Properties for fetching ship history by area from the archive
 */
@ConfigurationProperties(prefix = "bucket.archive.ship.area-index")
data class ShipHistoryAreaIndexArchiveProperties(
    override val enabled: Boolean,
    override val writeNotBefore: LocalDate?,
    override val prefix: String,
    override val name: String,
    override val region: Regions
) : BucketArchiveConfiguration
