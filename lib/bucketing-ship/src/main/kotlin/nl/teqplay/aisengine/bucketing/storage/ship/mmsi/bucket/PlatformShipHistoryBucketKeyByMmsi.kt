package nl.teqplay.aisengine.bucketing.storage.ship.mmsi.bucket

import nl.teqplay.aisengine.bucketing.storage.bucket.BucketKey
import nl.teqplay.aisengine.platform.model.LegacyHistoricShipInfo

object PlatformShipHistoryBucketKeyByMmsi : <PERSON>et<PERSON>ey<LegacyHistoricShipInfo, Int> {

    /**
     * Ship history by mmsi, so the mmsi of the [item] functions as key
     */
    override fun getBucketKey(item: LegacyHistoricShipInfo): Int = item.mmsi.toIntOrNull() ?: 0
}
