package nl.teqplay.aisengine.bucketing.storage.ship.area.bucket

import nl.teqplay.aisengine.bucketing.model.AisHistoricUnorderedDiffMessage
import nl.teqplay.aisengine.bucketing.storage.bucket.AreaBucketId
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketIdentifierDual
import nl.teqplay.aisengine.bucketing.storage.bucket.shared.BucketIdByArea
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessage
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessageInterface
import nl.teqplay.skeleton.model.Location

object ShipHistoryBucketIdentifierByArea :
    BucketIdentifierDual<AisHistoricUnorderedDiffMessage, AisHistoricMessageInterface, AisHistoricMessage, Location, AreaBucketId<Location>> {
    override val id = BucketIdByArea
    override val key = ShipHistoryBucketKeyByArea
    override val archive = BucketIdByArea
}
