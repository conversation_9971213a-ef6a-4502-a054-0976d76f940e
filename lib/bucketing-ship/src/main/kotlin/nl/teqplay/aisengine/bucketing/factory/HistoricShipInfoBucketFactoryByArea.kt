package nl.teqplay.aisengine.bucketing.factory

import nl.teqplay.aisengine.bucketing.storage.bucket.AreaBucketId
import nl.teqplay.aisengine.bucketing.storage.ship.area.bucket.PlatformShipHistoryBucketIdentifierByArea
import nl.teqplay.skeleton.model.Location

object HistoricShipInfoBucketFactoryByArea : HistoricShipInfoBucketFactory<Location, AreaBucketId<Location>> {
    override val bucketIdentifier = PlatformShipHistoryBucketIdentifierByArea
}
