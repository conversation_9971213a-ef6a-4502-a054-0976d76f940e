package nl.teqplay.aisengine.bucketing.storage.ship.mmsi.bucket

import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketIdentifier
import nl.teqplay.aisengine.bucketing.storage.bucket.shared.BucketIdByMmsi
import nl.teqplay.aisengine.platform.model.LegacyHistoricShipInfo

object PlatformShipHistoryBucketIdentifierByMmsi : BucketIdentifier<LegacyHistoricShipInfo, Int, BucketId<Int>> {
    override val id = BucketIdByMmsi
    override val key = PlatformShipHistoryBucketKeyByMmsi
    override val archive = defaultArchive()
}
