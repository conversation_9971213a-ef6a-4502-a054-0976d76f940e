package nl.teqplay.aisengine.bucketing.storage.ship.area.implementation

import com.fasterxml.jackson.databind.ObjectMapper
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.bucketing.factory.HistoricShipInfoBucketFactoryByArea
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveGlobalProperties
import nl.teqplay.aisengine.bucketing.properties.BucketProperties
import nl.teqplay.aisengine.bucketing.properties.PlatformBucketProperties
import nl.teqplay.aisengine.bucketing.properties.PlatformShipHistoryByAreaArchiveProperties
import nl.teqplay.aisengine.bucketing.storage.bucket.AreaBucketId
import nl.teqplay.aisengine.bucketing.storage.cache.BucketReadCache
import nl.teqplay.aisengine.bucketing.storage.cache.impl.ArchiveStorageFactory
import nl.teqplay.aisengine.bucketing.storage.cache.impl.BucketCacheFactory
import nl.teqplay.aisengine.bucketing.storage.ship.area.PlatformShipHistoryByAreaCache
import nl.teqplay.aisengine.platform.model.LegacyHistoricShipInfo
import nl.teqplay.skeleton.model.Location

/**
 * Read-only cache for platform's ship history by area
 *
 * IMPORTANT: platform compatibility must only expose reading, no writing should be done
 * since that would corrupt platform's data.
 */
class PlatformShipHistoryByAreaReadCache(
    bucketCacheFactory: BucketCacheFactory,
    archiveStorageFactory: ArchiveStorageFactory,
    mongoDatabase: MongoDatabase,
    objectMapper: ObjectMapper,
    bucket: BucketProperties,
    archive: PlatformShipHistoryByAreaArchiveProperties,
    archiveGlobal: BucketArchiveGlobalProperties,
    platformBucketProperties: PlatformBucketProperties
) : PlatformShipHistoryByAreaCache,
    BucketReadCache<LegacyHistoricShipInfo, Location, AreaBucketId<Location>>(
        bucketCacheFactory.bucketReadCacheFactory(
            collectionName = PlatformShipHistoryByAreaBucketDetails.collectionName,
            unorderedCollectionSuffix = PlatformShipHistoryByAreaBucketDetails.unorderedCollectionSuffix,
            mongoDatabase = mongoDatabase,
            objectMapper = objectMapper,

            bucket = bucket,
            archive = archive,
            archiveGlobal = archiveGlobal,
            factory = HistoricShipInfoBucketFactoryByArea,
            createArchiveReadStorage = archiveStorageFactory.archiveReadStoragePerDay()
        ),
        overwriteMaxAge = platformBucketProperties.maxAge
    )
