package nl.teqplay.aisengine.bucketing.storage.ship.mmsi.implementation

import com.fasterxml.jackson.databind.ObjectMapper
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.bucketing.config.BucketingMetricRegistry
import nl.teqplay.aisengine.bucketing.factory.AisHistoricDiffMessageBucketFactoryByMmsi
import nl.teqplay.aisengine.bucketing.model.AisHistoricOrderedDiffMessage
import nl.teqplay.aisengine.bucketing.model.AisHistoricUnorderedDiffMessage
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveGlobalProperties
import nl.teqplay.aisengine.bucketing.properties.BucketProperties
import nl.teqplay.aisengine.bucketing.properties.ShipHistoryByMmsiArchiveProperties
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.cache.BucketWriteCacheDual
import nl.teqplay.aisengine.bucketing.storage.cache.impl.ArchiveStorageFactory
import nl.teqplay.aisengine.bucketing.storage.cache.impl.BucketCacheFactory
import nl.teqplay.aisengine.bucketing.storage.ship.mmsi.ShipHistoryByMmsiCache
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessage
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessageInterface
import java.time.Duration
import java.util.concurrent.ScheduledExecutorService

/**
 * Write/read cache for ship history by mmsi
 */
class ShipHistoryByMmsiWriteCache(
    bucketCacheFactory: BucketCacheFactory,
    archiveStorageFactory: ArchiveStorageFactory,
    mongoDatabase: MongoDatabase,
    objectMapper: ObjectMapper,
    bucket: BucketProperties,
    archive: ShipHistoryByMmsiArchiveProperties,
    archiveGlobal: BucketArchiveGlobalProperties,
    scheduledExecutorService: ScheduledExecutorService,
    flushInitialDelay: Duration,
    bucketingMetricRegistry: BucketingMetricRegistry,
) : ShipHistoryByMmsiCache,
    BucketWriteCacheDual<AisHistoricOrderedDiffMessage, AisHistoricUnorderedDiffMessage, AisHistoricMessageInterface, AisHistoricMessage, Int, BucketId<Int>>(
        bucketCacheFactory.bucketWriteCacheFactoryDual(
            collectionName = ShipHistoryByMmsiBucketDetails.collectionName,
            unorderedCollectionSuffix = ShipHistoryByMmsiBucketDetails.unorderedCollectionSuffix,
            mongoDatabase = mongoDatabase,
            objectMapper = objectMapper,

            bucket = bucket,
            archive = archive,
            archiveGlobal = archiveGlobal,
            scheduledExecutorService = scheduledExecutorService,
            flushInitialDelay = flushInitialDelay,
            factory = AisHistoricDiffMessageBucketFactoryByMmsi,
            createArchiveWriteStorage = archiveStorageFactory.archiveWriteStoragePerMonthDual()
        ),
        bucketingMetricRegistry
    )
