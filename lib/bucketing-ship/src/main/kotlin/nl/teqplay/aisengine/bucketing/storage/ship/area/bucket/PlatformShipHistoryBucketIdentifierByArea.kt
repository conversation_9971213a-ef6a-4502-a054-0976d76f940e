package nl.teqplay.aisengine.bucketing.storage.ship.area.bucket

import nl.teqplay.aisengine.bucketing.storage.bucket.AreaBucketId
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketIdentifier
import nl.teqplay.aisengine.bucketing.storage.bucket.shared.BucketIdByArea
import nl.teqplay.aisengine.platform.model.LegacyHistoricShipInfo
import nl.teqplay.skeleton.model.Location

object PlatformShipHistoryBucketIdentifierByArea : BucketIdentifier<LegacyHistoricShipInfo, Location, AreaBucketId<Location>> {
    override val id = BucketIdByArea
    override val key = PlatformShipHistoryBucketKeyByArea
    override val archive = BucketIdByArea
}
