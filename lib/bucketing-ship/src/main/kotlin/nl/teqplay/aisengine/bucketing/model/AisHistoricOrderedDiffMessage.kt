package nl.teqplay.aisengine.bucketing.model

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import nl.teqplay.aisengine.aisstream.model.AisMessage
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessageInterface
import java.time.Instant

@JsonInclude(JsonInclude.Include.NON_NULL)
data class AisHistoricOrderedDiffMessage(
    // always included in a diff message
    @JsonProperty("mmsi") override val mmsi: Int,
    @JsonProperty("time") override val messageTime: Instant,
    @JsonProperty("rec") override val receptionTime: Instant?,

    // metadata, only included if other fields changed
    @JsonProperty("old") var historic: Boolean? = null,
    @JsonProperty("src") var source: String? = null,
    @JsonProperty("sub") var subSource: String? = null,
    @JsonProperty("mst") var messageType: String? = null,

    // starting from here, only included if changed
    // position
    @JsonProperty("lat") var lat: Double? = null,
    @JsonProperty("lon") var lon: Double? = null,
    @JsonProperty("hea") var heading: Int? = null,
    @JsonProperty("sog") var speedOverGround: Float? = null,
    @JsonProperty("cog") var courseOverGround: Float? = null,
    @JsonProperty("sts") var status: AisMessage.ShipStatus? = null,

    // static
    @JsonProperty("imo") var imo: Int? = null,
    @JsonProperty("typ") var shipType: AisMessage.ShipType? = null,
    @JsonProperty("dra") var draught: Float? = null,
    @JsonProperty("eta") var eta: Instant? = null,
    @JsonProperty("dst") var destination: String? = null,
    @JsonProperty("tpp") var transponderPosition: TransponderPositionBucketing? = null,
) : AisHistoricMessageInterface {

    @JsonIgnore
    fun hasChanges() =
        listOfNotNull(
            lat, lon, heading, speedOverGround, courseOverGround, status,
            imo, shipType, draught, eta, destination, transponderPosition
        ).isNotEmpty()
}
