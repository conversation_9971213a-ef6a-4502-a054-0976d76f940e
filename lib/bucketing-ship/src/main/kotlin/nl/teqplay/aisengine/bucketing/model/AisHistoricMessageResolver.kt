package nl.teqplay.aisengine.bucketing.model

import nl.teqplay.aisengine.bucketing.storage.BucketItemResolver
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessageInterface

object AisHistoricMessageResolver : BucketItemResolver<AisHistoricMessageInterface> {
    override fun toTimestamp(item: AisHistoricMessageInterface) = item.messageTime
    override fun toId(item: AisHistoricMessageInterface) = item.mmsi.toString()
}
