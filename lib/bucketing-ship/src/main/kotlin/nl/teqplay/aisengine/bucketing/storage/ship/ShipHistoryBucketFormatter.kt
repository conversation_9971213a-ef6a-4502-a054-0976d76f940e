package nl.teqplay.aisengine.bucketing.storage.ship

import nl.teqplay.aisengine.bucketing.model.AisHistoricOrderedDiffMessage
import nl.teqplay.aisengine.bucketing.storage.BucketFormatter
import nl.teqplay.aisengine.bucketing.util.convert
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessage
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessageInterface
import nl.teqplay.aisengine.util.AisSource
import nl.teqplay.aisengine.util.parseAisSource
import nl.teqplay.skeleton.model.Location
import java.time.Duration
import java.time.Instant

object ShipHistoryBucketFormatter :
    BucketFormatter<AisHistoricOrderedDiffMessage, AisHistoricMessageInterface, AisHistoricMessage> {

    private const val NO_SUB_SOURCE = ""
    private const val NO_MESSAGE_TYPE = ""
    private const val NO_HEADING = 511
    private const val NO_IMO = -1
    private val NO_ETA = Instant.EPOCH
    private const val NO_DESTINATION = ""

    private val noUpdateFilterDuration = Duration.ofMinutes(15)

    override fun convertToOutput(item: AisHistoricOrderedDiffMessage): AisHistoricMessage? {
        val lat = item.lat ?: return null
        val lon = item.lon ?: return null
        val location = Location(lat, lon)

        val aisSource = when {
            // backward compatibility: old data only has source, and contains the source,subSource,messageType
            item.source?.startsWith("AIS:") == true -> parseAisSource(item.source)
            else -> AisSource(
                source = item.source ?: "",
                subSource = item.subSource,
                messageType = item.messageType
            )
        }

        return AisHistoricMessage(
            mmsi = item.mmsi,
            messageTime = item.messageTime,
            receptionTime = item.receptionTime,
            historic = item.historic ?: false,
            source = aisSource.source,
            subSource = aisSource.subSource,
            messageType = aisSource.messageType,
            location = location,
            heading = item.heading,
            speedOverGround = item.speedOverGround,
            courseOverGround = item.courseOverGround,
            status = item.status,
            imo = item.imo,
            shipType = item.shipType,
            draught = item.draught,
            eta = item.eta,
            destination = item.destination,
            transponderPosition = item.transponderPosition?.convert()
        )
    }

    override fun initForZip(data: List<AisHistoricOrderedDiffMessage>): List<AisHistoricOrderedDiffMessage> {
        return data
            // init, fields that are genuinely nullable have a replacement "null" marker
            .diffWithMutablePrevious { it, previous ->
                if (it.subSource == null) it.subSource = NO_SUB_SOURCE
                if (it.messageType == null) it.messageType = NO_MESSAGE_TYPE

                // historic messages might not know fields with "null" markers for sure
                // if we encounter null values, use the previous value instead of the "null", to preserve the value
                val takePrevious = it.historic == true && previous != null
                if (it.heading == null) it.heading = if (takePrevious) previous?.heading else NO_HEADING
                if (it.imo == null) it.imo = if (takePrevious) previous?.imo else NO_IMO
                if (it.eta == null) it.eta = if (takePrevious) previous?.eta else NO_ETA
                if (it.destination == null) it.destination = if (takePrevious) previous?.destination else NO_DESTINATION

                // do the same for fields without a "null" marker, but only setting to previous if we should
                if (takePrevious && it.lat == null) it.lat = previous?.lat
                if (takePrevious && it.lon == null) it.lon = previous?.lon
                if (takePrevious && it.speedOverGround == null) it.speedOverGround = previous?.speedOverGround
                if (takePrevious && it.courseOverGround == null) it.courseOverGround = previous?.courseOverGround
                if (takePrevious && it.status == null) it.status = previous?.status
                if (takePrevious && it.shipType == null) it.shipType = previous?.shipType
                if (takePrevious && it.draught == null) it.draught = previous?.draught
                if (takePrevious && it.transponderPosition == null) it.transponderPosition = previous?.transponderPosition
            }
    }

    override fun zip(data: List<AisHistoricOrderedDiffMessage>): List<AisHistoricOrderedDiffMessage> {
        val zippedByMmsi = mutableMapOf<Int, MutableList<AisHistoricOrderedDiffMessage>>()
        initForZip(data)
            // first diff fields
            .diff { it, previous ->
                if (it.lat == previous.lat) it.lat = null
                if (it.lon == previous.lon) it.lon = null
                if (it.heading == previous.heading) it.heading = null
                if (it.speedOverGround == previous.speedOverGround) it.speedOverGround = null
                if (it.courseOverGround == previous.courseOverGround) it.courseOverGround = null
                if (it.status == previous.status) it.status = null
                if (it.imo == previous.imo) it.imo = null
                if (it.shipType == previous.shipType) it.shipType = null
                if (it.draught == previous.draught) it.draught = null
                if (it.eta == previous.eta) it.eta = null
                if (it.destination == previous.destination) it.destination = null
                if (it.transponderPosition == previous.transponderPosition) it.transponderPosition = null
            }
            // now we can filter out empty updates
            .forEach {
                val zipped = zippedByMmsi[it.mmsi]
                if (zipped == null || zipped.size < 2) {
                    // not enough data yet to filter
                    if (zipped == null) {
                        zippedByMmsi[it.mmsi] = mutableListOf(it)
                    } else {
                        zipped.add(it)
                    }
                } else {
                    val (secondToLast, last) = zipped.takeLast(2)
                    if (!last.hasChanges() &&
                        Duration.between(secondToLast.messageTime, it.messageTime) <= noUpdateFilterDuration &&
                        // replace last message with current message if:
                        // - always if the last was a historic message (with no changes), irrelevant
                        // - if both the second to last and current message are real-time, don't need to know if the
                        //   last was real-time or historic, we can safely drop it
                        (last.historic == true || (secondToLast.historic == false && it.historic == false))
                    ) {
                        // no changes, we can overwrite the previous
                        zipped[zipped.size - 1] = it
                    } else {
                        zipped.add(it)
                    }
                }
            }

        return zippedByMmsi.values.flatten().sortedBy { it.messageTime }.diff { it, previous ->
            if (it.historic == previous.historic) it.historic = null
            if (it.source == previous.source) it.source = null
            if (it.subSource == previous.subSource) it.subSource = null
            if (it.messageType == previous.messageType) it.messageType = null
        }
    }

    /**
     * Run an [update] on each [AisHistoricOrderedDiffMessage] with the next.
     * Does one extra iteration, by including `null` as the first entry.
     * No copy of the message is made, like in [diff], meaning the actions applied by [update] for
     * the current item is visible when used as the previous item.
     */
    private inline fun <C : Iterable<AisHistoricOrderedDiffMessage>> C.diffWithMutablePrevious(
        crossinline update: (AisHistoricOrderedDiffMessage, AisHistoricOrderedDiffMessage?) -> Unit
    ): C {
        val map = mutableMapOf<Int, AisHistoricOrderedDiffMessage>()
        onEach {
            val previous = map[it.mmsi]
            update(it, previous)
            map[it.mmsi] = it
        }
        return this
    }

    /**
     * Run an [update] on each [AisHistoricOrderedDiffMessage] with the next.
     * Making a copy of the current message to be used as the previous later.
     */
    private inline fun <C : Iterable<AisHistoricOrderedDiffMessage>> C.diff(
        crossinline update: (AisHistoricOrderedDiffMessage, AisHistoricOrderedDiffMessage) -> Unit
    ): C {
        val map = mutableMapOf<Int, AisHistoricOrderedDiffMessage>()
        onEach {
            val previous = map[it.mmsi]
            map[it.mmsi] = it.copy()
            if (previous != null) update(it, previous)
        }
        return this
    }

    override fun unzip(data: List<AisHistoricOrderedDiffMessage>): List<AisHistoricOrderedDiffMessage> {
        val map = mutableMapOf<Int, AisHistoricOrderedDiffMessage>()
        return data.map { unzipItem(it, map) }
    }

    override fun unzipAsSequence(data: Sequence<AisHistoricOrderedDiffMessage>): Sequence<AisHistoricOrderedDiffMessage> {
        val map = mutableMapOf<Int, AisHistoricOrderedDiffMessage>()
        return data.map { unzipItem(it, map) }
    }

    private fun unzipItem(
        message: AisHistoricOrderedDiffMessage,
        map: MutableMap<Int, AisHistoricOrderedDiffMessage>,
    ): AisHistoricOrderedDiffMessage {
        val previous = map[message.mmsi]
        if (previous != null) {
            if (message.historic == null) message.historic = previous.historic
            if (message.source == null) message.source = previous.source
            if (message.subSource == null) message.subSource = previous.subSource
            if (message.messageType == null) message.messageType = previous.messageType
            if (message.lat == null) message.lat = previous.lat
            if (message.lon == null) message.lon = previous.lon
            if (message.heading == null) message.heading = previous.heading
            if (message.speedOverGround == null) message.speedOverGround = previous.speedOverGround
            if (message.courseOverGround == null) message.courseOverGround = previous.courseOverGround
            if (message.status == null) message.status = previous.status
            if (message.imo == null) message.imo = previous.imo
            if (message.shipType == null) message.shipType = previous.shipType
            if (message.draught == null) message.draught = previous.draught
            if (message.eta == null) message.eta = previous.eta
            if (message.destination == null) message.destination = previous.destination
            if (message.transponderPosition == null) message.transponderPosition = previous.transponderPosition
        }

        // replace "null" markers with actual null
        if (message.subSource == NO_SUB_SOURCE) message.subSource = null
        if (message.messageType == NO_MESSAGE_TYPE) message.messageType = null
        if (message.heading == NO_HEADING) message.heading = null
        if (message.imo == NO_IMO) message.imo = null
        if (message.eta == NO_ETA) message.eta = null
        if (message.destination == NO_DESTINATION) message.destination = null

        map[message.mmsi] = message
        return message
    }
}
