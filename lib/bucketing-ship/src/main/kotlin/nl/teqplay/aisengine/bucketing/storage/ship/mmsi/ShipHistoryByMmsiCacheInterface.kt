package nl.teqplay.aisengine.bucketing.storage.ship.mmsi

import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.cache.BucketCacheDual
import nl.teqplay.skeleton.model.TimeWindow

/**
 * Interface specifying re-usable logic for ship history by mmsi
 */
interface ShipHistoryByMmsiCacheInterface<Data : Any> : ShipHistoryByMmsiCacheInterfaceDual<Data, Data, Data, Data>

interface ShipHistoryByMmsiCacheInterfaceDual<
    OrderedData : Data,
    UnorderedData : Data,
    Data : Any,
    Output : Data
    > : BucketCacheDual<OrderedData, UnorderedData, Data, Output, Int, BucketId<Int>> {

    fun findHistory(
        window: TimeWindow,
        mmsi: Int,
        extendToLongTerm: Boolean = true
    ): Sequence<Output> = fetchHistoryInTimeWindow(window, mmsi, extendToLongTerm)
}
