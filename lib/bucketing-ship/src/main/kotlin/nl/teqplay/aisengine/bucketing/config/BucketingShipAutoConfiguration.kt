package nl.teqplay.aisengine.bucketing.config

import nl.teqplay.aisengine.YamlPropertyLoaderFactory
import nl.teqplay.aisengine.bucketing.properties.PlatformShipHistoryByAreaArchiveProperties
import nl.teqplay.aisengine.bucketing.properties.PlatformShipHistoryByMmsiArchiveProperties
import nl.teqplay.aisengine.bucketing.properties.ShipHistoryAreaIndexArchiveProperties
import nl.teqplay.aisengine.bucketing.properties.ShipHistoryByAreaArchiveProperties
import nl.teqplay.aisengine.bucketing.properties.ShipHistoryByMmsiArchiveProperties
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.PropertySource

@Configuration
@EnableConfigurationProperties(
    ShipHistoryByMmsiArchiveProperties::class,
    ShipHistoryByAreaArchiveProperties::class,
    ShipHistoryAreaIndexArchiveProperties::class,
    PlatformShipHistoryByMmsiArchiveProperties::class,
    PlatformShipHistoryByAreaArchiveProperties::class
)
@PropertySource("classpath:application-lib-bucketing-ship.yml", factory = YamlPropertyLoaderFactory::class)
class BucketingShipAutoConfiguration
