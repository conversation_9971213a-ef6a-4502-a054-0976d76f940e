package nl.teqplay.aisengine.bucketing.storage.ship.mmsi.implementation

import com.fasterxml.jackson.databind.ObjectMapper
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.bucketing.factory.HistoricShipInfoBucketFactoryByMmsi
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveGlobalProperties
import nl.teqplay.aisengine.bucketing.properties.BucketProperties
import nl.teqplay.aisengine.bucketing.properties.PlatformBucketProperties
import nl.teqplay.aisengine.bucketing.properties.PlatformShipHistoryByMmsiArchiveProperties
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.cache.BucketReadCache
import nl.teqplay.aisengine.bucketing.storage.cache.impl.ArchiveStorageFactory
import nl.teqplay.aisengine.bucketing.storage.cache.impl.BucketCacheFactory
import nl.teqplay.aisengine.bucketing.storage.ship.mmsi.PlatformShipHistoryByMmsiCache
import nl.teqplay.aisengine.platform.model.LegacyHistoricShipInfo

/**
 * Read-only cache for platform's ship history by mmsi
 *
 * IMPORTANT: platform compatibility must only expose reading, no writing should be done
 * since that would corrupt platform's data.
 */
class PlatformShipHistoryByMmsiReadCache(
    bucketCacheFactory: BucketCacheFactory,
    archiveStorageFactory: ArchiveStorageFactory,
    mongoDatabase: MongoDatabase,
    objectMapper: ObjectMapper,
    bucket: BucketProperties,
    archive: PlatformShipHistoryByMmsiArchiveProperties,
    archiveGlobal: BucketArchiveGlobalProperties,
    platformBucketProperties: PlatformBucketProperties
) : PlatformShipHistoryByMmsiCache,
    BucketReadCache<LegacyHistoricShipInfo, Int, BucketId<Int>>(
        bucketCacheFactory.bucketReadCacheFactory(
            collectionName = PlatformShipHistoryByMmsiBucketDetails.collectionName,
            unorderedCollectionSuffix = PlatformShipHistoryByMmsiBucketDetails.unorderedCollectionSuffix,
            mongoDatabase = mongoDatabase,
            objectMapper = objectMapper,

            bucket = bucket,
            archive = archive,
            archiveGlobal = archiveGlobal,
            factory = HistoricShipInfoBucketFactoryByMmsi,
            createArchiveReadStorage = archiveStorageFactory.archiveReadStoragePerDay()
        ),
        overwriteMaxAge = platformBucketProperties.maxAge
    )
