package nl.teqplay.aisengine.bucketing.util

import nl.teqplay.aisengine.aisstream.model.TransponderPosition
import nl.teqplay.aisengine.bucketing.model.TransponderPositionBucketing

fun TransponderPosition.convert() = TransponderPositionBucketing(
    distanceToBow = distanceToBow,
    distanceToStern = distanceToStern,
    distanceToPort = distanceToPort,
    distanceToStarboard = distanceToStarboard
)

fun TransponderPositionBucketing.convert() = TransponderPosition(
    distanceToBow = distanceToBow,
    distanceToStern = distanceToStern,
    distanceToPort = distanceToPort,
    distanceToStarboard = distanceToStarboard
)
