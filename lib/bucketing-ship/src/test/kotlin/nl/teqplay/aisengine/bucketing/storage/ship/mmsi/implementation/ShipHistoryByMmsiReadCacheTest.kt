package nl.teqplay.aisengine.bucketing.storage.ship.mmsi.implementation

import nl.teqplay.aisengine.bucketing.factory.AisHistoricDiffMessageBucketFactoryByMmsi
import nl.teqplay.aisengine.bucketing.model.AisHistoricOrderedDiffMessage
import nl.teqplay.aisengine.bucketing.model.AisHistoricUnorderedDiffMessage
import nl.teqplay.aisengine.bucketing.model.bucket.OrderedBucket
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveConfiguration
import nl.teqplay.aisengine.bucketing.properties.BucketProperties
import nl.teqplay.aisengine.bucketing.storage.BucketFactoryDual
import nl.teqplay.aisengine.bucketing.storage.archive.ArchiveReadStorage
import nl.teqplay.aisengine.bucketing.storage.bucket.AreaBucketId
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.cache.BucketReadCacheFactoryDual
import nl.teqplay.aisengine.bucketing.storage.cache.impl.ArchiveStorageFactory
import nl.teqplay.aisengine.bucketing.storage.cache.impl.BucketCacheFactory
import nl.teqplay.aisengine.bucketing.storage.mongo.MongoBucketReadStorage
import nl.teqplay.aisengine.bucketing.storage.mongo.UnorderedMongoBucketReadStorage
import nl.teqplay.aisengine.bucketing.storage.ship.ShipHistoryBucketFormatter
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessage
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessageInterface
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.TimeWindow
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.ArgumentMatchers.anyList
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.time.Duration
import java.time.Instant
import java.time.temporal.ChronoUnit

class ShipHistoryByMmsiReadCacheTest {

    private var collectionNameCheck: String? = null
    private var unorderedCollectionSuffix: String? = null

    private val ship1 = AisHistoricOrderedDiffMessage(
        mmsi = 0,
        messageTime = Instant.EPOCH,
        receptionTime = Instant.EPOCH,
        lat = 52.0,
        lon = 4.0
    )
    private val ship2 = AisHistoricOrderedDiffMessage(
        mmsi = 0,
        messageTime = Instant.EPOCH.plusSeconds(1),
        receptionTime = Instant.EPOCH.plusSeconds(1),
        lat = 52.0,
        lon = 4.0
    )

    private val mongoStorage =
        mock<MongoBucketReadStorage<AisHistoricOrderedDiffMessage, AisHistoricMessageInterface>>().apply {
            whenever(load(anyList())).thenAnswer {
                listOf(OrderedBucket("1970-01-01,0", mutableListOf(ship1))).iterator()
            }
        }

    private val unorderedStorage =
        mock<UnorderedMongoBucketReadStorage<AisHistoricOrderedDiffMessage, AisHistoricUnorderedDiffMessage, AisHistoricMessageInterface>>().apply {
            whenever(load(anyList())).thenAnswer {
                listOf(OrderedBucket("1970-01-01,0", mutableListOf(ship2))).iterator()
            }
        }

    private val bucketCacheFactory = mock<BucketCacheFactory>().apply {
        whenever(
            bucketReadCacheFactoryDual<AisHistoricOrderedDiffMessage, AisHistoricUnorderedDiffMessage, AisHistoricMessageInterface, AisHistoricMessage, Location, AreaBucketId<Location>>(
                collectionName = any(),
                mongoDatabase = any(),
                objectMapper = any(),
                bucket = any(),
                archive = any(),
                archiveGlobal = any(),
                factory = any(),
                createArchiveReadStorage = any(),
                unorderedCollectionSuffix = any()
            )
        ).thenAnswer {
            collectionNameCheck = it.getArgument<String>(0)
            unorderedCollectionSuffix = it.getArgument<String>(8)
            val factory =
                it.getArgument<BucketFactoryDual<AisHistoricOrderedDiffMessage, AisHistoricUnorderedDiffMessage, AisHistoricMessageInterface, AisHistoricMessage, Int, BucketId<Int>>>(
                    6
                )
            object :
                BucketReadCacheFactoryDual<AisHistoricOrderedDiffMessage, AisHistoricUnorderedDiffMessage, AisHistoricMessageInterface, AisHistoricMessage, Int, BucketId<Int>> {
                override val collectionName: String = collectionNameCheck!!
                override val bucket = BucketProperties(
                    sweeper = BucketProperties.Sweeper(Duration.ZERO),
                    mongo = BucketProperties.Mongo(10, mock())
                )
                override val archive = mock<BucketArchiveConfiguration>()
                override val factory = factory

                override fun mongoStorage() = mongoStorage

                override fun unorderedStorage() = unorderedStorage

                override fun archiveStorage() =
                    mock<ArchiveReadStorage<AisHistoricOrderedDiffMessage>>()
            }
        }
    }

    private val archiveStorageFactory = mock<ArchiveStorageFactory>().apply {
        whenever(archiveReadStoragePerMonthDual<AisHistoricOrderedDiffMessage, AisHistoricUnorderedDiffMessage, AisHistoricMessageInterface, AisHistoricMessage, Location, AreaBucketId<Location>>())
            .thenReturn(mock())
    }

    private val shipHistoryByAreaMmsiCache = ShipHistoryByMmsiReadCache(
        bucketCacheFactory = bucketCacheFactory,
        archiveStorageFactory = archiveStorageFactory,
        mongoDatabase = mock(),
        objectMapper = mock(),
        bucket = mock(),
        archive = mock(),
        archiveGlobal = mock()
    )

    @Test
    fun validate() {
        assertEquals("shipHistoryByMmsi", collectionNameCheck)
        assertEquals("unordered", unorderedCollectionSuffix)
        assertEquals(AisHistoricDiffMessageBucketFactoryByMmsi, shipHistoryByAreaMmsiCache.factory)
    }

    @Test
    fun findHistory() {
        assertEquals(
            listOf(ship1, ship2).map(ShipHistoryBucketFormatter::convertToOutput),
            shipHistoryByAreaMmsiCache.findHistory(
                window = TimeWindow(Instant.EPOCH, Instant.EPOCH.plus(1, ChronoUnit.DAYS)),
                mmsi = 0,
                extendToLongTerm = false
            ).toList()
        )
    }
}
