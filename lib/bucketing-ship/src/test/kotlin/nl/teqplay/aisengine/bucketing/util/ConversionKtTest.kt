package nl.teqplay.aisengine.bucketing.util

import nl.teqplay.aisengine.aisstream.model.TransponderPosition
import nl.teqplay.aisengine.bucketing.model.TransponderPositionBucketing
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class ConversionKtTest {

    @Test
    fun convert() {
        val tpb = TransponderPositionBucketing(
            distanceToBow = 1,
            distanceToStern = 2,
            distanceToPort = 3,
            distanceToStarboard = 4
        )
        val tp = TransponderPosition(
            distanceToBow = 1,
            distanceToStern = 2,
            distanceToPort = 3,
            distanceToStarboard = 4
        )

        assertEquals(tp, tpb.convert())
        assertEquals(tpb, tp.convert())
    }
}
