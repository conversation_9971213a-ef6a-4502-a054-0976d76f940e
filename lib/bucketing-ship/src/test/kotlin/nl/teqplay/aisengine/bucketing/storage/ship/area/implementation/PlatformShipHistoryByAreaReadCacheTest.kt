package nl.teqplay.aisengine.bucketing.storage.ship.area.implementation

import nl.teqplay.aisengine.bucketing.factory.HistoricShipInfoBucketFactoryByArea
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveConfiguration
import nl.teqplay.aisengine.bucketing.properties.BucketProperties
import nl.teqplay.aisengine.bucketing.storage.BucketFactory
import nl.teqplay.aisengine.bucketing.storage.archive.ArchiveReadStorage
import nl.teqplay.aisengine.bucketing.storage.bucket.AreaBucketId
import nl.teqplay.aisengine.bucketing.storage.cache.BucketReadCacheFactory
import nl.teqplay.aisengine.bucketing.storage.cache.impl.ArchiveStorageFactory
import nl.teqplay.aisengine.bucketing.storage.cache.impl.BucketCacheFactory
import nl.teqplay.aisengine.bucketing.storage.mongo.MongoBucketReadStorage
import nl.teqplay.aisengine.bucketing.storage.mongo.UnorderedMongoBucketReadStorage
import nl.teqplay.aisengine.platform.model.LegacyHistoricShipInfo
import nl.teqplay.skeleton.model.Location
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever

class PlatformShipHistoryByAreaReadCacheTest {

    @Test
    fun init() {
        var collectionName: String? = null
        var unorderedCollectionSuffix: String? = null

        val bucketCacheFactory = mock<BucketCacheFactory>().apply {
            whenever(
                bucketReadCacheFactory<LegacyHistoricShipInfo, Location, AreaBucketId<Location>>(
                    collectionName = any(),
                    mongoDatabase = any(),
                    objectMapper = any(),
                    bucket = any(),
                    archive = any(),
                    archiveGlobal = any(),
                    factory = any(),
                    createArchiveReadStorage = any(),
                    unorderedCollectionSuffix = any()
                )
            ).thenAnswer {
                collectionName = it.getArgument<String>(0)
                unorderedCollectionSuffix = it.getArgument<String>(8)
                val factory = it.getArgument<BucketFactory<LegacyHistoricShipInfo, Location, AreaBucketId<Location>>>(6)
                object : BucketReadCacheFactory<LegacyHistoricShipInfo, Location, AreaBucketId<Location>> {
                    override val collectionName: String = collectionName!!
                    override val bucket = mock<BucketProperties>()
                    override val archive = mock<BucketArchiveConfiguration>()
                    override val factory = factory

                    override fun mongoStorage() =
                        mock<MongoBucketReadStorage<LegacyHistoricShipInfo, LegacyHistoricShipInfo>>()

                    override fun unorderedStorage() =
                        mock<UnorderedMongoBucketReadStorage<LegacyHistoricShipInfo, LegacyHistoricShipInfo, LegacyHistoricShipInfo>>()

                    override fun archiveStorage() =
                        mock<ArchiveReadStorage<LegacyHistoricShipInfo>>()
                }
            }
        }

        val archiveStorageFactory = mock<ArchiveStorageFactory>().apply {
            whenever(archiveReadStoragePerDay<LegacyHistoricShipInfo, Location, AreaBucketId<Location>>())
                .thenReturn(mock())
        }

        val platformByAreaReadCache = PlatformShipHistoryByAreaReadCache(
            bucketCacheFactory = bucketCacheFactory,
            archiveStorageFactory = archiveStorageFactory,
            mongoDatabase = mock(),
            objectMapper = mock(),
            bucket = mock(),
            archive = mock(),
            archiveGlobal = mock(),
            platformBucketProperties = mock()
        )

        assertNotNull(platformByAreaReadCache)
        assertEquals("areahistory", collectionName)
        assertEquals("new_data", unorderedCollectionSuffix)
        assertEquals(HistoricShipInfoBucketFactoryByArea, platformByAreaReadCache.factory)
    }
}
