package nl.teqplay.aisengine.bucketing.storage.ship.area.implementation

import nl.teqplay.aisengine.bucketing.factory.AisHistoricDiffMessageBucketFactoryByArea
import nl.teqplay.aisengine.bucketing.model.AisHistoricOrderedDiffMessage
import nl.teqplay.aisengine.bucketing.model.AisHistoricUnorderedDiffMessage
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveConfiguration
import nl.teqplay.aisengine.bucketing.properties.BucketProperties
import nl.teqplay.aisengine.bucketing.storage.BucketFactoryDual
import nl.teqplay.aisengine.bucketing.storage.archive.ArchiveWriteStorage
import nl.teqplay.aisengine.bucketing.storage.bucket.AreaBucketId
import nl.teqplay.aisengine.bucketing.storage.cache.BucketWriteCacheFactoryDual
import nl.teqplay.aisengine.bucketing.storage.cache.impl.ArchiveStorageFactory
import nl.teqplay.aisengine.bucketing.storage.cache.impl.BucketCacheFactory
import nl.teqplay.aisengine.bucketing.storage.mongo.MongoBucketWriteStorage
import nl.teqplay.aisengine.bucketing.storage.mongo.UnorderedMongoBucketWriteStorage
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessage
import nl.teqplay.aisengine.shiphistory.model.AisHistoricMessageInterface
import nl.teqplay.skeleton.model.Location
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import java.time.Duration

class ShipHistoryByAreaWriteCacheTest {

    @Test
    fun init() {
        var collectionName: String? = null
        var unorderedCollectionSuffix: String? = null

        val bucketCacheFactory = mock<BucketCacheFactory>().apply {
            whenever(
                bucketWriteCacheFactoryDual<AisHistoricOrderedDiffMessage, AisHistoricUnorderedDiffMessage, AisHistoricMessageInterface, AisHistoricMessage, Location, AreaBucketId<Location>>(
                    collectionName = any(),
                    mongoDatabase = any(),
                    objectMapper = any(),
                    bucket = any(),
                    archive = any(),
                    archiveGlobal = any(),
                    scheduledExecutorService = any(),
                    flushInitialDelay = any(),
                    factory = any(),
                    createArchiveWriteStorage = any(),
                    unorderedCollectionSuffix = any()
                )
            ).thenAnswer {
                collectionName = it.getArgument<String>(0)
                unorderedCollectionSuffix = it.getArgument<String>(10)
                val factory =
                    it.getArgument<BucketFactoryDual<AisHistoricOrderedDiffMessage, AisHistoricUnorderedDiffMessage, AisHistoricMessageInterface, AisHistoricMessage, Location, AreaBucketId<Location>>>(
                        8
                    )
                object :
                    BucketWriteCacheFactoryDual<AisHistoricOrderedDiffMessage, AisHistoricUnorderedDiffMessage, AisHistoricMessageInterface, AisHistoricMessage, Location, AreaBucketId<Location>> {
                    override val collectionName: String = collectionName!!
                    override val bucket = BucketProperties(
                        sweeper = BucketProperties.Sweeper(Duration.ZERO),
                        mongo = mock()
                    )
                    override val archive = mock<BucketArchiveConfiguration>()
                    override val factory = factory

                    override fun mongoStorage() =
                        mock<MongoBucketWriteStorage<AisHistoricOrderedDiffMessage, AisHistoricMessageInterface>>()

                    override fun unorderedStorage() =
                        mock<UnorderedMongoBucketWriteStorage<AisHistoricOrderedDiffMessage, AisHistoricUnorderedDiffMessage, AisHistoricMessageInterface>>()

                    override fun archiveStorage() =
                        mock<ArchiveWriteStorage<AisHistoricOrderedDiffMessage>>()
                }
            }
        }

        val archiveStorageFactory = mock<ArchiveStorageFactory>().apply {
            whenever(archiveWriteStoragePerMonthDual<AisHistoricOrderedDiffMessage, AisHistoricUnorderedDiffMessage, AisHistoricMessageInterface, AisHistoricMessage, Location, AreaBucketId<Location>>())
                .thenReturn(mock())
        }

        val shipHistoryByAreaWriteCache = ShipHistoryByAreaWriteCache(
            bucketCacheFactory = bucketCacheFactory,
            archiveStorageFactory = archiveStorageFactory,
            mongoDatabase = mock(),
            objectMapper = mock(),
            bucket = mock(),
            archive = mock(),
            archiveGlobal = mock(),
            scheduledExecutorService = mock(),
            flushInitialDelay = Duration.ZERO,
            bucketingMetricRegistry = mock(),
        )

        assertNotNull(shipHistoryByAreaWriteCache)
        assertEquals("shipHistoryByArea", collectionName)
        assertEquals("unordered", unorderedCollectionSuffix)
        assertEquals(AisHistoricDiffMessageBucketFactoryByArea, shipHistoryByAreaWriteCache.factory)
    }
}
