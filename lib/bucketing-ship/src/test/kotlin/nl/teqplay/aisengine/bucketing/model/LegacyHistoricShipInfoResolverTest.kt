package nl.teqplay.aisengine.bucketing.model

import nl.teqplay.aisengine.platform.model.LegacyHistoricShipInfo
import nl.teqplay.platform.model.Location
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.time.Instant

class LegacyHistoricShipInfoResolverTest {

    private val item = LegacyHistoricShipInfo(
        mmsi = "0",
        timeLastUpdate = 0,
        location = Location(0.0, 0.0)
    )

    @Test
    fun toTimestamp() {
        assertEquals(Instant.EPOCH, LegacyHistoricShipInfoResolver.toTimestamp(item))
    }

    @Test
    fun toId() {
        assertEquals("0", LegacyHistoricShipInfoResolver.toId(item))
    }
}
