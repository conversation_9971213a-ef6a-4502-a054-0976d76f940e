package nl.teqplay.aisengine.bucketing.factory

import nl.teqplay.aisengine.bucketing.model.LegacyHistoricShipInfoResolver
import nl.teqplay.aisengine.bucketing.storage.DefaultBucketFormatter
import nl.teqplay.aisengine.platform.model.LegacyHistoricShipInfo
import nl.teqplay.skeleton.model.Location
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import nl.teqplay.platform.model.Location as PlatformLocation

internal class HistoricShipInfoBucketFactoryByAreaTest {

    @Test
    fun getDataClass() {
        assertEquals(
            LegacyHistoricShipInfo::class.java,
            HistoricShipInfoBucketFactoryByArea.dataClass
        )
    }

    @Test
    fun getResolver() {
        assertEquals(
            LegacyHistoricShipInfoResolver,
            HistoricShipInfoBucketFactoryByArea.resolver
        )
    }

    @Test
    fun getBucketFormatter() {
        assertEquals(
            DefaultBucketFormatter<LegacyHistoricShipInfo>().javaClass,
            HistoricShipInfoBucketFactoryByArea.bucketFormatter.javaClass
        )
    }

    @Test
    fun getBucketKey() {
        val item = LegacyHistoricShipInfo(
            mmsi = "1234567",
            timeLastUpdate = 0,
            location = PlatformLocation(1.2, 3.4)
        )
        assertEquals(
            Location(1.2, 3.4),
            HistoricShipInfoBucketFactoryByArea.bucketIdentifier.key.getBucketKey(item)
        )
    }
}
