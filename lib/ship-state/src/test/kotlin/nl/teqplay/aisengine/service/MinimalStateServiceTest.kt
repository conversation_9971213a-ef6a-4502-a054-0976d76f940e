package nl.teqplay.aisengine.service

import com.mongodb.kotlin.client.FindIterable
import com.mongodb.kotlin.client.MongoCursor
import nl.teqplay.aisengine.aisstream.model.AisLongRangeMessage
import nl.teqplay.aisengine.aisstream.model.AisLongRangeWrapper
import nl.teqplay.aisengine.aisstream.model.AisPositionMessage
import nl.teqplay.aisengine.aisstream.model.AisPositionWrapper
import nl.teqplay.aisengine.aisstream.model.AisStaticMessage
import nl.teqplay.aisengine.aisstream.model.AisStaticWrapper
import nl.teqplay.aisengine.aisstream.model.AisStationMessage
import nl.teqplay.aisengine.aisstream.model.AisStationWrapper
import nl.teqplay.aisengine.aisstream.model.AisWrapper
import nl.teqplay.aisengine.aisstream.model.BaseAisMessage
import nl.teqplay.aisengine.aisstream.model.isNullOrEmpty
import nl.teqplay.aisengine.datasource.MinimalStateDataSourceImpl
import nl.teqplay.aisengine.datasource.MinimalStateWrapper
import nl.teqplay.aisengine.model.MinimalState
import nl.teqplay.skeleton.common.BaseTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.test.annotation.DirtiesContext
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.TestConstructor
import java.time.Duration
import java.time.Instant
import java.util.concurrent.atomic.AtomicInteger

@ContextConfiguration
@TestConstructor(autowireMode = TestConstructor.AutowireMode.ALL)
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
internal class MinimalStateServiceTest(
    private val minimalStateService: TestMinimalStateService
) : BaseTest() {

    companion object {
        private val counter = AtomicInteger()
        private val isNew = AtomicInteger()
    }

    class TestMinimalStateService(
        stateDataSource: MinimalStateDataSourceImpl,
        syncInterval: Duration
    ) : MinimalStateServiceImpl(stateDataSource, syncInterval) {
        override fun process(wrapper: AisWrapper<out BaseAisMessage>, previousState: MinimalState?) {
            counter.addAndGet(1)
            if (!wrapper.historic) {
                isNew.addAndGet(1)
            }
        }

        fun getState(mmsi: Int) = getStateByMmsi(mmsi)
    }

    @TestConfiguration
    class Configuration {
        @Bean
        fun testMinimalStateDataSource(): MinimalStateDataSourceImpl {
            val cursor = mock<MongoCursor<MinimalStateWrapper>>()
            val findIterable = mock<FindIterable<MinimalStateWrapper>>().also {
                whenever(it.cursor()).thenReturn(cursor)
            }
            val minimalStateDataSource = mock<MinimalStateDataSourceImpl>().also {
                whenever(it.getOlderState(any())).thenReturn(findIterable)
                whenever(it.getInitialState(any())).thenReturn(findIterable)
                whenever(it.findStateById(any())).thenReturn(null)
            }

            return minimalStateDataSource
        }

        @Bean
        fun testMinimalStateService(
            stateDataSource: MinimalStateDataSourceImpl
        ) = TestMinimalStateService(stateDataSource, Duration.ofMinutes(5))
    }

    @BeforeEach
    fun reset() {
        counter.set(0)
        isNew.set(0)
    }

    @Test
    fun process() {
        val mmsi = 0

        // all updates should up the counter
        var instant = Instant.ofEpochMilli(0)
        var positionWrapper = AisPositionWrapper(instant, instant, "", null, AisPositionMessage(mmsi))
        minimalStateService.update(positionWrapper)
        assertEquals(1, counter.get())
        assertEquals(1, isNew.get())
        assertEquals(positionWrapper, minimalStateService.getState(mmsi)?.position)

        instant = Instant.ofEpochMilli(1)
        val staticWrapper = AisStaticWrapper(instant, instant, "", null, AisStaticMessage(mmsi))
        minimalStateService.update(staticWrapper)
        assertEquals(2, counter.get())
        assertEquals(2, isNew.get())
        assertEquals(staticWrapper, minimalStateService.getState(mmsi)?.static)

        instant = Instant.ofEpochMilli(2)
        val stationWrapper = AisStationWrapper(instant, instant, "", null, AisStationMessage(mmsi))
        minimalStateService.update(stationWrapper)
        assertEquals(3, counter.get())
        assertEquals(3, isNew.get())
        assertEquals(stationWrapper, minimalStateService.getState(mmsi)?.station)

        instant = Instant.ofEpochMilli(3)
        var longRangeWrapper = AisLongRangeWrapper(instant, instant, "", null, AisLongRangeMessage(mmsi))
        minimalStateService.update(longRangeWrapper)
        assertEquals(4, counter.get())
        assertEquals(4, isNew.get())
        assertEquals(longRangeWrapper, minimalStateService.getState(mmsi)?.longrange)

        // send a new position update, and an older long range, the latter should not be new
        instant = Instant.ofEpochMilli(5)
        positionWrapper = AisPositionWrapper(instant, instant, "", null, AisPositionMessage(mmsi))
        minimalStateService.update(positionWrapper)
        assertEquals(5, counter.get())
        assertEquals(5, isNew.get())
        assertEquals(positionWrapper, minimalStateService.getState(mmsi)?.position)

        instant = Instant.ofEpochMilli(4)
        longRangeWrapper = AisLongRangeWrapper(instant, instant, "", null, AisLongRangeMessage(mmsi), historic = true)
        minimalStateService.update(longRangeWrapper)
        assertEquals(6, counter.get())
        assertEquals(5, isNew.get())
        assertEquals(longRangeWrapper, minimalStateService.getState(mmsi)?.longrange)

        assertEquals(
            MinimalState(positionWrapper, staticWrapper, stationWrapper, longRangeWrapper),
            minimalStateService.getState(mmsi)
        )
    }

    @Test
    fun isNew() {
        // initial update, up counter and is inherently new
        var instant = Instant.ofEpochMilli(2)
        minimalStateService.update(AisPositionWrapper(instant, instant, "", null, AisPositionMessage(0)))
        assertEquals(1, counter.get())
        assertEquals(1, isNew.get())

        // new update, but not new
        instant = Instant.ofEpochMilli(0)
        minimalStateService.update(AisPositionWrapper(instant, instant, "", null, AisPositionMessage(0), historic = true))
        assertEquals(2, counter.get())
        assertEquals(1, isNew.get())

        // new update, and new
        instant = Instant.ofEpochMilli(4)
        minimalStateService.update(AisPositionWrapper(instant, instant, "", null, AisPositionMessage(0)))
        assertEquals(3, counter.get())
        assertEquals(2, isNew.get())
    }

    @Test
    fun `old entries are expired correctly`() {
        minimalStateService.update(getMsgAt("2023-01-15T12:00:00Z", 1))
        minimalStateService.update(getMsgAt("2023-02-15T12:00:00Z", 2))
        val removed = minimalStateService.expire(Instant.parse("2023-02-01T00:00:00Z"))
        assertEquals(1, removed)
        assertTrue(minimalStateService.getState(1).isNullOrEmpty())
        assertFalse(minimalStateService.getState(2).isNullOrEmpty())
    }

    private fun getMsgAt(isostr: String, mmsi: Int): AisPositionWrapper {
        val instant = Instant.parse(isostr)
        return AisPositionWrapper(instant, instant, "", null, AisPositionMessage(mmsi))
    }
}
