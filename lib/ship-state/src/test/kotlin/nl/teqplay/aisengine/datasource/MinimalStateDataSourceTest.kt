package nl.teqplay.aisengine.datasource

import com.mongodb.bulk.BulkWriteResult
import com.mongodb.client.result.DeleteResult
import com.mongodb.kotlin.client.MongoCollection
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.aisstream.model.AisPositionMessage
import nl.teqplay.aisengine.aisstream.model.AisPositionWrapper
import nl.teqplay.aisengine.model.MinimalState
import nl.teqplay.skeleton.common.BaseTest
import org.bson.conversions.Bson
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.ArgumentMatchers.anyList
import org.mockito.kotlin.any
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.test.annotation.DirtiesContext
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.TestConstructor
import org.springframework.test.context.bean.override.mockito.MockitoBean
import java.time.Instant

@ContextConfiguration
@MockitoBean(types = [MongoDatabase::class])
@TestConstructor(autowireMode = TestConstructor.AutowireMode.ALL)
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
internal class MinimalStateDataSourceTest(
    private val minimalStateDataSource: MinimalStateDataSourceImpl
) : BaseTest() {

    companion object {
        val collection = mock<MongoCollection<MinimalStateWrapper>>()
    }

    @TestConfiguration
    class Configuration {
        @Bean
        fun minimalStateDataSource(): MinimalStateDataSourceImpl {
            whenever(collection.createIndex(any<Bson>(), any())).thenReturn("")
            val mongoDatabase: MongoDatabase = mock {
                on { getCollection(any(), any<Class<MinimalStateWrapper>>()) } doReturn collection
            }
            return MinimalStateDataSourceImpl(mongoDatabase)
        }
    }

    @Test
    fun size() {
        whenever(collection.createIndex(any<Bson>(), any())).thenReturn("")
        whenever(collection.estimatedDocumentCount(options = any())).thenReturn(1234)
        assertEquals(1234, minimalStateDataSource.size)
    }

    @Test
    fun updateState() {
        whenever(collection.createIndex(any<Bson>(), any())).thenReturn("")
        whenever(collection.deleteMany(any(), options = any())).thenReturn(DeleteResult.acknowledged(1))
        whenever(collection.bulkWrite(anyList(), any())).thenReturn(BulkWriteResult.unacknowledged())

        val minimalState = MinimalState(
            position = AisPositionWrapper(
                Instant.EPOCH,
                Instant.EPOCH,
                "",
                null,
                AisPositionMessage(0)
            )
        )

        minimalStateDataSource.updateState(mapOf(0 to minimalState))

        verify(collection, times(1)).deleteMany(any(), options = any())
        verify(collection, times(1)).bulkWrite(anyList(), any())
    }
}
