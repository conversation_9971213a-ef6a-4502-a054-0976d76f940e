# ship-state

Use as a dependency by adding:
```groovy
implementation project(':lib:ship-state')
```

This library requires a shutdown handler to persist the state on shutdown.

Use a shutdown handler, and extend if so required:

(where `StateServiceImpl` is your implementation of `StateService`)

```kotlin
private val LOG = KotlinLogging.logger {}

@Component
class ShutdownHandler(
    private val stateService: StateServiceImpl
) {
    @EventListener(ContextClosedEvent::class)
    fun shutdown(event: ContextClosedEvent) {
        LOG.info { "Shutting down" }

        try {
            stateService.shutdown()
        } catch (e: Throwable) {
            LOG.warn(e) { "Error shutting down ship state service" }
        }

        LOG.info { "Shutdown complete" }
    }
}
```