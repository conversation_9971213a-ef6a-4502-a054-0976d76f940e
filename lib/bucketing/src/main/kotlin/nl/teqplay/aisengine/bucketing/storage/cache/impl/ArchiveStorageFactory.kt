package nl.teqplay.aisengine.bucketing.storage.cache.impl

import nl.teqplay.aisengine.bucketing.storage.archive.ArchiveReadByMonthStorageImpl
import nl.teqplay.aisengine.bucketing.storage.archive.ArchiveReadStorageImpl
import nl.teqplay.aisengine.bucketing.storage.archive.ArchiveWriteByMonthStorageImpl
import nl.teqplay.aisengine.bucketing.storage.archive.ArchiveWriteStorageImpl
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId

object ArchiveStorageFactory {

    fun <
        Data : Any,
        Key : Any,
        ID : BucketId<Key>
        > archiveReadStoragePerDay() =
        CreateArchiveReadStorage<Data, Key, ID> { archive, objectMapper, factory, archiveClientService ->
            ArchiveReadStorageImpl(
                dataClass = factory.orderedDataClass,
                archive = archive,
                objectMapper = objectMapper,
                archiveClientService = archiveClientService,
            )
        }

    fun <
        OrderedData : Data,
        UnorderedData : Data,
        Data : Any,
        Output : Data,
        Key : Any,
        ID : BucketId<Key>
        > archiveReadStoragePerDayDual() =
        CreateArchiveReadStorageDual<OrderedData, UnorderedData, Data, Output, Key, ID> { archive, objectMapper, factory, archiveClientService ->
            ArchiveReadStorageImpl(
                dataClass = factory.orderedDataClass,
                archive = archive,
                objectMapper = objectMapper,
                archiveClientService = archiveClientService,
            )
        }

    fun <
        Data : Any,
        Key : Any,
        ID : BucketId<Key>
        > archiveWriteStoragePerDay() =
        CreateArchiveWriteStorage<Data, Key, ID> { archive, objectMapper, factory, archiveClientService ->
            ArchiveWriteStorageImpl(
                archive = archive,
                objectMapper = objectMapper,
                factory = factory,
                archiveClientService = archiveClientService,
            )
        }

    fun <
        OrderedData : Data,
        UnorderedData : Data,
        Data : Any,
        Output : Data,
        Key : Any,
        ID : BucketId<Key>
        > archiveWriteStoragePerDayDual() =
        CreateArchiveWriteStorageDual<OrderedData, UnorderedData, Data, Output, Key, ID> { archive, objectMapper, factory, archiveClientService ->
            ArchiveWriteStorageImpl(
                archive = archive,
                objectMapper = objectMapper,
                factory = factory,
                archiveClientService = archiveClientService,
            )
        }

    fun <
        Data : Any,
        Key : Any,
        ID : BucketId<Key>
        > archiveReadStoragePerMonth() =
        CreateArchiveReadStorage<Data, Key, ID> { archive, objectMapper, factory, archiveClientService ->
            ArchiveReadByMonthStorageImpl(
                dataClass = factory.orderedDataClass,
                archive = archive,
                objectMapper = objectMapper,
                archiveClientService = archiveClientService,
            )
        }

    fun <
        OrderedData : Data,
        UnorderedData : Data,
        Data : Any,
        Output : Data,
        Key : Any,
        ID : BucketId<Key>
        > archiveReadStoragePerMonthDual() =
        CreateArchiveReadStorageDual<OrderedData, UnorderedData, Data, Output, Key, ID> { archive, objectMapper, factory, archiveClientService ->
            ArchiveReadByMonthStorageImpl(
                dataClass = factory.orderedDataClass,
                archive = archive,
                objectMapper = objectMapper,
                archiveClientService = archiveClientService,
            )
        }

    fun <
        Data : Any,
        Key : Any,
        ID : BucketId<Key>
        > archiveWriteStoragePerMonth() =
        CreateArchiveWriteStorage<Data, Key, ID> { archive, objectMapper, factory, archiveClientService ->
            ArchiveWriteByMonthStorageImpl(
                archive = archive,
                objectMapper = objectMapper,
                factory = factory,
                archiveClientService = archiveClientService,
            )
        }

    fun <
        OrderedData : Data,
        UnorderedData : Data,
        Data : Any,
        Output : Data,
        Key : Any,
        ID : BucketId<Key>
        > archiveWriteStoragePerMonthDual() =
        CreateArchiveWriteStorageDual<OrderedData, UnorderedData, Data, Output, Key, ID> { archive, objectMapper, factory, archiveClientService ->
            ArchiveWriteByMonthStorageImpl(
                archive = archive,
                objectMapper = objectMapper,
                factory = factory,
                archiveClientService = archiveClientService,
            )
        }
}
