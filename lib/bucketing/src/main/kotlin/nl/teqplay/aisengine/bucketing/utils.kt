package nl.teqplay.aisengine.bucketing

import nl.teqplay.aisengine.bucketing.storage.BucketItemResolver
import java.time.Duration

/**
 * Remove items from a list to reduce the amount of items with a given factor
 * The items will be removed by picking items with minimal time difference.
 * The method will return a copy of the original list in which a factor of the data is removed.
 *
 * IMPORTANT: this method groups data with the [resolver] by using [BucketItemResolver.toId]. It targets removing data
 * with minimal time differences first. If however there is too much data, for example when every
 * [BucketItemResolver.toId] is unique, then the data will be removed on a best-effort basis by first removing entries
 * at odd indices.
 */
fun <T : Any> thinOut(
    items: List<T>,
    factor: Double,
    resolver: BucketItemResolver<in T>
): List<T> {
    // sorted map, to easily look up entries with the most values
    val map = mutableMapOf<Int, MutableSet<String>>().toSortedMap()

    val groupedById = items
        .groupBy { resolver.toId(it) }
        .mapValues { (key, values) ->
            map.getOrPut(values.size, ::mutableSetOf).add(key)
            values.toMutableList()
        }
        .toMutableMap()

    // use an index for the ID we want to select, to ensure we don't
    // always get the same ID, and we first only use odd ID indices
    var idIndex = 1

    val removeGlobalCount = (items.size * factor).toInt()
    repeat(removeGlobalCount) {
        val last = map.lastKey()

        val ids = requireNotNull(map[last]).toList()

        // reset index if exceeds available IDs
        if (idIndex >= ids.size) idIndex = 1.coerceAtMost(ids.size - 1)

        val id = ids[idIndex]
        val values = requireNotNull(groupedById[id])

        // add 1, we remove an ID so this essentially moves the pointer by 2
        idIndex++

        // remove from map
        map.compute(last) { _, value ->
            value?.remove(id)
            value?.ifEmpty { null }
        }

        val minimalTimeDifference = values
            .zipWithNext()
            .minByOrNull { (prev, curr) -> Duration.between(resolver.toTimestamp(prev), resolver.toTimestamp(curr)) }

        // remove item with minimal time difference
        if (minimalTimeDifference == null) {
            values.removeLast()
        } else {
            values.remove(minimalTimeDifference.second)
        }

        // clean up based on changes
        if (values.isEmpty()) {
            groupedById.remove(id)
        } else {
            // re-add
            map.getOrPut(values.size, ::mutableSetOf).add(id)
        }
    }

    return groupedById.values.flatten().sortedBy { resolver.toTimestamp(it) }.toMutableList()
}
