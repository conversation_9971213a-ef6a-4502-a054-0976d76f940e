package nl.teqplay.aisengine.bucketing.storage.cache

import com.google.common.collect.Iterators
import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.bucketing.BUCKET_ID_SEPARATOR
import nl.teqplay.aisengine.bucketing.model.bucket.OrderedBucket
import nl.teqplay.aisengine.bucketing.properties.BucketProperties
import nl.teqplay.aisengine.bucketing.storage.BucketFactoryDual
import nl.teqplay.aisengine.bucketing.storage.archive.ArchiveReadStorage
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.mongo.MongoBucketReadStorage
import nl.teqplay.aisengine.bucketing.storage.mongo.UnorderedMongoBucketReadStorage
import nl.teqplay.aisengine.util.floorToLocalDate
import nl.teqplay.skeleton.model.TimeWindow
import java.time.Clock
import java.time.Duration
import java.time.LocalDate
import java.time.ZoneOffset

private val LOG = KotlinLogging.logger {}

/**
 * Orchestrator of read operations for this specific bucket.
 */
abstract class BucketReadCacheInternal<
    OrderedData : Data,
    UnorderedData : Data,
    Data : Any,
    Output : Data,
    Key : Any,
    ID : BucketId<Key>
    >(
    private val collectionName: String,
    private val bucket: BucketProperties,
    override val factory: BucketFactoryDual<OrderedData, UnorderedData, Data, Output, Key, ID>,

    private val mongoStorage: MongoBucketReadStorage<OrderedData, Data>?,
    private val unorderedStorage: UnorderedMongoBucketReadStorage<OrderedData, UnorderedData, Data>?,
    open val archiveStorage: ArchiveReadStorage<OrderedData>?,

    overwriteMaxAge: Duration?,

    private val clock: Clock = Clock.systemUTC()
) : BucketCacheDual<OrderedData, UnorderedData, Data, Output, Key, ID> {
    init {
        LOG.info {
            "[$collectionName] BucketCache created with properties " +
                "fetchBatchSize: ${bucket.mongo.fetchBatchSize}, " +
                "collection: $collectionName"
        }
    }

    companion object {
        /**
         * the size of the buckets is fixed. you can't just change this size,
         * to do this, you have to migrate all data to buckets with the new
         * size and after that change the value here.
         */
        const val BUCKET_TIME_SIZE_IN_DAYS = 1L
    }

    protected val maxDaysInShortTermStorage = (overwriteMaxAge ?: bucket.sweeper.maxAge)
        ?.toDays()
        ?.coerceAtLeast(7)

    /**
     * Calculates the "edge" of history, so where our short term storage meets the long term storage
     *
     * The edge is always computed with the maximum days of history that can be in short term storage,
     * anything beyond is assumed to be part of the long term storage.
     *
     * If we currently don't have enough data in our short term storage to match the edge, we use the
     * oldest data available in our short term storage as our edge.
     */
    private fun calculateHistoryEdge(): LocalDate? {
        // if we don't delete old data, we don't need an edge for long term storage
        if (maxDaysInShortTermStorage == null) {
            return null
        }

        // determine the edge of short term and long term storage
        val historyEdge = clock.instant().floorToLocalDate()
            .minusDays(maxDaysInShortTermStorage)

        // move the edge to the oldest known data if less data is available in short term storage
        val oldestDataPoint = mongoStorage?.oldestDataPoint
        if (oldestDataPoint != null) {
            if (historyEdge.isBefore(oldestDataPoint)) {
                return oldestDataPoint
            }
        }

        return historyEdge
    }

    private fun inTimeWindow(window: TimeWindow, item: Output): Boolean {
        val timestamp = factory.resolver.toTimestamp(item)
        return window.from <= timestamp && timestamp < window.to
    }

    override fun fetchHistoryInTimeWindow(
        window: TimeWindow,
        key: Key,
        extendToLongTerm: Boolean
    ): Sequence<Output> {
        val getBucketIds: (TimeWindow) -> Iterator<String> =
            { factory.bucketIdentifier.getBucketIds(it, key) }

        return fetchHistoryInTimeWindow(
            window = window,
            getBucketIds = getBucketIds,
            getArchiveBucketIds = getBucketIds,
            sort = false, // when fetching history for one specific key, then it will already be sorted
            extendToLongTerm = extendToLongTerm
        )
    }

    override fun fetchHistoryInTimeWindow(
        window: TimeWindow,
        extendToLongTerm: Boolean,
        sort: Boolean,
        keys: Set<Key>
    ): Sequence<Output> {
        val getBucketIds: (TimeWindow) -> Iterator<String> =
            { factory.bucketIdentifier.getBucketIds(it, keys) }
        return fetchHistoryInTimeWindow(window, getBucketIds, getBucketIds, sort, extendToLongTerm)
    }

    override fun fetchHistoryInTimeWindow(
        window: TimeWindow,
        getBucketIds: (TimeWindow) -> Iterator<String>,
        getArchiveBucketIds: (TimeWindow) -> Iterator<String>,
        sort: Boolean,
        extendToLongTerm: Boolean
    ): Sequence<Output> {
        return getBuckets(window, getBucketIds, getArchiveBucketIds, sort, extendToLongTerm)
            .mapNotNull(factory.bucketFormatter::convertToOutput)
            .filter { inTimeWindow(window, it) }
    }

    private fun getBuckets(
        window: TimeWindow,
        getBucketIds: (TimeWindow) -> Iterator<String>,
        getArchiveBucketIds: (TimeWindow) -> Iterator<String>,
        sort: Boolean,
        extendToLongTerm: Boolean
    ): Sequence<OrderedData> = fetchBuckets(
        window = window,
        getBucketIds = getBucketIds,
        getArchiveBucketIds = getArchiveBucketIds,
        extendToLongTerm = extendToLongTerm,
        fetchLocal = { fetchWindow, bucketIds -> fetchLocalBuckets(fetchWindow, bucketIds, sort) },
        fetchArchive = { fetchWindow, bucketIds -> fetchArchiveBuckets(fetchWindow, bucketIds, sort) },
        append = { longTermHistory, shortTermHistory -> longTermHistory + shortTermHistory }
    )

    private inline fun <T> fetchBuckets(
        window: TimeWindow,
        getBucketIds: (TimeWindow) -> Iterator<String>,
        getArchiveBucketIds: (TimeWindow) -> Iterator<String>,
        extendToLongTerm: Boolean,

        fetchLocal: (TimeWindow, Iterator<String>) -> T,
        fetchArchive: (TimeWindow, Iterator<String>) -> T,
        append: (T, T) -> T
    ): T {
        if (!extendToLongTerm) {
            val bucketIds = getBucketIds(window)
            return fetchLocal(window, bucketIds)
        }

        val historyEdge = calculateHistoryEdge()
            ?.atStartOfDay(ZoneOffset.UTC)
            ?.toInstant()

        return when {
            // the history is contained in short term storage
            historyEdge == null || window.from >= historyEdge -> {
                val bucketIds = getBucketIds(window)
                fetchLocal(window, bucketIds)
            }
            // the history is contained in long term storage
            window.to <= historyEdge -> {
                val bucketIds = getArchiveBucketIds(window)
                fetchArchive(window, bucketIds)
            }
            // the history is contained in both long and short term storage
            else -> {
                val longTermWindow = TimeWindow(window.from, historyEdge)
                val shortTermWindow = TimeWindow(historyEdge, window.to)

                val longTermBucketIds = getArchiveBucketIds(longTermWindow)
                val shortTermBucketIds = getBucketIds(shortTermWindow)

                val longTermHistory = fetchArchive(longTermWindow, longTermBucketIds)
                val shortTermHistory = fetchLocal(shortTermWindow, shortTermBucketIds)

                append(longTermHistory, shortTermHistory)
            }
        }
    }

    /**
     * Load buckets from cache and/or database.
     * First the method looks in the cache, and the buckets that are
     * not in cache are fetched from mongodb and merged with the cached buckets
     *
     * When the bucketIds are in ascending order, the output will be ordered too.
     * If the bucketIds are not ordered, output will be ordered per batch
     * (the buckets are fetched in batches internally).
     *
     * @param bucketIds An iterator with the requested bucket id's, properly sorted
     * @param sort whether the buckets need to be sorted
     * @return Returns a stream with the items inside the requested buckets
     */
    override fun fetchLocalBuckets(
        window: TimeWindow,
        bucketIds: Iterator<String>,
        sort: Boolean
    ): Sequence<OrderedData> {
        val partitionedBucketIds: Iterator<List<String>> = partitionBucketIds(bucketIds)
        val buckets = partitionedBucketIds
            .asSequence()
            .flatMap { fetchBatchOfBuckets(it) }
        return buckets.flatten(sort)
    }

    override fun fetchArchiveBuckets(
        window: TimeWindow,
        archiveBucketIds: Iterator<String>,
        sort: Boolean
    ): Sequence<OrderedData> {
        val buckets = fetchArchiveBucketsRaw(window, archiveBucketIds)
        return buckets.flatten(sort)
    }

    private fun fetchArchiveBucketsRaw(
        window: TimeWindow,
        archiveBucketIds: Iterator<String>
    ): Sequence<OrderedBucket<OrderedData>> {
        val archiveStorage = this.archiveStorage ?: return emptySequence()
        return archiveStorage.get(window, archiveBucketIds)
            .map { it.orderedBucket }
    }

    private fun partitionBucketIds(bucketIds: Iterator<String>): Iterator<List<String>> =
        Iterators.partition(bucketIds, bucket.mongo.fetchBatchSize)

    private fun Sequence<OrderedBucket<OrderedData>>.flatten(sort: Boolean): Sequence<OrderedData> {
        if (!sort) {
            return this.flatMap { factory.bucketFormatter.unzip(it.getAll()) }
        }

        val iterator = this.iterator()
        return factory.bucketFormatter.unzipAsSequence(
            sequence {
                val group = mutableListOf<OrderedBucket<OrderedData>>()
                var bucketPrefix = ""
                while (true) {
                    val bucket = when {
                        iterator.hasNext() -> iterator.next()
                        else -> null
                    }

                    // drain groups if no buckets left or current bucket doesn't match prefix
                    if (bucket == null ||
                        (group.isNotEmpty() && !bucket.bucketId.startsWith(bucketPrefix))
                    ) {
                        while (group.isNotEmpty()) {
                            // find group with the oldest data
                            val minBucket = group.minBy { factory.resolver.toTimestamp(it.getAll().first()) }
                            yield(minBucket.data.removeFirst())

                            // cleanup if the bucket is now empty
                            if (minBucket.getAll().isEmpty()) {
                                group.remove(minBucket)
                            }
                        }
                    }

                    if (bucket == null) {
                        // nothing left to do
                        break
                    } else if (bucket.getAll().isNotEmpty()) {
                        // determine new bucket prefix, it will start with "date,"
                        if (group.isEmpty()) bucketPrefix = bucket.bucketId.substringBefore(BUCKET_ID_SEPARATOR)
                        group.add(bucket)
                    }
                }
            }
        )
    }

    /**
     * Load buckets from cache and mongo database and merge them.
     *
     * @param bucketIds
     * A limited list with bucket ids. A safe maximum is 100,000 items.
     * At some point the list exceeds 16MB when serialized into JSON,
     * which will throw an error when sending it to MongoDB
     */
    private fun fetchBatchOfBuckets(bucketIds: List<String>): Sequence<OrderedBucket<OrderedData>> {
        // Can't load in any database buckets when mongo isn't loaded in for either one
        if (mongoStorage == null || unorderedStorage == null) {
            return emptySequence()
        }

        // the list with bucketIds should be sorted already, but we cannot guarantee this, and it's essential
        // for this method that the ids are sorted, therefore we sort the buckets here to be sure
        val sortedBucketIds = bucketIds.sorted()

        // fetch the buckets from mongo (normally the main source of the data)
        val mongoBuckets = Iterators.peekingIterator(mongoStorage.load(sortedBucketIds))

        // fetch the buckets from unordered storage
        val unorderedBuckets = Iterators.peekingIterator(unorderedStorage.load(sortedBucketIds))
        return sortedBucketIds
            .asSequence()
            .map { bucketId ->
                val orderedBucket = OrderedBucket<OrderedData>(bucketId)

                // ordered
                if (mongoBuckets.hasNext() && mongoBuckets.peek().bucketId == bucketId) {
                    orderedBucket.addAll(
                        items = mongoBuckets.next().getAll(),
                        resolver = factory.resolver
                    )
                }

                // unordered
                if (unorderedBuckets.hasNext() && unorderedBuckets.peek().bucketId == bucketId) {
                    orderedBucket.addAll(
                        items = factory.bucketFormatter.initForZip(unorderedBuckets.next().getAll()),
                        resolver = factory.resolver
                    )
                }

                return@map orderedBucket
            }
    }
}
