package nl.teqplay.aisengine.bucketing.storage.mongo.unordered

import com.google.common.collect.Iterators
import com.google.common.collect.PeekingIterator
import com.mongodb.kotlin.client.MongoDatabase
import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.bucketing.model.bucket.BucketData
import nl.teqplay.aisengine.bucketing.model.bucket.OrderedBucket
import nl.teqplay.aisengine.bucketing.model.bucket.UnorderedBucket
import nl.teqplay.aisengine.bucketing.storage.BucketFactoryDual
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.mongo.MongoBucketStorageQuery
import nl.teqplay.aisengine.bucketing.storage.mongo.UnorderedMongoBucketReadStorage
import nl.teqplay.skeleton.datasource.kmongo.lt
import java.time.LocalDate

private val LOG = KotlinLogging.logger {}

open class UnorderedMongoBucketReadStorageImpl<
    OrderedData : Data,
    UnorderedData : Data,
    Data : Any,
    Output : Data,
    Key : Any,
    ID : BucketId<Key>
    >(
    private val mongoBucketStorageQuery: MongoBucketStorageQuery.Unordered,
    mongoDatabase: MongoDatabase,
    collectionName: String,
    collectionReadOnly: Boolean,
    unorderedCollectionSuffix: String,
    private val factory: BucketFactoryDual<OrderedData, UnorderedData, Data, Output, Key, ID>
) : UnorderedMongoBucketReadStorage<OrderedData, UnorderedData, Data>,
    UnorderedMongoBucketStorageImpl<UnorderedData>(
        mongoDatabase,
        collectionName,
        collectionReadOnly,
        unorderedCollectionSuffix,
        factory.unorderedBucketJavaType(),
        factory.bufferBucketJavaType(),
    ) {

    override fun findAllArchiveBucketIds(max: LocalDate): List<String> {
        LOG.info { "[$unorderedCollectionName] Finding all flushable bucketIds up until $max (included)" }

        val filter = UnorderedBucket<UnorderedData>::archiveBucketId lt max.plusDays(1).toString()

        return try {
            // collection.distinct() is fast, but is limited and crashes when
            // having more than two million results or so with an error
            // "distinct too big, 16mb cap".
            mongoBucketStorageQuery.findAllArchiveBucketIdsByDistinct(collection, filter)
        } catch (e: Exception) {
            LOG.info {
                "[$unorderedCollectionName] Failed to find bucketIds using collection.distinct(). " +
                    "Falling back to slower solution with projection instead. Error message: ${e.message}"
            }

            // this solution also works for larger results
            mongoBucketStorageQuery.findAllArchiveBucketIdsByProjection(collection, filter)
        }
    }

    override fun findBucketIdsForArchive(archiveBucketId: String): Set<String> =
        mongoBucketStorageQuery.findBucketIdsForArchive(collection, archiveBucketId)

    override fun load(bucketId: String): OrderedBucket<OrderedData>? {
        val iterator = load(listOf(bucketId))
        return if (iterator.hasNext()) {
            iterator.next()
        } else {
            null
        }
    }

    /**
     * IMPORTANT: The returned results will be sorted by bucketId
     */
    override fun load(bucketIds: List<String>): Iterator<OrderedBucket<OrderedData>> {
        if (bucketIds.isEmpty()) return emptyList<OrderedBucket<OrderedData>>().iterator()

        // the bucketIds must be unique and in-order, since we fetch and loop over the data in-order as well,
        // without it, it would result in weird results
        val sortedBucketIds = bucketIds.toSet().sorted()

        val unorderedBucketsByBucketIds = Iterators.peekingIterator(
            mongoBucketStorageQuery.getUnorderedBucketsByBucketIds(collection, sortedBucketIds)
        )
        val bufferDataByBucketIds = Iterators.peekingIterator(
            mongoBucketStorageQuery.getByBucketIdsInBufferStage(bufferCollection, sortedBucketIds)
        )

        fun nextWhileMatching(
            iterator: PeekingIterator<out BucketData<UnorderedData>>,
            bucketId: String
        ): List<BucketData<UnorderedData>> {
            val buckets = mutableListOf<BucketData<UnorderedData>>()
            while (iterator.hasNext() && iterator.peek().bucketId == bucketId) {
                buckets.add(iterator.next())
            }
            return buckets
        }

        return sortedBucketIds
            .mapNotNull { bucketId ->
                val unorderedBuckets = nextWhileMatching(unorderedBucketsByBucketIds, bucketId)
                val unorderedBucketsFromMemory = unorderedBucketMap[bucketId]
                val flushingDataFromMemory = flushingDataMap[bucketId]
                val bufferData = nextWhileMatching(bufferDataByBucketIds, bucketId)

                if (unorderedBuckets.isNotEmpty() || unorderedBucketsFromMemory != null || flushingDataFromMemory != null) {
                    val bucket = OrderedBucket<OrderedData>(bucketId)

                    // Note that bucket.add() will sort and dedupe the data from the three sources
                    unorderedBuckets.forEach { u ->
                        bucket.addAll(u.getAll().map { factory.convertToOrdered(it) }, factory.resolver)
                    }
                    unorderedBucketsFromMemory?.let { u ->
                        bucket.addAll(u.getAll().map { factory.convertToOrdered(it) }, factory.resolver)
                    }
                    flushingDataFromMemory?.let { u ->
                        bucket.addAll(u.getAll().map { factory.convertToOrdered(it) }, factory.resolver)
                    }
                    bufferData.forEach { u ->
                        bucket.addAll(u.getAll().map { factory.convertToOrdered(it) }, factory.resolver)
                    }

                    bucket
                } else {
                    null
                }
            }
            .iterator()
    }
}
