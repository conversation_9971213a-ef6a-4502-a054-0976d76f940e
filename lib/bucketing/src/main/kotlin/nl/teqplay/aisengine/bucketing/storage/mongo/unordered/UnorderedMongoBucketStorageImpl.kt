package nl.teqplay.aisengine.bucketing.storage.mongo.unordered

import com.fasterxml.jackson.databind.JavaType
import com.mongodb.kotlin.client.MongoCollection
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.bucketing.model.bucket.BucketData
import nl.teqplay.aisengine.bucketing.model.bucket.BufferData
import nl.teqplay.aisengine.bucketing.model.bucket.UnorderedBucket
import nl.teqplay.aisengine.bucketing.storage.BucketReadWithCollectionStorage
import nl.teqplay.aisengine.bucketing.storage.mongo.applyBucketCodec
import nl.teqplay.skeleton.datasource.kmongo.ensureIndex
import java.util.concurrent.ConcurrentHashMap

abstract class UnorderedMongoBucketStorageImpl<UnorderedData : Any>(
    mongoDatabase: MongoDatabase,
    collectionName: String,
    collectionReadOnly: Boolean,
    unorderedCollectionSuffix: String,
    unorderedJavaType: JavaType,
    bufferJavaType: JavaType,
) : BucketReadWithCollectionStorage<UnorderedData> {

    protected val unorderedCollectionName: String = "${collectionName}_$unorderedCollectionSuffix"
    private val bufferCollectionName: String = "${collectionName}_buffer"

    protected val collection = mongoDatabase
        .getCollection<UnorderedBucket<UnorderedData>>(unorderedCollectionName)
        .applyBucketCodec(UnorderedBucket::class.java, unorderedJavaType)
        .also {
            if (!collectionReadOnly) {
                it.ensureIndex(UnorderedBucket<UnorderedData>::bucketId)
                it.ensureIndex(
                    UnorderedBucket<UnorderedData>::archiveBucketId,
                    UnorderedBucket<UnorderedData>::bucketId
                )
            }
        }

    /**
     * Collection to store the most recent data, completely unordered. After a certain interval the data is grouped
     * and stored to the other collection.
     */
    protected val bufferCollection = mongoDatabase
        .getCollection<BufferData<UnorderedData>>(bufferCollectionName)
        .applyBucketCodec(BufferData::class.java, bufferJavaType)
        .also {
            if (!collectionReadOnly) {
                it.ensureIndex(BufferData<UnorderedData>::bucketId)
                it.ensureIndex(BufferData<UnorderedData>::archiveBucketId)
                it.ensureIndex(BufferData<UnorderedData>::generation)
            }
        }

    protected val unorderedBucketMap = ConcurrentHashMap<String, UnorderedBucket<UnorderedData>>()
    protected val flushingDataMap = ConcurrentHashMap<String, UnorderedBucket<UnorderedData>>()

    override fun <T> withCollection(with: (MongoCollection<out BucketData<UnorderedData>>) -> T): T {
        return with(collection)
    }
}
