package nl.teqplay.aisengine.bucketing.model.bucket

import nl.teqplay.aisengine.bucketing.storage.BucketItemResolver
import java.util.UUID

data class BufferData<Data : Any>(
    override val bucketId: String,
    val generation: Long,
    override val archiveBucketId: String = bucketId,
    override val data: MutableList<Data> = mutableListOf()
) : BucketData<Data> {
    companion object {
        private const val POISON_PILL_MARKER = "__poison_pill__"

        /**
         * Get a special marker object that marks the end of data in the queue.
         */
        fun <Data : Any> poisonPill() = BufferData<Data>(
            bucketId = POISON_PILL_MARKER,
            generation = -1,
            archiveBucketId = POISON_PILL_MARKER
        )
    }

    override val _id = UUID.randomUUID().toString()

    override fun add(item: Data, resolver: BucketItemResolver<in Data>) {
        data.add(item)
    }

    override fun addAll(items: Collection<Data>, resolver: BucketItemResolver<in Data>) {
        data.addAll(items)
    }

    val isPoisonPill: Boolean
        get() = bucketId == POISON_PILL_MARKER
}
