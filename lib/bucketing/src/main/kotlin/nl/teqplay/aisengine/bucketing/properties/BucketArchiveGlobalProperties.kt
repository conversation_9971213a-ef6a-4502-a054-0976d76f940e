package nl.teqplay.aisengine.bucketing.properties

import org.springframework.boot.context.properties.ConfigurationProperties

/**
 * Properties used for authenticating with AWS S3
 */
@ConfigurationProperties(prefix = "bucket.archive")
data class BucketArchiveGlobalProperties(
    val credentials: Credentials? = null
) {
    data class Credentials(
        val accessKeyId: String,
        val secretKey: String
    )
}
