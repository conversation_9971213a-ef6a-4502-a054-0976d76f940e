package nl.teqplay.aisengine.bucketing.storage

/**
 * Methods used to format buckets, by using [zip] to transform data into buckets, and [unzip] to
 * transform them out of them. Additionally, the data from the bucket can be converted to a different format,
 * using [convertToOutput].
 */
interface BucketFormatter<
    OrderedData : Data,
    Data : Any,
    Output : Data
    > {

    fun convertToOutput(item: OrderedData): Output?

    fun initForZip(data: List<OrderedData>): List<OrderedData> = data
    fun zip(data: List<OrderedData>): List<OrderedData> = data
    fun unzip(data: List<OrderedData>): List<OrderedData> = data
    fun unzipAsSequence(data: Sequence<OrderedData>): Sequence<OrderedData> = data
}

/**
 * A re-usable default implementation of [BucketFormatter] that doesn't transform data.
 */
open class DefaultBucketFormatter<Data : Any> : BucketFormatter<Data, Data, Data> {
    override fun convertToOutput(item: Data): Data = item
}
