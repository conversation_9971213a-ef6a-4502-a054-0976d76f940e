package nl.teqplay.aisengine.bucketing.config

import io.micrometer.core.instrument.MeterRegistry
import nl.teqplay.skeleton.metrics.Metric
import nl.teqplay.skeleton.metrics.MetricRegistry
import org.springframework.stereotype.Component

@Component
class BucketingMetricRegistry(
    meterRegistry: MeterRegistry,
) {

    private val collectionRegistry =
        MetricRegistry.of<BucketingMetricRegistry>(meterRegistry, listOf("collection-name"))

    private val timingRegistry =
        MetricRegistry.of<BucketingMetricRegistry>(meterRegistry, listOf("collection-name", "request-type"))

    private val countRegistry =
        MetricRegistry.of<BucketingMetricRegistry>(meterRegistry, listOf("collection-name", "request-count"))

    fun setPendingBucketsToFlush(
        collectionName: String,
        pending: Long,
    ) {
        collectionRegistry.getOrCreateGauge(Metric.MESSAGE_COUNT_PENDING, collectionName).set(pending)
    }

    fun addTimeSpent(
        collectionName: String,
        requestType: RequestType,
        time: Long,
    ) {
        timingRegistry.getOrCreateGauge(Metric.REQUEST_TIME, collectionName, requestType.type).getAndAdd(time)
        timingRegistry.getOrCreateGauge(Metric.REQUEST_RESPONSE, collectionName, requestType.type).getAndIncrement()
    }

    enum class RequestType(val type: String) {
        S3_FLUSH("s3-flush"),
        MONGO_FLUSH("mongo-flush"),
        FIND_BUCKET_IDS("find-bucket-ids"),
        WAIT_LOCK("wait-lock"),
        SLEEP("sleep"),
    }
}
