package nl.teqplay.aisengine.bucketing.storage.mongo.unordered

import com.antkorwin.xsync.XSync
import com.mongodb.kotlin.client.MongoDatabase
import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.bucketing.model.bucket.BufferData
import nl.teqplay.aisengine.bucketing.model.bucket.UnorderedBucket
import nl.teqplay.aisengine.bucketing.properties.BucketProperties
import nl.teqplay.aisengine.bucketing.storage.BucketFactoryDual
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.mongo.MongoBucketStorageQuery
import nl.teqplay.aisengine.bucketing.storage.mongo.UnorderedMongoBucketWriteStorage
import java.time.Duration
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.ScheduledFuture
import java.util.concurrent.TimeUnit
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.thread
import kotlin.concurrent.withLock

private val LOG = KotlinLogging.logger {}

class UnorderedMongoBucketWriteStorageImpl<
    OrderedData : Data,
    UnorderedData : Data,
    Data : Any,
    Output : Data,
    Key : Any,
    ID : BucketId<Key>
    >(
    private val mongoBucketStorageQuery: MongoBucketStorageQuery.Unordered,
    mongoDatabase: MongoDatabase,
    collectionName: String,
    collectionReadOnly: Boolean,
    unorderedCollectionSuffix: String,
    private val factory: BucketFactoryDual<OrderedData, UnorderedData, Data, Output, Key, ID>,
    private val scheduledExecutorService: ScheduledExecutorService,
    flushInitialDelay: Duration,
    private val unordered: BucketProperties.Mongo.Unordered
) : UnorderedMongoBucketWriteStorage<OrderedData, UnorderedData, Data>,
    UnorderedMongoBucketReadStorageImpl<OrderedData, UnorderedData, Data, Output, Key, ID>(
        mongoBucketStorageQuery,
        mongoDatabase,
        collectionName,
        collectionReadOnly,
        unorderedCollectionSuffix,
        factory
    ) {

    companion object {
        private const val BUFFER_WRITE_QUEUE_SIZE = 2000
    }

    private val bucketIdLock = XSync<String>()

    /**
     * newly inserted data is put in [unorderedBucketMap]. Once in a while,
     * this data is flushed into Mongo and removed from memory.
     * Whilst flushing (which can take a while) the data is temporarily
     * stored in [flushingDataMap] to make sure we always get all data
     * when loading a bucket.
     */
    private val insertUnorderedBucketLock = Object()
    private val writeUnorderedBucketLock = ReentrantLock()
    private var unorderedBucketDataCount = 0L
    private var unorderedBucketDataWriteScheduled = false

    init {
        // Load the buffer data into the unordered in-memory bucket on startup
        mongoBucketStorageQuery.findAllInBufferStage(bufferCollection).forEach { item ->
            val unorderedBucket = unorderedBucketMap.getOrPut(item.bucketId) {
                UnorderedBucket(
                    bucketId = item.bucketId,
                    archiveBucketId = factory.bucketIdentifier.archive.getArchiveBucketId(item.bucketId)
                )
            }
            unorderedBucket.addAll(item.data, factory.resolver)
            unorderedBucketDataCount += item.data.size
        }
    }

    private val writeDataTask: ScheduledFuture<*> = scheduledExecutorService.scheduleAtFixedRate(
        ::writeUnorderedBuckets,
        flushInitialDelay.toMillis(),
        unordered.flush.interval.toMillis(),
        TimeUnit.MILLISECONDS
    )

    /**
     * This is the "generation" number of the most recent buffered data. Whenever we move the buffered data to the
     * unordered mongo buckets, the generation number is set to the current time first. This allows us to easily delete
     * the previous generation of buffered data.
     */
    @Volatile
    private var generation = System.currentTimeMillis()

    private val bufferFlushThread = thread(
        block = ::bufferFlusher,
        name = "buffer-flusher",
        start = false
    )

    /**
     * Buffer to decouple handling incoming messages from writing them to Mongo.
     */
    private val bufferWriteQueue = LinkedBlockingQueue<BufferData<UnorderedData>>(BUFFER_WRITE_QUEUE_SIZE)

    override fun startup() {
        bufferFlushThread.start()
    }

    /**
     * This thread is used to decouple the message handling from the database writes. In the message handler thread,
     * new data is written to the [bufferWriteQueue]. This thread flushes it to the database, periodically if the buffer
     * is not full, and continuously if the buffer is full.
     */
    private fun bufferFlusher() {
        var running = true
        val flushQueue = LinkedBlockingQueue<BufferData<UnorderedData>>(BUFFER_WRITE_QUEUE_SIZE)
        while (running || bufferWriteQueue.isNotEmpty() || flushQueue.isNotEmpty()) {
            try {
                val maxTimeout = Duration.ofSeconds(5).toMillis()
                val startTime = System.currentTimeMillis()
                while (flushQueue.remainingCapacity() > 0) {
                    val timeout = startTime + maxTimeout - System.currentTimeMillis()
                    val bufferData = bufferWriteQueue.poll(timeout, TimeUnit.MILLISECONDS) ?: break
                    if (bufferData.isPoisonPill) {
                        running = false
                    } else {
                        flushQueue.put(bufferData)
                    }
                }

                if (flushQueue.isNotEmpty()) {
                    mongoBucketStorageQuery.bufferStageInsertMany(bufferCollection, flushQueue.toList())
                    flushQueue.clear()
                }
            } catch (e: Exception) {
                LOG.error(e) { "Exception while flushing buffer queue to mongo" }
            }
        }
    }

    /**
     * Insert the data specified by [item] into a bucket with given [bucketId]
     */
    override fun insert(bucketId: String, item: UnorderedData) {
        synchronized(insertUnorderedBucketLock) {
            val existing = unorderedBucketMap[bucketId]
            if (existing != null) {
                existing.add(item, factory.resolver)
            } else {
                val unorderedBucket = UnorderedBucket<UnorderedData>(
                    bucketId = bucketId,
                    archiveBucketId = factory.bucketIdentifier.archive.getArchiveBucketId(bucketId),
                )
                unorderedBucket.add(item, factory.resolver)
                unorderedBucketMap[bucketId] = unorderedBucket
            }
            unorderedBucketDataCount++
        }

        // Add the data to the write queue
        bufferWriteQueue.put(
            BufferData(
                bucketId = bucketId,
                generation = generation,
                data = mutableListOf(item)
            )
        )

        // to prevent memory overflow, we force a flush of all unordered data
        // when the configured max is exceeded.
        if (unorderedBucketDataCount > unordered.flush.maxItemsThreshold && !unorderedBucketDataWriteScheduled) {
            unorderedBucketDataWriteScheduled = true

            LOG.info {
                "[$unorderedCollectionName] Maximum number of unordered buckets exceeded the configured maximum " +
                    "of flushMaxItemsThreshold=${unordered.flush.maxItemsThreshold}. " +
                    "Forcing flushing unordered buckets from memory to mongo now. " +
                    "Please increase the threshold or reduce the flush interval " +
                    "(currently flushInterval=${unordered.flush.interval}). " +
                    "Tip: make sure there is enough RAM to keep the unordered data in memory."
            }

            scheduledExecutorService.schedule(::writeUnorderedBuckets, 0, TimeUnit.MILLISECONDS)
        }
    }

    /**
     * Write all unordered buckets that have been collected to mongo in (limited) bulk writes
     */
    override fun writeUnorderedBuckets() = writeUnorderedBucketLock.withLock {
        try {
            if (unorderedBucketMap.isEmpty()) {
                return
            }

            // Up the generation counter. Any data that comes in after this point is saved using the new number, and
            // won't be deleted after we've written the data to the unordered buckets. There is a race condition here:
            // data that comes in after this statement and before we start writing has the new generation number and is
            // written to the unordered buckets twice. This doesn't matter, since we will do deduping later.
            generation = System.currentTimeMillis()

            val start = System.currentTimeMillis()
            val count: Int
            val dataPoints: Long

            synchronized(insertUnorderedBucketLock) {
                flushingDataMap.putAll(unorderedBucketMap)
                count = flushingDataMap.size
                unorderedBucketMap.clear()
                dataPoints = unorderedBucketDataCount
                unorderedBucketDataCount = 0
                unorderedBucketDataWriteScheduled = false
            }

            LOG.info { "[$unorderedCollectionName] Start writing $count buckets with unordered data..." }

            // write in bulk, that's much faster
            // note that we write a maximum amount of buckets, but the number of items in each bucket may vary.
            flushingDataMap.values.chunked(unordered.bulkWrite.maxCount)
                .filter { it.isNotEmpty() }
                .forEach {
                    try {
                        bulkWrite(it)

                        // give the system some time to do something
                        Thread.sleep(unordered.bulkWrite.sleep.toMillis())
                    } catch (e: Exception) {
                        LOG.error(e) { "Exception when executing bulkWrite" }
                    }
                }

            synchronized(insertUnorderedBucketLock) {
                flushingDataMap.clear()
            }

            // Delete all the buffer data with an older generation number
            mongoBucketStorageQuery.deleteManyInBufferStageBeforeGeneration(bufferCollection, generation)

            val end = System.currentTimeMillis()
            val dps = dataPoints * 1000 / (end - start)

            LOG.info {
                "[$unorderedCollectionName] Finished writing $count buckets with $dataPoints unordered data points " +
                    "in ${end - start} ms at $dps items/second"
            }
        } catch (e: Exception) {
            LOG.error(e) { "[$unorderedCollectionName] Something went wrong while writing buckets" }
        }
    }

    private fun bulkWrite(data: List<UnorderedBucket<UnorderedData>>) {
        if (data.isEmpty()) return
        val bucketIds = data.map { it.bucketId }
        bucketIdLock.execute(bucketIds) {
            mongoBucketStorageQuery.insertMany(collection, data)
        }
    }

    override fun delete(bucketIds: Set<String>) {
        if (bucketIds.isEmpty()) return
        bucketIdLock.execute(bucketIds) {
            mongoBucketStorageQuery.deleteMany(collection, bucketIds)
        }
    }

    /**
     * helper function to lock some buckets for write/delete,
     * whilst doing some non-atomic operations inside the callback
     */
    override fun lock(bucketIds: Set<String>, callback: Runnable) {
        if (bucketIds.isEmpty()) return
        bucketIdLock.execute(bucketIds, callback)
    }

    override fun shutdown() {
        LOG.info { "[$unorderedCollectionName] Shutting down, flushing all data..." }
        bufferWriteQueue.put(BufferData.poisonPill())
        writeDataTask.cancel(false)
    }
}
