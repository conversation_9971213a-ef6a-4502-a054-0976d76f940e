package nl.teqplay.aisengine.bucketing.properties

import nl.teqplay.aisengine.bucketing.properties.BucketProperties.Sweeper
import org.springframework.boot.context.properties.ConfigurationProperties
import java.time.Duration
import java.time.LocalDate

/**
 * Properties used for the bucketing mechanism, combined with data coming from platform
 */
@ConfigurationProperties(prefix = "bucket.platform")
data class PlatformBucketProperties(
    /**
     * whether fetching data from platform is enabled
     */
    val enabled: Boolean = false,

    /**
     * (optionally) overwrites the [Sweeper.maxAge]
     */
    val maxAge: Duration? = null,

    /**
     * the point in time before which data from platform should be used
     */
    val edge: LocalDate? = null,

    /**
     * if set, overwrites the manually defined [edge] and calculates the edge dynamically.
     * [Sweeper.maxAge] is required to be set for [dynamicEdge] to be calculated.
     */
    val dynamicEdge: Boolean = false
)
