package nl.teqplay.aisengine.bucketing.config

import com.google.common.util.concurrent.ThreadFactoryBuilder
import nl.teqplay.aisengine.YamlPropertyLoaderFactory
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveGlobalProperties
import nl.teqplay.aisengine.bucketing.properties.BucketProperties
import nl.teqplay.aisengine.bucketing.properties.PlatformBucketProperties
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.PropertySource
import java.lang.annotation.Inherited
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService

@Configuration
@PropertySource("classpath:application-lib-bucketing.yml", factory = YamlPropertyLoaderFactory::class)
@EnableConfigurationProperties(BucketProperties::class, BucketArchiveGlobalProperties::class, PlatformBucketProperties::class)
class BucketingAutoConfiguration {

    @Bean(BUCKET_EXECUTOR_NAME)
    fun scheduledExecutorService(): ScheduledExecutorService =
        Executors.newScheduledThreadPool(
            /* corePoolSize = */ 4,
            /* threadFactory = */ ThreadFactoryBuilder().setNameFormat("$BUCKET_EXECUTOR_NAME-%d").build()
        )
}

const val BUCKET_EXECUTOR_NAME = "BucketExecutor"

/** Allow the template to be used as an annotation */
@Target(AnnotationTarget.VALUE_PARAMETER, AnnotationTarget.FIELD)
@Retention(AnnotationRetention.RUNTIME)
@MustBeDocumented
@Inherited
@Qualifier(BUCKET_EXECUTOR_NAME)
annotation class BucketExecutor
