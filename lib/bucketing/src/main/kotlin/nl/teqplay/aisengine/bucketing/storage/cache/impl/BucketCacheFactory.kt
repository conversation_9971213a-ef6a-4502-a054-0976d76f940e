package nl.teqplay.aisengine.bucketing.storage.cache.impl

import com.fasterxml.jackson.databind.ObjectMapper
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveConfiguration
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveGlobalProperties
import nl.teqplay.aisengine.bucketing.properties.BucketProperties
import nl.teqplay.aisengine.bucketing.storage.BucketFactory
import nl.teqplay.aisengine.bucketing.storage.BucketFactoryDual
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.cache.BucketReadCacheFactory
import nl.teqplay.aisengine.bucketing.storage.cache.BucketReadCacheFactoryDual
import nl.teqplay.aisengine.bucketing.storage.cache.BucketWriteCacheFactory
import nl.teqplay.aisengine.bucketing.storage.cache.BucketWriteCacheFactoryDual
import java.time.Duration
import java.util.concurrent.ScheduledExecutorService

object BucketCacheFactory {

    fun <Data : Any, Key : Any, ID : BucketId<Key>> bucketReadCacheFactory(
        collectionName: String,
        mongoDatabase: MongoDatabase,
        objectMapper: ObjectMapper,

        bucket: BucketProperties,
        archive: BucketArchiveConfiguration,
        archiveGlobal: BucketArchiveGlobalProperties,
        factory: BucketFactory<Data, Key, ID>,
        createArchiveReadStorage: CreateArchiveReadStorage<Data, Key, ID>,

        unorderedCollectionSuffix: String
    ): BucketReadCacheFactory<Data, Key, ID> = BucketReadCacheFactoryImpl(
        collectionName = collectionName,
        unorderedCollectionSuffix = unorderedCollectionSuffix,
        mongoDatabase = mongoDatabase,
        objectMapper = objectMapper,

        bucket = bucket,
        archive = archive,
        archiveGlobal = archiveGlobal,
        factory = factory,
        createArchiveReadStorage = createArchiveReadStorage
    )

    fun <
        OrderedData : Data,
        UnorderedData : Data,
        Data : Any,
        Output : Data,
        Key : Any,
        ID : BucketId<Key>
        > bucketReadCacheFactoryDual(
        collectionName: String,
        mongoDatabase: MongoDatabase?,
        objectMapper: ObjectMapper,

        bucket: BucketProperties,
        archive: BucketArchiveConfiguration,
        archiveGlobal: BucketArchiveGlobalProperties,
        factory: BucketFactoryDual<OrderedData, UnorderedData, Data, Output, Key, ID>,
        createArchiveReadStorage: CreateArchiveReadStorageDual<OrderedData, UnorderedData, Data, Output, Key, ID>,

        unorderedCollectionSuffix: String
    ): BucketReadCacheFactoryDual<OrderedData, UnorderedData, Data, Output, Key, ID> = BucketReadCacheFactoryDualImpl(
        collectionName = collectionName,
        unorderedCollectionSuffix = unorderedCollectionSuffix,
        mongoDatabase = mongoDatabase,
        objectMapper = objectMapper,

        bucket = bucket,
        archive = archive,
        archiveGlobal = archiveGlobal,
        factory = factory,
        createArchiveReadStorage = createArchiveReadStorage
    )

    fun <
        Data : Any,
        Key : Any,
        ID : BucketId<Key>
        > bucketWriteCacheFactory(
        collectionName: String,
        mongoDatabase: MongoDatabase,
        objectMapper: ObjectMapper,

        bucket: BucketProperties,
        archive: BucketArchiveConfiguration,
        archiveGlobal: BucketArchiveGlobalProperties,
        scheduledExecutorService: ScheduledExecutorService,
        flushInitialDelay: Duration,
        factory: BucketFactory<Data, Key, ID>,
        createArchiveWriteStorage: CreateArchiveWriteStorage<Data, Key, ID>,

        unorderedCollectionSuffix: String
    ): BucketWriteCacheFactory<Data, Key, ID> = BucketWriteCacheFactoryImpl(
        collectionName = collectionName,
        unorderedCollectionSuffix = unorderedCollectionSuffix,
        mongoDatabase = mongoDatabase,
        objectMapper = objectMapper,

        bucket = bucket,
        archive = archive,
        archiveGlobal = archiveGlobal,
        scheduledExecutorService = scheduledExecutorService,
        flushInitialDelay = flushInitialDelay,
        factory = factory,
        createArchiveWriteStorage = createArchiveWriteStorage
    )

    fun <
        OrderedData : Data,
        UnorderedData : Data,
        Data : Any,
        Output : Data,
        Key : Any,
        ID : BucketId<Key>
        > bucketWriteCacheFactoryDual(
        collectionName: String,
        mongoDatabase: MongoDatabase,
        objectMapper: ObjectMapper,

        bucket: BucketProperties,
        archive: BucketArchiveConfiguration,
        archiveGlobal: BucketArchiveGlobalProperties,
        scheduledExecutorService: ScheduledExecutorService,
        flushInitialDelay: Duration,
        factory: BucketFactoryDual<OrderedData, UnorderedData, Data, Output, Key, ID>,
        createArchiveWriteStorage: CreateArchiveWriteStorageDual<OrderedData, UnorderedData, Data, Output, Key, ID>,

        unorderedCollectionSuffix: String
    ): BucketWriteCacheFactoryDual<OrderedData, UnorderedData, Data, Output, Key, ID> = BucketWriteCacheFactoryDualImpl(
        collectionName = collectionName,
        unorderedCollectionSuffix = unorderedCollectionSuffix,
        mongoDatabase = mongoDatabase,
        objectMapper = objectMapper,

        bucket = bucket,
        archive = archive,
        archiveGlobal = archiveGlobal,
        scheduledExecutorService = scheduledExecutorService,
        flushInitialDelay = flushInitialDelay,
        factory = factory,
        createArchiveWriteStorage = createArchiveWriteStorage
    )
}
