package nl.teqplay.aisengine.bucketing.model.bucket

import com.fasterxml.jackson.annotation.JsonIgnore
import nl.teqplay.aisengine.bucketing.storage.BucketItemResolver

/**
 * A minimal implementation of a bucket containing [data] of type [Data] with a bucket [_id]
 */
interface BucketData<Data : Any> {
    val _id: String
    val bucketId: String
    val archiveBucketId: String
    val data: MutableList<Data>

    fun add(item: Data, resolver: BucketItemResolver<in Data>)
    fun addAll(items: Collection<Data>, resolver: BucketItemResolver<in Data>)

    /**
     * Used to get all [data], being downcast to a [List] instead of a [MutableList]
     */
    @JsonIgnore
    fun getAll(): List<Data> = data

    /**
     * Get the size of the [data]
     */
    fun size(): Int = data.size
}
