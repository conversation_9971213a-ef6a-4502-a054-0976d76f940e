package nl.teqplay.aisengine.bucketing.properties

import com.amazonaws.regions.Regions
import nl.teqplay.aisengine.bucketing.BUCKET_ID_SEPARATOR
import java.time.LocalDate

/**
 * Re-usable configuration for buckets stored in an AWS S3 archive
 */
interface BucketArchiveConfiguration {
    // if reading/writing is enabled (writing depends on which class implementation is used)
    val enabled: Boolean

    // if writing is enabled, don't write before this date
    val writeNotBefore: LocalDate?

    // data is saved in an object, that object has a name, and this will be the prefix of that name
    val prefix: String

    // separator between the prefix and the object name
    val prefixSeparator: String
        get() = BUCKET_ID_SEPARATOR

    // which S3 bucket is used
    val name: String

    // in which region this S3 bucket resides
    val region: Regions
}
