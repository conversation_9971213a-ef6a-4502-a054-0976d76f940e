package nl.teqplay.aisengine.bucketing.properties

import org.springframework.boot.context.properties.ConfigurationProperties
import java.time.Duration

/**
 * Properties used for the bucketing mechanism
 *
 * @param sweeper settings for removing data from storage
 * @param mongo settings for mongo storage
 */
@ConfigurationProperties(prefix = "bucket")
data class BucketProperties(
    val sweeper: Sweeper,
    val mongo: Mongo
) {
    data class Sweeper(
        // when data is considered "old" and can be deleted
        // if not set, removing of old data is disabled
        val maxAge: Duration?
    )

    data class Mongo(
        // buckets are fetched in batches, this determines how large that batch is
        val fetchBatchSize: Int,
        val unordered: Unordered
    ) {

        data class Unordered(
            val schedule: Schedule,
            val flush: Flush,
            val bulkWrite: BulkWrite
        ) {

            data class Schedule(
                // how long does it take after startup to start the initial schedule
                val afterStartup: Duration
            )

            data class Flush(
                // how old the data must be in order to be written to storage
                val minAge: Duration,
                // how often the flushing runs
                val interval: Duration,
                // how many items can be stored in-memory before flushing regardless of the set interval
                val maxItemsThreshold: Int
            )

            data class BulkWrite(
                // when writing data the items are batched, this determines how large the batch is
                val maxCount: Int,
                // after writing the data, we sleep for some time, should be relatively low to ensure some "breathing room"
                val sleep: Duration
            )
        }
    }
}
