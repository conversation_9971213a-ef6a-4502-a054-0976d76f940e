package nl.teqplay.aisengine.bucketing.storage.cache

import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import java.time.Duration

/**
 * Orchestrator of read operations for this specific bucket.
 */
abstract class BucketReadCache<
    Data : Any,
    Key : Any,
    ID : BucketId<Key>
    >(
    cacheFactory: BucketReadCacheFactory<Data, Key, ID>,

    overwriteMaxAge: Duration? = null,
) : BucketReadCacheDual<Data, Data, Data, Data, Key, ID>(
    cacheFactory,
    overwriteMaxAge,
)

/**
 * Orchestrator of read operations for this specific bucket, supporting different types for
 * ordered, unordered and output data with a common supertype.
 */
abstract class BucketReadCacheDual<
    OrderedData : Data,
    UnorderedData : Data,
    Data : Any,
    Output : Data,
    Key : Any,
    ID : BucketId<Key>
    >(
    cacheFactory: BucketReadCacheFactoryDual<OrderedData, UnorderedData, Data, Output, Key, ID>,

    overwriteMaxAge: Duration? = null,
) : BucketReadCacheInternal<OrderedData, UnorderedData, Data, Output, Key, ID>(
    collectionName = cacheFactory.collectionName,
    bucket = cacheFactory.bucket,
    factory = cacheFactory.factory,
    mongoStorage = cacheFactory.mongoStorage(),
    unorderedStorage = cacheFactory.unorderedStorage(),
    archiveStorage = cacheFactory.archiveStorage(),
    overwriteMaxAge = overwriteMaxAge
)
