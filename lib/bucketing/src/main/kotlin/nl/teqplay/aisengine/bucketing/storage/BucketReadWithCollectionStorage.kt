package nl.teqplay.aisengine.bucketing.storage

import com.mongodb.kotlin.client.MongoCollection
import nl.teqplay.aisengine.bucketing.model.bucket.BucketData

/**
 * An interface used to ensure read storage classes implement
 * a way to run custom queries with the collection directly.
 */
interface BucketReadWithCollectionStorage<Data : Any> {
    fun <T> withCollection(with: (MongoCollection<out BucketData<Data>>) -> T): T
}
