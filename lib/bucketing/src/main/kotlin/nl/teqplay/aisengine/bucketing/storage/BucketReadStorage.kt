package nl.teqplay.aisengine.bucketing.storage

import nl.teqplay.aisengine.bucketing.model.bucket.BucketData
import nl.teqplay.aisengine.bucketing.model.bucket.OrderedBucket

/**
 * An interface used to ensure read storage classes implement
 * the [load] method of bucket(s) with the correct [BucketData].
 */
interface BucketReadStorage<Data : Any> {

    fun load(bucketId: String): OrderedBucket<Data>?

    fun load(bucketIds: List<String>): Iterator<OrderedBucket<Data>>
}
