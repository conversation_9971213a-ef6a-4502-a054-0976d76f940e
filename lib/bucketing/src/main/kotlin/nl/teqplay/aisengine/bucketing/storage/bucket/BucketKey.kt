package nl.teqplay.aisengine.bucketing.storage.bucket

interface BucketKey<
    Data : Any,
    Key : Any
    > : BucketKeyDual<Data, Data, Data, Key> {
    override fun getOutputBucketKey(item: Data) = getBucketKey(item)
}

interface BucketKeyDual<
    UnorderedData : Data,
    Data : Any,
    Output : Data,
    Key : Any
    > {
    fun getBucketKey(item: UnorderedData): Key
    fun getOutputBucketKey(item: Output): Key
}
