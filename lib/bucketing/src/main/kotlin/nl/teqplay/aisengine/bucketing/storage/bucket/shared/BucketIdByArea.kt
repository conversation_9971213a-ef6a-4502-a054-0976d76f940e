package nl.teqplay.aisengine.bucketing.storage.bucket.shared

import nl.teqplay.aisengine.bucketing.BUCKET_ID_SEPARATOR
import nl.teqplay.aisengine.bucketing.storage.bucket.AreaBucketId.Companion.ARCHIVE_BUCKET_LOCATION_DECIMALS
import nl.teqplay.aisengine.bucketing.storage.bucket.AreaBucketId.Companion.BUCKET_LOCATION_DECIMALS
import nl.teqplay.aisengine.util.floorLatitudeToDecimals
import nl.teqplay.aisengine.util.floorLongitudeToDecimals
import nl.teqplay.skeleton.model.BoundingBox
import nl.teqplay.skeleton.model.Location
import nl.teqplay.skeleton.model.TimeWindow
import java.time.LocalDate

object BucketIdByArea : BucketIdByAreaShared<Location> {

    override fun getBucketId(date: LocalDate, key: Location, decimals: Int): String {
        val flooredLocation = floorToBucketSize(key, decimals)
        val flooredLon = roundToDecimalString(flooredLocation.lon, decimals)
        val flooredLat = roundToDecimalString(flooredLocation.lat, decimals)
        return "$date,$flooredLon,$flooredLat"
    }

    override fun getArchiveBucketId(bucketId: String): String {
        val (date, lon, lat) = bucketId.split(BUCKET_ID_SEPARATOR)
        return getBucketId(
            date = LocalDate.parse(date),
            key = Location(
                lat = lat.toDouble(),
                lon = lon.toDouble()
            ),
            decimals = ARCHIVE_BUCKET_LOCATION_DECIMALS
        )
    }

    private val keys: (Location) -> Iterator<Location> = { iterator { yield(it) } }

    fun floorToBucketSize(
        location: Location,
        decimals: Int = BUCKET_LOCATION_DECIMALS
    ): Location = Location(
        lat = floorLatitudeToDecimals(location.lat, decimals),
        lon = floorLongitudeToDecimals(location.lon, decimals)
    )

    fun ceilToBucketSize(
        location: Location,
        decimals: Int = BUCKET_LOCATION_DECIMALS
    ): Location {
        val flooredLocation = floorToBucketSize(location, decimals)
        val bucketSize = getBucketSize(decimals)
        return Location(
            lat = nextDegree(flooredLocation.lat, decimals, bucketSize),
            lon = nextDegree(flooredLocation.lon, decimals, bucketSize)
        )
    }

    override fun getBucketIds(window: TimeWindow, boundingBox: BoundingBox) =
        getBucketIds(
            window = window,
            boundingBox = boundingBox,
            decimals = BUCKET_LOCATION_DECIMALS,
            keys = keys
        )

    override fun getArchiveBucketIds(window: TimeWindow, boundingBox: BoundingBox) =
        getBucketIds(
            window = window,
            boundingBox = boundingBox,
            decimals = ARCHIVE_BUCKET_LOCATION_DECIMALS,
            keys = keys
        )
}
