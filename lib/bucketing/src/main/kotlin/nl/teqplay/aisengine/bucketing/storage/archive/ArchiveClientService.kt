package nl.teqplay.aisengine.bucketing.storage.archive

import com.amazonaws.auth.AWSStaticCredentialsProvider
import com.amazonaws.auth.BasicAWSCredentials
import com.amazonaws.services.s3.AmazonS3
import com.amazonaws.services.s3.AmazonS3ClientBuilder
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveConfiguration
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveGlobalProperties

class ArchiveClientService(
    val archive: BucketArchiveConfiguration,
    private val archiveGlobal: BucketArchiveGlobalProperties
) {

    val s3Client: AmazonS3 = AmazonS3ClientBuilder
        .standard()
        .withRegion(archive.region)
        .enableForceGlobalBucketAccess()
        .withCredentials()
        .build()

    private fun AmazonS3ClientBuilder.withCredentials() = if (archiveGlobal.credentials != null) {
        withCredentials(
            AWSStaticCredentialsProvider(
                with(archiveGlobal.credentials) { BasicAWSCredentials(accessKeyId, secretKey) }
            )
        )
    } else this
}
