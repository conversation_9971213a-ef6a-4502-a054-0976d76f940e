package nl.teqplay.aisengine.bucketing.storage.archive

import com.amazonaws.services.s3.model.ObjectMetadata
import com.fasterxml.jackson.databind.ObjectMapper
import nl.teqplay.aisengine.bucketing.model.bucket.OrderedBucket
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveConfiguration
import nl.teqplay.aisengine.bucketing.storage.BucketFactoryDual
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.time.LocalDate
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream

/**
 * Methods to write+read buckets to/from the archive
 */
class ArchiveWriteByMonthStorageImpl<
    OrderedData : Data,
    UnorderedData : Data,
    Data : Any,
    Output : Data,
    Key : Any,
    ID : BucketId<Key>
    >(
    override val archive: BucketArchiveConfiguration,
    objectMapper: ObjectMapper,
    private val factory: BucketFactoryDual<OrderedData, UnorderedData, Data, Output, Key, ID>,
    override val archiveClientService: ArchiveClientService
) : ArchiveByMonthStorage, ArchiveWriteStorage<OrderedData>,
    ArchiveReadByMonthStorageImpl<OrderedData>(factory.orderedDataClass, archive, objectMapper, archiveClientService) {

    /**
     * Write data into the archive
     *
     * The data will be written as a zip file containing one file per date
     * which contains an array with the data points
     *
     * This will update an existing archive with the same [archiveKey], by appending the [data] with an [OrderedBucket]
     */
    override fun addAll(
        archiveKey: String,
        date: LocalDate,
        data: List<OrderedData>,
    ): Boolean {
        try {
            val bucketsToWrite = mutableMapOf<LocalDate, OrderedBucket<OrderedData>>()

            // read prior data
            val outputStream = ByteArrayOutputStream()
            val zipOutputStream = ZipOutputStream(outputStream)
            visitFilesInS3Zip(archiveKey) { zipDate, zipInputStream ->
                val content = zipInputStream.readAllBytes()
                if (date != zipDate) {
                    // not writing for this date, just copy the data
                    zipOutputStream.putNextEntry(ZipEntry("$zipDate.json"))
                    zipOutputStream.write(content)
                    zipOutputStream.closeEntry()
                } else {
                    // prepare this data to be re-written
                    val iterator = s3ObjectMapper.readerFor(dataClass).readValues<OrderedData>(content)
                    bucketsToWrite[zipDate] = OrderedBucket(archiveKey, iterator.readAll())
                }
            }

            // append the new data
            bucketsToWrite.getOrPut(date) { OrderedBucket(archiveKey) }.addAll(data, factory.resolver)

            // append to ZIP
            bucketsToWrite.forEach { (date, bucket) ->
                val bytes = s3ObjectMapper.writeValueAsBytes(bucket.data)
                zipOutputStream.putNextEntry(ZipEntry("$date.json"))
                zipOutputStream.write(bytes)
                zipOutputStream.closeEntry()
            }

            zipOutputStream.close()
            putS3Object(archiveKey, outputStream)
            return true
        } catch (e: Exception) {
            LOG.error(e) { "Something went wrong while storing an object in the S3 archive" }
            return false
        }
    }

    fun overwriteDirect(
        archiveKey: String,
        data: Map<LocalDate, ByteArray>
    ): Boolean {
        return try {
            val outputStream = ByteArrayOutputStream()
            val zipOutputStream = ZipOutputStream(outputStream)
            data.forEach { (date, bytes) ->
                zipOutputStream.putNextEntry(ZipEntry("$date.json"))
                zipOutputStream.write(bytes)
                zipOutputStream.closeEntry()
            }
            zipOutputStream.close()
            putS3Object(archiveKey, outputStream)
            true
        } catch (e: Exception) {
            LOG.error(e) { "Something went wrong while storing an object in the S3 archive" }
            false
        }
    }

    private fun putS3Object(
        archiveKey: String,
        outputStream: ByteArrayOutputStream
    ) {
        val bytes = outputStream.toByteArray()
        val inputStream = ByteArrayInputStream(bytes)

        val metadata = ObjectMetadata()
        metadata.contentLength = bytes.size.toLong()
        metadata.contentType = "application/zip, application/octet-stream"

        archiveClientService.s3Client.putObject(archive.name, archiveKey, inputStream, metadata)
    }
}
