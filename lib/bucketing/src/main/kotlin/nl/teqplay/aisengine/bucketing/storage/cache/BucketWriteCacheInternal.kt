package nl.teqplay.aisengine.bucketing.storage.cache

import com.mongodb.MongoInterruptedException
import io.github.oshai.kotlinlogging.KotlinLogging
import jakarta.annotation.PostConstruct
import nl.teqplay.aisengine.bucketing.config.BucketingMetricRegistry
import nl.teqplay.aisengine.bucketing.model.bucket.OrderedBucket
import nl.teqplay.aisengine.bucketing.properties.BucketProperties
import nl.teqplay.aisengine.bucketing.storage.BucketFactoryDual
import nl.teqplay.aisengine.bucketing.storage.archive.ArchiveWriteStorage
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.mongo.MongoBucketWriteStorage
import nl.teqplay.aisengine.bucketing.storage.mongo.UnorderedMongoBucketWriteStorage
import java.time.Duration
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicLong
import kotlin.concurrent.thread
import kotlin.math.roundToInt
import kotlin.time.ExperimentalTime
import kotlin.time.measureTime
import kotlin.time.measureTimedValue

private val LOG = KotlinLogging.logger {}

/**
 * Orchestrator of write and read operations for this specific bucket.
 */
abstract class BucketWriteCacheInternal<
    OrderedData : Data,
    UnorderedData : Data,
    Data : Any,
    Output : Data,
    Key : Any,
    ID : BucketId<Key>
    >(
    private val collectionName: String,
    private val bucket: BucketProperties,
    override val factory: BucketFactoryDual<OrderedData, UnorderedData, Data, Output, Key, ID>,

    private val mongoStorage: MongoBucketWriteStorage<OrderedData, Data>,
    private val unorderedStorage: UnorderedMongoBucketWriteStorage<OrderedData, UnorderedData, Data>,
    override val archiveStorage: ArchiveWriteStorage<OrderedData>?,

    overwriteMaxAge: Duration?,
    private val bucketingMetricRegistry: BucketingMetricRegistry
) : BucketReadCacheInternal<OrderedData, UnorderedData, Data, Output, Key, ID>(
    collectionName,
    bucket,
    factory,
    mongoStorage,
    unorderedStorage,
    archiveStorage,
    overwriteMaxAge
) {
    /**
     * Queue on which we can sleep but be interrupted by inserting a value. Used as an alternative to Thread.sleep()
     * and Thread#interrupt, which breaks the mongo client.
     */
    private val interrupt = LinkedBlockingQueue<Boolean>(1)

    @PostConstruct
    fun startup() {
        thread(start = true, name = "Bucket flush", block = ::runBucketFlush)
        unorderedStorage.startup()
    }

    private fun runBucketFlush() {
        // initially, sleep until we may start
        var interrupted = interrupt.poll(bucket.mongo.unordered.schedule.afterStartup.toMillis(), TimeUnit.MILLISECONDS)
        if (interrupted == true) {
            return
        }

        LOG.info { "[$collectionName] Scheduling of flushing data to mongo starting..." }

        while (true) {
            try {
                // optionally wait before flushing, if we are early enough
                // at the start of the day to include more buckets
                val flushStartTime = LocalDate.now()
                    .atStartOfDay()
                    .plus(bucket.mongo.unordered.flush.minAge)
                    .plusMinutes(1) // allow for some skew in clock
                val waitBeforeFlushing = Duration.between(LocalDateTime.now(), flushStartTime)
                if (!waitBeforeFlushing.isNegative) {
                    LOG.info { "[$collectionName] Sleeping $waitBeforeFlushing before flushing buckets..." }
                    interrupted = interrupt.poll(waitBeforeFlushing.toMillis(), TimeUnit.MILLISECONDS)
                    if (interrupted == true) {
                        break
                    }
                }

                // only flush buckets in the past, not today's buckets which are still being filled
                val maxDate = getMaxFlushDate()

                // first clean up old ordered buckets, they should already be archived
                if (maxDaysInShortTermStorage != null) {
                    deleteOrderedBucketsBefore(maxDate.minusDays(maxDaysInShortTermStorage))
                } else {
                    LOG.warn { "[$collectionName] Deleting old buckets is not enabled" }
                }

                // get all bucket ids that need to be flushed
                val archiveBucketIds = unorderedStorage.findAllArchiveBucketIds(maxDate)
                if (archiveBucketIds.isEmpty()) {
                    // nothing to do, wait for a later time
                    LOG.info { "[$collectionName] No buckets to flush, skipping..." }
                    interrupted = interrupt.poll(1, TimeUnit.HOURS)
                    if (interrupted == true) {
                        break
                    } else {
                        continue
                    }
                }

                val nextFlushTime = LocalDate.now().plusDays(1)
                    .atStartOfDay()
                    .plus(bucket.mongo.unordered.flush.minAge)
                val period = Duration.between(LocalDateTime.now(), nextFlushTime)
                    .coerceAtLeast(Duration.ofHours(8))
                    .coerceAtMost(Duration.ofDays(BUCKET_TIME_SIZE_IN_DAYS))

                flushArchiveBuckets(archiveBucketIds, period)
            } catch (e: MongoInterruptedException) {
                break
            } catch (e: InterruptedException) {
                break
            } catch (e: Exception) {
                // don't let an exception break this loop
                e.printStackTrace()
            }
        }

        LOG.info { "[$collectionName] Scheduling of flushing data to mongo stopped" }
    }

    @OptIn(ExperimentalTime::class)
    internal fun flushArchiveBuckets(
        archiveBucketIds: List<String>,
        period: Duration
    ) {
        // calculate desired pace to process the data
        val interval = period.dividedBy(archiveBucketIds.size.toLong())
        val itemsPerMinute = archiveBucketIds.size / period.toMinutes().coerceAtLeast(1)
        val periodMillis = period.toMillis().coerceAtLeast(1)
        LOG.info { "[$collectionName] Flushing started for ${archiveBucketIds.size} buckets ($itemsPerMinute per minute), period $period, interval $interval" }

        // use a thread pool, to be able to run flushes in parallel
        // use essentially 4 threads to run tasks (including this thread), due to CallerRunsPolicy
        val flushThreadPoolSize = 3
        val flushQueue = LinkedBlockingQueue<Runnable>(flushThreadPoolSize)
        val flushThreadPool = ThreadPoolExecutor(
            /* corePoolSize = */ flushThreadPoolSize,
            /* maximumPoolSize = */ flushThreadPoolSize,
            /* keepAliveTime = */ 0,
            /* unit = */ TimeUnit.MILLISECONDS,
            /* workQueue = */ flushQueue,
            /* handler = */ ThreadPoolExecutor.CallerRunsPolicy()
        )

        val startTime = System.currentTimeMillis()
        val items = AtomicLong()
        archiveBucketIds.forEach { archiveBucketId ->
            try {
                val bucketIds = measureTimedValue {
                    unorderedStorage.findBucketIdsForArchive(archiveBucketId)
                }
                bucketingMetricRegistry.addTimeSpent(
                    collectionName,
                    BucketingMetricRegistry.RequestType.FIND_BUCKET_IDS,
                    bucketIds.duration.inWholeMilliseconds,
                )

                // calculate how long to sleep, to keep up with the desired pace
                val elapsedTime = System.currentTimeMillis() - startTime
                val targetTime = interval.multipliedBy(items.get())
                val targetItems = (elapsedTime / periodMillis.toDouble() * archiveBucketIds.size).roundToInt()
                val sleepDuration = targetTime.toMillis() - elapsedTime
                if (sleepDuration > 0) {
                    TimeUnit.MILLISECONDS.sleep(sleepDuration)
                    bucketingMetricRegistry.addTimeSpent(
                        collectionName,
                        BucketingMetricRegistry.RequestType.SLEEP,
                        sleepDuration,
                    )
                }

                flushThreadPool.submit {
                    flushByArchiveBucket(archiveBucketId, bucketIds.value)

                    // occasionally report stats
                    val currentItems = items.addAndGet(1)
                    if (currentItems > 0 && currentItems % 1000 == 0L) {
                        val behindCount = targetItems - currentItems
                        val scheduleText = if (behindCount > 0) "behind by $behindCount buckets" else "on schedule"
                        LOG.info { "[$collectionName] Flushed $currentItems / ${archiveBucketIds.size} buckets ($scheduleText)" }

                        bucketingMetricRegistry.setPendingBucketsToFlush(
                            collectionName = collectionName,
                            pending = behindCount.coerceAtLeast(0)
                        )
                    }
                }
            } catch (e: InterruptedException) {
                // re-throw
                throw e
            } catch (e: Exception) {
                // don't let an exception break this loop
                e.printStackTrace()
            }
        }

        // shut down the pool and wait for completion
        flushThreadPool.shutdown()
        while (!flushThreadPool.awaitTermination(10, TimeUnit.SECONDS)) {
            LOG.info { "[$collectionName] Waiting for flushing to finish" }
        }

        LOG.info { "[$collectionName] Flushing finished for ${archiveBucketIds.size} buckets" }
    }

    open fun insert(item: UnorderedData) {
        val bucketId = factory.bucketIdentifier.getBucketIdForItem(item, factory.resolver)
        insert(bucketId, item)
    }

    /**
     * Insert an [item] in the corresponding [bucketId]
     * Also append to a cached copy of the matching bucket when in cache
     */
    fun insert(bucketId: String, item: UnorderedData) {
        unorderedStorage.insert(bucketId, item)
    }

    /**
     * Force writing any unordered data that is still in memory to mongo
     * This is different from flushing unordered buckets to final buckets in mongo
     */
    fun flushUnorderedBucketsFromMemory() {
        unorderedStorage.writeUnorderedBuckets()
    }

    private fun getMaxFlushDate() = LocalDateTime.now()
        .minusDays(BUCKET_TIME_SIZE_IN_DAYS)
        .minus(bucket.mongo.unordered.flush.minAge)
        .toLocalDate()

    private fun flushByArchiveBucket(
        archiveBucketId: String,
        bucketIds: Set<String>
    ) {
        try {
            flushBuckets(bucketIds) { buckets ->
                val archiveStorage = this.archiveStorage
                if (archiveStorage != null) {
                    val archiveDate = archiveStorage.getArchiveDate(archiveBucketId)
                    val writeNotBefore = archiveStorage.archive.writeNotBefore
                    if (writeNotBefore == null || writeNotBefore <= archiveDate) {
                        val archiveKey = archiveStorage.getArchiveKey(archiveBucketId)
                        val data = buckets.flatMap { it.getAll() }
                        archiveStorage.addAll(archiveKey, archiveDate, data)
                    }
                }
            }
        } catch (e: Exception) {
            LOG.error(e) { "[$collectionName] Exception whilst flushing bucket with id $archiveBucketId" }
        }
    }

    fun interface FlushBucketCallback<T> {
        fun invoke(buckets: List<T>)
    }

    /**
     * move data from unordered storage to final buckets in mongo,
     * and optionally hook into it to also store the data elsewhere
     */
    @OptIn(ExperimentalTime::class)
    private fun flushBuckets(
        bucketIds: Set<String>,
        callback: FlushBucketCallback<OrderedBucket<OrderedData>>
    ) {
        // we need a lock here because we want to load some buckets, do stuff,
        // and when successful, delete them. But they should not be changed
        // in between, then we would delete newly added data.
        val beforeLock = System.currentTimeMillis()
        unorderedStorage.lock(bucketIds) {
            bucketingMetricRegistry.addTimeSpent(
                collectionName,
                BucketingMetricRegistry.RequestType.WAIT_LOCK,
                System.currentTimeMillis() - beforeLock,
            )

            try {
                // load from unordered storage, store merged result in mongo
                val buckets = measureTimedValue {
                    bucketIds.mapNotNull { bucketId ->
                        val unorderedBucket = unorderedStorage.load(bucketId)
                            ?: return@mapNotNull null // no bucket found, nothing to flush for this bucket

                        // merge with any existing data in Mongo
                        val mongoBucket = mongoStorage.load(bucketId)
                        if (mongoBucket != null) {
                            // unzip, to add to unordered data
                            unorderedBucket.addAll(factory.bucketFormatter.unzip(mongoBucket.getAll()), factory.resolver)
                        }

                        // zip, to finalize bucket
                        val orderedBucket = OrderedBucket<OrderedData>(bucketId)
                        orderedBucket.addAll(factory.bucketFormatter.zip(unorderedBucket.getAll()), factory.resolver)

                        // may throw exception if bucket is too large, pass it on
                        mongoStorage.save(orderedBucket)

                        orderedBucket
                    }
                }

                bucketingMetricRegistry.addTimeSpent(
                    collectionName,
                    BucketingMetricRegistry.RequestType.MONGO_FLUSH,
                    buckets.duration.inWholeMilliseconds,
                )

                // invoke a callback with the fetched buckets.
                // Can be used for example to store the data in the archive too.
                val s3Time = measureTime {
                    callback.invoke(buckets.value)
                }

                bucketingMetricRegistry.addTimeSpent(
                    collectionName,
                    BucketingMetricRegistry.RequestType.S3_FLUSH,
                    s3Time.inWholeMilliseconds,
                )

                // delete the buckets when storing was successful
                unorderedStorage.delete(bucketIds)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    private fun deleteOrderedBucketsBefore(date: LocalDate) {
        mongoStorage.deleteBucketsBefore(date)
    }

    fun shutdown() {
        interrupt.put(true)
        unorderedStorage.shutdown()
    }
}
