package nl.teqplay.aisengine.bucketing.storage.archive

import com.amazonaws.util.IOUtils
import com.fasterxml.jackson.databind.ObjectMapper
import nl.teqplay.aisengine.bucketing.model.bucket.DatedOrderedBucket
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveConfiguration
import nl.teqplay.skeleton.model.TimeWindow
import java.util.zip.ZipInputStream

/**
 * Methods to read buckets from the archive
 */
open class ArchiveReadStorageImpl<Data : Any>(
    private val dataClass: Class<Data>,
    override val archive: BucketArchiveConfiguration,
    private val objectMapper: ObjectMapper,
    override val archiveClientService: ArchiveClientService
) : ArchiveReadStorage<Data> {

    override fun get(
        window: TimeWindow,
        archiveBucketIds: Iterator<String>
    ): Sequence<DatedOrderedBucket<Data>> {
        return archiveBucketIds
            .asSequence()
            .mapNotNull { archiveBucketId ->
                val data = get(getArchiveKey(archiveBucketId)) ?: return@mapNotNull null
                val date = getArchiveDate(archiveBucketId)
                DatedOrderedBucket(
                    _id = archiveBucketId,
                    date = date,
                    data = data
                )
            }
    }

    /**
     * Get the data from an object in the archive with an [archiveKey]
     * The object is assumed to be a zip with 1 or more JSON files,
     * where each JSON file contains an array of data points with type [Data].
     */
    fun get(archiveKey: String): MutableList<Data>? {
        val s3Object = getS3Object(archiveClientService, archiveKey) ?: return null
        val zipInputStream = ZipInputStream(s3Object.objectContent)

        val data = mutableListOf<Data>()

        do {
            val entry = zipInputStream.nextEntry
            if (entry != null) {
                val content = zipInputStream.readAllBytes()
                val iterator = objectMapper.readerFor(dataClass).readValues<Data>(content)
                data.addAll(iterator.readAll())
            }
        } while (entry != null)

        // ensure the input stream is drained before closing
        IOUtils.drainInputStream(s3Object.objectContent)
        IOUtils.drainInputStream(zipInputStream)
        zipInputStream.close()
        s3Object.close()

        return data
    }
}
