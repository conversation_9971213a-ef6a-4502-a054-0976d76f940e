package nl.teqplay.aisengine.bucketing.storage

import nl.teqplay.skeleton.model.TimeWindow

/**
 * Methods used to fetch buckets
 */
interface BucketFetcher<
    OrderedData : Data,
    UnorderedData : Data,
    Data : Any,
    Output : Data,
    Key : Any
    > {

    fun fetchHistoryInTimeWindow(
        window: TimeWindow,
        key: Key,
        extendToLongTerm: Boolean
    ): Sequence<Output>

    fun fetchHistoryInTimeWindow(
        window: TimeWindow,
        extendToLongTerm: Boolean,
        sort: Boolean,
        keys: Set<Key>
    ): Sequence<Output>

    fun fetchHistoryInTimeWindow(
        window: TimeWindow,
        getBucketIds: (TimeWindow) -> Iterator<String>,
        getArchiveBucketIds: (TimeWindow) -> Iterator<String>,
        sort: Boolean,
        extendToLongTerm: Boolean
    ): Sequence<Output>

    fun fetchLocalBuckets(
        window: TimeWindow,
        bucketIds: Iterator<String>,
        sort: <PERSON>olean
    ): Sequence<OrderedData>

    fun fetchArchiveBuckets(
        window: <PERSON>W<PERSON>ow,
        archiveBucketIds: Iterator<String>,
        sort: <PERSON><PERSON><PERSON>
    ): Sequence<OrderedData>
}
