package nl.teqplay.aisengine.bucketing.storage.cache

import nl.teqplay.aisengine.bucketing.storage.archive.ArchiveWriteStorage
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.mongo.MongoBucketWriteStorage
import nl.teqplay.aisengine.bucketing.storage.mongo.UnorderedMongoBucketWriteStorage

interface BucketWriteCacheFactory<
    Data : Any,
    Key : Any,
    ID : BucketId<Key>
    > : BucketWriteCacheFactoryDual<Data, Data, Data, Data, Key, ID>

interface BucketWriteCacheFactoryDual<
    OrderedData : Data,
    UnorderedData : Data,
    Data : Any,
    Output : Data,
    Key : Any,
    ID : BucketId<Key>
    > : BucketReadCacheFactoryDual<OrderedData, UnorderedData, Data, Output, Key, ID> {

    override fun mongoStorage(): MongoBucketWriteStorage<OrderedData, Data>
    override fun unorderedStorage(): UnorderedMongoBucketWriteStorage<OrderedData, UnorderedData, Data>
    override fun archiveStorage(): ArchiveWriteStorage<OrderedData>?
}
