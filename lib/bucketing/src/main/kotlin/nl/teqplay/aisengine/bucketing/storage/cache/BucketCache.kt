package nl.teqplay.aisengine.bucketing.storage.cache

import nl.teqplay.aisengine.bucketing.storage.BucketFactoryDual
import nl.teqplay.aisengine.bucketing.storage.BucketFetcher
import nl.teqplay.aisengine.bucketing.storage.BucketFormatter
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketIdentifierDual

/**
 * An interface combining [BucketFetcher] and [BucketFactoryDual]
 */
interface BucketCache<
    Data : Any,
    Key : Any,
    ID : BucketId<Key>
    > : BucketCacheDual<Data, Data, Data, Data, Key, ID>

interface BucketCacheDual<
    OrderedData : Data,
    UnorderedData : Data,
    Data : Any,
    Output : Data,
    Key : Any,
    ID : BucketId<Key>
    > : BucketFetcher<OrderedData, UnorderedData, Data, Output, Key> {
    val factory: BucketFactoryDual<OrderedData, UnorderedData, Data, Output, Key, ID>
}

interface BucketCachePropertiesDual<
    OrderedData : Data,
    UnorderedData : Data,
    Data : Any,
    Output : Data,
    Key : Any,
    ID : BucketId<Key>
    > {
    val bucketIdentifier: BucketIdentifierDual<UnorderedData, Data, Output, Key, ID>
    val bucketFormatter: BucketFormatter<OrderedData, Data, Output>
}
