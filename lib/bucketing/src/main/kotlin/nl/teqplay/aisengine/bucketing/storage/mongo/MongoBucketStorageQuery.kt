package nl.teqplay.aisengine.bucketing.storage.mongo

import com.mongodb.client.model.Aggregates.sort
import com.mongodb.client.result.DeleteResult
import com.mongodb.client.result.InsertManyResult
import com.mongodb.kotlin.client.MongoCollection
import nl.teqplay.aisengine.bucketing.model.MongoIdAggregate
import nl.teqplay.aisengine.bucketing.model.bucket.BucketData
import nl.teqplay.aisengine.bucketing.model.bucket.BufferData
import nl.teqplay.aisengine.bucketing.model.bucket.OrderedBucket
import nl.teqplay.aisengine.bucketing.model.bucket.UnorderedBucket
import nl.teqplay.aisengine.util.ceilToLocalDate
import nl.teqplay.aisengine.util.floorToLocalDate
import nl.teqplay.skeleton.datasource.kmongo.aggregate
import nl.teqplay.skeleton.datasource.kmongo.and
import nl.teqplay.skeleton.datasource.kmongo.ascending
import nl.teqplay.skeleton.datasource.kmongo.distinct
import nl.teqplay.skeleton.datasource.kmongo.eq
import nl.teqplay.skeleton.datasource.kmongo.findOne
import nl.teqplay.skeleton.datasource.kmongo.gte
import nl.teqplay.skeleton.datasource.kmongo.`in`
import nl.teqplay.skeleton.datasource.kmongo.limit
import nl.teqplay.skeleton.datasource.kmongo.lt
import nl.teqplay.skeleton.datasource.kmongo.project
import nl.teqplay.skeleton.datasource.kmongo.projection
import nl.teqplay.skeleton.datasource.kmongo.save
import nl.teqplay.skeleton.model.TimeWindow
import org.bson.conversions.Bson
import java.time.LocalDate

object MongoBucketStorageQuery {

    object Ordered {

        fun <OrderedData : Data, Data : Any> getOldestBucketId(
            collection: MongoCollection<OrderedBucket<OrderedData>>
        ): String? = collection.aggregate<MongoIdAggregate>(
            sort(ascending(BucketData<OrderedData>::_id)),
            project(BucketData<OrderedData>::_id),
            limit(1)
        ).firstOrNull()?._id

        fun <OrderedData : Data, Data : Any> findExistingBucketIds(
            collection: MongoCollection<OrderedBucket<OrderedData>>,
            window: TimeWindow
        ): Iterator<String> {
            val from = window.from.floorToLocalDate().toString()
            val to = window.to.ceilToLocalDate().toString()
            return collection.projection(
                property = BucketData<OrderedData>::_id,
                query = and(BucketData<OrderedData>::_id gte from, BucketData<OrderedData>::_id lt to)
            ).toList().iterator()
        }

        fun <OrderedData : Data, Data : Any> load(
            collection: MongoCollection<OrderedBucket<OrderedData>>,
            bucketId: String
        ): OrderedBucket<OrderedData>? = collection.findOne(BucketData<OrderedData>::_id eq bucketId)

        fun <OrderedData : Data, Data : Any> load(
            collection: MongoCollection<OrderedBucket<OrderedData>>,
            bucketIds: List<String>
        ): Iterator<OrderedBucket<OrderedData>> = collection
            .find(BucketData<OrderedData>::_id `in` bucketIds)
            .sort(ascending(BucketData<OrderedData>::_id))
            .toList()
            .iterator()

        fun <OrderedData : Data, Data : Any> save(
            collection: MongoCollection<OrderedBucket<OrderedData>>,
            bucket: OrderedBucket<OrderedData>
        ) {
            collection.save(bucket)
        }

        fun <OrderedData : Data, Data : Any> deleteBucketsBefore(
            collection: MongoCollection<OrderedBucket<OrderedData>>,
            date: LocalDate
        ) {
            collection.deleteMany(BucketData<Data>::_id lt date.toString())
        }
    }

    object Unordered {

        fun <UnorderedData : Data, Data : Any> findAllInBufferStage(
            bufferCollection: MongoCollection<BufferData<UnorderedData>>,
        ): Iterable<BufferData<UnorderedData>> = bufferCollection.find().toList()

        fun <UnorderedData : Data, Data : Any> bufferStageInsertMany(
            bufferCollection: MongoCollection<BufferData<UnorderedData>>,
            list: List<BufferData<UnorderedData>>,
        ): InsertManyResult = bufferCollection.insertMany(list)

        fun <UnorderedData : Data, Data : Any> deleteManyInBufferStageBeforeGeneration(
            bufferCollection: MongoCollection<BufferData<UnorderedData>>,
            generation: Long,
        ): DeleteResult = bufferCollection.deleteMany(BufferData<UnorderedData>::generation lt generation)

        fun <UnorderedData : Data, Data : Any> getByBucketIdsInBufferStage(
            bufferCollection: MongoCollection<BufferData<UnorderedData>>,
            sortedBucketIds: List<String>
        ): Iterator<BufferData<UnorderedData>> = bufferCollection
            .find(BufferData<UnorderedData>::bucketId `in` sortedBucketIds)
            .sort(ascending(BufferData<UnorderedData>::bucketId))
            .toList().iterator()

        @Throws(Exception::class)
        fun <UnorderedData : Data, Data : Any> findAllArchiveBucketIdsByDistinct(
            collection: MongoCollection<UnorderedBucket<UnorderedData>>,
            filter: Bson
        ): List<String> = collection.distinct(UnorderedBucket<UnorderedData>::archiveBucketId, filter).toList()

        fun <UnorderedData : Data, Data : Any> findAllArchiveBucketIdsByProjection(
            collection: MongoCollection<UnorderedBucket<UnorderedData>>,
            filter: Bson
        ): List<String> = collection.projection(UnorderedBucket<UnorderedData>::archiveBucketId, filter).distinct()

        fun <UnorderedData : Data, Data : Any> findBucketIdsForArchive(
            collection: MongoCollection<UnorderedBucket<UnorderedData>>,
            archiveBucketId: String
        ): Set<String> = collection.distinct(
            UnorderedBucket<UnorderedData>::bucketId,
            UnorderedBucket<UnorderedData>::archiveBucketId eq archiveBucketId
        ).toSet()

        fun <UnorderedData : Data, Data : Any> getUnorderedBucketsByBucketIds(
            collection: MongoCollection<UnorderedBucket<UnorderedData>>,
            sortedBucketIds: List<String>
        ): Iterator<UnorderedBucket<UnorderedData>> = collection
            .find(UnorderedBucket<UnorderedData>::bucketId `in` sortedBucketIds)
            .sort(ascending(UnorderedBucket<UnorderedData>::bucketId))
            .toList().iterator()

        fun <UnorderedData : Data, Data : Any> insertMany(
            collection: MongoCollection<UnorderedBucket<UnorderedData>>,
            data: List<UnorderedBucket<UnorderedData>>
        ): InsertManyResult = collection.insertMany(data)

        fun <UnorderedData : Data, Data : Any> deleteMany(
            collection: MongoCollection<UnorderedBucket<UnorderedData>>,
            bucketIds: Set<String>
        ): DeleteResult = collection.deleteMany(UnorderedBucket<UnorderedData>::bucketId `in` bucketIds)
    }
}
