package nl.teqplay.aisengine.bucketing.storage

import nl.teqplay.aisengine.bucketing.model.bucket.OrderedBucket
import nl.teqplay.aisengine.bucketing.storage.cache.BucketCache
import nl.teqplay.aisengine.bucketing.storage.cache.BucketReadCacheInternal
import java.time.Instant

/**
 * Interface containing helper functions needed to retrieve data needed for bucketing.
 *
 * This is used by the following:
 * - [OrderedBucket]
 * - [BucketCache]
 * - [BucketReadCacheInternal]
 *
 * @param Data the type of the bucket item being stored
 */
interface BucketItemResolver<Data : Any> {

    /**
     * The timestamp needed for adding and reading items from the bucket.
     *
     * @return the timestamp of the [item]
     */
    fun toTimestamp(item: Data): Instant

    /**
     * The id used when adding data to the bucket, so we can update the data if it already exists.
     *
     * @return the id of the [item]
     */
    fun toId(item: Data): String
}
