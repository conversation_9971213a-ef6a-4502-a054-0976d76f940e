package nl.teqplay.aisengine.bucketing.model.bucket

import com.fasterxml.jackson.annotation.JsonIgnore
import nl.teqplay.aisengine.bucketing.storage.BucketItemResolver
import java.time.LocalDate

/**
 * Contains [data] that is ordered, backed by an [OrderedBucket], including a [date].
 */
data class DatedOrderedBucket<Data : Any>(
    override val _id: String,
    val date: LocalDate,
    override val data: MutableList<Data> = mutableListOf(),

    @JsonIgnore override val bucketId: String = _id,
    @JsonIgnore override val archiveBucketId: String = bucketId
) : BucketData<Data> {

    val orderedBucket = OrderedBucket(_id, data, bucketId, archiveBucketId)

    override fun add(item: Data, resolver: BucketItemResolver<in Data>) {
        orderedBucket.add(item, resolver)
    }

    override fun addAll(items: Collection<Data>, resolver: BucketItemResolver<in Data>) {
        orderedBucket.addAll(items, resolver)
    }
}
