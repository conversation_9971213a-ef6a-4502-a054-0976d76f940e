package nl.teqplay.aisengine.bucketing.model.bucket

import com.fasterxml.jackson.annotation.JsonIgnore
import nl.teqplay.aisengine.bucketing.storage.BucketItemResolver
import kotlin.math.absoluteValue

/**
 * Contains [data] that is ordered. Using [add] and [addAll] ensure the bucket stays ordered.
 *
 * IMPORTANT: the [_id] and [bucketId] are equal, since this bucket has been finalized.
 * This isn't true for the [UnorderedBucket].
 */
data class OrderedBucket<Data : Any>(
    override val _id: String,
    override val data: MutableList<Data> = mutableListOf(),
    @JsonIgnore override val bucketId: String = _id,
    @JsonIgnore override val archiveBucketId: String = bucketId
) : BucketData<Data> {

    /**
     * Add an item to the bucket
     * Items are inserted ordered by their timestamp.
     */
    override fun add(item: Data, resolver: BucketItemResolver<in Data>) {
        val itemTimestamp = resolver.toTimestamp(item)

        if (data.isEmpty() || itemTimestamp.isAfter(resolver.toTimestamp(data.last()))) {
            // append at the end
            data.add(item)
        } else {
            // this new item is NOT after the last event,
            // so we have to insert it at the right place

            // find the right index to insert the item
            var index = data.binarySearchBy(itemTimestamp) { resolver.toTimestamp(it) }

            if (index < 0) {
                // item is not in the list, inverted insertion point `(-insertion point - 1)`
                index = (index + 1).absoluteValue
                data.add(index, item)
            } else {
                // there's at least one but possibly more items with this timestamp, find the first one, and then see
                // if this item is a duplicate of an existing item
                while (index > 0 && resolver.toTimestamp(data[index - 1]) == itemTimestamp) {
                    index--
                }
                // find and replace duplicate, or add to the end of the sequence of items with this timestamp
                while (index + 1 < data.size && resolver.toTimestamp(data[index + 1]) == itemTimestamp) {
                    if (resolver.toId(data[index]) == resolver.toId(item)) {
                        break
                    } else {
                        index++
                    }
                }
                // replace the existing item, or add a new one
                if (resolver.toId(data[index]) == resolver.toId(item)) {
                    data[index] = item
                } else {
                    data.add(index, item)
                }
            }
        }
    }

    /**
     * Add multiple items to the bucket.
     * Items are inserted ordered by their timestamp.
     */
    override fun addAll(items: Collection<Data>, resolver: BucketItemResolver<in Data>) {
        items.forEach { add(it, resolver) }
    }
}
