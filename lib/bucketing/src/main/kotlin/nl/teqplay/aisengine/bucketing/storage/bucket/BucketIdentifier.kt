package nl.teqplay.aisengine.bucketing.storage.bucket

import nl.teqplay.aisengine.bucketing.storage.BucketItemResolver
import nl.teqplay.aisengine.bucketing.storage.cache.BucketReadCacheInternal.Companion.BUCKET_TIME_SIZE_IN_DAYS
import nl.teqplay.aisengine.util.ceilToLocalDate
import nl.teqplay.aisengine.util.floorToLocalDate
import nl.teqplay.skeleton.model.TimeWindow
import java.time.LocalDate

/**
 * An interface exposing some standard method to gather bucket ids.
 */
interface BucketIdentifier<
    Data : Any,
    Key : Any,
    ID : BucketId<Key>
    > : BucketIdentifierDual<Data, Data, Data, Key, ID>

interface BucketIdentifierDual<
    UnorderedData : Data,
    Data : Any,
    Output : Data,
    Key : Any,
    ID : BucketId<Key>
    > {
    val id: ID
    val key: BucketKeyDual<UnorderedData, Data, Output, Key>
    val archive: BucketArchive

    fun defaultArchive() = object : BucketArchive {}

    fun getBucketIdForItem(
        item: UnorderedData,
        resolver: BucketItemResolver<in Data>
    ): String {
        val timestamp = resolver.toTimestamp(item)
        val key = key.getBucketKey(item)

        val date = timestamp.floorToLocalDate()
        return id.getBucketId(date, key)
    }

    private fun getBucketIds(from: LocalDate, to: LocalDate, key: Key): Iterator<String> = iterator {
        var date = from
        do {
            yield(id.getBucketId(date, key))
            date = date.plusDays(BUCKET_TIME_SIZE_IN_DAYS)
        } while (date < to)
    }

    fun getBucketIds(window: TimeWindow, key: Key): Iterator<String> {
        val from = window.from.floorToLocalDate()
        val to = window.to.ceilToLocalDate()
        return getBucketIds(from, to, key)
    }

    fun getBucketIds(window: TimeWindow, keys: Set<Key>): Iterator<String> = iterator {
        keys.forEach { key ->
            yieldAll(getBucketIds(window, key))
        }
    }
}
