package nl.teqplay.aisengine.bucketing.storage.cache

import nl.teqplay.aisengine.bucketing.config.BucketingMetricRegistry
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import java.time.Duration

/**
 * Orchestrator of write and read operations for this specific bucket.
 */
abstract class BucketWriteCache<
    Data : Any,
    Key : Any,
    ID : BucketId<Key>
    >(
    cacheFactory: BucketWriteCacheFactory<Data, Key, ID>,
    bucketingMetricRegistry: BucketingMetricRegistry,

    overwriteMaxAge: Duration? = null,
) : BucketWriteCacheDual<Data, Data, Data, Data, Key, ID>(
    cacheFactory,
    bucketingMetricRegistry,
    overwriteMaxAge,
)

/**
 * Orchestrator of write and read operations for this specific bucket, supporting different types for
 * ordered, unordered and output data with a common supertype.
 */
abstract class BucketWriteCacheDual<
    OrderedData : Data,
    UnorderedData : Data,
    Data : Any,
    Output : Data,
    Key : Any,
    ID : BucketId<Key>
    >(
    cacheFactory: Bucket<PERSON>riteCacheFactoryDual<OrderedData, UnorderedData, Data, Output, Key, ID>,
    bucketingMetricRegistry: BucketingMetricRegistry,

    overwriteMaxAge: Duration? = null,
) : BucketWriteCacheInternal<OrderedData, UnorderedData, Data, Output, Key, ID>(
    collectionName = cacheFactory.collectionName,
    bucket = cacheFactory.bucket,
    factory = cacheFactory.factory,
    mongoStorage = cacheFactory.mongoStorage(),
    unorderedStorage = cacheFactory.unorderedStorage(),
    archiveStorage = cacheFactory.archiveStorage(),
    overwriteMaxAge = overwriteMaxAge,
    bucketingMetricRegistry = bucketingMetricRegistry,
)
