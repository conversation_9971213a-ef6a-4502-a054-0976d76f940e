package nl.teqplay.aisengine.bucketing.storage.mongo.ordered

import com.fasterxml.jackson.databind.JavaType
import com.mongodb.kotlin.client.MongoCollection
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.bucketing.model.bucket.BucketData
import nl.teqplay.aisengine.bucketing.model.bucket.OrderedBucket
import nl.teqplay.aisengine.bucketing.storage.BucketReadWithCollectionStorage
import nl.teqplay.aisengine.bucketing.storage.mongo.applyBucketCodec

/**
 * A re-usable storage class for Mongo, which exposes the used [collection].
 */
abstract class MongoBucketStorageImpl<OrderedData : Any>(
    mongoDatabase: MongoDatabase,
    collectionName: String,
    javaType: JavaType
) : BucketReadWithCollectionStorage<OrderedData> {

    protected val collection = mongoDatabase
        .getCollection<OrderedBucket<OrderedData>>(collectionName)
        .applyBucketCodec(OrderedBucket::class.java, javaType)

    override fun <T> withCollection(with: (MongoCollection<out BucketData<OrderedData>>) -> T): T {
        return with(collection)
    }
}
