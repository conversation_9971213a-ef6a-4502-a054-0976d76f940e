package nl.teqplay.aisengine.bucketing.storage.cache.impl

import com.fasterxml.jackson.databind.ObjectMapper
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveConfiguration
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveGlobalProperties
import nl.teqplay.aisengine.bucketing.properties.BucketProperties
import nl.teqplay.aisengine.bucketing.storage.BucketFactory
import nl.teqplay.aisengine.bucketing.storage.BucketFactoryDual
import nl.teqplay.aisengine.bucketing.storage.archive.ArchiveClientService
import nl.teqplay.aisengine.bucketing.storage.archive.ArchiveReadStorage
import nl.teqplay.aisengine.bucketing.storage.bucket.BucketId
import nl.teqplay.aisengine.bucketing.storage.cache.BucketReadCacheFactory
import nl.teqplay.aisengine.bucketing.storage.cache.BucketReadCacheFactoryDual
import nl.teqplay.aisengine.bucketing.storage.mongo.MongoBucketStorageQuery
import nl.teqplay.aisengine.bucketing.storage.mongo.ordered.MongoBucketReadStorageImpl
import nl.teqplay.aisengine.bucketing.storage.mongo.unordered.UnorderedMongoBucketReadStorageImpl

internal class BucketReadCacheFactoryImpl<
    Data : Any,
    Key : Any,
    ID : BucketId<Key>
    >(
    collectionName: String,
    mongoDatabase: MongoDatabase,
    objectMapper: ObjectMapper,

    bucket: BucketProperties,
    archive: BucketArchiveConfiguration,
    archiveGlobal: BucketArchiveGlobalProperties,
    factory: BucketFactory<Data, Key, ID>,
    createArchiveReadStorage: CreateArchiveReadStorage<Data, Key, ID>,

    unorderedCollectionSuffix: String
) : BucketReadCacheFactory<Data, Key, ID>, BucketReadCacheFactoryDualImpl<Data, Data, Data, Data, Key, ID>(
    collectionName, mongoDatabase, objectMapper,
    bucket, archive, archiveGlobal, factory, createArchiveReadStorage, unorderedCollectionSuffix
)

internal open class BucketReadCacheFactoryDualImpl<
    OrderedData : Data,
    UnorderedData : Data,
    Data : Any,
    Output : Data,
    Key : Any,
    ID : BucketId<Key>
    >(
    override val collectionName: String,
    val mongoDatabase: MongoDatabase?,
    val objectMapper: ObjectMapper,

    override val bucket: BucketProperties,
    override val archive: BucketArchiveConfiguration,
    val archiveGlobal: BucketArchiveGlobalProperties,
    override val factory: BucketFactoryDual<OrderedData, UnorderedData, Data, Output, Key, ID>,
    private val createArchiveReadStorage: CreateArchiveReadStorageDual<OrderedData, UnorderedData, Data, Output, Key, ID>,

    @Deprecated(message = "can be removed once `new_data` backwards compatibility isn't required anymore")
    private val unorderedCollectionSuffix: String,
) : BucketReadCacheFactoryDual<OrderedData, UnorderedData, Data, Output, Key, ID> {

    override fun mongoStorage() = mongoDatabase?.let {
        MongoBucketReadStorageImpl(
            mongoBucketStorageQuery = MongoBucketStorageQuery.Ordered,
            mongoDatabase = mongoDatabase,
            collectionName = collectionName,
            factory = factory
        )
    }

    override fun unorderedStorage() = mongoDatabase?.let {
        UnorderedMongoBucketReadStorageImpl(
            mongoBucketStorageQuery = MongoBucketStorageQuery.Unordered,
            mongoDatabase = mongoDatabase,
            collectionName = collectionName,
            collectionReadOnly = true,
            unorderedCollectionSuffix = unorderedCollectionSuffix,
            factory = factory
        )
    }

    override fun archiveStorage(): ArchiveReadStorage<OrderedData>? =
        if (archive.enabled) createArchiveReadStorage.invoke(
            archive = archive,
            objectMapper = objectMapper,
            factory = factory,
            archiveClientService = ArchiveClientService(archive, archiveGlobal)
        ) else null
}

fun interface CreateArchiveReadStorage<
    Data : Any,
    Key : Any,
    ID : BucketId<Key>
    > : CreateArchiveReadStorageDual<Data, Data, Data, Data, Key, ID>

fun interface CreateArchiveReadStorageDual<
    OrderedData : Data,
    UnorderedData : Data,
    Data : Any,
    Output : Data,
    Key : Any,
    ID : BucketId<Key>
    > {
    fun invoke(
        archive: BucketArchiveConfiguration,
        objectMapper: ObjectMapper,
        factory: BucketFactoryDual<OrderedData, UnorderedData, Data, Output, Key, ID>,
        archiveClientService: ArchiveClientService,
    ): ArchiveReadStorage<OrderedData>
}
