package nl.teqplay.aisengine.bucketing.storage.archive

import com.amazonaws.util.IOUtils
import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.databind.ObjectMapper
import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.bucketing.model.bucket.DatedOrderedBucket
import nl.teqplay.aisengine.bucketing.properties.BucketArchiveConfiguration
import nl.teqplay.aisengine.event.interfaces.TeqplayEvent
import nl.teqplay.aisengine.util.ceilToLocalDate
import nl.teqplay.aisengine.util.floorToLocalDate
import nl.teqplay.skeleton.model.TimeWindow
import java.time.LocalDate
import java.util.zip.ZipInputStream

/**
 * Methods to read buckets from the archive
 */
open class ArchiveReadByMonthStorageImpl<Data : Any>(
    protected val dataClass: Class<Data>,
    override val archive: BucketArchiveConfiguration,
    objectMapper: ObjectMapper,
    override val archiveClientService: ArchiveClientService
) : ArchiveByMonthStorage, ArchiveReadStorage<Data> {

    protected val LOG = KotlinLogging.logger {}

    protected val s3ObjectMapper: ObjectMapper = objectMapper.copy().disable(JsonGenerator.Feature.AUTO_CLOSE_TARGET)

    override fun get(
        window: TimeWindow,
        archiveBucketIds: Iterator<String>
    ): Sequence<DatedOrderedBucket<Data>> {
        val archiveKeys = archiveBucketIds.asSequence().map { getArchiveKey(it) }.distinct()
        return archiveKeys
            .mapNotNull { archiveKey ->
                val dataPerDay = get(archiveKey, window)
                    ?.toSortedMap { date1, date2 -> date1.compareTo(date2) }
                    ?: return@mapNotNull null
                dataPerDay.map { (date, data) ->
                    val archiveBucketId = getArchiveBucketId(date, archiveKey)
                    DatedOrderedBucket(
                        _id = archiveBucketId,
                        date = date,
                        data = data
                    )
                }
            }
            .flatten()
    }

    /**
     * Get the data from an object in the archive with an [archiveKey]
     * The object is assumed to be a zip with 1 or more JSON files,
     * where each JSON file contains a map of data points with type [Data] by key [LocalDate].
     * [Data] is filtered by [LocalDate] if the [window] is set.
     */
    fun get(
        archiveKey: String,
        window: TimeWindow? = null
    ): Map<LocalDate, MutableList<Data>>? {
        val from = window?.from?.floorToLocalDate()
        val to = window?.to?.ceilToLocalDate()

        val data = mutableMapOf<LocalDate, MutableList<Data>>()
        visitFilesInS3Zip(archiveKey) { date, zipInputStream ->
            // if time window is set, only deserialize if it's requested
            if (from == null || to == null || from <= date && date < to) {
                val content = zipInputStream.readAllBytes()
                val iterator = s3ObjectMapper.readerFor(dataClass).readValues<Data>(content)

                // Data for this specific date
                val listForDate = data.getOrPut(date, ::mutableListOf)
                // Event ids we have seen already
                val seen = mutableSetOf<String>()

                iterator.forEach { elem ->
                    // There was a bug that resulted in duplicate events in some S3 zipfiles, this bit filters them out
                    if (elem is TeqplayEvent) {
                        if (!seen.contains(elem._id)) {
                            seen.add(elem._id)
                            listForDate.add(elem)
                        }
                    } else {
                        listForDate.add(elem)
                    }
                }
            }
        }

        return data.ifEmpty { null }
    }

    /**
     * Requests a ZIP file from S3 (if any exists), given the [archiveKey].
     * Allowing an implementer to [visit] the files in the ZIP.
     */
    protected inline fun visitFilesInS3Zip(
        archiveKey: String,
        visit: (LocalDate, ZipInputStream) -> Unit
    ) {
        val s3Object = getS3Object(archiveClientService, archiveKey) ?: return
        val zipInputStream = ZipInputStream(s3Object.objectContent)
        do {
            val entry = zipInputStream.nextEntry
            if (entry != null) {
                val date = LocalDate.parse(entry.name.removeSuffix(".json"))
                visit(date, zipInputStream)
            }
        } while (entry != null)

        // ensure the input stream is drained before closing
        IOUtils.drainInputStream(s3Object.objectContent)
        IOUtils.drainInputStream(zipInputStream)
        zipInputStream.close()
        s3Object.close()
    }
}
