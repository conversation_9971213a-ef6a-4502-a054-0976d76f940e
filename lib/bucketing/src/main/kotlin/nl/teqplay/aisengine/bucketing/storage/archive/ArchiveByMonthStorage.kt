package nl.teqplay.aisengine.bucketing.storage.archive

import nl.teqplay.aisengine.bucketing.BUCKET_ID_SEPARATOR
import java.time.YearMonth

interface ArchiveByMonthStorage : ArchiveStorage {

    override fun getArchiveKey(archiveBucketId: String): String {
        val date = getArchiveDate(archiveBucketId)
        val yearMonth = YearMonth.of(date.year, date.monthValue)
        val key = archiveBucketId.substringAfter(BUCKET_ID_SEPARATOR)
        val separator = archive.prefixSeparator
        return "${archive.prefix}$separator$yearMonth$separator$key.zip"
    }
}
