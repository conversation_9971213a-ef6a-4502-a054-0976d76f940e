package nl.teqplay.aisengine.reventsengine.common.datasource

import com.mongodb.client.model.ReplaceOneModel
import com.mongodb.client.model.ReplaceOptions
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.reventsengine.common.model.ship.ScenarioInterestRelevantShip
import nl.teqplay.skeleton.datasource.kmongo.and
import nl.teqplay.skeleton.datasource.kmongo.contains
import nl.teqplay.skeleton.datasource.kmongo.div
import nl.teqplay.skeleton.datasource.kmongo.ensureIndex
import nl.teqplay.skeleton.datasource.kmongo.eq
import nl.teqplay.skeleton.datasource.kmongo.gte
import nl.teqplay.skeleton.datasource.kmongo.lte
import nl.teqplay.skeleton.model.TimeWindow
import org.springframework.stereotype.Component

/**
 * Data source where the resolved interests in a scenario will be stored.
 *
 * A scenario specifies higher level interests, like POMA areas, custom polygons or specific ships.
 * These interests get resolved into specific interests, namely which specific ships were relevant and at what time.
 * This also includes which scenario this interest was part of and with which other scenarios it might have overlap.
 */
@Component
class ScenariosInterestsDataSource(
    mongoDatabase: MongoDatabase
) {

    private val collection = mongoDatabase.getCollection<ScenarioInterestRelevantShip>("scenarios.interests").apply {
        ensureIndex(
            ScenarioInterestRelevantShip::scenarios,
            ScenarioInterestRelevantShip::mmsi,
            ScenarioInterestRelevantShip::window / TimeWindow::from,
            ScenarioInterestRelevantShip::window / TimeWindow::to
        )
        ensureIndex(
            ScenarioInterestRelevantShip::scenarios,
            ScenarioInterestRelevantShip::imo,
            ScenarioInterestRelevantShip::window / TimeWindow::from,
            ScenarioInterestRelevantShip::window / TimeWindow::to
        )
    }

    /**
     * Get all interests by [scenario].
     */
    fun getInterestsByScenario(
        scenario: String
    ): List<ScenarioInterestRelevantShip> = collection
        .find(ScenarioInterestRelevantShip::scenarios contains scenario)
        .toList()

    /**
     * Get interests by [mmsi] and a [window].
     */
    fun getInterestsByMmsi(
        scenario: String,
        mmsi: Int,
        window: TimeWindow
    ): List<ScenarioInterestRelevantShip> = collection
        .find(
            and(
                createContainsScenarioFilter(scenario) +
                    createTimeWindowFilter(window) +
                    listOf(ScenarioInterestRelevantShip::mmsi eq mmsi)
            )
        )
        .toList()

    /**
     * Get interests by [imo] and a [window].
     */
    fun getInterestsByImo(
        scenario: String,
        imo: Int,
        window: TimeWindow
    ): List<ScenarioInterestRelevantShip> = collection
        .find(
            and(
                createContainsScenarioFilter(scenario) +
                    createTimeWindowFilter(window) +
                    listOf(ScenarioInterestRelevantShip::imo eq imo)
            )
        )
        .toList()

    private fun createContainsScenarioFilter(scenario: String) = listOf(
        ScenarioInterestRelevantShip::scenarios contains scenario
    )

    /**
     * A filter for overlapping [ScenarioInterestRelevantShip.window]
     */
    private fun createTimeWindowFilter(window: TimeWindow) = listOf(
        ScenarioInterestRelevantShip::window / TimeWindow::from lte window.to,
        ScenarioInterestRelevantShip::window / TimeWindow::to gte window.from
    )

    /**
     * Perform upserts in bulk.
     */
    fun bulkWrite(
        newInterests: List<ScenarioInterestRelevantShip>
    ) {
        val upserts = newInterests.map {
            ReplaceOneModel(ScenarioInterestRelevantShip::id eq it.id, it, ReplaceOptions().upsert(true))
        }

        if (upserts.isEmpty()) return
        collection.bulkWrite(upserts)
    }
}
