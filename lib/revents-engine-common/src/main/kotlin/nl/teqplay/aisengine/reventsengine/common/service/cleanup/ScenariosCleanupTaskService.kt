package nl.teqplay.aisengine.reventsengine.common.service.cleanup

import nl.teqplay.aisengine.reventsengine.common.datasource.ScenariosDataSource
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.CRASHED
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.FINISHED
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.INITIALIZING_JOB_CREATE
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.POST_PROCESSING_INIT
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.POST_PROCESSING_TEARDOWN
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.PROGRESSING_END
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.TEARDOWN_STREAMING
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.TEARDOWN_UNASSIGN_CONTEXT
import nl.teqplay.aisengine.reventsengine.model.exception.ScenarioCrashReason.REVENTS_API_RESTARTED
import org.springframework.stereotype.Component

@Component
class ScenariosCleanupTaskService(
    private val scenariosDataSource: ScenariosDataSource,
) {

    /**
     * Augments the given [scenario] to be used for teardown.
     */
    fun createTeardownScenario(scenario: ScenarioState): ScenarioState {
        val phase = when (scenario.phase) {
            in INITIALIZING_JOB_CREATE..PROGRESSING_END -> PROGRESSING_END
            // teardown must always stay the same
            in TEARDOWN_STREAMING..TEARDOWN_UNASSIGN_CONTEXT -> scenario.phase
            in POST_PROCESSING_INIT..POST_PROCESSING_TEARDOWN -> POST_PROCESSING_TEARDOWN
            // otherwise perform no teardown and jump straight to the end
            else -> FINISHED
        }
        return scenario.copy(
            id = "TEARDOWN_${scenario.id}",
            phase = phase,
            teardown = true
        )
    }

    fun createAndSaveTeardownScenario(scenario: ScenarioState): ScenarioState {
        val teardownScenario = createTeardownScenario(scenario)
        scenariosDataSource.save(teardownScenario)
        return teardownScenario
    }

    /**
     * Sets a [scenario] to crashed with the [REVENTS_API_RESTARTED] reason.
     */
    fun setToCrashedAfterRestart(scenario: ScenarioState) {
        scenariosDataSource.save(scenario.copy(phase = CRASHED, crashed = REVENTS_API_RESTARTED))
    }
}
