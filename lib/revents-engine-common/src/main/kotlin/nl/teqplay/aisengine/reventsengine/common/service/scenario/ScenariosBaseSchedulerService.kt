package nl.teqplay.aisengine.reventsengine.common.service.scenario

import io.fabric8.kubernetes.client.KubernetesClientTimeoutException
import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.reventsengine.common.datasource.ScenariosDataSource
import nl.teqplay.aisengine.reventsengine.common.model.exception.ScenarioCancelledException
import nl.teqplay.aisengine.reventsengine.common.model.exception.ScenarioCrashedException
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.CANCELLED
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.CRASHED
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.Companion.FINAL_PHASES
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.FINISHED
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.FORKED
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.INITIALIZING_QUEUED
import org.springframework.stereotype.Component

private val LOG = KotlinLogging.logger { }

@Component
class ScenariosBaseSchedulerService(
    private val scenariosDataSource: ScenariosDataSource,
    private val scenariosPhaseService: ScenariosPhaseService,
) {

    enum class SchedulerState {
        CONTINUE,
        RETRY,
        FINISHED,
        CRASHED
    }

    /**
     * Runs a phase of the [scenario], returns the new state of [scenario] and [SchedulerState] of the process.
     */
    fun runPhase(scenario: ScenarioState): Pair<ScenarioState, SchedulerState> {
        var s = scenario

        val scenarioIndex = ScenarioState.InternalPhase.entries.withIndex()
            .find { it.value == s.phase }?.index
            ?: return s to SchedulerState.CRASHED

        LOG.info { "[${s.id}] ${s.phase}: running" }

        // Nothing to do if the teardown is already finished.
        if (s.teardown && s.phase == FINISHED) {
            return s to SchedulerState.FINISHED
        }

        try {
            val result = scenariosPhaseService.runPhase(s)

            if (result.moveToNextPhase) {
                // move to next phase
                val nextPhase = when {
                    result.forked -> {
                        LOG.info { "[${s.id}] ${s.phase}: forked" }
                        FORKED
                    }

                    result.skipToPhase != null -> {
                        LOG.info { "[${s.id}] ${s.phase}: skipping to phase ${result.skipToPhase}" }
                        result.skipToPhase
                    }

                    else -> ScenarioState.InternalPhase.entries[scenarioIndex + 1]
                }

                s = s.copy(phase = nextPhase, status = result.newStatus)
                checkForCancellation(s)
                scenariosDataSource.save(s)

                // scenario is final, or we just handed off to the orchestrator
                return if (s.phase in FINAL_PHASES || s.phase == INITIALIZING_QUEUED) {
                    // stop processing
                    s to SchedulerState.FINISHED
                } else {
                    s to SchedulerState.CONTINUE
                }
            } else {
                // update status
                s = s.copy(status = result.newStatus)
                checkForCancellation(s)
                scenariosDataSource.save(s)
                return s to SchedulerState.RETRY
            }
        } catch (e: ScenarioCrashedException) {
            // we crashed, stop processing this scenario
            scenariosDataSource.save(s.copy(phase = CRASHED, crashed = e.reason))
            LOG.error(e) { "Scenario crashed" }
            return s to SchedulerState.CRASHED
        } catch (e: ScenarioCancelledException) {
            LOG.info(e) { "Scenario cancelled" }
            return s to SchedulerState.CRASHED
        } catch (e: Exception) {
            // print, but ignore for now
            val errorTitle = "Exception occurred during scenario"
            when (e) {
                // timeouts are normal, it means the condition wasn't met within the timeout, it will be retried
                is KubernetesClientTimeoutException -> LOG.info { "$errorTitle: kubernetes request timeout" }
                else -> LOG.warn(e) { errorTitle }
            }
            return s to SchedulerState.RETRY
        }
    }

    private fun checkForCancellation(scenario: ScenarioState) {
        // Only if this scenario is not about a teardown can we check for cancellations.
        if (scenario.teardown) {
            return
        }

        // If current scenario was cancelled, we should abort.
        val currentScenario = scenariosDataSource.get(scenario.id)
        if (currentScenario?.phase == CANCELLED) {
            throw ScenarioCancelledException()
        }
    }
}
