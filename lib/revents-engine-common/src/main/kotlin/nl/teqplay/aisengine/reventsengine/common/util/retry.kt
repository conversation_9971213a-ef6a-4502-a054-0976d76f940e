package nl.teqplay.aisengine.reventsengine.common.util

import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.web.client.HttpStatusCodeException
import org.springframework.web.client.ResourceAccessException

/**
 * Exponential retries of HTTP requests.
 */
inline fun <reified T> retryHttp(func: () -> T): T {
    val log = KotlinLogging.logger { }
    var counter = 0

    // applying an exponential backoff strategy
    val retryAmount = 6
    var initialDelay = 500L

    while (counter < retryAmount) {
        val result = try {
            func()
        } catch (e: HttpStatusCodeException) {
            if (e.statusCode.value() in 499..599) {
                counter++
                Thread.sleep(initialDelay)
                initialDelay *= 2
                log.warn(e) { "HTTP status code exception" }
                continue
            }
            throw e
        } catch (e: ResourceAccessException) {
            counter++
            Thread.sleep(initialDelay)
            initialDelay *= 2
            log.warn(e) { "Resource access exception during HTTP request" }
            continue
        }
        return result
    }
    throw Exception("Retried too many times")
}
