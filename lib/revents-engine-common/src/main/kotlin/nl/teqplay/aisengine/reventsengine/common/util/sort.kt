package nl.teqplay.aisengine.reventsengine.common.util

import nl.teqplay.aisengine.reventsengine.common.model.event.VesselVoyageEntryV2Wrapper
import nl.teqplay.vesselvoyage.model.v2.NewVoyage

fun Iterable<VesselVoyageEntryV2Wrapper>.sort(): Iterable<VesselVoyageEntryV2Wrapper> {
    return this.sortedWith(
        // First sort by start time.
        compareBy<VesselVoyageEntryV2Wrapper> { it.entry.start.time }
            // If start time equal, ensure voyage is returned first.
            .thenByDescending { it.entry is NewVoyage }
    )
}
