package nl.teqplay.aisengine.reventsengine.common.service.event

import nl.teqplay.aisengine.event.interfaces.ActualEvent
import nl.teqplay.aisengine.reventsengine.common.datasource.ActualEventsBaseDataSource
import nl.teqplay.aisengine.reventsengine.common.model.ship.ScenarioInterestRelevantShip
import nl.teqplay.aisengine.reventsengine.common.service.scenario.ScenariosInterestsBaseService
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioRequestEvent
import nl.teqplay.aisengine.util.coerceTimeWindowWithinBoundary
import nl.teqplay.skeleton.model.TimeWindow

abstract class ActualEventsBaseService(
    private val actualEventsBaseDataSource: ActualEventsBaseDataSource,
    private val scenariosInterestsBaseService: ScenariosInterestsBaseService
) {

    /**
     * Fetch [ActualEvent] generated in the [scenario], specific to [mmsi],
     * filtered by event [types], and within the [window].
     */
    fun fetchEventsByMmsi(
        scenario: String,
        mmsi: Int,
        window: TimeWindow,
        types: Set<String>
    ): List<ActualEvent> {
        val requests = fetchScenarioRequestsByMmsi(scenario, mmsi, window)
        return fetchAndStitchEvents(requests, types)
    }

    /**
     * Fetch scenario requests that would be used by [fetchEventsByMmsi] to request events.
     */
    fun fetchScenarioRequestsByMmsi(
        scenario: String,
        mmsi: Int,
        window: TimeWindow
    ): List<ScenarioRequestEvent> {
        val interests = scenariosInterestsBaseService.getInterestsByMmsi(scenario, mmsi, window)
        return fetchScenarioRequestsByInterests(interests, window)
    }

    /**
     * Fetch [ActualEvent] generated in the [scenario], specific to [imo],
     * filtered by event [types], and within the [window].
     */
    fun fetchEventsByImo(
        scenario: String,
        imo: Int,
        window: TimeWindow,
        types: Set<String>
    ): List<ActualEvent> {
        val requests = fetchScenarioRequestsByImo(scenario, imo, window)
        return fetchAndStitchEvents(requests, types)
    }

    /**
     * Fetch scenario requests that would be used by [fetchEventsByImo] to request events.
     */
    fun fetchScenarioRequestsByImo(
        scenario: String,
        imo: Int,
        window: TimeWindow
    ): List<ScenarioRequestEvent> {
        val interests = scenariosInterestsBaseService.getInterestsByImo(scenario, imo, window)
        return fetchScenarioRequestsByInterests(interests, window)
    }

    /**
     * Fetch [ActualEvent] generated in the [ScenarioInterestRelevantShip.parent] scenario,
     * filtered by event [types], and within the [window].
     */
    fun fetchEventsByInterests(
        interests: List<ScenarioInterestRelevantShip>,
        window: TimeWindow,
        types: Set<String>
    ): List<ActualEvent> {
        val requests = fetchScenarioRequestsByInterests(interests, window)
        return fetchAndStitchEvents(requests, types)
    }

    /**
     * Fetch scenario requests by [interests], within a [window].
     */
    private fun fetchScenarioRequestsByInterests(
        interests: List<ScenarioInterestRelevantShip>,
        window: TimeWindow
    ): List<ScenarioRequestEvent> {
        val requests = interests
            .map { ScenarioRequestEvent(mmsi = it.mmsi, scenario = it.parent, window = it.window) }
            .sortedWith(compareBy({ it.mmsi }, { it.window.from }))
            .fold(mutableListOf<ScenarioRequestEvent>()) { acc, curr ->
                val previous = acc.lastOrNull()
                if (previous != null &&
                    previous.scenario == curr.scenario &&
                    previous.window.to == curr.window.from &&
                    previous.mmsi == curr.mmsi
                ) {
                    acc[acc.size - 1] = ScenarioRequestEvent(
                        mmsi = previous.mmsi,
                        scenario = previous.scenario,
                        window = TimeWindow(
                            from = previous.window.from,
                            to = curr.window.to
                        )
                    )
                } else {
                    acc.add(curr)
                }
                acc
            }

        if (requests.isEmpty()) {
            return emptyList()
        }

        // ensure all windows are corrected to not exceed the request window
        return requests.map {
            it.copy(
                window = coerceTimeWindowWithinBoundary(it.window, window)
            )
        }
    }

    /**
     * Fetches events based on the [requests], filtering on [types].
     * Stitching them together based on the available scenarios.
     */
    private fun fetchAndStitchEvents(
        requests: List<ScenarioRequestEvent>,
        types: Set<String>
    ): List<ActualEvent> {
        // TODO: stitch events from different scenarios and/or IMO/MMSI transitions while applying some overlap/margin
        val events = actualEventsBaseDataSource.fetch(requests, types)
        return events.map { it.event }
    }
}
