package nl.teqplay.aisengine.reventsengine.common.service.scenario

import nl.teqplay.aisengine.reventsengine.common.datasource.ScenariosMetadataDataSource
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioMetadata
import org.springframework.stereotype.Component

/**
 * Service to fetch and save metadata of a scenario.
 */
@Component
class ScenariosMetadataService(
    private val scenariosMetadataDataSource: ScenariosMetadataDataSource
) {

    /**
     * Get metadata for this [scenario].
     */
    fun getMetadataByScenario(
        scenario: String
    ): ScenarioMetadata? = scenariosMetadataDataSource.getMetadataByScenario(scenario)

    /**
     * Save [metadata] for a scenario.
     */
    fun save(
        metadata: ScenarioMetadata
    ) {
        scenariosMetadataDataSource.save(metadata)
    }
}
