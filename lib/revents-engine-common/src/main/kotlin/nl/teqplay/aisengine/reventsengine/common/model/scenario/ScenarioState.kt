package nl.teqplay.aisengine.reventsengine.common.model.scenario

import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioState.InternalPhase.QUEUED
import nl.teqplay.aisengine.reventsengine.global.model.InterestsRunProgress
import nl.teqplay.aisengine.reventsengine.model.exception.ScenarioCrashReason
import nl.teqplay.aisengine.reventsengine.model.interest.scenario.Interest
import nl.teqplay.aisengine.reventsengine.model.interest.scenario.InterestArea
import nl.teqplay.aisengine.reventsengine.model.scenario.Scenario
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioCreateRequest
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioEvent
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioGuarantee
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioPostProcessing
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioResponse
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioResponse.InheritingScenario
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioResponse.Status.ScenarioDuration
import nl.teqplay.skeleton.model.TimeWindow
import org.bson.codecs.pojo.annotations.BsonId
import java.time.Duration
import java.time.Instant
import java.util.UUID

/**
 * Model used to store state of the scenario in the database.
 */
data class ScenarioState(
    @BsonId
    val id: String,
    val phase: InternalPhase,
    val crashed: ScenarioCrashReason?,

    val queued: Instant,
    val pruned: Instant?,
    val cancelled: Instant?,
    val status: InternalStatus?,

    override val window: TimeWindow,
    override val windowMargin: Scenario.WindowMargin,
    override val events: Set<ScenarioEvent> = emptySet(),
    override val postProcessing: Set<ScenarioPostProcessing> = emptySet(),
    override val guarantees: Set<ScenarioGuarantee> = emptySet(),
    override val interests: List<Interest>,
    override val settings: Scenario.Settings?,
    override val metaData: Scenario.MetaData?,
    val teardown: Boolean,

    /**
     * Parent scenario, if any, indicating that this is a child scenario for the parent.
     */
    val parent: String?,

    /**
     * Inherits from another scenario, reusing all the events that were generated within this scenario.
     */
    val inherit: String?
) : Scenario {

    /**
     * Create state from a [ScenarioCreateRequest]
     */
    constructor(scenario: ScenarioCreateRequest) : this(
        id = UUID.randomUUID().toString(),
        phase = QUEUED,
        crashed = null,
        queued = Instant.now(),
        pruned = null,
        cancelled = null,
        status = null,
        window = scenario.window,
        windowMargin = scenario.windowMargin,
        events = scenario.events,
        postProcessing = scenario.postProcessing,
        guarantees = scenario.guarantees,
        interests = scenario.interests,
        settings = scenario.settings,
        teardown = false,
        parent = null,
        inherit = null,
        metaData = scenario.metaData
    )

    /**
     * Convert state into [ScenarioResponse]
     */
    fun toResponse(
        childScenarios: List<ScenarioState>,
        inheritedScenarios: List<ScenarioState>,
    ): ScenarioResponse = ScenarioResponse(
        id = id,
        phase = phase.convert(childScenarios),
        crashed = crashed,
        queued = queued,
        pruned = pruned,
        cancelled = cancelled,
        status = toStatus(childScenarios),
        window = window,
        windowMargin = windowMargin,
        events = events,
        postProcessing = postProcessing,
        guarantees = guarantees,
        interests = interests,
        settings = settings,
        metaData = metaData,
        inheritors = inheritedScenarios.map {
            InheritingScenario(
                id = it.id,
                queued = it.queued,
                postProcessing = it.postProcessing
            )
        }
    )

    /**
     * Convert [InternalStatus] to public [ScenarioResponse.Status].
     */
    private fun toStatus(childScenarios: List<ScenarioState>): ScenarioResponse.Status? {
        val children = childScenarios.mapNotNull { it.toStatus() }
        if (phase != InternalPhase.FORKED || children.isEmpty()) {
            return this.toStatus()
        }
        val percentage = children.sumOf { it.percentage } / childScenarios.size
        val initializing = children.minBy { it.initializing.time }.initializing
        val progressing = aggregateScenarioDurations(children.mapNotNull { it.progressing })
        val postProcessing = aggregateScenarioDurations(children.mapNotNull { it.postProcessing })
        return ScenarioResponse.Status(
            percentage = percentage,
            initializing = initializing,
            progressing = progressing,
            // can't have a progressing cursor on the parent, since it's forked, but can have child statuses
            progressingCursor = null,
            postProcessing = postProcessing,
            children = children
        )
    }

    private fun toStatus(): ScenarioResponse.Status? = if (status != null) {
        val (factor, progressingEstimate) = calculateProgressingEstimate()

        // Stop timer if pruned or cancelled.
        val endTime = pruned ?: cancelled ?: Instant.now()
        ScenarioResponse.Status(
            percentage = factor.times(100).toInt(),
            initializing = ScenarioDuration(
                time = status.start,
                spent = Duration.between(status.start, status.initialized ?: endTime),
                estimate = null
            ),
            progressing = if (status.initialized != null) {
                ScenarioDuration(
                    time = status.initialized,
                    spent = Duration.between(status.initialized, status.progressed ?: endTime),
                    estimate = progressingEstimate
                )
            } else null,
            progressingCursor = status.progress?.cursor,
            postProcessing = if (status.progressed != null && postProcessing.isNotEmpty()) ScenarioDuration(
                time = status.progressed,
                spent = Duration.between(status.progressed, status.postProcessed ?: endTime),
                estimate = null
            ) else null,
            children = emptyList()
        )
    } else null

    /**
     * Calculate progress of scenario, as well as an estimate how long it will
     * take for the scenario to finish progressing.
     */
    private fun calculateProgressingEstimate(): Pair<Double, Duration?> {
        return if (status?.initialized != null && status.progress != null) {
            if (status.progressed != null) {
                1.0 to null
            } else {
                val now = Instant.now()
                val spent = Duration.between(status.initialized, now)

                val start = status.progress.cursor.toEpochMilli() - status.progress.window.from.toEpochMilli()
                val end = status.progress.window.to.toEpochMilli() - status.progress.window.from.toEpochMilli()
                val factor = start / end.toDouble()

                val estimate = when (factor) {
                    0.0 -> null
                    else -> Duration.ofMillis(((spent.toMillis() / factor) - spent.toMillis()).toLong())
                }
                factor to estimate
            }
        } else 0.0 to null
    }

    fun toPortInterestAreaIds() = interests.filterIsInstance<InterestArea>()
        .filter { it.area.type == AreaIdentifier.AreaType.PORT }
        .map { it.area.id }.toSet()

    /**
     * Data could be sourced from the inherited scenario.
     */
    fun getSourceScenarioId() = inherit ?: id

    /**
     * Scenario could be forked, we are a child scenario, store for the parent.
     */
    fun getStoreScenarioId() = parent ?: id

    data class InternalStatus(
        val start: Instant,
        val initialized: Instant? = null,
        val progressed: Instant? = null,
        val postProcessed: Instant? = null,

        val contextIndex: Int? = null,
        val progress: InterestsRunProgress? = null,
    )

    enum class InternalPhase {
        QUEUED,
        QUEUED_SCENARIO_NOTIFY_EXTERNAL,
        QUEUED_INITIALIZE_INTERESTS,
        QUEUED_END,

        INITIALIZING_QUEUED,
        INITIALIZING_ASSIGN_CONTEXT,
        INITIALIZING_JOB_CREATE,
        INITIALIZING_JOB_WAIT_READY,
        INITIALIZING_STREAMING,
        INITIALIZING_RUN_SCENARIO,
        INITIALIZING_END,

        PROGRESSING,
        PROGRESSING_END,

        TEARDOWN_STREAMING,
        TEARDOWN_JOB,
        TEARDOWN_UNASSIGN_CONTEXT,

        POST_PROCESSING_INIT,
        POST_PROCESSING_VESSEL_VOYAGE,
        POST_PROCESSING_VESSEL_VOYAGE_WAIT,
        POST_PROCESSING_VESSEL_VOYAGE_V2,
        POST_PROCESSING_VESSEL_VOYAGE_V2_WAIT,
        POST_PROCESSING_TEARDOWN,
        POST_PROCESSING_END,

        FINISHED,
        CRASHED,
        FORKED,
        CANCELLED,
        PRUNED

        ;

        /**
         * Convert [InternalPhase] into public [ScenarioResponse.Phase]
         */
        fun convert(childScenarios: List<ScenarioState>): ScenarioResponse.Phase {
            val phases = childScenarios.map { it.phase.convert() } + this.convert()
            if (phases.all { it == ScenarioResponse.Phase.FINISHED }) return ScenarioResponse.Phase.FINISHED
            if (phases.any { it == ScenarioResponse.Phase.CRASHED }) return ScenarioResponse.Phase.CRASHED
            val aggregatedPhase = phases
                .filterNot { it in setOf(ScenarioResponse.Phase.FINISHED, ScenarioResponse.Phase.CRASHED) }
                .maxBy { it.ordinal }
            return aggregatedPhase
        }

        /**
         * Converts the [InternalPhase] to the public [ScenarioResponse.Phase].
         */
        private fun convert(): ScenarioResponse.Phase = when (this) {
            QUEUED,
            QUEUED_INITIALIZE_INTERESTS,
            QUEUED_SCENARIO_NOTIFY_EXTERNAL,
            QUEUED_END,
            INITIALIZING_QUEUED -> ScenarioResponse.Phase.QUEUED

            INITIALIZING_ASSIGN_CONTEXT,
            INITIALIZING_JOB_CREATE,
            INITIALIZING_JOB_WAIT_READY,
            INITIALIZING_STREAMING,
            INITIALIZING_RUN_SCENARIO,
            INITIALIZING_END -> ScenarioResponse.Phase.INITIALIZING

            PROGRESSING,
            PROGRESSING_END,
            TEARDOWN_STREAMING,
            TEARDOWN_JOB,
            TEARDOWN_UNASSIGN_CONTEXT -> ScenarioResponse.Phase.PROGRESSING

            POST_PROCESSING_INIT,
            POST_PROCESSING_VESSEL_VOYAGE,
            POST_PROCESSING_VESSEL_VOYAGE_WAIT,
            POST_PROCESSING_VESSEL_VOYAGE_V2,
            POST_PROCESSING_VESSEL_VOYAGE_V2_WAIT,
            POST_PROCESSING_TEARDOWN,
            POST_PROCESSING_END -> ScenarioResponse.Phase.POST_PROCESSING

            FINISHED,
            FORKED -> ScenarioResponse.Phase.FINISHED

            CRASHED -> ScenarioResponse.Phase.CRASHED
            PRUNED -> ScenarioResponse.Phase.PRUNED
            CANCELLED -> ScenarioResponse.Phase.CANCELLED
        }

        companion object {
            val FINAL_PHASES = values().filter { it.convert().final }.toSet()
        }
    }
}
