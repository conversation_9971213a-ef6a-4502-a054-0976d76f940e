package nl.teqplay.aisengine.reventsengine.common.datasource

import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.aisengine.reventsengine.common.model.scenario.ScenarioMetadata
import nl.teqplay.skeleton.datasource.kmongo.findOneById
import nl.teqplay.skeleton.datasource.kmongo.save
import org.springframework.stereotype.Component

/**
 * Data source where the metadata of a scenario will be stored.
 */
@Component
class ScenariosMetadataDataSource(
    mongoDatabase: MongoDatabase
) {

    private val collection = mongoDatabase.getCollection<ScenarioMetadata>("scenarios.metadata")

    /**
     * Get metadata by [scenario].
     */
    fun getMetadataByScenario(
        scenario: String
    ): ScenarioMetadata? = collection.findOneById(scenario)

    /**
     * Save the [metadata].
     */
    fun save(
        metadata: ScenarioMetadata
    ) {
        collection.save(metadata)
    }
}
