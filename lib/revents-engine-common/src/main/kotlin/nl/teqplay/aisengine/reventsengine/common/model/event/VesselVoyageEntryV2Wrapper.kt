package nl.teqplay.aisengine.reventsengine.common.model.event

import nl.teqplay.vesselvoyage.model.v2.NewESoF
import nl.teqplay.vesselvoyage.model.v2.NewEntry
import org.bson.codecs.pojo.annotations.BsonId

/**
 * Wraps an [entry] to optionally include extra information.
 */
data class VesselVoyageEntryV2Wrapper(
    val entry: NewEntry,
    val esof: NewESoF?,

    @BsonId
    val id: String = entry._id,
)
