package nl.teqplay.aisengine.nats.stream

import nl.teqplay.aisengine.nats.stream.properties.AisStreamNatsProperties
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Configuration

@Configuration
@ConditionalOnProperty(prefix = "nats.ais-stream", name = ["enabled"], havingValue = "true")
@EnableConfigurationProperties(AisStreamNatsProperties::class)
class AisStreamNatsAutoConfiguration {
    companion object {
        const val AIS_STREAM = "ais-stream"
        const val AIS_STREAM_DIFF = "$AIS_STREAM:diff"
        const val AIS_STREAM_HISTORY = "$AIS_STREAM:history"
        const val AIS_STREAM_DIFF_SUBJECT = "$AIS_STREAM.diff.>"
        const val AIS_STREAM_HISTORY_SUBJECT = "$AIS_STREAM.history.>"
    }
}
