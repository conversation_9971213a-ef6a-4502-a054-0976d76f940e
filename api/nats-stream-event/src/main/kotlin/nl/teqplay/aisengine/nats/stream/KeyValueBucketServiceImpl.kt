package nl.teqplay.aisengine.nats.stream

import nl.teqplay.skeleton.common.config.NatsClientConfiguration
import nl.teqplay.skeleton.nats.NatsClientBuilder
import nl.teqplay.skeleton.nats.NatsKeyValueBucket
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Component
import java.time.Duration

@Component
@ConditionalOnProperty(prefix = "nats.event-stream", name = ["enabled"], havingValue = "true")
class KeyValueBucketServiceImpl(
    private val natsClientBuilder: NatsClientBuilder,
) : KeyValueBucketService {

    override fun <T> keyValueBucket(
        config: NatsClientConfiguration,
        name: String,
        serializer: (T) -> ByteArray,
        deserializer: (ByteArray) -> T,
        maxAge: Duration?,
    ): NatsKeyValueBucket<T> = natsClientBuilder.keyValueBucket(
        config = config,
        name = name,
        serializer = serializer,
        deserializer = deserializer,
        storeOnDisk = true,
        maxAge = maxAge,
    ) ?: throw Exception("Error creating KV bucket")
}
