package nl.teqplay.aisengine.nats.stream

import com.fasterxml.jackson.databind.ObjectMapper
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.nats.stream.properties.EventStreamNatsProperties
import nl.teqplay.aisengine.revents.NotReventsProfile
import nl.teqplay.skeleton.nats.NatsClientBuilder
import nl.teqplay.skeleton.nats.NatsClientHealthBuilder
import nl.teqplay.skeleton.nats.NatsProducerStream
import org.springframework.boot.actuate.health.HealthIndicator
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.time.Duration

@Configuration
@NotReventsProfile
@ConditionalOnProperty(prefix = "nats.event-stream", name = ["enabled"], havingValue = "true")
class EventStreamAutoConfiguration(
    private val natsClientBuilder: Nats<PERSON>lient<PERSON>uilder,
    private val natsClientHealthBuilder: NatsClientHealthBuilder,
    private val properties: EventStreamNatsProperties,
    private val objectMapper: ObjectMapper
) {
    companion object {
        const val EVENT_STREAM = "event-stream"
        const val EVENT_STREAM_SUBJECT = "event.>"
    }

    @Bean
    fun natsEventProducerStream(): NatsProducerStream<Event>? {
        return natsClientBuilder.producerStream(
            config = properties,
            stream = EVENT_STREAM,
            subjects = listOf(EVENT_STREAM_SUBJECT),
            serializer = objectMapper::writeValueAsBytes,
            maxAge = Duration.ofHours(1),
            storeOnDisk = true
        )
    }

    @Bean
    fun natsEventProducerStreamHealthIndicator(
        producerStream: NatsProducerStream<Event>
    ): HealthIndicator {
        return natsClientHealthBuilder.natsClientHealthIndicator(producerStream)
    }
}
