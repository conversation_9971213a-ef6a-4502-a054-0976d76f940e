package nl.teqplay.aisengine.nats.stream

import nl.teqplay.aisengine.nats.stream.properties.EventStreamNatsProperties
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Configuration

@Configuration
@ConditionalOnProperty(prefix = "nats.event-stream", name = ["enabled"], havingValue = "true")
@EnableConfigurationProperties(EventStreamNatsProperties::class)
class EventStreamNatsAutoConfiguration
