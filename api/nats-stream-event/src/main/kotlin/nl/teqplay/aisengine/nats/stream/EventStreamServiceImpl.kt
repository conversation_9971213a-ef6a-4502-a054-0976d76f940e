package nl.teqplay.aisengine.nats.stream

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.nats.stream.EventStreamService.MessageContext
import nl.teqplay.aisengine.nats.stream.revents.model.ReventsAisStreamOptions
import nl.teqplay.aisengine.nats.stream.revents.model.ReventsEventStreamOptions
import nl.teqplay.skeleton.nats.NatsConsumerStream
import nl.teqplay.skeleton.nats.NatsProducerStream
import nl.teqplay.skeleton.nats.NatsSubscriber
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Service
import java.time.Duration

private val LOG = KotlinLogging.logger {}

@Service
@ConditionalOnProperty(prefix = "nats.event-stream", name = ["enabled"], havingValue = "true")
class EventStreamServiceImpl(
    private val producer: NatsProducerStream<Event>,
) : EventStreamService {

    override fun <T : Event> publish(event: T) {
        val subject = event.getSubject()
        val messageId = constructMessageId(event)

        try {
            val pubAck = producer.publish(subject, event, messageId)
            if (pubAck.isDuplicate) {
                LOG.warn { "Duplicate message published for subject: $subject, messageId: $messageId" }
            } else {
                LOG.debug { "Successfully published event ${event::class.simpleName} to subject: $subject, messageId: $messageId" }
            }
        } catch (e: Exception) {
            LOG.error(e) { "Failed to publish event ${event::class.simpleName} to subject: $subject, messageId: $messageId" }
            throw EventPublishException("Failed to publish event to NATS stream", e, event, subject, messageId)
        }
    }

    override fun consume(
        stream: NatsConsumerStream<Event>,
        suffix: String?,
        subjects: List<String>,
        enableStateEvents: Boolean,
        revents: ReventsEventStreamOptions, // ignored if not running revents
        handler: (Event, MessageContext) -> Unit,
    ): NatsSubscriber {
        val termFilter = { event: Event -> termFilter(event, enableStateEvents) }
        return genericConsume(stream, suffix, subjects, termFilter, handler)
    }

    override fun consume(
        stream: NatsConsumerStream<AisDiffMessage>,
        suffix: String?,
        revents: ReventsAisStreamOptions, // ignored if not running revents
        handler: (AisDiffMessage, MessageContext) -> Unit,
    ) = genericConsume(stream, suffix, emptyList(), { false }, handler)

    private inline fun <T> genericConsume(
        stream: NatsConsumerStream<T>,
        suffix: String?,
        subjects: List<String>,
        crossinline termFilter: (T) -> Boolean,
        crossinline handler: (T, MessageContext) -> Unit,
    ): NatsSubscriber {
        return stream.consume(
            suffix = suffix,
            subjects = subjects,
            handler = { data, message ->
                val context = object : MessageContext {
                    override fun ack() = message.ack()
                    override fun nakWithDelay(delay: Duration) = message.nakWithDelay(delay)
                    override fun term() = message.term()
                    override fun metaData() = message.metaData()
                    override fun inProgress() = message.inProgress()
                }

                try {
                    if (termFilter(data)) {
                        message.term()
                    } else {
                        handler(data, context)
                    }
                } catch (e: Exception) {
                    LOG.error(e) { "Error processing message: $e" }
                }
            }
        )
    }
}
