package nl.teqplay.aisengine.nats.stream.revents

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.nats.stream.EventStreamAutoConfiguration.Companion.EVENT_STREAM
import nl.teqplay.aisengine.nats.stream.properties.EventStreamNatsProperties
import nl.teqplay.aisengine.revents.ReventsProfile
import nl.teqplay.skeleton.nats.NatsClientBuilder
import nl.teqplay.skeleton.nats.NatsClientHealthBuilder
import nl.teqplay.skeleton.nats.NatsConsumerStream
import org.springframework.boot.actuate.health.HealthIndicator
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
@ReventsProfile
@ConditionalOnProperty(prefix = "nats.event-stream", name = ["enabled"], havingValue = "true")
class EventStreamConsumerReventsAutoConfiguration(
    private val natsClientBuilder: NatsClientBuilder,
    private val natsClientHealthBuilder: NatsClientHealthBuilder,
    private val properties: EventStreamNatsProperties,
    private val objectMapper: ObjectMapper,
) {

    @Bean
    fun natsConsumerStream(): NatsConsumerStream<Event?>? {
        return natsClientBuilder.consumerStream(
            config = properties,
            stream = EVENT_STREAM,
            deserializer = {
                if (it.isEmpty()) null
                else objectMapper.readValue(it)
            }
        )
    }

    @Bean
    fun natsConsumerStreamHealthIndicator(
        consumerStream: NatsConsumerStream<Event?>,
    ): HealthIndicator {
        return natsClientHealthBuilder.natsClientHealthIndicator(consumerStream)
    }
}
