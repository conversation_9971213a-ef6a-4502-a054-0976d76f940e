package nl.teqplay.aisengine.nats.stream.revents

import io.github.oshai.kotlinlogging.KotlinLogging
import jakarta.annotation.PostConstruct
import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.event.interfaces.ActualEvent
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.nats.stream.EventStreamService
import nl.teqplay.aisengine.nats.stream.EventStreamService.MessageContext
import nl.teqplay.aisengine.nats.stream.revents.EventStreamReventsServiceImpl.PublishBatchTimestamp.DYNAMIC_PUBLISHERS
import nl.teqplay.aisengine.nats.stream.revents.EventStreamReventsServiceImpl.PublishBatchTimestamp.SINGLE_PUBLISHER
import nl.teqplay.aisengine.nats.stream.revents.model.ReventsAisStreamOptions
import nl.teqplay.aisengine.nats.stream.revents.model.ReventsEventStreamOptions
import nl.teqplay.aisengine.revents.NATS_BATCH_APP_EXPECTED_NAMES_HEADER
import nl.teqplay.aisengine.revents.NATS_BATCH_APP_HEADER
import nl.teqplay.aisengine.revents.NATS_BATCH_TIMESTAMP_HEADER
import nl.teqplay.aisengine.revents.NATS_POISON_PILL_HEADER
import nl.teqplay.aisengine.revents.ReventsDependenciesService
import nl.teqplay.aisengine.revents.ReventsProfile
import nl.teqplay.aisengine.revents.publishBatchTimestamp
import nl.teqplay.aisengine.revents.publishPoisonPill
import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioEvent
import nl.teqplay.skeleton.nats.NatsConsumerStream
import nl.teqplay.skeleton.nats.NatsProducerStream
import nl.teqplay.skeleton.nats.NatsSubscriber
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Primary
import org.springframework.stereotype.Component
import java.time.Duration
import java.time.Instant
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicLong
import kotlin.concurrent.thread

private val LOG = KotlinLogging.logger { }

@Component
@ReventsProfile
@Primary
@ConditionalOnProperty(prefix = "nats.event-stream", name = ["enabled"], havingValue = "true")
class EventStreamReventsServiceImpl(
    private val reventsDependenciesService: ReventsDependenciesService,
    private val producer: NatsProducerStream<Event?>,
) : EventStreamService {

    /**
     * List of application names/components that are required dependencies for
     * this component to function. If all required components have indicated they are
     * finished, then this component can also indicate it's finished.
     */
    private val componentDependencies: MutableList<String> = getComponentDependencies()

    private fun getComponentDependencies(): MutableList<String> = reventsDependenciesService
        .getDependentApplicationNames()
        .toMutableList()

    /**
     * An only increasing sequence number (floor) that is raised when a message is received and is expected to get acknowledged.
     * Whether the message is acknowledged is captured by [poisonPillAckedSequenceFloor]
     * The floor should only ever be raised up, so when compared with a lower value, the higher value must stay.
     */
    private val poisonPillExpectedSequenceFloor = AtomicLong(0)

    /**
     * An only increasing sequence number (floor) that is raised when a message is acknowledged.
     * The floor should only ever be raised up, so when compared with a lower value, the higher value must stay.
     */
    private val poisonPillAckedSequenceFloor = AtomicLong(0)

    /**
     * Whether the poison pill has been received or not.
     */
    @Volatile
    private var poisonPillReceived = false

    /**
     * Indicates whether we need to wait before sending the poison pill.
     * If we've received other messages beforehand, we'd need to wait for them to be acked before we send.
     * Otherwise, we can send the poison pill immediately.
     */
    @Volatile
    private var poisonPillRequiresWait = false

    enum class PublishBatchTimestamp {
        /**
         * It's guaranteed to only be a single publisher. For example for ais-stream's diff data.
         */
        SINGLE_PUBLISHER,

        /**
         * The amount of publishers is dynamic, and depends on provided settings during runtime.
         * For example when only running area-monitor, or also other monitors.
         */
        DYNAMIC_PUBLISHERS,
    }

    override fun <T : Event> publish(event: T) {
        events.put(EventWrapper(event))
    }

    private var calledConsume = false

    override fun consume(
        stream: NatsConsumerStream<Event>,
        suffix: String?,
        subjects: List<String>,
        enableStateEvents: Boolean,
        revents: ReventsEventStreamOptions,
        handler: (Event, MessageContext) -> Unit,
    ): NatsSubscriber {
        val termFilter = { event: Event -> termFilter(event, enableStateEvents) }
        val getTimestamp = { event: Event -> if (event is ActualEvent) event.actualTime else event.createdTime }
        return genericConsume(
            stream = stream,
            suffix = suffix,
            subjects = subjects,
            requireBatchSort = revents.requireBatchSort,
            publishBatchTimestamp = DYNAMIC_PUBLISHERS,
            getTimestamp = getTimestamp,
            onPoisonPill = revents.onPoisonPill,
            termFilter = termFilter,
            handler = handler
        )
    }

    override fun consume(
        stream: NatsConsumerStream<AisDiffMessage>,
        suffix: String?,
        revents: ReventsAisStreamOptions,
        handler: (AisDiffMessage, MessageContext) -> Unit,
    ) = genericConsume(
        stream = stream,
        suffix = suffix,
        subjects = when {
            // include all messages if service vessels are included
            revents.includeServiceVessels -> listOf("ais-stream.diff.>")
            // otherwise, only use the interests
            else -> listOf("ais-stream.diff.*.interest")
        },
        requireBatchSort = false,
        publishBatchTimestamp = SINGLE_PUBLISHER,
        getTimestamp = { it.messageTime },
        termFilter = { false },
        onPoisonPill = {},
        handler = handler
    )

    private inline fun <T> genericConsume(
        stream: NatsConsumerStream<T>,
        suffix: String?,
        subjects: List<String>,
        requireBatchSort: Boolean,
        publishBatchTimestamp: PublishBatchTimestamp,
        crossinline getTimestamp: (T) -> Instant,
        crossinline onPoisonPill: () -> Unit,
        crossinline termFilter: (T) -> Boolean,
        crossinline handler: (T, MessageContext) -> Unit,
    ): NatsSubscriber {
        // ensure this method is only called once, otherwise global variables would be overwritten by multiple threads
        if (calledConsume) {
            throw Exception("consume was already called before, only once is supported")
        }
        calledConsume = true

        val batchBuffer = mutableListOf<T>()
        val batchStatus = mutableMapOf<Instant, MutableList<String>>()

        return stream.consume(
            suffix = suffix,
            subjects = subjects.ifEmpty { listOf(">") },
            ordered = true,
            handler = { data, message ->
                val streamSequence = message.metaData().streamSequence()
                val context = object : MessageContext {
                    override fun ack() {
                        // noop

                        // an acked message moves the floor up
                        poisonPillAckedSequenceFloor.getAndUpdate { oldStreamSequence ->
                            maxOf(oldStreamSequence, streamSequence)
                        }
                    }

                    override fun nakWithDelay(delay: Duration) {
                        // noop
                    }

                    override fun term() {
                        // noop
                    }

                    override fun inProgress() {
                        message.inProgress()
                    }

                    override fun metaData() = message.metaData()
                }

                val poisonPillApplicationName = message.headers?.getFirst(NATS_POISON_PILL_HEADER)
                val batchApplicationName = message.headers?.getFirst(NATS_BATCH_APP_HEADER)

                // check if we've received a poison pill, indicating no messages come after,
                // and we should prepare to shut down
                if (poisonPillApplicationName != null) {
                    componentDependencies.removeIf { dependencyName ->
                        poisonPillApplicationName.contains(dependencyName)
                    }

                    LOG.info {
                        "Received poison pill for $poisonPillApplicationName, " +
                            "expecting ${componentDependencies.size} remaining poison pill(s)"
                    }

                    if (componentDependencies.isEmpty()) {
                        // Invoke callback first, to not introduce a race between handling
                        // the callback and stopping the publishing loop.
                        onPoisonPill()
                        poisonPillReceived = true

                        // if we haven't received any messages, but already got poisoned, then we need to
                        // adjust the ack floor manually
                        if (!poisonPillRequiresWait) {
                            poisonPillExpectedSequenceFloor.set(streamSequence)
                            poisonPillAckedSequenceFloor.set(streamSequence)
                        }
                    }
                } else if (batchApplicationName != null) {
                    val headers = requireNotNull(message.headers)
                    val timestamp = Instant.parse(headers.getFirst(NATS_BATCH_TIMESTAMP_HEADER))
                    val expectedAppNames = headers.get(NATS_BATCH_APP_EXPECTED_NAMES_HEADER).toSet()

                    // Now we know which apps are expected, remove the ones that we depend on
                    // but don't expect during the current runtime.
                    componentDependencies.removeIf { it !in expectedAppNames }

                    val batchDependencies = batchStatus.getOrPut(timestamp) {
                        val dependencies = getComponentDependencies()
                        when (publishBatchTimestamp) {
                            SINGLE_PUBLISHER -> dependencies
                            else -> expectedAppNames.filter { it in dependencies }.toMutableList()
                        }
                    }
                    batchDependencies.removeIf { dependencyName ->
                        batchApplicationName.contains(dependencyName)
                    }

                    if (batchDependencies.isEmpty()) {
                        batchStatus.remove(timestamp)

                        // Collect and remove data.
                        val sortData = mutableListOf<T>()
                        batchBuffer.removeIf { bufferedMessage ->
                            val remove = getTimestamp(bufferedMessage) <= timestamp
                            if (remove) sortData.add(bufferedMessage)
                            remove
                        }

                        // Sort, handle and signal batch completion.
                        sortData.sortedBy(getTimestamp).forEach { handler(it, context) }
                        events.put(EventBatchTimestamp(timestamp, expectedAppNames))
                    }
                } else {
                    if (termFilter(data)) {
                        message.term()
                        return@consume
                    }

                    poisonPillRequiresWait = true
                    poisonPillExpectedSequenceFloor.getAndUpdate { oldStreamSequence ->
                        maxOf(oldStreamSequence, streamSequence)
                    }
                    if (requireBatchSort) {
                        batchBuffer.add(data)
                    } else {
                        handler(data, context)
                    }
                }
            }
        )
    }

    @PostConstruct
    fun init() {
        if (reventsDependenciesService.usesEventStreamProducerThread()) {
            thread(start = true, name = "publish", block = ::publish)
        }
    }

    sealed interface EventType

    data class EventWrapper(val event: Event) : EventType
    data class EventBatchTimestamp(val timestamp: Instant, val expectedAppNames: Set<String>) : EventType

    private val events = LinkedBlockingQueue<EventType>(100_000)

    private fun publish() {
        val queue = LinkedBlockingQueue<Event>(1000)
        val pending = AtomicLong()
        val published = AtomicLong()

        // only stop running when poison pill is received and there are no more events to publish
        // the poison pill is the last message, so the last valid message that needs to be processed is one earlier
        while (
            !poisonPillReceived ||
            events.isNotEmpty() ||
            poisonPillAckedSequenceFloor.get() < poisonPillExpectedSequenceFloor.get()
        ) {
            val maxTimeout = Duration.ofSeconds(5).toMillis()
            val startTime = System.currentTimeMillis()
            var batchEvent: EventBatchTimestamp? = null
            while (queue.remainingCapacity() > 0) {
                val timeout = startTime + maxTimeout - System.currentTimeMillis()
                val message = events.poll(timeout, TimeUnit.MILLISECONDS) ?: break
                when (message) {
                    is EventBatchTimestamp -> {
                        batchEvent = message
                        break
                    }
                    is EventWrapper -> queue.put(message.event)
                }
            }

            while (queue.isNotEmpty()) {
                producer.publishAsync(
                    timeout = null,
                    queue = queue,
                    pending = pending,
                    published = published,
                    subject = { requireNotNull(it).getSubject() },
                    messageId = { constructMessageId(requireNotNull(it)) }
                )
            }

            if (batchEvent != null) {
                publishBatchTimestamp(
                    subject = ScenarioEvent.REVENTS_BATCH_TIMESTAMP_SUBJECT,
                    timestamp = batchEvent.timestamp,
                    expectedAppNames = batchEvent.expectedAppNames,
                    properties = producer.getConfig(),
                    stream = producer
                )
            }
        }

        // publish poison pill to indicate this component has finished processing/publishing events
        publishPoisonPill(
            subject = ScenarioEvent.REVENTS_POISON_PILL_SUBJECT,
            properties = producer.getConfig(),
            stream = producer
        )
    }
}
