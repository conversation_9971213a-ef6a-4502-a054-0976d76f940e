package nl.teqplay.aisengine.nats.stream.revents

import io.nats.client.Connection
import nl.teqplay.aisengine.nats.stream.KeyValueBucketService
import nl.teqplay.aisengine.revents.ReventsProfile
import nl.teqplay.skeleton.common.config.NatsClientConfiguration
import nl.teqplay.skeleton.nats.NatsClient
import nl.teqplay.skeleton.nats.NatsKeyValueBucket
import nl.teqplay.skeleton.nats.NatsKeyValueBucketEntry
import nl.teqplay.skeleton.nats.NatsKeyValueBucketWatcher
import nl.teqplay.skeleton.nats.NatsKeyValueWatchOption
import nl.teqplay.skeleton.nats.NatsSubscriber
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Primary
import org.springframework.stereotype.Component
import java.time.Duration
import java.util.concurrent.BlockingQueue
import java.util.concurrent.atomic.AtomicLong

@Component
@ReventsProfile
@Primary
@ConditionalOnProperty(prefix = "nats.event-stream", name = ["enabled"], havingValue = "true")
class KeyValueBucketReventsServiceImpl : KeyValueBucketService {

    /**
     * Returns a [NatsKeyValueBucket] that is a "mock" of the real thing
     * It currently doesn't have an implementation, and relies on the app not using KV interactively.
     */
    override fun <T> keyValueBucket(
        config: NatsClientConfiguration,
        name: String,
        serializer: (T) -> ByteArray,
        deserializer: (ByteArray) -> T,
        maxAge: Duration?,
    ): NatsKeyValueBucket<T> = object : NatsKeyValueBucket<T> {

        override val natsClient: NatsClient
            get() = throw RuntimeException("Don't call NatsClient")

        override fun delete(key: String): Boolean = true
        override fun entries(): List<T> = emptyList()
        override fun get(key: String): NatsKeyValueBucketEntry<T>? = null
        override fun entries(watch: (NatsKeyValueBucketEntry<T>) -> Unit) {}
        override fun create(key: String, value: T): Long = 0
        override fun getStatus(): Connection.Status = Connection.Status.DISCONNECTED
        override fun keys(): List<String> = emptyList()
        override fun watchAll(
            vararg options: NatsKeyValueWatchOption,
            watcher: NatsKeyValueBucketWatcher<T>,
        ) = NatsSubscriber { }

        override fun putAsync(
            queue: BlockingQueue<T>,
            pending: AtomicLong,
            published: AtomicLong,
            key: (T) -> String,
            timeout: Duration?
        ) {
        }

        override fun put(key: String, value: T): Long = 0
        override fun update(key: String, value: T, expectedRevision: Long): Long = 0
    }
}
