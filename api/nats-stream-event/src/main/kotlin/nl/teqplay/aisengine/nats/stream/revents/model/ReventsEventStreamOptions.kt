package nl.teqplay.aisengine.nats.stream.revents.model

data class ReventsEventStreamOptions(
    /**
     * Batch sorting is required when we need data from multiple independent sources and require them to be sorted.
     */
    val requireBatchSort: Boolean = false,

    /**
     * Callback upon receiving poison pill, indicating no other data will be sent to us.
     */
    val onPoisonPill: () -> Unit = {},
)
