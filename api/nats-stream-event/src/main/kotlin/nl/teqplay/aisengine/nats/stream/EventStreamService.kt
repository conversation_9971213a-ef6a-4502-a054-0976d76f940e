package nl.teqplay.aisengine.nats.stream

import io.nats.client.impl.NatsJetStreamMetaData
import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.event.interfaces.ActualEvent
import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.interfaces.ExternalEvent
import nl.teqplay.aisengine.event.interfaces.PortcallEvent
import nl.teqplay.aisengine.event.interfaces.PredictedEvent
import nl.teqplay.aisengine.event.interfaces.StateEvent
import nl.teqplay.aisengine.nats.stream.revents.model.ReventsAisStreamOptions
import nl.teqplay.aisengine.nats.stream.revents.model.ReventsEventStreamOptions
import nl.teqplay.skeleton.nats.NatsConsumerStream
import nl.teqplay.skeleton.nats.NatsSubscriber
import java.time.Duration

interface EventStreamService {
    /**
     * Regex used to check for case-sensitive alphanumeric characters, which are only allowed by NATS, being A–Z, a–z and 0–9
     */
    private val allowNatsCharacters: Regex
        get() = Regex("[^0-9a-zA-Z]")

    /**
     * Publish an [Event] to the event-stream, automatically using the [Event.getSubject] to determine the subject
     * and using the [constructMessageId] function to generate a messageId so deduplication can be done.
     *
     * @param event The event object that will be pushed to the event stream
     */
    fun <T : Event> publish(event: T)

    /**
     * Consume from a [stream] of type [Event]
     */
    fun consume(
        stream: NatsConsumerStream<Event>,
        suffix: String? = null,
        subjects: List<String> = emptyList(),
        enableStateEvents: Boolean = false,
        revents: ReventsEventStreamOptions = ReventsEventStreamOptions(),
        handler: (Event, MessageContext) -> Unit
    ): NatsSubscriber

    /**
     * Consume from a [stream] of type [AisDiffMessage]
     */
    fun consume(
        stream: NatsConsumerStream<AisDiffMessage>,
        suffix: String? = null,
        revents: ReventsAisStreamOptions,
        handler: (AisDiffMessage, MessageContext) -> Unit
    ): NatsSubscriber

    /**
     * Ignore certain events if they are not enabled, by terminating them.
     */
    fun termFilter(
        event: Event,
        enableStateEvents: Boolean,
    ): Boolean {
        return !enableStateEvents && event is StateEvent
    }

    /**
     * Interface containing functions that NATS consumers use to, for example, [ack] messages.
     * Used as a wrapper to 'noop' when running revents.
     */
    interface MessageContext {
        fun ack()
        fun nakWithDelay(delay: Duration)
        fun term()
        fun inProgress()

        fun metaData(): NatsJetStreamMetaData
    }

    /**
     * Construct a messageId that is unique and can be used by nats for deduplication
     *
     * @param event The event used to create the messageId
     * @return The constructed messageId
     */
    fun <T : Event> constructMessageId(event: T): String? {
        // Don't construct a message id for external events as they are not detected by us,
        //  and we want to publish all of them to nats.
        if (event is ExternalEvent) {
            return null
        }

        val messageIdBuilder = StringBuilder(event.getSubject())
        val messageIdItems = mutableListOf<String?>()

        messageIdItems.add("${event.ship.mmsi}")
        if (event is PortcallEvent) messageIdItems.add(event.portcallId)
        if (event is EncounterEvent) messageIdItems.add("${event.otherShip.mmsi}")
        if (event is ActualEvent) messageIdItems.add("${event.actualTime.toEpochMilli()}")
        if (event is PredictedEvent) messageIdItems.add("${event.predictedTime.toEpochMilli()}")

        messageIdItems.forEach { item ->
            val escapedItem = item?.replace(allowNatsCharacters, "-")
            messageIdBuilder.append(".$escapedItem")
        }

        return messageIdBuilder.toString()
    }
}
