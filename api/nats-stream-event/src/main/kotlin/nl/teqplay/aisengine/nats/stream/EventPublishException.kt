package nl.teqplay.aisengine.nats.stream

import nl.teqplay.aisengine.event.interfaces.Event

/**
 * Exception thrown when an event fails to publish to the NATS stream.
 * Contains detailed information about the failed event for debugging and monitoring.
 */
class EventPublishException(
    message: String,
    cause: Throwable,
    val event: Event,
    val subject: String,
    val messageId: String
) : RuntimeException(message, cause) {
    
    override fun toString(): String {
        return "EventPublishException(message='$message', event=${event::class.simpleName}, subject='$subject', messageId='$messageId', cause=${cause?.message})"
    }
}
