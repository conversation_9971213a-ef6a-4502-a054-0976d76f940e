package nl.teqplay.aisengine.revents

import io.github.oshai.kotlinlogging.KotlinLogging
import io.nats.client.impl.Headers
import io.nats.client.impl.NatsMessage
import nl.teqplay.skeleton.common.config.NatsClientConfiguration
import nl.teqplay.skeleton.nats.NatsProducerStream
import java.util.concurrent.TimeUnit

const val NATS_POISON_PILL_HEADER = "Revents-Poison-Pill"

private val LOG = KotlinLogging.logger { }

fun <T> publishPoisonPill(
    subject: String,
    properties: NatsClientConfiguration,
    stream: NatsProducerStream<T>
) {
    val headers = Headers()
    headers.put(NATS_POISON_PILL_HEADER, properties.username)

    val message = NatsMessage.builder()
        .subject(subject)
        .headers(headers)
        .build()

    while (true) {
        try {
            stream.publish(message)
            break
        } catch (e: InterruptedException) {
            // rethrow so we break out of the loop and our caller is interrupted
            throw e
        } catch (e: Exception) {
            LOG.error(e) { "Something went wrong while publishing poison pill" }
            TimeUnit.SECONDS.sleep(10)
        }
    }
}
