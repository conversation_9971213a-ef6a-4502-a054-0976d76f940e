package nl.teqplay.aisengine.revents

import io.github.oshai.kotlinlogging.KotlinLogging
import io.nats.client.impl.Headers
import io.nats.client.impl.NatsMessage
import nl.teqplay.skeleton.common.config.NatsClientConfiguration
import nl.teqplay.skeleton.nats.NatsProducerStream
import java.time.Instant
import java.util.concurrent.TimeUnit

const val NATS_BATCH_TIMESTAMP_HEADER = "Revents-Batch-Timestamp"
const val NATS_BATCH_APP_HEADER = "Revents-Batch-App-Name"
const val NATS_BATCH_APP_EXPECTED_NAMES_HEADER = "Revents-Batch-App-Expected-Names"

private val LOG = KotlinLogging.logger { }

fun <T> publishBatchTimestamp(
    subject: String,
    timestamp: Instant,
    expectedAppNames: Set<String>,
    properties: NatsClientConfiguration,
    stream: NatsProducerStream<T>,
) {
    val headers = Headers()
    headers.put(NATS_BATCH_TIMESTAMP_HEADER, timestamp.toString())
    headers.put(NATS_BATCH_APP_HEADER, properties.username)
    headers.put(NATS_BATCH_APP_EXPECTED_NAMES_HEADER, expectedAppNames)

    val message = NatsMessage.builder()
        .subject(subject)
        .headers(headers)
        .build()

    while (true) {
        try {
            stream.publish(message)
            break
        } catch (e: InterruptedException) {
            // rethrow so we break out of the loop and our caller is interrupted
            throw e
        } catch (e: Exception) {
            LOG.error(e) { "Something went wrong while publishing batch timestamp" }
            TimeUnit.SECONDS.sleep(10)
        }
    }
}
