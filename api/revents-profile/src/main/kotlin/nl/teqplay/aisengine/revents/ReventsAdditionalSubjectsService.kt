package nl.teqplay.aisengine.revents

import nl.teqplay.aisengine.reventsengine.model.scenario.ScenarioEvent
import org.springframework.stereotype.Component

/**
 * Service that returns additional subject that are required for (r)events to function.
 * These are only available if the "revents" Spring Profile is enabled though.
 * Only in that case do we use multiple filters.
 */
@Component
@ReventsProfile
class ReventsAdditionalSubjectsService {

    fun getAdditionalSubjects() = listOf(
        ScenarioEvent.REVENTS_POISON_PILL_SUBJECT,
        ScenarioEvent.REVENTS_BATCH_TIMESTAMP_SUBJECT,
    )
}
