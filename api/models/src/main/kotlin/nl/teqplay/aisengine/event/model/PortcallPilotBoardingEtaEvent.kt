package nl.teqplay.aisengine.event.model

import nl.teqplay.aisengine.event.annotation.PlatformConvertable
import nl.teqplay.aisengine.event.interfaces.EventIdentifier
import nl.teqplay.aisengine.event.interfaces.PortcallPilotBoardingPredictionEvent
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.event.util.sha1
import java.time.Instant

@PlatformConvertable
data class PortcallPilotBoardingEtaEvent(
    override val _id: EventIdentifier,
    override val portcallId: String?,
    override val ship: AisShipIdentifier,
    override val area: AreaIdentifier,
    override val source: String?,
    override val predictedTime: Instant,
    override val createdTime: Instant = Instant.now(),
    override val deleted: Boolean? = null,
    override val regenerated: Boolean? = null
) : PortcallPilotBoardingPredictionEvent {
    override fun getEventType() = "prediction"
    override fun getEventSubType() = "portcall-pilot-boarding-eta"

    override fun getSubject(): String {
        val areaIdentifierSubject = area.id ?: sha1(area.name) ?: "unknown"
        return "${getEventTypePrefix()}.${getEventType()}.${getEventSubType()}.$areaIdentifierSubject"
    }
}
