package nl.teqplay.aisengine.event.model

import nl.teqplay.aisengine.event.annotation.PlatformConvertable
import nl.teqplay.aisengine.event.interfaces.EndEvent
import nl.teqplay.aisengine.event.interfaces.EventIdentifier
import nl.teqplay.aisengine.event.interfaces.RelatedBerthEvent
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.event.model.identifier.BerthIdentifier
import nl.teqplay.skeleton.model.Location
import java.time.Instant

@PlatformConvertable
data class ConfirmedBerthEndEvent(
    override val _id: EventIdentifier,
    override val startEventId: EventIdentifier?,
    override val berthEventId: EventIdentifier,
    override val ship: AisShipIdentifier,
    override val area: AreaIdentifier,
    override val berth: BerthIdentifier,
    override val heading: Int?,
    override val draught: Float?,
    override val location: Location,
    override val actualTime: Instant,
    override val createdTime: Instant = Instant.now(),
    override val deleted: Boolean? = null,
    override val regenerated: Boolean? = null
) : RelatedBerthEvent, EndEvent {
    override fun getEventType() = "berth-confirmed"
}
