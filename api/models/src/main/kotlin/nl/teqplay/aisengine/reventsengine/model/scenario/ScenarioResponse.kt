package nl.teqplay.aisengine.reventsengine.model.scenario

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonInclude
import nl.teqplay.aisengine.reventsengine.model.exception.ScenarioCrashReason
import nl.teqplay.aisengine.reventsengine.model.interest.scenario.Interest
import nl.teqplay.skeleton.model.TimeWindow
import java.time.Duration
import java.time.Instant

/**
 * Model used as a response to getting the scenario.
 */
data class ScenarioResponse(
    val id: String,
    val phase: Phase,
    val crashed: ScenarioCrashReason?,
    val queued: Instant,
    val pruned: Instant?,
    val cancelled: Instant?,
    val status: Status?,
    override val window: TimeWindow,
    override val windowMargin: Scenario.WindowMargin,
    override val events: Set<ScenarioEvent>,
    override val postProcessing: Set<ScenarioPostProcessing>,
    override val guarantees: Set<ScenarioGuarantee>,
    override val interests: List<Interest>,
    override val settings: Scenario.Settings?,
    override val metaData: Scenario.MetaData?,
    val inheritors: List<InheritingScenario>,
) : Scenario {

    /**
     * Another scenario that inherited from this one.
     * A minimal representation, since the inheriting scenario could also be forked and prompt recursive lookups.
     */
    data class InheritingScenario(
        val id: String,
        val queued: Instant,
        val postProcessing: Set<ScenarioPostProcessing>
    )

    enum class Phase(
        /**
         * Indicates whether this [Phase] is [final].
         * If [final] is set, then this scenario's phases will not be updated anymore and it's considered stable.
         */
        val final: Boolean = false
    ) {
        /**
         * Scenario is queued, waiting to be picked up.
         */
        QUEUED,

        /**
         * Scenario is initializing, an instance is being spun up and the applications are started.
         */
        INITIALIZING,

        /**
         * Scenario is progressing, data is being fetched and events are being generated.
         */
        PROGRESSING,

        /**
         * Scenario is post-processing, events are sent off for post-processing.
         */
        POST_PROCESSING,

        /**
         * Scenario is finished. Data is available.
         */
        FINISHED(final = true),

        /**
         * Scenario is crashed. Data is not to be trusted, something went wrong somewhere.
         */
        CRASHED(final = true),

        /**
         * Scenario is pruned, events or other data is either fully or partially removed. Data is not to be trusted.
         */
        PRUNED(final = true),

        /**
         * Scenario is cancelled. Data is not to be trusted.
         */
        CANCELLED(final = true),
    }

    data class Status(
        val percentage: Int,

        val initializing: ScenarioDuration,
        val progressing: ScenarioDuration?,
        val progressingCursor: Instant?,
        val postProcessing: ScenarioDuration?,

        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        val children: List<Status> = emptyList()
    ) {
        data class ScenarioDuration(
            val time: Instant,
            @JsonFormat(shape = JsonFormat.Shape.STRING) val spent: Duration,
            @JsonFormat(shape = JsonFormat.Shape.STRING) val estimate: Duration?
        )
    }
}
