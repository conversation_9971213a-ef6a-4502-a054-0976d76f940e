package nl.teqplay.aisengine.event.model.hamis

import nl.teqplay.aisengine.event.annotation.PlatformConvertable
import nl.teqplay.aisengine.event.interfaces.EventIdentifier
import nl.teqplay.aisengine.event.interfaces.HamisEvent
import nl.teqplay.aisengine.event.interfaces.PortcallVisitEvent
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.event.model.identifier.GeneralShipIdentifier
import java.time.Instant

@PlatformConvertable
data class HamisAddPortcallVisitEvent(
    override val _id: EventIdentifier,
    override val ship: GeneralShipIdentifier,
    override val portcallId: String,
    override val port: AreaIdentifier,
    override val berthName: String?,
    override val berthOwnerId: String?,
    override val visitIndex: Int,
    override val createdTime: Instant = Instant.now(),
    override val deleted: Boolean? = null,
    override val regenerated: Boolean? = null
) : PortcallVisitEvent, HamisEvent {
    override fun getEventSubType(): String {
        return "add"
    }
}
