package nl.teqplay.aisengine.event.model.hamis

import nl.teqplay.aisengine.event.annotation.PlatformConvertable
import nl.teqplay.aisengine.event.interfaces.EtaCancelEvent
import nl.teqplay.aisengine.event.interfaces.EventIdentifier
import nl.teqplay.aisengine.event.interfaces.HamisEvent
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.event.model.identifier.GeneralShipIdentifier
import java.time.Instant

@PlatformConvertable
data class HamisEtaCancelEvent(
    override val _id: EventIdentifier,
    override val ship: GeneralShipIdentifier,
    override val portcallId: String,
    override val port: AreaIdentifier,
    override val movementId: String?,
    override val vesselAgent: String?,
    override val createdTime: Instant = Instant.now(),
    override val deleted: Boolean? = null,
    override val regenerated: Boolean? = null
) : EtaCancelEvent, HamisEvent {
    override val source: String
        get() = "Port Authority"
}
