package nl.teqplay.aisengine.aisstream.model

import nl.teqplay.aisengine.aisstream.model.AisMessage.PositionAccuracy
import nl.teqplay.aisengine.aisstream.model.AisMessage.PositionSensorType
import nl.teqplay.skeleton.model.Location

data class AisStationMessage(
    override val mmsi: Int,
    val location: Location? = null,
    val positionAccuracy: PositionAccuracy? = null,
    val transponderPosition: TransponderPosition? = null,
    val positionSensorType: PositionSensorType? = null,
) : BaseAisMessage
