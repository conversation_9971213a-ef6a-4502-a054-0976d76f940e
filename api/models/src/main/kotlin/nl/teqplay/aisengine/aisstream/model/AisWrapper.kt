package nl.teqplay.aisengine.aisstream.model

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonSubTypes.Type
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.annotation.JsonTypeInfo.As
import com.fasterxml.jackson.annotation.JsonTypeInfo.Id
import nl.teqplay.aisengine.aisstream.model.TypeNames.LONGRANGE
import nl.teqplay.aisengine.aisstream.model.TypeNames.POSITION
import nl.teqplay.aisengine.aisstream.model.TypeNames.STATIC
import nl.teqplay.aisengine.aisstream.model.TypeNames.STATION
import java.time.Instant

object TypeNames {
    const val POSITION = "position"
    const val LONGRANGE = "longrange"
    const val STATIC = "static"
    const val STATION = "station"
}

/**
 * Wrapper class to store an AIS message and associated metadata. Storing the metadata in the (immutable) AIS message
 * data class itself is impractical because this requires the conversion functions to have access to that metadata.
 */
@JsonTypeInfo(
    use = Id.NAME,
    include = As.EXISTING_PROPERTY,
    property = "type"
)
@JsonSubTypes(
    Type(AisPositionWrapper::class, name = POSITION),
    Type(AisLongRangeWrapper::class, name = LONGRANGE),
    Type(AisStaticWrapper::class, name = STATIC),
    Type(AisStationWrapper::class, name = STATION)
)
sealed interface AisWrapper<T : BaseAisMessage> {
    val type: String
    val timestamp: Instant
    val receptionTimestamp: Instant
    val source: String
    val subSource: String?
    val message: T

    /**
     * Derived field, which will be updated just before being sent.
     * It can only be determined when comparing against known state.
     * Either being a real-time or [historic] update.
     */
    val historic: Boolean
}

/**
 * Class wrapping a position message, to get around generic type erasure issues.
 */
data class AisPositionWrapper(
    override val timestamp: Instant,
    override val receptionTimestamp: Instant = timestamp,
    override val source: String,
    override val subSource: String?,
    override val message: AisPositionMessage,

    override val historic: Boolean = false,
) : AisWrapper<AisPositionMessage> {
    override val type: String = POSITION
}

/**
 * Class wrapping a long range message, to get around generic type erasure issues.
 */
data class AisLongRangeWrapper(
    override val timestamp: Instant,
    override val receptionTimestamp: Instant = timestamp,
    override val source: String,
    override val subSource: String?,
    override val message: AisLongRangeMessage,

    override val historic: Boolean = false,
) : AisWrapper<AisLongRangeMessage> {
    override val type: String = LONGRANGE
}

/**
 * Class wrapping a static message, to get around generic type erasure issues.
 */
data class AisStaticWrapper(
    override val timestamp: Instant,
    override val receptionTimestamp: Instant = timestamp,
    override val source: String,
    override val subSource: String?,
    override val message: AisStaticMessage,

    override val historic: Boolean = false,
) : AisWrapper<AisStaticMessage> {
    override val type: String = STATIC
}

/**
 * Class wrapping a station message, to get around generic type erasure issues.
 */
data class AisStationWrapper(
    override val timestamp: Instant,
    override val receptionTimestamp: Instant = timestamp,
    override val source: String,
    override val subSource: String?,
    override val message: AisStationMessage,

    override val historic: Boolean = false,
) : AisWrapper<AisStationMessage> {
    override val type: String = STATION
}

/**
 * Convenience function taking metadata and [message], and returning the correct wrapped message type.
 */
fun wrapIt(
    timestamp: Instant,
    receptionTimestamp: Instant,
    source: String,
    subSource: String?,
    message: BaseAisMessage
): AisWrapper<out BaseAisMessage> =
    when (message) {
        is AisPositionMessage -> AisPositionWrapper(timestamp, receptionTimestamp, source, subSource, message)
        is AisLongRangeMessage -> AisLongRangeWrapper(timestamp, receptionTimestamp, source, subSource, message)
        is AisStaticMessage -> AisStaticWrapper(timestamp, receptionTimestamp, source, subSource, message)
        is AisStationMessage -> AisStationWrapper(timestamp, receptionTimestamp, source, subSource, message)
    }
