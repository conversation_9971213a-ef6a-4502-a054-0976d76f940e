package nl.teqplay.aisengine.event.model

import nl.teqplay.aisengine.event.annotation.PlatformConvertable
import nl.teqplay.aisengine.event.interfaces.EventIdentifier
import nl.teqplay.aisengine.event.interfaces.SpeedEvent
import nl.teqplay.aisengine.event.interfaces.SpeedEvent.SpeedType
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.skeleton.model.Location
import java.time.Instant

@PlatformConvertable
data class SpeedChangedEvent(
    override val _id: EventIdentifier,
    override val ship: AisShipIdentifier,
    override val location: Location,
    override val speedType: SpeedType,
    override val actualTime: Instant,
    override val createdTime: Instant = Instant.now(),
    override val deleted: Boolean? = null,
    override val regenerated: Boolean? = null
) : SpeedEvent
