package nl.teqplay.aisengine.reventsengine.model.exception

enum class ScenarioCrashReason {
    SCENARIO_INTERESTS_RESOLVE_NONE_SPECIFIED,
    SC<PERSON>ARIO_INTERESTS_RESOLVE_INVALID_COMBINATION,

    <PERSON><PERSON><PERSON><PERSON>_INTERESTS_RESOLVE_SHIP_NOT_SUPPORTED,

    <PERSON><PERSON><PERSON><PERSON>_INTERESTS_RESOLVE_AREA_NOT_SUPPORTED,
    SC<PERSON><PERSON>IO_INTERESTS_RESOLVE_AREA_REQUEST_FAILED,
    SCENARIO_INTERESTS_RESOLVE_AREA_RESPONSE_INCOMPLETE,
    <PERSON><PERSON>AR<PERSON>_INTERESTS_RESOLVE_AREA_REQUIRES_ONLY_MAIN_PORTS,

    <PERSON><PERSON><PERSON><PERSON>_NOTIFY_FAILED,

    SC<PERSON><PERSON>IO_INHERIT_PARENT_NOT_FOUND,
    SC<PERSON>ARIO_INHERIT_CHILDREN_NOT_FOUND,

    REVENTS_ENGINE_INCORRECT_SCENARIO,
    REVENTS_ENGINE_CRASHED,

    REVENTS_API_RESTARTED,
    REVENTS_API_STREAMING_FAILED,
    REVENTS_API_INCORRECT_PHASE,
    REVENTS_API_NO_CONTEXT_ASSIGNED,

    POST_PROCESSING_VESSEL_VOYAGE_FAILED,
    POST_PROCESSING_VESSEL_VOYAGE_V2_FAILED,
}
