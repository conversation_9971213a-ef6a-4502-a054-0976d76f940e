package nl.teqplay.aisengine.reventsengine.model.scenario

import nl.teqplay.aisengine.reventsengine.model.interest.scenario.Interest
import nl.teqplay.skeleton.model.TimeWindow

/**
 * Model used when creating a scenario.
 */
data class ScenarioCreateRequest(
    override val window: TimeWindow,
    override val windowMargin: Scenario.WindowMargin = Scenario.WindowMargin(),
    override val events: Set<ScenarioEvent> = emptySet(),
    override val postProcessing: Set<ScenarioPostProcessing> = emptySet(),
    override val guarantees: Set<ScenarioGuarantee> = emptySet(),
    override val interests: List<Interest>,
    override val settings: Scenario.Settings? = null,
    override val metaData: Scenario.MetaData? = null,
) : Scenario {

    /**
     * Used to copy one [scenario] into a new one, with the same definitions.
     */
    constructor(scenario: Scenario) : this(
        window = scenario.window,
        windowMargin = scenario.windowMargin,
        events = scenario.events,
        postProcessing = scenario.postProcessing,
        interests = scenario.interests,
        settings = scenario.settings,
        metaData = scenario.metaData
    )
}
