package nl.teqplay.aisengine.event.model

import nl.teqplay.aisengine.event.annotation.PlatformConvertable
import nl.teqplay.aisengine.event.interfaces.EncounterEvent
import nl.teqplay.aisengine.event.interfaces.EncounterMetadata
import nl.teqplay.aisengine.event.interfaces.EndEvent
import nl.teqplay.aisengine.event.interfaces.EventIdentifier
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.event.util.nameAsSubject
import nl.teqplay.skeleton.model.Location
import java.time.Instant

@PlatformConvertable
data class EncounterEndEvent(
    override val _id: EventIdentifier,
    override val startEventId: EventIdentifier?,
    override val ship: AisShipIdentifier,
    override val otherShip: AisShipIdentifier,
    override val encounterType: EncounterEvent.EncounterType,
    override val location: Location,
    override val actualTime: Instant,
    override val createdTime: Instant = Instant.now(),
    override val deleted: Boolean? = null,
    override val regenerated: Boolean? = null,
    override val probability: Double,
    override val metadata: EncounterMetadata? = null,
    /** In some cases we can see when the encouter ends that it was not a valid encounter. This flag indicates that. */
    val valid: Boolean = true,
) : EncounterEvent, EndEvent {
    override fun getEventType() = "encounter"
    override fun getEventSubType(): String {
        val encounterTypeSubject = encounterType.nameAsSubject()

        return "${super.getEventSubType()}.$encounterTypeSubject"
    }
}
