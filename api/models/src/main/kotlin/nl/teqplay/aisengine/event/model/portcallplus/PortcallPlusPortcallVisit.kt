package nl.teqplay.aisengine.event.model.portcallplus

import java.time.Instant

/**
 * Stripped down version of the portcall visit of portcall plus.
 * Providing only the fields that are currently used by the Platform portcalls and the PortReporterMonitor.
 */
data class PortcallPlusPortcallVisit(
    val berthEta: Instant? = null,
    val berthAta: Instant? = null,
    val berthAtd: Instant? = null,
    val berthEtd: Instant? = null,
    val terminal: String? = null,
    val berthName: String? = null,
    val arrivalMovementId: String? = null,
    val departureMovementId: String? = null,
)
