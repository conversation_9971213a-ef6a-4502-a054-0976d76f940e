package nl.teqplay.aisengine.aisstream.model

import nl.teqplay.aisengine.aisstream.model.AisMessage.PositionAccuracy
import nl.teqplay.aisengine.aisstream.model.AisMessage.ShipStatus
import nl.teqplay.aisengine.aisstream.model.AisMessage.SpecialManeuverStatus
import nl.teqplay.skeleton.model.Location

/**
 * AIS position message
 */
data class AisPositionMessage(
    override val mmsi: Int,
    override val location: Location? = null,
    val heading: Int? = null,
    override val positionAccuracy: PositionAccuracy? = null,
    override val speedOverGround: Float? = null,
    override val courseOverGround: Float? = null,
    val status: ShipStatus? = null,
    val rateOfTurn: Int? = null,
    val specialManeuverStatus: SpecialManeuverStatus? = null
) : BaseAisMessage, PositionalAisMessage
