package nl.teqplay.aisengine.event.model.portcallplus

import nl.teqplay.aisengine.event.interfaces.EventIdentifier
import nl.teqplay.aisengine.event.interfaces.PortcallPlusEvent
import nl.teqplay.aisengine.event.interfaces.PortcallVisitsUpdateEvent
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.event.model.identifier.GeneralShipIdentifier
import java.time.Instant

data class PortcallPlusVisitsUpdateEvent(
    override val _id: EventIdentifier,
    override val ship: GeneralShipIdentifier,
    override val portcallId: String,
    override val port: AreaIdentifier,
    override val visits: List<PortcallPlusPortcallVisit>,
    override val vesselAgent: String? = null,
    override val createdTime: Instant = Instant.now(),
    override val deleted: Boolean? = null,
    override val regenerated: Boolean? = null,
) : PortcallVisitsUpdateEvent<PortcallPlusPortcallVisit>, PortcallPlusEvent
