package nl.teqplay.aisengine.event.model.portcallplus

import nl.teqplay.aisengine.event.annotation.PlatformConvertable
import nl.teqplay.aisengine.event.interfaces.EventIdentifier
import nl.teqplay.aisengine.event.interfaces.PortcallAgentChangedEvent
import nl.teqplay.aisengine.event.interfaces.PortcallPlusEvent
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.event.model.identifier.GeneralShipIdentifier
import java.time.Instant

@PlatformConvertable
data class PortcallPlusAgentChangedEvent(
    override val _id: EventIdentifier,
    override val ship: GeneralShipIdentifier,
    override val portcallId: String,
    override val port: AreaIdentifier,
    override val vesselAgent: String,
    override val source: String?,
    override val actualTime: Instant,
    override val createdTime: Instant = Instant.now(),
    override val deleted: Boolean? = null,
    override val regenerated: Boolean? = null
) : PortcallAgentChangedEvent, PortcallPlusEvent
