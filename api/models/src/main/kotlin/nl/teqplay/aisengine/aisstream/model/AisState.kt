package nl.teqplay.aisengine.aisstream.model

interface AisState<S : AisState<S>> {
    val position: AisPositionWrapper?
    val static: AisStaticWrapper?
    val station: AisStationWrapper?
    val longrange: AisLongRangeWrapper?

    fun apply(
        position: AisPositionWrapper?,
        static: AisStaticWrapper?,
        station: AisStationWrapper?,
        longrange: AisLongRangeWrapper?
    ): S
}

fun AisState<*>?.isNullOrEmpty() = this == null || listOfNotNull(position, static, station, longrange).isEmpty()

fun AisState<*>.latestTimestamp() = listOfNotNull(
    station?.timestamp,
    longrange?.timestamp,
    static?.timestamp,
    position?.timestamp
).maxOrNull()
