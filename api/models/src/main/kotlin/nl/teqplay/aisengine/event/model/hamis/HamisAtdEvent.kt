package nl.teqplay.aisengine.event.model.hamis

import nl.teqplay.aisengine.event.annotation.PlatformConvertable
import nl.teqplay.aisengine.event.interfaces.EventIdentifier
import nl.teqplay.aisengine.event.interfaces.HamisEvent
import nl.teqplay.aisengine.event.interfaces.PortcallDepartureEvent
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.event.model.identifier.GeneralShipIdentifier
import java.time.Instant

@PlatformConvertable
data class HamisAtdEvent(
    override val _id: EventIdentifier,
    override val portcallId: String,
    val movementId: String,
    val externalVisitId: String,
    override val ship: GeneralShipIdentifier,
    override val area: AreaIdentifier,
    override val port: AreaIdentifier,
    override val actualTime: Instant,
    override val createdTime: Instant = Instant.now(),
    override val deleted: Boolean? = null,
    override val regenerated: Boolean? = null,
) : PortcallDepartureEvent, HamisEvent
