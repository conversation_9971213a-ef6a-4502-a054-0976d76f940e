package nl.teqplay.aisengine.event.interfaces

import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.event.util.nameAsSubject

sealed interface AreaBasedEvent : Event {
    val area: AreaIdentifier

    override fun getSubject(): String {
        val areaTypeSubject = area.type.nameAsSubject()
        val areaIdentifierSubject = area.id ?: "unknown"
        return "${super.getSubject()}.$areaTypeSubject.$areaIdentifierSubject"
    }
}
