package nl.teqplay.aisengine.event.model.identifier

data class AreaIdentifier(
    val id: String?,
    val type: AreaType,
    val name: String? = null,
    val unlocode: String? = null
) {
    enum class AreaType {
        PORT,
        ANCHOR,

        /**
         * Also known as pilotarea in platform
         */
        PILOT_BOARDING_PLACE,
        TERMINAL,
        BERTH,
        VHF,
        END_OF_SEA_PASSAGE,
        LOCK,
        BASIN,
        TUG,

        /**
         * All nautical mile areas, for example area.rtm12nm
         */
        NAUTICAL_MILE,
        TERMINAL_NEARBY,
        TERMINAL_MOORING_AREA,
        APPROACH_POINT,
        FAIRWAY,
        BRIDGE,

        @Deprecated(message = "Use CUSTOM instead for custom areas.", replaceWith = ReplaceWith("CUSTOM"))
        REGION,
        CUSTOM,

        /**
         * Area type used for backwards compatibility with <PERSON> when there is no indication in the event that it should belong to any kind of area type.
         *
         * For example, ETA/ETD events are about named areas without an indicator what kind of type they should belong to.
         */
        @Deprecated("Do not use this, only for backwards compatibility with <PERSON> when there is no indication in the event that it should belong to any kind of area type")
        UNKNOWN
    }
}
