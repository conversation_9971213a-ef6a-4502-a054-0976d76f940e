package nl.teqplay.aisengine.event.model

import nl.teqplay.aisengine.event.interfaces.AreaBasedStateEvent
import nl.teqplay.aisengine.event.interfaces.AreaEvent
import nl.teqplay.aisengine.event.interfaces.EventIdentifier
import nl.teqplay.aisengine.event.interfaces.StateEvent
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.event.model.identifier.BerthIdentifier
import nl.teqplay.skeleton.model.Location
import java.time.Instant

/**
 * [StateEvent] that indicates a [ship] is outside a specified [area] (opposed to the [StateAreaInsideEvent]).
 * Containing also the latest [speedOverGround] and [location] of the [ship].
 */
data class StateAreaOutsideEvent(
    override val _id: EventIdentifier,
    override val ship: AisShipIdentifier,
    override val area: AreaIdentifier,
    override val berth: BerthIdentifier?,
    override val heading: Int?,
    override val draught: Float?,
    val speedOverGround: Float?,
    override val location: Location,
    override val actualTime: Instant,
    override val createdTime: Instant = Instant.now(),
    override val deleted: Boolean? = null,
    override val regenerated: Boolean? = null,
) : AreaBasedStateEvent, AreaEvent {

    override fun getEventType() = "area"
    override fun getEventSubType() = "state-outside"
}
