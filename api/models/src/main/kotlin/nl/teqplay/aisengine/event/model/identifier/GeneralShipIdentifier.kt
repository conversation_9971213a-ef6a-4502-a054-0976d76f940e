package nl.teqplay.aisengine.event.model.identifier

data class GeneralShipIdentifier(
    override val mmsi: Int?,
    override var imo: Int?
) : ShipIdentifier {
    init {
        assert(this.mmsi != null || this.imo != null) {
            "The MMSI and IMO are both null, cannot create GeneralShipIdentifier"
        }
    }

    override fun getMainIdentifier(): String {
        return mmsi?.toString() ?: "I$imo"
    }
}
