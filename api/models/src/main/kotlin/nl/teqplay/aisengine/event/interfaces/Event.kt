package nl.teqplay.aisengine.event.interfaces

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import nl.teqplay.aisengine.event.model.AisDestinationChangedEvent
import nl.teqplay.aisengine.event.model.AisDraughtChangedEvent
import nl.teqplay.aisengine.event.model.AisEtaChangedEvent
import nl.teqplay.aisengine.event.model.AisLostEvent
import nl.teqplay.aisengine.event.model.AisRecoverEvent
import nl.teqplay.aisengine.event.model.AisStatusChangedEvent
import nl.teqplay.aisengine.event.model.AnchoredEndEvent
import nl.teqplay.aisengine.event.model.AnchoredStartEvent
import nl.teqplay.aisengine.event.model.AreaEndEvent
import nl.teqplay.aisengine.event.model.AreaStartEvent
import nl.teqplay.aisengine.event.model.ConfirmedBerthEndEvent
import nl.teqplay.aisengine.event.model.ConfirmedBerthStartEvent
import nl.teqplay.aisengine.event.model.EncounterEndEvent
import nl.teqplay.aisengine.event.model.EncounterStartEvent
import nl.teqplay.aisengine.event.model.EtaEvent
import nl.teqplay.aisengine.event.model.EtdEvent
import nl.teqplay.aisengine.event.model.LockEtaEvent
import nl.teqplay.aisengine.event.model.LockEtdEvent
import nl.teqplay.aisengine.event.model.PortcallPilotBoardingEtaEvent
import nl.teqplay.aisengine.event.model.ShipMovingEndEvent
import nl.teqplay.aisengine.event.model.ShipMovingStartEvent
import nl.teqplay.aisengine.event.model.SpeedChangedEvent
import nl.teqplay.aisengine.event.model.StateAreaInsideEvent
import nl.teqplay.aisengine.event.model.StateAreaOutsideEvent
import nl.teqplay.aisengine.event.model.StopEndEvent
import nl.teqplay.aisengine.event.model.StopStartEvent
import nl.teqplay.aisengine.event.model.TrueDestinationChangedEvent
import nl.teqplay.aisengine.event.model.UniqueBerthEndEvent
import nl.teqplay.aisengine.event.model.UniqueBerthStartEvent
import nl.teqplay.aisengine.event.model.hamis.HamisAddPortcallVisitEvent
import nl.teqplay.aisengine.event.model.hamis.HamisAgentOrderEvent
import nl.teqplay.aisengine.event.model.hamis.HamisAgentReportsTugsEvent
import nl.teqplay.aisengine.event.model.hamis.HamisAtaEvent
import nl.teqplay.aisengine.event.model.hamis.HamisAtdEvent
import nl.teqplay.aisengine.event.model.hamis.HamisCancelPortcallVisitEvent
import nl.teqplay.aisengine.event.model.hamis.HamisEtaBerthEvent
import nl.teqplay.aisengine.event.model.hamis.HamisEtaCancelEvent
import nl.teqplay.aisengine.event.model.hamis.HamisEtaEvent
import nl.teqplay.aisengine.event.model.hamis.HamisEtaRequestEvent
import nl.teqplay.aisengine.event.model.hamis.HamisEtdEvent
import nl.teqplay.aisengine.event.model.hamis.HamisEtdRequestEvent
import nl.teqplay.aisengine.event.model.hamis.HamisNauticalOrderEvent
import nl.teqplay.aisengine.event.model.hamis.HamisPilotBoardingEtaEvent
import nl.teqplay.aisengine.event.model.hamis.HamisPilotOnBoardEndEvent
import nl.teqplay.aisengine.event.model.hamis.HamisPilotOnBoardStartEvent
import nl.teqplay.aisengine.event.model.hamis.HamisUpdatePortcallVisitEvent
import nl.teqplay.aisengine.event.model.hamis.HamisVisitCancellationEvent
import nl.teqplay.aisengine.event.model.hamis.HamisVisitDeclarationEvent
import nl.teqplay.aisengine.event.model.identifier.ShipIdentifier
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusAgentChangedEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusAtaEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusAtdEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusEtaBerthEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusEtaEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusEtdEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusPortcallFinishEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusShipChangedEvent
import nl.teqplay.aisengine.event.model.portcallplus.PortcallPlusVisitsUpdateEvent
import java.time.Instant

typealias EventIdentifier = String

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type")
@JsonSubTypes(
    // Teqplay events
    JsonSubTypes.Type(value = AisDestinationChangedEvent::class, name = "AisDestinationChangedEvent"),
    JsonSubTypes.Type(value = AisDraughtChangedEvent::class, name = "AisDraughtChangedEvent"),
    JsonSubTypes.Type(value = AisEtaChangedEvent::class, name = "AisEtaChangedEvent"),
    JsonSubTypes.Type(value = AisStatusChangedEvent::class, name = "AisStatusChangedEvent"),
    JsonSubTypes.Type(value = AreaStartEvent::class, name = "AreaStartEvent"),
    JsonSubTypes.Type(value = AreaEndEvent::class, name = "AreaEndEvent"),
    JsonSubTypes.Type(value = StateAreaInsideEvent::class, name = "StateAreaInsideEvent"),
    JsonSubTypes.Type(value = StateAreaOutsideEvent::class, name = "StateAreaOutsideEvent"),
    JsonSubTypes.Type(value = ConfirmedBerthStartEvent::class, name = "ConfirmedBerthStartEvent"),
    JsonSubTypes.Type(value = ConfirmedBerthEndEvent::class, name = "ConfirmedBerthEndEvent"),
    JsonSubTypes.Type(value = UniqueBerthStartEvent::class, name = "UniqueBerthStartEvent"),
    JsonSubTypes.Type(value = UniqueBerthEndEvent::class, name = "UniqueBerthEndEvent"),
    JsonSubTypes.Type(value = EncounterStartEvent::class, name = "EncounterStartEvent"),
    JsonSubTypes.Type(value = EncounterEndEvent::class, name = "EncounterEndEvent"),
    JsonSubTypes.Type(value = ShipMovingStartEvent::class, name = "ShipMovingStartEvent"),
    JsonSubTypes.Type(value = ShipMovingEndEvent::class, name = "ShipMovingEndEvent"),
    JsonSubTypes.Type(value = SpeedChangedEvent::class, name = "SpeedChangedEvent"),
    JsonSubTypes.Type(value = StopStartEvent::class, name = "StopStartEvent"),
    JsonSubTypes.Type(value = StopEndEvent::class, name = "StopEndEvent"),
    JsonSubTypes.Type(value = TrueDestinationChangedEvent::class, name = "TrueDestinationChangedEvent"),
    JsonSubTypes.Type(value = AisLostEvent::class, name = "AisLostEvent"),
    JsonSubTypes.Type(value = AisRecoverEvent::class, name = "AisRecoverEvent"),
    JsonSubTypes.Type(value = AnchoredStartEvent::class, name = "AnchoredStartEvent"),
    JsonSubTypes.Type(value = AnchoredEndEvent::class, name = "AnchoredEndEvent"),
    JsonSubTypes.Type(value = EtaEvent::class, name = "EtaEvent"),
    JsonSubTypes.Type(value = EtdEvent::class, name = "EtdEvent"),
    JsonSubTypes.Type(value = LockEtaEvent::class, name = "LockEtaEvent"),
    JsonSubTypes.Type(value = LockEtdEvent::class, name = "LockEtdEvent"),
    JsonSubTypes.Type(value = PortcallPilotBoardingEtaEvent::class, name = "PortcallPilotBoardingEtaEvent"),

    // Hamis/Iris Events
    JsonSubTypes.Type(value = HamisAtaEvent::class, name = "HamisAtaEvent"),
    JsonSubTypes.Type(value = HamisAtdEvent::class, name = "HamisAtdEvent"),
    JsonSubTypes.Type(value = HamisEtaBerthEvent::class, name = "HamisEtaBerthEvent"),
    JsonSubTypes.Type(value = HamisEtaEvent::class, name = "HamisEtaEvent"),
    JsonSubTypes.Type(value = HamisEtdEvent::class, name = "HamisEtdEvent"),
    JsonSubTypes.Type(value = HamisAddPortcallVisitEvent::class, name = "HamisAddPortcallVisitEvent"),
    JsonSubTypes.Type(value = HamisAgentOrderEvent::class, name = "HamisAgentOrderEvent"),
    JsonSubTypes.Type(value = HamisAgentReportsTugsEvent::class, name = "HamisAgentReportsTugsEvent"),
    JsonSubTypes.Type(value = HamisCancelPortcallVisitEvent::class, name = "HamisCancelPortcallVisitEvent"),
    JsonSubTypes.Type(value = HamisEtaCancelEvent::class, name = "HamisEtaCancelEvent"),
    JsonSubTypes.Type(value = HamisEtaRequestEvent::class, name = "HamisEtaRequestEvent"),
    JsonSubTypes.Type(value = HamisEtdRequestEvent::class, name = "HamisEtdRequestEvent"),
    JsonSubTypes.Type(value = HamisNauticalOrderEvent::class, name = "HamisNauticalOrderEvent"),
    JsonSubTypes.Type(value = HamisPilotBoardingEtaEvent::class, name = "HamisPilotBoardingEtaEvent"),
    JsonSubTypes.Type(value = HamisPilotOnBoardStartEvent::class, name = "HamisPilotOnBoardStartEvent"),
    JsonSubTypes.Type(value = HamisPilotOnBoardEndEvent::class, name = "HamisPilotOnBoardEndEvent"),
    JsonSubTypes.Type(value = HamisUpdatePortcallVisitEvent::class, name = "HamisUpdatePortcallVisitEvent"),
    JsonSubTypes.Type(value = HamisVisitCancellationEvent::class, name = "HamisVisitCancellationEvent"),
    JsonSubTypes.Type(value = HamisVisitDeclarationEvent::class, name = "HamisVisitDeclarationEvent"),

    // PortcallPlus events
    JsonSubTypes.Type(value = PortcallPlusAgentChangedEvent::class, name = "PortcallPlusAgentChangedEvent"),
    JsonSubTypes.Type(value = PortcallPlusAtaEvent::class, name = "PortcallPlusAtaEvent"),
    JsonSubTypes.Type(value = PortcallPlusAtdEvent::class, name = "PortcallPlusAtdEvent"),
    JsonSubTypes.Type(value = PortcallPlusEtaBerthEvent::class, name = "PortcallPlusEtaBerthEvent"),
    JsonSubTypes.Type(value = PortcallPlusEtaEvent::class, name = "PortcallPlusEtaEvent"),
    JsonSubTypes.Type(value = PortcallPlusEtdEvent::class, name = "PortcallPlusEtdEvent"),
    JsonSubTypes.Type(value = PortcallPlusShipChangedEvent::class, name = "PortcallPlusShipChangedEvent"),
    JsonSubTypes.Type(value = PortcallPlusVisitsUpdateEvent::class, name = "PortcallPlusVisitsUpdateEvent"),
    JsonSubTypes.Type(value = PortcallPlusPortcallFinishEvent::class, name = "PortcallPlusPortcallFinishEvent")
)
sealed interface Event {
    val _id: EventIdentifier
    val ship: ShipIdentifier
    val createdTime: Instant
    val deleted: Boolean?
    val regenerated: Boolean?

    @JsonIgnore
    fun getSubject(): String {
        return "${getEventTypePrefix()}.${getEventType()}.${getEventSubType()}"
    }

    @JsonIgnore
    fun getEventTypePrefix(): String

    @JsonIgnore
    fun getEventType(): String

    @JsonIgnore
    fun getEventSubType(): String

    /**
     * Function to get the type needed for SQL filtering or include/exclude filter in event history
     */
    @JsonProperty("type")
    fun getType(): String {
        return this::class.simpleName.toString()
    }
}
