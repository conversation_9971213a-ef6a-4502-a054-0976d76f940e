package nl.teqplay.aisengine.event.model.encountermetadata

import nl.teqplay.aisengine.event.interfaces.EncounterMetadata

data class BoatmanEncounterMetadata(
    /**
     * Flag to indicate that the main vessel for this encounter has an ongoing tug encounter as well. Used in the
     * portreporter monitor to determine which of multiple encounters for this service vessel is most likely to be the
     * correct one.
     */
    val hasSimultaneousTugEncounter: Boolean,
    /**
     * The number of boatmen encounters for the main vessel for this encounter. Duplicate boatmen encounters are often
     * detected because of adjacent berths, but we know that the actual departing vessel must have two boatmen
     * encounters at the same time. The portreporter monitor uses this info to deduce which of multiple encounters is
     * the correct one.
     */
    val nrOfBoatmen: Int,
    /**
     * Flag to indicate whether this encounter takes place during arrival or departure of the main vessel. Used in
     * conjunction with the other metadata in the portreporter monitor to deduce which of multiple encounters is the
     * correct one.
     */
    val arrival: Boolean
) : EncounterMetadata
