package nl.teqplay.aisengine.reventsengine.model.scenario

import nl.teqplay.aisengine.event.interfaces.Event
import nl.teqplay.aisengine.event.model.AisDestinationChangedEvent
import nl.teqplay.aisengine.event.model.AisStatusChangedEvent
import nl.teqplay.aisengine.event.model.AnchoredEndEvent
import nl.teqplay.aisengine.event.model.AnchoredStartEvent
import nl.teqplay.aisengine.event.model.AreaEndEvent
import nl.teqplay.aisengine.event.model.AreaStartEvent
import nl.teqplay.aisengine.event.model.ConfirmedBerthEndEvent
import nl.teqplay.aisengine.event.model.ConfirmedBerthStartEvent
import nl.teqplay.aisengine.event.model.EncounterEndEvent
import nl.teqplay.aisengine.event.model.EncounterStartEvent
import nl.teqplay.aisengine.event.model.ShipMovingEndEvent
import nl.teqplay.aisengine.event.model.ShipMovingStartEvent
import nl.teqplay.aisengine.event.model.StopEndEvent
import nl.teqplay.aisengine.event.model.StopStartEvent
import nl.teqplay.aisengine.event.model.UniqueBerthEndEvent
import nl.teqplay.aisengine.event.model.UniqueBerthStartEvent
import kotlin.reflect.KClass

/**
 * Types of events that can be generated. Maps exactly onto which application will be generating the events.
 *
 * [resolveDependencies] can be used to indicate which dependencies there are for the event to work properly.
 */
enum class ScenarioEvent(
    val app: String,
    val eventTypes: Set<KClass<out Event>>,
    val eventSubjects: List<String>,
) {
    DIFF(
        app = "ais-diff",
        eventTypes = setOf(
            AisStatusChangedEvent::class,
            AisDestinationChangedEvent::class,
            ShipMovingStartEvent::class,
            ShipMovingEndEvent::class,
        ),
        eventSubjects = listOf(
            "event.ais-changed.>",
            "event.ship-moving.>"
        )
    ),
    STOP(
        app = "stop-monitor",
        eventTypes = setOf(
            StopStartEvent::class,
            StopEndEvent::class
        ),
        eventSubjects = listOf("event.stop.>")
    ),
    ANCHOR(
        app = "anchor-monitor",
        eventTypes = setOf(
            AnchoredStartEvent::class,
            AnchoredEndEvent::class,
        ),
        eventSubjects = listOf("event.anchored.>")
    ),
    AREA(
        app = "area-monitor",
        eventTypes = setOf(
            AreaStartEvent::class,
            AreaEndEvent::class,
        ),
        eventSubjects = listOf(
            "event.area.start.>",
            "event.area.end.>"
            // Don't include state events.
        )
    ),
    BERTH(
        app = "berth-monitor",
        eventTypes = setOf(
            ConfirmedBerthStartEvent::class,
            ConfirmedBerthEndEvent::class,
            UniqueBerthStartEvent::class,
            UniqueBerthEndEvent::class,
        ),
        eventSubjects = listOf(
            "event.berth-confirmed.>",
            "event.berth-unique.>"
        )
    ),
    ENCOUNTER(
        app = "encounter-monitor",
        eventTypes = setOf(
            EncounterStartEvent::class,
            EncounterEndEvent::class,
        ),
        eventSubjects = listOf("event.encounter.>")
    )

    ;

    fun resolveDependencies(): List<ScenarioEvent> = when (this) {
        ANCHOR -> listOf(AREA)
        BERTH -> listOf(AREA)
        else -> emptyList()
    }

    companion object {
        const val REVENTS_ENGINE_COMPONENT = "revents-engine"
        const val REVENTS_POISON_PILL_SUBJECT = "event.revents.poison"
        const val REVENTS_BATCH_TIMESTAMP_SUBJECT = "event.revents.batch-timestamp"
    }
}
