package nl.teqplay.aisengine.event.model.hamis

import nl.teqplay.aisengine.event.annotation.PlatformConvertable
import nl.teqplay.aisengine.event.interfaces.EventIdentifier
import nl.teqplay.aisengine.event.interfaces.HamisEvent
import nl.teqplay.aisengine.event.interfaces.PortcallPredictionEvent
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.event.model.identifier.GeneralShipIdentifier
import java.time.Instant

@PlatformConvertable
data class HamisEtaEvent(
    override val _id: EventIdentifier,
    override val portcallId: String,
    override val ship: GeneralShipIdentifier,
    override val area: AreaIdentifier,
    override val port: AreaIdentifier,
    override val source: String?,
    override val berth: String?,
    override val berthId: String?,
    override val vesselAgent: String?,
    override val predictedTime: Instant,
    override val createdTime: Instant = Instant.now(),
    override val deleted: Boolean? = null,
    override val regenerated: Boolean? = null
) : PortcallPredictionEvent, HamisEvent {
    override fun getEventSubType() = "eta"
}
