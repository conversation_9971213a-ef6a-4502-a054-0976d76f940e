package nl.teqplay.aisengine.event.model.hamis

import nl.teqplay.aisengine.event.annotation.PlatformConvertable
import nl.teqplay.aisengine.event.interfaces.AgentReportsTugsEvent
import nl.teqplay.aisengine.event.interfaces.EventIdentifier
import nl.teqplay.aisengine.event.interfaces.HamisEvent
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.aisengine.event.model.identifier.GeneralShipIdentifier
import java.time.Instant

@PlatformConvertable
data class HamisAgentReportsTugsEvent(
    override val _id: EventIdentifier,
    override val ship: GeneralShipIdentifier,
    override val serviceShip: String,
    override val portcallId: String,
    override val port: AreaIdentifier,
    override val berth: AreaIdentifier,
    override val reportedTime: Instant,
    override val movementId: String?,
    override val externalVisitId: String?,
    override val vesselAgent: String?,
    override val towingCompany: String?,
    override val createdTime: Instant = Instant.now(),
    override val deleted: Boolean? = null,
    override val regenerated: Boolean? = null
) : AgentReportsTugsEvent, HamisEvent
