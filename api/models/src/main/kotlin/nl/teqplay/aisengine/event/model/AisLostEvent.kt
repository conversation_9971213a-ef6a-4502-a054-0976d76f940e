package nl.teqplay.aisengine.event.model

import nl.teqplay.aisengine.event.annotation.PlatformConvertable
import nl.teqplay.aisengine.event.interfaces.AisEvent
import nl.teqplay.aisengine.event.interfaces.EventIdentifier
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.skeleton.model.Location
import java.time.Instant

@PlatformConvertable
data class AisLostEvent(
    override val _id: EventIdentifier,
    override val ship: AisShipIdentifier,
    override val location: Location,
    override val actualTime: Instant,
    override val createdTime: Instant = Instant.now(),
    override val deleted: Boolean? = null,
    override val regenerated: Boolean? = null
) : AisEvent {
    override fun getEventSubType() = "lost"
}
