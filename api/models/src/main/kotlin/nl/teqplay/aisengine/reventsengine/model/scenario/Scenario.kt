package nl.teqplay.aisengine.reventsengine.model.scenario

import nl.teqplay.aisengine.reventsengine.model.interest.scenario.Interest
import nl.teqplay.aisengine.reventsengine.model.scenario.Scenario.FilterHistory.ONLY_REAL_TIME
import nl.teqplay.aisengine.reventsengine.model.scenario.Scenario.FilterHistory.USE_ALL_DATA
import nl.teqplay.skeleton.model.TimeWindow

/**
 * Common interface for all scenario models.
 */
interface Scenario {
    /**
     * A time [window], within which the scenario takes place.
     */
    val window: TimeWindow

    /**
     * A margin to be used for the [window].
     * Applying it before the [TimeWindow.from].
     * Or applying it after the [TimeWindow.to].
     */
    val windowMargin: WindowMargin

    /**
     * Which [events] to generate within this scenario.
     * An empty list = all events*
     *
     * * = unless [postProcessing] is specified and post-processing doesn't require all events.
     */
    val events: Set<ScenarioEvent>

    /**
     * Which [postProcessing] to perform after the [events] have been generated within this scenario.
     * An empty list = no post-processing.
     */
    val postProcessing: Set<ScenarioPostProcessing>

    /**
     * Which [guarantees] this scenario should provide.
     * Enforces the use of certain [events], [postProcessing] and [settings] to adhere to the listed guarantees.
     */
    val guarantees: Set<ScenarioGuarantee>

    /**
     * A list of [interests] for this scenario. Can be area-based or target specific ships.
     */
    val interests: List<Interest>

    /**
     * Settings for a [Scenario]. Can be overwritten to not use the defaults.
     */
    val settings: Settings?
    /**
     * User who created the Scenario
     */
    val metaData: MetaData?

    data class WindowMargin(
        val beforeInDays: Long = 0,
        val afterInDays: Long = 0,
    )

    data class Settings(
        /**
         * After resolving area/ship [interests], this determines whether MMSIs should be resolved into IMOs.
         * This resolves the [interests] further by:
         * - removing any ships that don't have an IMO
         * - you only want to use history for a ship if the MMSI was active for a specific IMO at that time
         *
         * Automatically turned on if:
         * - [postProcessing] contains [ScenarioPostProcessing.VESSEL_VOYAGE]
         * - and settings not manually specified
         */
        val resolveShipInterestsForImo: Boolean = false,

        /**
         * When enabled [events] are resolved based on the provided [postProcessing] and [guarantees].
         * If disabled, you need to make sure yourself the proper events are provided for [postProcessing].
         *
         * Events depending on other events are always resolved,
         * otherwise the specified event would not be able to be generated.
         */
        val resolveScenarioEventsByPostProcessing: Boolean = true,

        /**
         * Indicates how to filter on ship history.
         * - [ONLY_REAL_TIME] => only real-time history is used (default)
         * - [USE_ALL_DATA] => no filtering is done, all history is used
         */
        val filterHistory: FilterHistory = ONLY_REAL_TIME,

        /**
         * When resolving the [interests] there can be ships that fit inside the [window] or
         * ones that are overlapping but on the edge of the [window].
         *
         * If the ship is on the edge it will not be returned as an interest,
         * unless [allowPartialInterests] is enabled.
         */
        val allowPartialInterests: Boolean = false,

        /**
         * Decides whether to persist generated [events].
         * Can be turned off when using streamed post-processing and the events are only used for this.
         */
        val persistEvents: Boolean = true,

        /**
         * By default, interests are distributed to multiple scenarios if the interests count exceeds a threshold.
         * This can be disabled via this flag, meaning interests will not be distributed
         * and all will be run on only one instance.
         *
         * This can be used when you:
         * - want to minimize the impact to available parallel scenario slots, forcing only one slot to be used
         * - know that the underlying instance running the scenario will be able to handle the load
         */
        val useInterestDistribution: Boolean = true,
    ) {
        constructor(scenario: Scenario) : this(
            resolveShipInterestsForImo = ScenarioPostProcessing.VESSEL_VOYAGE in scenario.postProcessing ||
                ScenarioPostProcessing.VESSEL_VOYAGE_V2 in scenario.postProcessing,
        )
    }

    data class MetaData(
        val createdBy: String,
        val requestFromSystem: String,
        // Is used to inform the user that this is already a retryAttempt, and should not be retried again
        val isRetryAttempt: Boolean = false
    )

    enum class ResolveAreaInterestsBy {
        VESSEL_VOYAGE,
        POLYGON,
    }

    enum class FilterHistory {
        ONLY_REAL_TIME,
        USE_ALL_DATA,
    }
}
