package nl.teqplay.aisengine.aisstream.model

import nl.teqplay.skeleton.model.Location
import java.time.Instant

/**
 * A common interface for storing state of AIS messages.
 */
interface AisCommonMessage {
    val mmsi: Int
    val messageTime: Instant
    val sources: Set<String>

    // position
    val location: Location
    val heading: Int?
    val positionAccuracy: AisMessage.PositionAccuracy?
    val speedOverGround: Float?
    val courseOverGround: Float?
    val status: AisMessage.ShipStatus?
    val rateOfTurn: Int?
    val specialManeuverStatus: AisMessage.SpecialManeuverStatus?

    // static
    val imo: Int?
    val name: String?
    val callSign: String?
    val shipType: AisMessage.ShipType?
    val draught: Float?
    val eta: Instant?
    val destination: String?
    val transponderPosition: TransponderPosition?
    val positionSensorType: AisMessage.PositionSensorType?
    val aisVersion: AisMessage.AisVersionIndicator?
    val usingDataTerminal: Boolean?

    // other
    val eni: Int?
}
