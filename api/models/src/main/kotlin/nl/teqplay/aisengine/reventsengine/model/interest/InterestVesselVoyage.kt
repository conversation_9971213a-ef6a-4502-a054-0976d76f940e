package nl.teqplay.aisengine.reventsengine.model.interest

import nl.teqplay.skeleton.model.TimeWindow

/**
 * Simplified version of [InterestRelevantShip].
 * An interest that specifies a ship with [imo] was relevant in a given [window].
 * Not exposing which MMSIs were used, which would be done by [InterestRelevantShip].
 */
data class InterestVesselVoyage(
    val imo: Int,
    val window: TimeWindow,
    val windowNoMargin: TimeWindow,
    val partial: Boolean
)
