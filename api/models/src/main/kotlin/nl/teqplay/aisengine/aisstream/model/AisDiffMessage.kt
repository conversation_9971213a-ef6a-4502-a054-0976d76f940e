package nl.teqplay.aisengine.aisstream.model

import nl.teqplay.skeleton.model.Location
import java.time.Instant

data class AisDiffMessage(
    val mmsi: Int,

    val messageTime: Instant,
    val oldMessageTime: Instant?,

    val sources: Set<String>,

    // position
    val location: AisDiffFieldNonNullable<Location>,
    val heading: AisDiffFieldInitialNullable<Int>,
    val positionAccuracy: AisDiffFieldNullable<AisMessage.PositionAccuracy>,
    val speedOverGround: AisDiffFieldNullable<Float>,
    val courseOverGround: AisDiffFieldNullable<Float>,
    val status: AisDiffFieldInitialNullable<AisMessage.ShipStatus>,
    val rateOfTurn: AisDiffFieldInitialNullable<Int>,
    val specialManeuverStatus: AisDiffFieldInitialNullable<AisMessage.SpecialManeuverStatus>,

    // static
    val imo: AisDiffFieldNullable<Int>,
    val name: AisDiffFieldNullable<String>,
    val callSign: AisDiffFieldNullable<String>,
    val shipType: AisDiffFieldNullable<AisMessage.ShipType>,
    val draught: AisDiffFieldNullable<Float>,
    val eta: AisDiffFieldNullable<Instant>,
    val destination: AisDiffFieldNullable<String>,
    val transponderPosition: AisDiffFieldNullable<TransponderPosition>,
    val positionSensorType: AisDiffFieldNullable<AisMessage.PositionSensorType>,
    val aisVersion: AisDiffFieldNullable<AisMessage.AisVersionIndicator>,
    val usingDataTerminal: AisDiffFieldNullable<Boolean>,

    // other
    val eni: AisDiffFieldNullable<Int>,
)
