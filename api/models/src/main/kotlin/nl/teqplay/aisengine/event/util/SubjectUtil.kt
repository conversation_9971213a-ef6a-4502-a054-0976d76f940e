package nl.teqplay.aisengine.event.util

import java.security.MessageDigest

/**
 * Change any enum to an acceptable value that can be used by nats as part of the subject.
 *
 * Do the following with the [Enum.name] field:
 * - lower case
 * - replace all underscores with hyphens
 */
fun Enum<*>.nameAsSubject(): String {
    return this.name.lowercase().replace('_', '-')
}

/**
 * Hash [String] to SHA1 format
 */
fun sha1(input: String?): String? {
    if (input == null) return null

    return MessageDigest
        .getInstance("SHA-1")
        .digest(input.toByteArray())
        .joinToString(separator = "", transform = { "%02x".format(it) })
}
