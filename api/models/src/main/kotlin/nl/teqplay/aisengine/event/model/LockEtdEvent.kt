package nl.teqplay.aisengine.event.model

import nl.teqplay.aisengine.event.annotation.PlatformConvertable
import nl.teqplay.aisengine.event.interfaces.EventIdentifier
import nl.teqplay.aisengine.event.interfaces.LockEvent
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import nl.teqplay.skeleton.model.Location
import java.time.Instant

@PlatformConvertable
data class LockEtdEvent(
    override val _id: EventIdentifier,
    override val isrsId: String,
    override val ship: AisShipIdentifier,
    override val area: AreaIdentifier,
    override val location: Location,
    override val direction: LockEvent.LockDirection,
    override val predictedTime: Instant,
    override val actualTime: Instant,
    override val createdTime: Instant = Instant.now(),
    override val deleted: Boolean? = null,
    override val regenerated: Boolean? = null
) : LockEvent {
    override fun getEventType() = "prediction"
    override fun getEventSubType() = "lock-etd"
}
