package nl.teqplay.aisengine.aisstream.model

import com.fasterxml.jackson.annotation.JsonIgnore
import nl.teqplay.aisengine.aisstream.model.AisMessage.AisVersionIndicator
import nl.teqplay.aisengine.aisstream.model.AisMessage.PositionSensorType
import nl.teqplay.aisengine.aisstream.model.AisMessage.ShipType
import java.time.Instant

data class AisStaticMessage(
    override val mmsi: Int,
    val imo: Int? = null,
    val name: String? = null,
    val callSign: String? = null,
    val shipType: ShipType = ShipType.UNDEFINED,
    val draught: Float? = null,
    val eta: Instant? = null,
    val destination: String? = null,
    val transponderPosition: TransponderPosition? = null,
    val positionSensorType: PositionSensorType? = null,
    val aisVersion: AisVersionIndicator? = null,
    val usingDataTerminal: Boolean = false,
) : BaseAisMessage {

    @JsonIgnore
    val length = transponderPosition?.length ?: 0

    @JsonIgnore
    val width = transponderPosition?.width ?: 0
}
