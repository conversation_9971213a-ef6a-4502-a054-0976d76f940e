package nl.teqplay.aisengine.event.model

import nl.teqplay.aisengine.event.annotation.PlatformConvertable
import nl.teqplay.aisengine.event.interfaces.EventIdentifier
import nl.teqplay.aisengine.event.interfaces.PredictionEvent
import nl.teqplay.aisengine.event.model.identifier.AisShipIdentifier
import nl.teqplay.aisengine.event.model.identifier.AreaIdentifier
import java.time.Instant

// TODO Re-envision model, this should be our ETD event, not related to any external systems?
@PlatformConvertable
data class EtdEvent(
    override val _id: EventIdentifier,
    override val portcallId: String,
    override val ship: AisShipIdentifier,
    override val area: AreaIdentifier,
    override val vesselAgent: String?,
    override val predictedTime: Instant,
    override val createdTime: Instant = Instant.now(),
    override val deleted: Boolean? = null,
    override val regenerated: Boolean? = null
) : PredictionEvent {
    override fun getEventType() = "prediction"
    override fun getEventSubType() = "etd"
}
