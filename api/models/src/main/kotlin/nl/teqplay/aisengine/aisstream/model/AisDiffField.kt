package nl.teqplay.aisengine.aisstream.model

import com.fasterxml.jackson.annotation.JsonFormat

/**
 * Diff field where the old and new value is always provided if changed.
 */
typealias AisDiffFieldNonNullable<T> = AisDiffField<T, T>

/**
 * Diff field where the initial value can be null, but once set is always provided if changed.
 */
typealias AisDiffFieldInitialNullable<T> = AisDiffField<T?, T>

/**
 * Diff field where the old and new value can be both set to null.
 */
typealias AisDiffFieldNullable<T> = AisDiffField<T?, T?>

@JsonFormat(shape = JsonFormat.Shape.ARRAY)
data class AisDiffField<T, U : T>(
    val old: T,
    val changed: Change<U>?
) {
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    data class Change<T>(
        val new: T
    )

    fun latest(): T {
        return if (changed != null) {
            changed.new
        } else {
            old
        }
    }
}
