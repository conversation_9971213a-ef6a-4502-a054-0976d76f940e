package nl.teqplay.aisengine.nats.stream.revents

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.aisengine.aisstream.model.AisDiffMessage
import nl.teqplay.aisengine.nats.stream.AisStreamNatsAutoConfiguration.Companion.AIS_STREAM_DIFF
import nl.teqplay.aisengine.nats.stream.properties.AisStreamNatsProperties
import nl.teqplay.aisengine.revents.ReventsProfile
import nl.teqplay.skeleton.nats.NatsClientBuilder
import nl.teqplay.skeleton.nats.NatsClientHealthBuilder
import nl.teqplay.skeleton.nats.NatsConsumerStream
import org.springframework.boot.actuate.health.HealthIndicator
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
@ReventsProfile
@ConditionalOnProperty(prefix = "nats.ais-stream", name = ["enabled"], havingValue = "true")
class AisStreamConsumeDiffReventsAutoConfiguration(
    private val natsClientBuilder: NatsClientBuilder,
    private val natsClientHealthBuilder: NatsClientHealthBuilder,
    private val properties: AisStreamNatsProperties,
    private val objectMapper: ObjectMapper,
) {
    @Bean
    fun natsAisDiffConsumerStream(): NatsConsumerStream<AisDiffMessage?>? {
        return natsClientBuilder.consumerStream(
            config = properties,
            stream = AIS_STREAM_DIFF,
            deserializer = {
                if (it.isEmpty()) null
                else objectMapper.readValue(it)
            }
        )
    }

    @Bean
    fun natsAisDiffConsumerStreamHealthIndicator(
        consumerStream: NatsConsumerStream<AisDiffMessage?>,
    ): HealthIndicator {
        return natsClientHealthBuilder.natsClientHealthIndicator(consumerStream)
    }
}
