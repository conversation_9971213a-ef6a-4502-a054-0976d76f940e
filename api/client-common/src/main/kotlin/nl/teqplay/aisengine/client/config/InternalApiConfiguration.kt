package nl.teqplay.aisengine.client.config

import nl.teqplay.aisengine.client.annotations.INTERNAL_API_REST_TEMPLATE
import nl.teqplay.aisengine.client.properties.InternalApiProperties
import nl.teqplay.skeleton.auth.credentials.keycloak.s2s.client.KeycloakS2SClientWrapper
import nl.teqplay.skeleton.common.logging.OutgoingRequestLogger
import org.springframework.beans.factory.ObjectProvider
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.web.client.RestTemplate

@Configuration
@EnableConfigurationProperties(InternalApiProperties::class)
class InternalApiConfiguration {

    @Bean(INTERNAL_API_REST_TEMPLATE)
    fun internalApiRestTemplate(
        restTemplateBuilder: RestTemplateBuilder,
        internalApi: InternalApiProperties,
        outgoingRequestLoggerProvider: ObjectProvider<OutgoingRequestLogger>,
    ): RestTemplate = KeycloakS2SClientWrapper.create(
        restTemplateBuilder = restTemplateBuilder,
        outgoingRequestLoggerProvider = outgoingRequestLoggerProvider,
        keycloakS2SClient = internalApi,
        includeBearer = true
    )
}
