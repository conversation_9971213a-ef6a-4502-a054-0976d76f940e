package nl.teqplay.aisengine.client.annotations

import org.springframework.beans.factory.annotation.Qualifier
import java.lang.annotation.Inherited

/** Standard name for the template */
const val INTERNAL_API_REST_TEMPLATE = "internalApiRestTemplate"

/** Allow the template to be used as an annotation */
@Target(AnnotationTarget.VALUE_PARAMETER, AnnotationTarget.FIELD)
@Retention(AnnotationRetention.RUNTIME)
@MustBeDocumented
@Inherited
@Qualifier(INTERNAL_API_REST_TEMPLATE)
annotation class InternalApiRestTemplate
