<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="revents build" type="ShConfigurationType">
    <option name="SCRIPT_TEXT" value="bash app/revents-engine-api/scripts/buildImages.sh" />
    <option name="INDEPENDENT_SCRIPT_PATH" value="true" />
    <option name="INDEPENDENT_SCRIPT_WORKING_DIRECTORY" value="true" />
    <option name="SCRIPT_WORKING_DIRECTORY" value="$PROJECT_DIR$" />
    <option name="INDEPENDENT_INTERPRETER_PATH" value="true" />
    <option name="EXECUTE_IN_TERMINAL" value="false" />
    <option name="EXECUTE_SCRIPT_FILE" value="false" />
    <envs />
    <method v="2" />
  </configuration>
</component>